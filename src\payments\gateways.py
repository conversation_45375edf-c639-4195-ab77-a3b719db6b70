class StripeGateway:
    NAME = 'Stripe'

    def __init__(self, credentials):
        import stripe

        stripe.api_key = credentials['secret_key']
        self.stripe = stripe

    def charge(self, amount, customer_id, description):
        try:
            intent = self.stripe.PaymentIntent.create(
                amount=int(amount * 100),
                currency='usd',
                customer=customer_id,
                description=description,
                automatic_payment_methods={'enabled': True},
            )
            # wait for webhook for final confirmation
            return intent.id
        except self.stripe.error.StripeError as e:
            raise PaymentProviderError(str(e), code=e.code) from e


class PaymentProviderError(Exception):
    pass
