"""
Management command to clear demo data from the tour business management system.
This command removes all demo users, businesses, and related data.
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model

from businesses.models import Business

User = get_user_model()


class Command(BaseCommand):
    help = 'Clear all demo data from the system'

    def add_arguments(self, parser):
        parser.add_argument(
            '--confirm',
            action='store_true',
            help='Confirm deletion of demo data',
        )

    def handle(self, *args, **options):
        if not options['confirm']:
            self.stdout.write(
                self.style.WARNING(
                    'This command will delete all demo data. '
                    'Use --confirm to proceed.'
                )
            )
            return

        self.stdout.write('Clearing demo data...')
        
        # Delete demo users (those with demo_ prefix)
        demo_users = User.objects.filter(username__startswith='demo_')
        user_count = demo_users.count()
        
        # Delete demo businesses (those with Demo prefix)
        demo_businesses = Business.objects.filter(name__startswith='Demo ')
        business_count = demo_businesses.count()
        
        # Delete the data
        demo_users.delete()
        demo_businesses.delete()
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully cleared demo data:\n'
                f'- {user_count} demo users deleted\n'
                f'- {business_count} demo businesses deleted'
            )
        )
