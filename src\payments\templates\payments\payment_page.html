{% extends "base.html" %}

{% block title %}Payment for Invoice {{ invoice.invoice_number }}{% endblock %}

{% block content %}
<div class="max-w-2xl mx-auto">
    <div class="bg-white rounded-lg shadow p-6">
        <!-- Invoice Information -->
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">Payment Required</h1>
            <p class="text-lg text-gray-600">Invoice {{ invoice.invoice_number }}</p>
        </div>

        <!-- Invoice Details -->
        <div class="bg-gray-50 rounded-lg p-6 mb-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">Invoice Details</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <p class="text-sm text-gray-500">From:</p>
                    <p class="font-medium text-gray-900">{{ invoice.business.name }}</p>
                    {% if invoice.business.address %}
                        <p class="text-sm text-gray-600">{{ invoice.business.address }}</p>
                    {% endif %}
                    {% if invoice.business.city %}
                        <p class="text-sm text-gray-600">
                            {{ invoice.business.city }}{% if invoice.business.state_province %}, {{ invoice.business.state_province }}{% endif %}
                        </p>
                    {% endif %}
                </div>
                
                <div>
                    <p class="text-sm text-gray-500">To:</p>
                    <p class="font-medium text-gray-900">{{ invoice.client.display_name }}</p>
                    {% if invoice.client.email %}
                        <p class="text-sm text-gray-600">{{ invoice.client.email }}</p>
                    {% endif %}
                </div>
            </div>
            
            <div class="mt-6 pt-6 border-t border-gray-200">
                <div class="flex justify-between items-center">
                    <div>
                        <p class="text-sm text-gray-500">Issue Date:</p>
                        <p class="font-medium text-gray-900">{{ invoice.issue_date|date:"F d, Y" }}</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">Due Date:</p>
                        <p class="font-medium text-gray-900">{{ invoice.due_date|date:"F d, Y" }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Amount Due -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
            <div class="text-center">
                <p class="text-sm text-blue-600 font-medium">Amount Due</p>
                <p class="text-4xl font-bold text-blue-900">{{ invoice.total_amount }}</p>
            </div>
        </div>

        <!-- Payment Instructions -->
        <div class="text-center">
            <p class="text-gray-600 mb-6">
                Click the button below to proceed with secure payment via Lemon Squeezy.
            </p>
            
            <a href="{{ payment_link.lemon_squeezy_checkout_url }}" 
               class="inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                </svg>
                Pay Now
            </a>
        </div>

        <!-- Security Notice -->
        <div class="mt-8 pt-6 border-t border-gray-200">
            <div class="flex items-center justify-center text-sm text-gray-500">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                </svg>
                Secure payment powered by Lemon Squeezy
            </div>
        </div>
    </div>
</div>
{% endblock %}
