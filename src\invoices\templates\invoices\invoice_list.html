{% load i18n cotton %}

<c-layouts.dashboard>
  <c-slot name="title">
    {% trans "Invoices" %}
  </c-slot>

  <c-slot name="main">
    <div class="container mx-auto px-4 py-8">
      <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900">{% trans "Invoices" %}</h1>
        <a href="{% url 'invoices:create' %}" class="btn btn-primary">
          {% trans "Create Invoice" %}
        </a>
      </div>

      <!-- Filter tabs -->
      <div class="mb-6">
        <nav class="flex space-x-8">
          <a href="?status=" class="tab-link {% if not request.GET.status %}active{% endif %}">
            {% trans "All" %}
          </a>
          <a href="?status=draft" class="tab-link {% if request.GET.status == 'draft' %}active{% endif %}">
            {% trans "Draft" %}
          </a>
          <a href="?status=sent" class="tab-link {% if request.GET.status == 'sent' %}active{% endif %}">
            {% trans "Sent" %}
          </a>
          <a href="?status=paid" class="tab-link {% if request.GET.status == 'paid' %}active{% endif %}">
            {% trans "Paid" %}
          </a>
          <a href="?status=overdue" class="tab-link {% if request.GET.status == 'overdue' %}active{% endif %}">
            {% trans "Overdue" %}
          </a>
        </nav>
      </div>

      <!-- Grid view for invoice cards -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {% for invoice in invoices %}
        <c-invoice-card invoice="{{ invoice }}" show_actions="True" business_theme="True" />
        {% empty %}
        <div class="col-span-full text-center py-12">
          <div class="text-gray-500">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">{% trans "No invoices" %}</h3>
            <p class="mt-1 text-sm text-gray-500">{% trans "Get started by creating a new invoice." %}</p>
            <div class="mt-6">
              <a href="{% url 'invoices:create' %}" class="btn btn-primary">
                {% trans "Create Invoice" %}
              </a>
            </div>
          </div>
        </div>
        {% endfor %}
      </div>

      <!-- Pagination -->
      {% if is_paginated %}
      <div class="flex justify-center mt-8">
        <nav class="flex space-x-1">
          {% if page_obj.has_previous %}
          <a href="?page=1{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}"
            class="pagination-btn">First</a>
          <a href="?page={{ page_obj.previous_page_number }}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}"
            class="pagination-btn">Previous</a>
          {% endif %}

          {% for num in page_obj.paginator.page_range %}
          {% if page_obj.number == num %}
          <span class="pagination-btn pagination-current">{{ num }}</span>
          {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
          <a href="?page={{ num }}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}"
            class="pagination-btn">{{ num }}</a>
          {% endif %}
          {% endfor %}

          {% if page_obj.has_next %}
          <a href="?page={{ page_obj.next_page_number }}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}"
            class="pagination-btn">Next</a>
          <a href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}"
            class="pagination-btn">Last</a>
          {% endif %}
        </nav>
      </div>
      {% endif %}
    </div>

    <style>
      .tab-link {
        @apply py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300;
      }

      .tab-link.active {
        @apply border-blue-500 text-blue-600;
      }

      .pagination-btn {
        @apply px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 hover:bg-gray-50 hover:text-gray-700;
      }

      .pagination-current {
        @apply bg-blue-50 border-blue-500 text-blue-600;
      }
    </style>
  </c-slot>
</c-layouts.dashboard>
