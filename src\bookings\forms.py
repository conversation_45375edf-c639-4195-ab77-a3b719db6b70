"""
Forms for bookings app.
"""

from crispy_forms.helper import FormHelper
from crispy_forms.layout import Column, Field, Layout, Row
from django import forms
from djmoney.forms import MoneyWidget

from .models import Booking


class BookingForm(forms.ModelForm):
    """Form for creating and editing bookings."""

    class Meta:
        model = Booking
        fields = [
            'business',
            'client',
            'number_of_participants',
            'total_amount',
            'deposit_amount',
            'special_requests',
        ]
        widgets = {
            'business': forms.Select(
                attrs={
                    'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500'
                }
            ),
            'client': forms.Select(
                attrs={
                    'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500'
                }
            ),
            'number_of_participants': forms.NumberInput(
                attrs={
                    'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500',
                    'min': 1,
                }
            ),
            'total_amount': MoneyWidget(
                attrs={
                    'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500',
                }
            ),
            'deposit_amount': MoneyWidget(
                attrs={
                    'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500',
                }
            ),
            'special_requests': forms.Textarea(
                attrs={
                    'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500',
                    'rows': 3,
                    'placeholder': 'Any special requests or requirements',
                }
            ),
        }

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)

        # Filter choices based on user permissions
        if self.user and not self.user.is_superuser:
            from django.db.models import Q

            from businesses.models import Business
            from clients.models import Client

            accessible_businesses = Business.objects.filter(
                Q(created_by=self.user) | Q(users=self.user)
            ).distinct()

            self.fields['business'].queryset = accessible_businesses
            self.fields['client'].queryset = Client.objects.filter(
                business__in=accessible_businesses
            )
            self.fields['tour_event'].queryset = TourEvent.objects.filter(
                business__in=accessible_businesses
            )

        # Setup crispy forms
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_class = 'space-y-6'

        # Custom layout with Tailwind classes
        self.helper.layout = Layout(
            Row(
                Column(
                    Field(
                        'business',
                        css_class='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500',
                    ),
                    css_class='form-group col-md-6',
                ),
                Column(
                    Field(
                        'client',
                        css_class='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500',
                    ),
                    css_class='form-group col-md-6',
                ),
            ),
            Field(
                'tour_event',
                css_class='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500',
            ),
            Row(
                Column(
                    Field(
                        'number_of_participants',
                        css_class='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500',
                    ),
                    css_class='form-group col-md-6',
                ),
                Column(
                    Field(
                        'total_amount',
                        css_class='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500',
                    ),
                    css_class='form-group col-md-6',
                ),
            ),
            Field(
                'deposit_amount',
                css_class='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500',
            ),
            Field(
                'special_requests',
                css_class='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 resize-vertical',
            ),
        )

    def clean(self):
        """Validate the booking data."""
        cleaned_data = super().clean()
        tour_event = cleaned_data.get('tour_event')
        number_of_participants = cleaned_data.get('number_of_participants')
        total_amount = cleaned_data.get('total_amount')
        deposit_amount = cleaned_data.get('deposit_amount')

        # Validate capacity
        if tour_event and number_of_participants:
            if not tour_event.can_add_participants(number_of_participants):
                raise forms.ValidationError(
                    f'Not enough capacity. Only {tour_event.available_spots} spots available.'
                )

        # Validate deposit amount
        if total_amount and deposit_amount:
            if deposit_amount > total_amount:
                raise forms.ValidationError(
                    'Deposit amount cannot be greater than total amount.'
                )

        return cleaned_data
