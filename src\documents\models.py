from django.db import models

from bookings.models import Booking


class BookingDocument(models.Model):
    DOCUMENT_TYPES = (
        ('PASSPORT', 'Passport Copy'),
        ('WAIVER', 'Signed Waiver'),
        ('INSURANCE', 'Travel Insurance'),
    )
    booking = models.ForeignKey(Booking, on_delete=models.CASCADE)
    doc_type = models.CharField(max_length=20, choices=DOCUMENT_TYPES)
    file = models.FileField(upload_to='booking_docs/%Y/%m/')
    uploaded_at = models.DateTimeField(auto_now_add=True)
