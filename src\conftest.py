"""
Pytest configuration and fixtures for the tour business management system.
"""
import pytest
from django.contrib.auth import get_user_model
from django.test import Client
from django.utils import timezone
from datetime import timedelta
from decimal import Decimal

from businesses.models import Business
from clients.models import Client
from quotes.models import Quote
from invoices.models import Invoice
from payments.models import PaymentLink, Subscription, Payment

User = get_user_model()


@pytest.fixture
def api_client():
    """Provide a Django test client."""
    return Client()


@pytest.fixture
def user():
    """Create a test user."""
    return User.objects.create_user(
        username='testuser',
        email='<EMAIL>',
        password='testpass123'
    )


@pytest.fixture
def business_owner():
    """Create a business owner user."""
    return User.objects.create_user(
        username='business_owner',
        email='<EMAIL>',
        password='testpass123'
    )


@pytest.fixture
def business(business_owner):
    """Create a test business."""
    return Business.objects.create(
        name='Test Tour Company',
        email='<EMAIL>',
        phone='******-123-4567',
        address='123 Test Street',
        city='Test City',
        state_province='TS',
        country='US',
        postal_code='12345',
        website='https://testtours.com',
        description='A test tour company',
        primary_color='#007bff',
        secondary_color='#6c757d',
        accent_color='#28a745',
        created_by=business_owner
    )


@pytest.fixture
def client_record(business):
    """Create a test client."""
    return Client.objects.create(
        business=business,
        first_name='John',
        last_name='Doe',
        email='<EMAIL>',
        phone='******-987-6543',
        date_of_birth='1985-06-15',
        nationality='US',
        passport_number='*********',
        emergency_contact_name='Jane Doe',
        emergency_contact_phone='******-987-6544',
        dietary_restrictions='Vegetarian',
        medical_conditions='None',
        travel_preferences='Adventure tours, hiking'
    )


@pytest.fixture
def quote(business, client_record):
    """Create a test quote."""
    return Quote.objects.create(
        business=business,
        client=client_record,
        title='7-Day Adventure Package',
        description='Complete adventure tour package including hiking, rafting, and camping',
        subtotal=Decimal('1500.00'),
        tax_amount=Decimal('150.00'),
        total_amount=Decimal('1650.00'),
        currency='USD',
        valid_until=timezone.now().date() + timedelta(days=30),
        terms_conditions='Payment required within 30 days of approval'
    )


@pytest.fixture
def approved_quote(quote):
    """Create an approved quote."""
    quote.status = Quote.Status.APPROVED
    quote.save()
    return quote


@pytest.fixture
def invoice(business, client_record, approved_quote):
    """Create a test invoice."""
    return Invoice.objects.create(
        business=business,
        client=client_record,
        quote=approved_quote,
        subtotal=approved_quote.subtotal,
        tax_amount=approved_quote.tax_amount,
        total_amount=approved_quote.total_amount,
        currency=approved_quote.currency,
        issue_date=timezone.now().date(),
        due_date=timezone.now().date() + timedelta(days=30),
        payment_terms='Net 30',
        terms_conditions='Payment due within 30 days'
    )


@pytest.fixture
def payment_link(invoice, user):
    """Create a test payment link."""
    return PaymentLink.objects.create(
        invoice=invoice,
        expires_at=timezone.now() + timedelta(days=30),
        lemon_squeezy_checkout_url='https://checkout.lemonsqueezy.com/test',
        lemon_squeezy_checkout_id='test_checkout_123',
        created_by=user
    )


@pytest.fixture
def subscription(business):
    """Create a test subscription."""
    return Subscription.objects.create(
        business=business,
        lemon_squeezy_subscription_id='test_sub_123',
        lemon_squeezy_customer_id='test_customer_123',
        status=Subscription.Status.ACTIVE,
        plan=Subscription.Plan.BASIC,
        current_period_start=timezone.now(),
        current_period_end=timezone.now() + timedelta(days=30)
    )


@pytest.fixture
def payment(invoice):
    """Create a test payment."""
    return Payment.objects.create(
        invoice=invoice,
        amount=invoice.total_amount,
        payment_method=Payment.PaymentMethod.LEMON_SQUEEZY,
        status=Payment.Status.COMPLETED,
        lemon_squeezy_order_id='test_order_123',
        external_payment_id='test_payment_123',
        payment_processor='lemon_squeezy',
        reference_number='REF123'
    )


@pytest.fixture
def authenticated_client(api_client, user):
    """Provide an authenticated test client."""
    api_client.force_login(user)
    return api_client


@pytest.fixture
def business_authenticated_client(api_client, business_owner):
    """Provide an authenticated test client for business owner."""
    api_client.force_login(business_owner)
    return api_client


# Mock fixtures for external services
@pytest.fixture
def mock_lemon_squeezy_response():
    """Mock Lemon Squeezy API response."""
    return {
        'data': {
            'id': 'test_123',
            'type': 'checkouts',
            'attributes': {
                'url': 'https://checkout.lemonsqueezy.com/test_123',
                'store_id': 1,
                'variant_id': 1,
                'custom': {
                    'invoice_id': '1',
                    'business_id': '1'
                }
            }
        }
    }


@pytest.fixture
def mock_webhook_payload():
    """Mock webhook payload from Lemon Squeezy."""
    return {
        'meta': {
            'event_name': 'order_created'
        },
        'data': {
            'id': 'test_order_123',
            'type': 'orders',
            'attributes': {
                'store_id': 1,
                'customer_id': 1,
                'order_number': 'ORD-123',
                'user_email': '<EMAIL>',
                'total': 1650,
                'currency': 'USD',
                'status': 'paid',
                'custom': {
                    'invoice_id': '1',
                    'business_id': '1'
                }
            }
        }
    }


# Database fixtures
@pytest.fixture(scope='session')
def django_db_setup():
    """Configure the test database."""
    pass


@pytest.fixture
def transactional_db(db):
    """Provide transactional database access."""
    pass


# Pytest markers
pytestmark = pytest.mark.django_db
