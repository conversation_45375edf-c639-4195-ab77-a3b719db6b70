"""
Data generation services for businesses app using model_bakery.
"""

import random
from model_bakery import baker
from faker import Faker

from .models import Business

fake = Faker()


class BusinessesDataGenerator:
    """Data generator for businesses app models."""

    def __init__(self):
        self.created_objects = {
            'businesses': []
        }

    def generate_businesses(self, count=5, users=None):
        """Generate businesses with realistic data."""
        # Ensure we have at least one user to assign as created_by
        if not users:
            from accounts.data_generation import AccountsDataGenerator
            accounts_gen = AccountsDataGenerator()
            users = accounts_gen.generate_users(1)

        businesses = []

        # Business type themes
        business_themes = {
            'safari': {
                'names': [
                    'African Safari Adventures', 'Serengeti Expeditions', 'Big Five Safaris',
                    'Wilderness Safaris', 'Maasai Mara Tours', 'Kilimanjaro Adventures',
                    'Ngorongoro Expeditions', 'Amboseli Safari Co', 'Tsavo Wildlife Tours'
                ],
                'descriptions': [
                    'Authentic African safari experiences with expert guides',
                    'Wildlife photography and conservation tours',
                    'Luxury safari adventures in East Africa',
                    'Family-friendly safari experiences'
                ],
                'countries': ['Kenya', 'Tanzania', 'South Africa', 'Botswana', 'Namibia']
            },
            'cultural': {
                'names': [
                    'Heritage Cultural Tours', 'Ancient Civilizations', 'Cultural Immersion Tours',
                    'Historical Journeys', 'Traditional Arts Tours', 'Local Culture Experiences',
                    'Anthropological Adventures', 'Living Heritage Tours'
                ],
                'descriptions': [
                    'Immersive cultural experiences with local communities',
                    'Historical and archaeological site tours',
                    'Traditional arts and crafts workshops',
                    'Authentic cultural exchange programs'
                ],
                'countries': ['Peru', 'India', 'Morocco', 'Egypt', 'Greece', 'Japan']
            },
            'adventure': {
                'names': [
                    'Mountain Peak Adventures', 'Extreme Expeditions', 'Alpine Adventures',
                    'Summit Seekers', 'Adrenaline Tours', 'Outdoor Odyssey', 'Wild Adventures',
                    'Peak Performance Tours', 'Adventure Unlimited'
                ],
                'descriptions': [
                    'Thrilling outdoor adventures for adrenaline seekers',
                    'Mountain climbing and trekking expeditions',
                    'Extreme sports and adventure activities',
                    'Multi-day wilderness adventures'
                ],
                'countries': ['Nepal', 'Chile', 'New Zealand', 'Switzerland', 'Canada', 'Norway']
            },
            'eco': {
                'names': [
                    'Green Earth Tours', 'Eco Adventures', 'Sustainable Travel Co',
                    'Nature First Tours', 'Conservation Expeditions', 'Eco-Friendly Journeys',
                    'Responsible Travel', 'Planet Positive Tours'
                ],
                'descriptions': [
                    'Sustainable and eco-friendly travel experiences',
                    'Conservation-focused wildlife tours',
                    'Carbon-neutral adventure packages',
                    'Environmental education tours'
                ],
                'countries': ['Costa Rica', 'Ecuador', 'Madagascar', 'Bhutan', 'Iceland']
            },
            'luxury': {
                'names': [
                    'Platinum Experiences', 'Elite Travel', 'Luxury Escapes',
                    'Premium Adventures', 'Five Star Expeditions', 'Exclusive Journeys',
                    'VIP Travel Services', 'Luxury Safari Co'
                ],
                'descriptions': [
                    'Ultra-luxury travel experiences with personalized service',
                    'Exclusive access to premium destinations',
                    'High-end accommodations and private guides',
                    'Bespoke luxury adventure packages'
                ],
                'countries': ['Maldives', 'Dubai', 'Monaco', 'Seychelles', 'French Polynesia']
            }
        }

        for i in range(count):
            # Select random theme
            theme_name = random.choice(list(business_themes.keys()))
            theme = business_themes[theme_name]

            # Select creator user
            created_by = random.choice(users) if users else None

            business = baker.make(
                Business,
                name=random.choice(theme['names']),
                description=random.choice(theme['descriptions']),
                email=fake.company_email(),
                phone=fake.phone_number(),
                website=f'https://www.{theme_name}tours{i+1}.com',

                # Address information
                address_line1=fake.street_address(),
                address_line2=fake.secondary_address() if fake.boolean(chance_of_getting_true=30) else '',
                city=fake.city(),
                state_province=fake.state(),
                postal_code=fake.postcode(),
                country=random.choice(theme['countries']),

                # Business settings
                timezone=random.choice([
                    'UTC', 'America/New_York', 'Europe/London', 'Asia/Tokyo',
                    'Africa/Nairobi', 'Australia/Sydney', 'America/Los_Angeles'
                ]),
                currency=random.choice(['USD', 'EUR', 'GBP', 'KES', 'TZS', 'ZAR']),

                # Subscription and status
                plan=random.choice(['basic', 'premium', 'enterprise']),
                is_active=fake.boolean(chance_of_getting_true=95),

                # Branding
                primary_color=fake.hex_color(),
                secondary_color=fake.hex_color(),
                accent_color=fake.hex_color(),

                # Templates
                quote_template=random.choice(['default', 'modern', 'classic']),
                invoice_template=random.choice(['default', 'professional', 'minimal']),

                # Custom content
                email_signature=f'Best regards,\n{random.choice(theme["names"])}\n{fake.phone_number()}\n{fake.company_email()}' if fake.boolean(chance_of_getting_true=60) else '',

                created_by=created_by,
            )

            businesses.append(business)

        self.created_objects['businesses'].extend(businesses)
        return businesses

    def generate_themed_business(self, theme, users=None):
        """Generate a single business with specific theme."""
        business_themes = {
            'safari': {
                'name': 'African Safari Adventures',
                'description': 'Authentic African safari experiences with expert guides and luxury accommodations',
                'country': 'Kenya',
                'currency': 'KES',
                'timezone': 'Africa/Nairobi'
            },
            'cultural': {
                'name': 'Heritage Cultural Tours',
                'description': 'Immersive cultural experiences connecting travelers with local traditions',
                'country': 'Peru',
                'currency': 'USD',
                'timezone': 'America/Lima'
            },
            'adventure': {
                'name': 'Mountain Peak Adventures',
                'description': 'Thrilling outdoor adventures and mountain expeditions for all skill levels',
                'country': 'Nepal',
                'currency': 'USD',
                'timezone': 'Asia/Kathmandu'
            },
            'eco': {
                'name': 'Green Earth Tours',
                'description': 'Sustainable and eco-friendly travel experiences focused on conservation',
                'country': 'Costa Rica',
                'currency': 'USD',
                'timezone': 'America/Costa_Rica'
            },
            'luxury': {
                'name': 'Platinum Experiences',
                'description': 'Ultra-luxury travel experiences with personalized service and exclusive access',
                'country': 'Maldives',
                'currency': 'USD',
                'timezone': 'Indian/Maldives'
            }
        }

        if theme not in business_themes:
            theme = 'safari'  # Default fallback

        theme_data = business_themes[theme]
        created_by = random.choice(users) if users else None

        business = baker.make(
            Business,
            name=theme_data['name'],
            description=theme_data['description'],
            email=fake.company_email(),
            phone=fake.phone_number(),
            website=f'https://www.{theme}tours.com',

            # Address information
            address_line1=fake.street_address(),
            city=fake.city(),
            state_province=fake.state(),
            postal_code=fake.postcode(),
            country=theme_data['country'],

            # Business settings
            timezone=theme_data['timezone'],
            currency=theme_data['currency'],

            # Premium settings for themed businesses
            plan='premium',
            is_active=True,

            # Branding
            primary_color='#007bff',
            secondary_color='#6c757d',
            accent_color='#28a745',

            # Templates
            quote_template='modern',
            invoice_template='professional',

            created_by=created_by,
        )

        self.created_objects['businesses'].append(business)
        return business

    def get_generation_summary(self):
        """Get summary of generated objects."""
        return {
            key: len(objects) for key, objects in self.created_objects.items()
        }
