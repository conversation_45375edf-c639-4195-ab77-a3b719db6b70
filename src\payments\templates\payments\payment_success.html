{% extends "base.html" %}

{% block title %}Payment Successful{% endblock %}

{% block content %}
<div class="max-w-2xl mx-auto">
    <div class="bg-white rounded-lg shadow p-6">
        <div class="text-center">
            <!-- Success Icon -->
            <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-6">
                <svg class="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
            </div>
            
            <h1 class="text-3xl font-bold text-gray-900 mb-4">Payment Successful!</h1>
            
            {% if message %}
                <p class="text-lg text-gray-600 mb-6">{{ message }}</p>
            {% else %}
                <p class="text-lg text-gray-600 mb-6">
                    Your payment has been processed successfully. Thank you for your business!
                </p>
            {% endif %}
            
            {% if payment_link.invoice %}
                <div class="bg-gray-50 rounded-lg p-4 mb-6">
                    <p class="text-sm text-gray-500">Invoice Number</p>
                    <p class="text-xl font-semibold text-gray-900">{{ payment_link.invoice.invoice_number }}</p>
                </div>
            {% endif %}
            
            <div class="space-y-3">
                <p class="text-sm text-gray-500">
                    You will receive a confirmation email shortly with your receipt.
                </p>
                
                <div class="pt-4">
                    <a href="/" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors">
                        Return to Home
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
