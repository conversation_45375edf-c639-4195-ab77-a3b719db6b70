"""
Views for businesses app.
"""

from django.contrib import messages
from django.contrib.auth.mixins import LoginRequiredMixin
from django.db.models import Q
from django.shortcuts import get_object_or_404
from django.urls import reverse_lazy
from django.views.generic import Create<PERSON>iew, DetailView, ListView, UpdateView

from .models import Business


class BusinessListView(LoginRequiredMixin, ListView):
    """List view for businesses."""

    model = Business
    template_name = 'businesses/business_list.html'
    context_object_name = 'businesses'
    paginate_by = 20

    def get_queryset(self):
        """Filter businesses based on user permissions."""
        queryset = Business.objects.filter(is_active=True)

        # If user is not superuser, only show their businesses
        if not self.request.user.is_superuser:
            queryset = queryset.filter(
                Q(created_by=self.request.user) | Q(users=self.request.user)
            ).distinct()

        # Search functionality
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search)
                | Q(email__icontains=search)
                | Q(city__icontains=search)
            )

        return queryset.order_by('name')


class BusinessDetailView(LoginRequiredMixin, DetailView):
    """Detail view for a business."""

    model = Business
    template_name = 'businesses/business_detail.html'
    context_object_name = 'business'

    def get_object(self):
        """Get business and check permissions."""
        business = get_object_or_404(Business, pk=self.kwargs['pk'])

        # Check if user has access to this business
        if not self.request.user.is_superuser:
            if (
                business.created_by != self.request.user
                and self.request.user not in business.users.all()
            ):
                raise PermissionError("You don't have access to this business")

        return business


class BusinessCreateView(LoginRequiredMixin, CreateView):
    """Create view for businesses."""

    model = Business
    template_name = 'businesses/business_form.html'
    fields = '__all__'
    success_url = reverse_lazy('businesses:list')

    def form_valid(self, form):
        """Set the created_by field."""
        form.instance.created_by = self.request.user
        messages.success(
            self.request, f'Business "{form.instance.name}" created successfully!'
        )
        return super().form_valid(form)


class BusinessUpdateView(LoginRequiredMixin, UpdateView):
    """Update view for businesses."""

    model = Business
    template_name = 'businesses/business_form.html'
    fields = [
        'name',
        'email',
        'phone',
        'address',
        'city',
        'state_province',
        'country',
        'postal_code',
        'website',
        'description',
        'primary_color',
        'secondary_color',
        'accent_color',
        'logo',
        'custom_css',
        'email_signature',
        'quote_template',
        'invoice_template',
    ]

    def get_object(self):
        """Get business and check permissions."""
        business = get_object_or_404(Business, pk=self.kwargs['pk'])

        # Check if user has access to this business
        if not self.request.user.is_superuser:
            if (
                business.created_by != self.request.user
                and self.request.user.profile not in business.profiles.all()
            ):
                raise PermissionError("You don't have access to this business")

        return business

    def form_valid(self, form):
        """Handle successful form submission."""
        messages.success(
            self.request, f'Business "{form.instance.name}" updated successfully!'
        )
        return super().form_valid(form)

    def get_success_url(self):
        """Return to business detail page."""
        return reverse_lazy('businesses:detail', kwargs={'pk': self.object.pk})


class BusinessDashboardView(LoginRequiredMixin, DetailView):
    """Dashboard view for a business."""

    model = Business
    template_name = 'businesses/business_dashboard.html'
    context_object_name = 'business'

    def get_object(self):
        """Get business and check permissions."""
        business = get_object_or_404(Business, pk=self.kwargs['pk'])

        # Check if user has access to this business
        if not self.request.user.is_superuser:
            if (
                business.created_by != self.request.user
                and self.request.user not in business.users.all()
            ):
                raise PermissionError("You don't have access to this business")

        return business

    def get_context_data(self, **kwargs):
        """Add dashboard statistics."""
        context = super().get_context_data(**kwargs)
        business = self.get_object()

        # Get recent statistics
        from clients.models import Client
        from invoices.models import Invoice
        from payments.models import Payment
        from quotes.models import Quote

        context.update(
            {
                'total_clients': Client.objects.filter(business=business).count(),
                'total_quotes': Quote.objects.filter(business=business).count(),
                'total_invoices': Invoice.objects.filter(business=business).count(),
                'total_payments': Payment.objects.filter(business=business).count(),
                'recent_clients': Client.objects.filter(business=business).order_by(
                    '-created_at'
                )[:5],
                'recent_quotes': Quote.objects.filter(business=business).order_by(
                    '-created_at'
                )[:5],
                'recent_invoices': Invoice.objects.filter(business=business).order_by(
                    '-created_at'
                )[:5],
            }
        )

        return context
