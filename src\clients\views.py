"""
Views for clients app.
"""

from django.contrib import messages
from django.contrib.auth.mixins import LoginRequiredMixin
from django.db.models import Q
from django.shortcuts import get_object_or_404
from django.urls import reverse_lazy
from django.views.generic import (
    CreateView,
    DeleteView,
    DetailView,
    ListView,
    UpdateView,
)

from businesses.models import Business
from clients.forms import ClientForm

from .models import Client


class ClientListView(LoginRequiredMixin, ListView):
    """List view for clients."""

    model = Client
    template_name = 'clients/client_list.html'
    context_object_name = 'clients'
    paginate_by = 20

    def get_queryset(self):
        """Filter clients based on user's business access."""
        queryset = Client.objects.filter(is_active=True)

        # Filter by user's accessible businesses
        # if not self.request.user.is_superuser:
        #     accessible_businesses = Business.objects.filter(
        #         Q(created_by=self.request.user) | Q(users=self.request.user)
        #     ).distinct()
        #     queryset = queryset.filter(business__in=accessible_businesses)

        # Search functionality
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(first_name__icontains=search)
                | Q(last_name__icontains=search)
                | Q(email__icontains=search)
                | Q(phone__icontains=search)
            )

        # Filter by business
        business_id = self.request.GET.get('business')
        if business_id:
            queryset = queryset.filter(business_id=business_id)

        return queryset.order_by('last_name', 'first_name')


class ClientDetailView(LoginRequiredMixin, DetailView):
    """Detail view for a client."""

    model = Client
    template_name = 'clients/client_detail.html'
    context_object_name = 'client'

    def get_object(self):
        """Get client and check permissions."""
        client = get_object_or_404(Client, pk=self.kwargs['pk'])

        # Check if user has access to this client's business
        if not self.request.user.is_superuser:
            if (
                client.business.created_by != self.request.user
                and self.request.user not in client.business.users.all()
            ):
                raise PermissionError("You don't have access to this client")

        return client

    def get_context_data(self, **kwargs):
        """Add related data to context."""
        context = super().get_context_data(**kwargs)
        client = self.get_object()

        # Get related quotes, invoices, and bookings
        context.update(
            {
                'quotes': client.quotes.all().order_by('-created_at')[:10],
                'invoices': client.invoices.all().order_by('-created_at')[:10],
                'bookings': client.bookings.all().order_by('-created_at')[:10],
            }
        )

        return context


class ClientCreateView(LoginRequiredMixin, CreateView):
    """Create view for clients."""

    model = Client
    form_class = ClientForm
    template_name = 'clients/client_form.html'
    success_url = reverse_lazy('clients:client-list')

    def form_valid(self, form):
        """Handle successful form submission."""
        # Set the created_by field to the current user
        form.instance.created_by = self.request.user
        form.instance.business = self.request.user.profile.business

        messages.success(
            self.request,
            f'Client "{form.instance.display_name}" created successfully!',
        )
        return super().form_valid(form)


class ClientUpdateView(LoginRequiredMixin, UpdateView):
    """Update view for clients."""

    model = Client
    template_name = 'clients/client_form.html'
    fields = '__all__'

    def get_object(self):
        """Get client and check permissions."""
        client = get_object_or_404(Client, pk=self.kwargs['pk'])

        # Check if user has access to this client's business
        if not self.request.user.is_superuser:
            if (
                client.business.created_by != self.request.user
                and self.request.user not in client.business.users.all()
            ):
                raise PermissionError("You don't have access to this client")

        return client

    def get_form(self, form_class=None):
        """Limit business choices to user's accessible businesses."""
        form = super().get_form(form_class)

        if not self.request.user.is_superuser:
            accessible_businesses = Business.objects.filter(
                Q(created_by=self.request.user) | Q(users=self.request.user)
            ).distinct()
            form.fields['business'].queryset = accessible_businesses

        return form

    def form_valid(self, form):
        """Handle successful form submission."""
        messages.success(
            self.request, f'Client "{form.instance.display_name}" updated successfully!'
        )
        return super().form_valid(form)

    def get_success_url(self):
        """Return to client detail page."""
        return reverse_lazy('clients:client-detail', kwargs={'pk': self.object.pk})


class ClientDeleteView(LoginRequiredMixin, DeleteView):
    """Delete view for clients."""

    model = Client
    template_name = 'clients/client_confirm_delete.html'
    success_url = reverse_lazy('clients:client-list')

    def get_object(self):
        """Get client and check permissions."""
        client = get_object_or_404(Client, pk=self.kwargs['pk'])

        # Check if user has access to this client's business
        if not self.request.user.is_superuser:
            if (
                client.business.created_by != self.request.user
                and self.request.user not in client.business.users.all()
            ):
                raise PermissionError("You don't have access to this client")

        return client

    def delete(self, request, *args, **kwargs):
        """Handle deletion with soft delete."""
        client = self.get_object()
        client.is_active = False
        client.save()
        messages.success(
            request, f'Client "{client.display_name}" deactivated successfully!'
        )
        return super().delete(request, *args, **kwargs)
