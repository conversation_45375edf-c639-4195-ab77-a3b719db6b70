"""
Factory classes for accounts app models using factory_boy and faker.
"""

import factory
from django.contrib.auth import get_user_model

from accounts.models import Profile

User = get_user_model()


class UserFactory(factory.django.DjangoModelFactory):
    """Factory for creating User instances."""

    class Meta:
        model = User

    username = factory.Sequence(lambda n: f'user{n}')
    email = factory.LazyAttribute(lambda obj: f'{obj.username}@example.com')
    first_name = factory.Faker('first_name')
    last_name = factory.Faker('last_name')
    is_active = True
    email_confirmed = True
    profile_completed = True
    onboarding_completed = True
    role = Profile.Role.CUSTOMER

    @factory.post_generation
    def set_password(obj, create, extracted, **kwargs):
        """Set a default password for the user."""
        if create:
            obj.set_password('demo123')
            obj.save()


class BusinessOwnerFactory(UserFactory):
    """Factory for creating business owner users."""

    role = Profile.Role.BUSINESS_OWNER


class AgentFactory(UserFactory):
    """Factory for creating agent users."""

    role = Profile.Role.AGENT


class SuperuserFactory(UserFactory):
    """Factory for creating superuser accounts."""

    role = Profile.Role.BUSINESS_OWNER
    is_staff = True
    is_superuser = True


class ProfileFactory(factory.django.DjangoModelFactory):
    """Factory for creating Profile instances."""

    class Meta:
        model = Profile

    user = factory.SubFactory(UserFactory)
    first_name = factory.LazyAttribute(lambda obj: obj.user.first_name)
    last_name = factory.LazyAttribute(lambda obj: obj.user.last_name)
    phone = factory.Faker('phone_number')
    address_line1 = factory.Faker('street_address')
    address_line2 = factory.Faker('secondary_address')
    city = factory.Faker('city')
    state_province = factory.Faker('state')
    postal_code = factory.Faker('postcode')
    country = factory.Faker('country')
    job_title = factory.Faker('job')
    bio = factory.Faker('text', max_nb_chars=200)
    timezone = 'UTC'
    language = 'en'
