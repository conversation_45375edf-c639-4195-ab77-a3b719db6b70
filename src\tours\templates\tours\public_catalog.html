{% extends "base.html" %}
{% load static %}
{% block title %}Discover Amazing Tours - Alpine Adventures{% endblock %}
{% block content %}
    <div class="min-h-screen bg-gray-50">
        <!-- Hero Section -->
        <div class="bg-gradient-to-r from-blue-600 to-green-600 text-white">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
                <div class="text-center">
                    <h1 class="text-4xl md:text-6xl font-bold mb-4">Discover Amazing Tours</h1>
                    <p class="text-xl md:text-2xl mb-8 text-blue-100">Unforgettable adventures await you around the world</p>
                    <!-- Quick Search -->
                    <div class="max-w-2xl mx-auto">
                        <form method="get"
                              action="{% url 'tours:public-search' %}"
                              class="flex flex-col sm:flex-row gap-4">
                            <input type="text"
                                   name="q"
                                   placeholder="Where do you want to go?"
                                   class="flex-1 px-4 py-3 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-300">
                            <button type="submit"
                                    class="px-8 py-3 bg-orange-500 hover:bg-orange-600 text-white font-semibold rounded-lg transition-colors">
                                Search Tours
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <!-- Featured Destinations -->
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-8 text-center">Featured Destinations</h2>
            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-12">
                {% for destination in featured_destinations %}
                    <a href="{% url 'tours:public-search' %}?destination={{ destination|urlencode }}"
                       class="group bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-4 text-center">
                        <div class="text-2xl mb-2">🏔️</div>
                        <h3 class="font-semibold text-gray-900 group-hover:text-blue-600">{{ destination }}</h3>
                    </a>
                {% endfor %}
            </div>
            <!-- Popular Activities -->
            <h2 class="text-3xl font-bold text-gray-900 mb-8 text-center">Popular Activities</h2>
            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-12">
                {% for activity_code, activity_name in popular_activities %}
                    <a href="{% url 'tours:public-search' %}?activity={{ activity_code }}"
                       class="group bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-4 text-center">
                        <div class="text-2xl mb-2">
                            {% if activity_code == 'hiking' %}
                                🥾
                            {% elif activity_code == 'cycling' %}
                                🚴
                            {% elif activity_code == 'wildlife' %}
                                🦁
                            {% elif activity_code == 'cultural' %}
                                🏛️
                            {% elif activity_code == 'adventure' %}
                                🧗
                            {% else %}
                                🌟
                            {% endif %}
                        </div>
                        <h3 class="font-semibold text-gray-900 group-hover:text-blue-600">{{ activity_name }}</h3>
                    </a>
                {% endfor %}
            </div>
            <!-- Tour Listings -->
            <div class="flex justify-between items-center mb-8">
                <h2 class="text-3xl font-bold text-gray-900">Available Tours</h2>
                <a href="{% url 'tours:public-search' %}"
                   class="text-blue-600 hover:text-blue-800 font-semibold">View All {{ total_tours }} Tours →</a>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {% for tour in tours %}
                    <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow overflow-hidden">
                        <!-- Tour Image Placeholder -->
                        <div class="h-48 bg-gradient-to-r from-blue-400 to-green-400 flex items-center justify-center">
                            <span class="text-white text-6xl">
                                {% if tour.activity_type == 'hiking' %}
                                    🥾
                                {% elif tour.activity_type == 'cycling' %}
                                    🚴
                                {% elif tour.activity_type == 'wildlife' %}
                                    🦁
                                {% elif tour.activity_type == 'cultural' %}
                                    🏛️
                                {% elif tour.activity_type == 'adventure' %}
                                    🧗
                                {% else %}
                                    🌟
                                {% endif %}
                            </span>
                        </div>
                        <div class="p-6">
                            <div class="flex justify-between items-start mb-2">
                                <h3 class="text-xl font-bold text-gray-900">{{ tour.name }}</h3>
                                {% if tour.has_urgent_availability %}
                                    <span class="bg-red-100 text-red-800 text-xs font-semibold px-2 py-1 rounded-full">
                                        Only {{ tour.min_available_spots }} left!
                                    </span>
                                {% endif %}
                            </div>
                            <p class="text-gray-600 mb-2">📍 {{ tour.destination }}</p>
                            <p class="text-gray-600 mb-4">⏱️ {{ tour.duration_days }} days • {{ tour.get_difficulty_level_display }}</p>
                            <p class="text-gray-700 mb-4 line-clamp-2">{{ tour.description|truncatewords:20 }}</p>
                            <div class="flex justify-between items-center">
                                <div>
                                    {% if tour.next_available_date %}
                                        <p class="text-sm text-gray-600">Next tour: {{ tour.next_available_date|date:"M d, Y" }}</p>
                                    {% else %}
                                        <p class="text-sm text-gray-500">No upcoming dates</p>
                                    {% endif %}
                                </div>
                                <a href="{% url 'tours:public-detail' tour.pk %}"
                                   class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-semibold transition-colors">
                                    View Details
                                </a>
                            </div>
                        </div>
                    </div>
                {% empty %}
                    <div class="col-span-full text-center py-12">
                        <div class="text-6xl mb-4">🔍</div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">No tours found</h3>
                        <p class="text-gray-600">Try adjusting your search criteria or check back later for new tours.</p>
                    </div>
                {% endfor %}
            </div>
            {% if tours|length >= 12 %}
                <div class="text-center mt-12">
                    <a href="{% url 'tours:public-search' %}"
                       class="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors">
                        View All Tours
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
{% endblock %}
{% block extra_css %}
    <style>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
    </style>
{% endblock %}
