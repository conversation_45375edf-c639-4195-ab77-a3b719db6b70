"""
Unit tests for invoices models using pytest.
"""

from datetime import date, timedelta

import pytest
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from djmoney.money import Money

from businesses.models import Business
from clients.models import Client
from invoices.models import Invoice
from quotes.models import Quote

User = get_user_model()


@pytest.mark.django_db
class TestInvoiceModel:
    """Test cases for Invoice model."""

    def setup_method(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            username="testuser", email="<EMAIL>", password="testpass123"
        )

        self.business = Business.objects.create(
            name="Test Tours",
            email="<EMAIL>",
            phone="+1234567890",
            address_line1="123 Test St",
            city="Test City",
            state_province="Test State",
            postal_code="12345",
            country="Test Country",
            created_by=self.user,
        )

        self.client = Client.objects.create(
            business=self.business,
            first_name="<PERSON>",
            last_name="Doe",
            email="<EMAIL>",
            phone="+1234567890",
            created_by=self.user,
        )

        self.approved_quote = Quote.objects.create(
            business=self.business,
            client=self.client,
            title="Safari Adventure",
            status=Quote.Status.APPROVED,
            subtotal=Money(1000, "USD"),
            tax_amount=Money(100, "USD"),
            total_amount=Money(1100, "USD"),
            created_by=self.user,
        )

    def test_invoice_creation_from_quote(self):
        """Test invoice creation from approved quote."""
        invoice = Invoice.objects.from_quote(self.approved_quote)

        self.assertEqual(invoice.business, self.business)
        self.assertEqual(invoice.client, self.client)
        self.assertEqual(invoice.quote, self.approved_quote)
        self.assertEqual(invoice.subtotal, Money(1000, "USD"))
        self.assertEqual(invoice.tax_amount, Money(100, "USD"))
        self.assertEqual(invoice.total_amount, Money(1100, "USD"))
        self.assertEqual(invoice.status, Invoice.Status.DRAFT)
        self.assertIsNotNone(invoice.invoice_number)
        self.assertIsNotNone(invoice.due_date)

    def test_invoice_creation_from_non_approved_quote_fails(self):
        """Test that invoice creation fails for non-approved quotes."""
        draft_quote = Quote.objects.create(
            business=self.business,
            client=self.client,
            title="Draft Quote",
            status=Quote.Status.DRAFT,
            subtotal=Money(1000, "USD"),
            tax_amount=Money(100, "USD"),
            total_amount=Money(1100, "USD"),
            created_by=self.user,
        )

        with self.assertRaises(ValidationError):
            Invoice.objects.from_quote(draft_quote)

    def test_invoice_manual_creation(self):
        """Test manual invoice creation."""
        invoice = Invoice.objects.create(
            business=self.business,
            client=self.client,
            quote=self.approved_quote,
            subtotal=Money(1000, "USD"),
            tax_amount=Money(100, "USD"),
            total_amount=Money(1100, "USD"),
            created_by=self.user,
        )

        self.assertEqual(invoice.status, Invoice.Status.DRAFT)
        self.assertIsNotNone(invoice.invoice_number)
        self.assertIsNotNone(invoice.due_date)
        self.assertEqual(invoice.issue_date, date.today())

    def test_invoice_str_representation(self):
        """Test invoice string representation."""
        invoice = Invoice.objects.from_quote(self.approved_quote)

        expected = f"{invoice.invoice_number} - {self.client.display_name}"
        self.assertEqual(str(invoice), expected)

    def test_invoice_number_generation(self):
        """Test invoice number generation is unique and sequential."""
        invoice1 = Invoice.objects.from_quote(self.approved_quote)

        # Create another quote and invoice
        quote2 = Quote.objects.create(
            business=self.business,
            client=self.client,
            title="Another Tour",
            status=Quote.Status.APPROVED,
            subtotal=Money(2000, "USD"),
            tax_amount=Money(200, "USD"),
            total_amount=Money(2200, "USD"),
            created_by=self.user,
        )

        invoice2 = Invoice.objects.create(
            business=self.business,
            client=self.client,
            quote=quote2,
            subtotal=Money(2000, "USD"),
            tax_amount=Money(200, "USD"),
            total_amount=Money(2200, "USD"),
            created_by=self.user,
        )

        # Invoice numbers should be different
        self.assertNotEqual(invoice1.invoice_number, invoice2.invoice_number)

        # Should be sequential
        invoice1_num = int(invoice1.invoice_number.split("-")[-1])
        invoice2_num = int(invoice2.invoice_number.split("-")[-1])
        self.assertEqual(invoice2_num, invoice1_num + 1)

    def test_invoice_due_date_auto_generation(self):
        """Test automatic due date generation."""
        invoice = Invoice.objects.from_quote(self.approved_quote)

        expected_due_date = invoice.issue_date + timedelta(days=30)
        self.assertEqual(invoice.due_date, expected_due_date)

    def test_invoice_is_overdue_property(self):
        """Test is_overdue property."""
        # Create additional quotes for multiple invoices
        quote2 = Quote.objects.create(
            business=self.business,
            client=self.client,
            title="Tour 2",
            status=Quote.Status.APPROVED,
            subtotal=Money(1000, "USD"),
            tax_amount=Money(100, "USD"),
            total_amount=Money(1100, "USD"),
            created_by=self.user,
        )

        quote3 = Quote.objects.create(
            business=self.business,
            client=self.client,
            title="Tour 3",
            status=Quote.Status.APPROVED,
            subtotal=Money(1000, "USD"),
            tax_amount=Money(100, "USD"),
            total_amount=Money(1100, "USD"),
            created_by=self.user,
        )

        # Create invoice with past due date
        overdue_invoice = Invoice.objects.create(
            business=self.business,
            client=self.client,
            quote=self.approved_quote,
            status=Invoice.Status.SENT,
            due_date=date.today() - timedelta(days=1),
            subtotal=Money(1000, "USD"),
            tax_amount=Money(100, "USD"),
            total_amount=Money(1100, "USD"),
            created_by=self.user,
        )

        self.assertTrue(overdue_invoice.is_overdue)

        # Create invoice with future due date
        current_invoice = Invoice.objects.create(
            business=self.business,
            client=self.client,
            quote=quote2,
            status=Invoice.Status.SENT,
            due_date=date.today() + timedelta(days=30),
            subtotal=Money(1000, "USD"),
            tax_amount=Money(100, "USD"),
            total_amount=Money(1100, "USD"),
            created_by=self.user,
        )

        self.assertFalse(current_invoice.is_overdue)

        # Paid invoice should not be overdue even if past due date
        paid_invoice = Invoice.objects.create(
            business=self.business,
            client=self.client,
            quote=quote3,
            status=Invoice.Status.PAID,
            due_date=date.today() - timedelta(days=1),
            subtotal=Money(1000, "USD"),
            tax_amount=Money(100, "USD"),
            total_amount=Money(1100, "USD"),
            created_by=self.user,
        )

        self.assertFalse(paid_invoice.is_overdue)

    def test_mark_as_paid_method(self):
        """Test mark_as_paid method."""
        invoice = Invoice.objects.from_quote(self.approved_quote)

        self.assertEqual(invoice.status, Invoice.Status.DRAFT)
        self.assertIsNone(invoice.paid_at)

        invoice.mark_as_paid()

        self.assertEqual(invoice.status, Invoice.Status.PAID)
        self.assertIsNotNone(invoice.paid_at)

    def test_invoice_manager_methods(self):
        """Test InvoiceManager methods."""
        # Create additional quotes for multiple invoices
        quote2 = Quote.objects.create(
            business=self.business,
            client=self.client,
            title="Manager Test 2",
            status=Quote.Status.APPROVED,
            subtotal=Money(1000, "USD"),
            tax_amount=Money(100, "USD"),
            total_amount=Money(1100, "USD"),
            created_by=self.user,
        )

        quote3 = Quote.objects.create(
            business=self.business,
            client=self.client,
            title="Manager Test 3",
            status=Quote.Status.APPROVED,
            subtotal=Money(1000, "USD"),
            tax_amount=Money(100, "USD"),
            total_amount=Money(1100, "USD"),
            created_by=self.user,
        )

        # Create invoices with different statuses
        paid_invoice = Invoice.objects.create(
            business=self.business,
            client=self.client,
            quote=self.approved_quote,
            status=Invoice.Status.PAID,
            subtotal=Money(1000, "USD"),
            tax_amount=Money(100, "USD"),
            total_amount=Money(1100, "USD"),
            created_by=self.user,
        )

        overdue_invoice = Invoice.objects.create(
            business=self.business,
            client=self.client,
            quote=quote2,
            status=Invoice.Status.SENT,
            due_date=date.today() - timedelta(days=1),
            subtotal=Money(1000, "USD"),
            tax_amount=Money(100, "USD"),
            total_amount=Money(1100, "USD"),
            created_by=self.user,
        )

        current_invoice = Invoice.objects.create(
            business=self.business,
            client=self.client,
            quote=quote3,
            status=Invoice.Status.SENT,
            due_date=date.today() + timedelta(days=30),
            subtotal=Money(1000, "USD"),
            tax_amount=Money(100, "USD"),
            total_amount=Money(1100, "USD"),
            created_by=self.user,
        )

        # Test manager methods
        paid_invoices = Invoice.objects.paid()
        self.assertIn(paid_invoice, paid_invoices)
        self.assertNotIn(overdue_invoice, paid_invoices)
        self.assertNotIn(current_invoice, paid_invoices)

        overdue_invoices = Invoice.objects.overdue()
        self.assertNotIn(paid_invoice, overdue_invoices)
        self.assertIn(overdue_invoice, overdue_invoices)
        self.assertNotIn(current_invoice, overdue_invoices)

        business_invoices = Invoice.objects.for_business(self.business)
        self.assertIn(paid_invoice, business_invoices)
        self.assertIn(overdue_invoice, business_invoices)
        self.assertIn(current_invoice, business_invoices)

        sent_invoices = Invoice.objects.by_status(Invoice.Status.SENT)
        self.assertNotIn(paid_invoice, sent_invoices)
        self.assertIn(overdue_invoice, sent_invoices)
        self.assertIn(current_invoice, sent_invoices)

    def test_invoice_audit_fields(self):
        """Test audit fields are set correctly."""
        invoice = Invoice.objects.from_quote(self.approved_quote)

        self.assertIsNotNone(invoice.created_at)
        self.assertIsNotNone(invoice.updated_at)
        self.assertEqual(invoice.created_by, self.user)
        self.assertIsNone(invoice.sent_at)
        self.assertIsNone(invoice.paid_at)

    def test_one_to_one_relationship_with_quote(self):
        """Test that each quote can only have one invoice."""
        # Create first invoice
        invoice1 = Invoice.objects.from_quote(self.approved_quote)

        # Try to create second invoice from same quote should fail
        with self.assertRaises(Exception):  # IntegrityError or similar
            Invoice.objects.create(
                business=self.business,
                client=self.client,
                quote=self.approved_quote,
                subtotal=Money(1000, "USD"),
                tax_amount=Money(100, "USD"),
                total_amount=Money(1100, "USD"),
                created_by=self.user,
            )
