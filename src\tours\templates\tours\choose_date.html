<!-- tours/choose_date.html -->
 {% comment %} url: /tours/<toud-id>/choose-date/ {% endcomment %}
<div class="date-picker"
     data-tour-id="{{ tour.id }}"
     data-reservation-timeout="900"> <!-- 15 min in seconds -->

  <h2>Select Date</h2>
  <div class="spots-counter" data-initial-spots="{{ current_date.available_spots }}">
    Spots left: <strong id="spots-remaining">{{ current_date.available_spots }}</strong>
  </div>

  <calendar-component
    :available-dates="{{ tour.dates|json|escapejs }}"
    @date-selected="reserveSpot"></calendar-component>

  <!-- Reservation Timer (Hidden by default) -->
  <div id="reservation-timer" class="hidden">
    <span>⏰ Your spot is reserved for: <strong id="timer">15:00</strong></span>
    <button id="extend-reservation">Need more time?</button>
  </div>
</div>

<script>
  // Minimal JS for reservation flow
document.addEventListener('DOMContentLoaded', () => {
  const timerEl = document.getElementById('timer');
  const spotsEl = document.getElementById('spots-remaining');
  const timeout = parseInt(document.querySelector('[data-reservation-timeout]').dataset.reservationTimeout);

  // Start reservation countdown (if existing pre-reserved booking exists)
  function startTimer(remainingSeconds) {
    document.getElementById('reservation-timer').classList.remove('hidden');

    const timer = setInterval(() => {
      remainingSeconds--;
      const mins = Math.floor(remainingSeconds / 60).toString().padStart(2, '0');
      const secs = (remainingSeconds % 60).toString().padStart(2, '0');
      timerEl.textContent = `${mins}:${secs}`;

      if (remainingSeconds <= 0) {
        clearInterval(timer);
        handleReservationExpired();
      }
    }, 1000);
  }

  // Optimistic UI update on spot reservation
  function reserveSpot(dateId) {
    fetch(`/api/reserve-spot/`, {
      method: 'POST',
      headers: {'X-CSRFToken': getCookie('csrftoken')},
      body: JSON.stringify({tour_date_id: dateId})
    })
    .then(response => {
      if (response.ok) {
        // ATOMIC UI UPDATE (no full page reload)
        spotsEl.textContent = parseInt(spotsEl.textContent) - 1;
        startTimer(timeout);
        showCustomizationSection();
      }
    });
  }
});

</script>
