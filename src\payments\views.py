"""
Payment views for handling Lemon Squeezy integration.
"""

import json
import logging

from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.http import HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404, redirect, render
from django.utils import timezone
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_POST

from invoices.models import Invoice

from .models import PaymentLink, Subscription
from .services import (
    LemonSqueezyService,
    create_payment_link_for_invoice,
)

logger = logging.getLogger(__name__)


def payment_page(request, link_id):
    """Display payment page for a payment link."""
    payment_link = get_object_or_404(PaymentLink, link_id=link_id)

    # Track access
    payment_link.track_access()

    # Check if link is still active
    if not payment_link.is_active:
        context = {
            'payment_link': payment_link,
            'error': 'This payment link has expired or is no longer valid.',
        }
        return render(request, 'payments/payment_error.html', context)

    # Check if invoice is already paid
    if payment_link.invoice.status == 'paid':
        context = {
            'payment_link': payment_link,
            'message': 'This invoice has already been paid.',
        }
        return render(request, 'payments/payment_success.html', context)

    # If we have a Lemon Squeezy checkout URL, redirect to it
    if payment_link.lemon_squeezy_checkout_url:
        return redirect(payment_link.lemon_squeezy_checkout_url)

    context = {
        'payment_link': payment_link,
        'invoice': payment_link.invoice,
    }
    return render(request, 'payments/payment_page.html', context)


@login_required
@require_POST
def create_payment_link(request, invoice_id):
    """Create a payment link for an invoice."""
    invoice = get_object_or_404(Invoice, id=invoice_id)

    # Check permissions
    if not request.user.has_perm('invoices.change_invoice', invoice):
        return JsonResponse(
            {'success': False, 'error': 'Permission denied'}, status=403
        )

    # Check if invoice can have a payment link
    if invoice.status not in ['sent', 'overdue']:
        return JsonResponse(
            {
                'success': False,
                'error': 'Payment links can only be created for sent or overdue invoices',
            },
            status=400,
        )

    try:
        # Create or get existing payment link
        if hasattr(invoice, 'payment_link'):
            payment_link = invoice.payment_link
            if payment_link.is_active:
                return JsonResponse(
                    {
                        'success': True,
                        'payment_url': payment_link.get_payment_url(),
                        'message': 'Payment link already exists',
                    }
                )

        payment_link = create_payment_link_for_invoice(invoice, request.user)

        if payment_link:
            return JsonResponse(
                {
                    'success': True,
                    'payment_url': payment_link.get_payment_url(),
                    'message': 'Payment link created successfully',
                }
            )
        else:
            return JsonResponse(
                {'success': False, 'error': 'Failed to create payment link'}, status=500
            )

    except Exception as e:
        logger.error(f'Error creating payment link for invoice {invoice_id}: {e}')
        return JsonResponse(
            {
                'success': False,
                'error': 'An error occurred while creating the payment link',
            },
            status=500,
        )


@csrf_exempt
@require_POST
def lemon_squeezy_webhook(request):
    """Handle Lemon Squeezy webhooks."""
    try:
        # Get the raw payload
        payload = request.body.decode('utf-8')
        signature = request.headers.get('X-Signature', '')

        # Initialize service
        service = LemonSqueezyService()

        # Verify webhook signature
        if not service.validate_webhook_signature(payload, signature):
            logger.warning('Invalid Lemon Squeezy webhook signature')
            return HttpResponse(status=401)

        # Parse the webhook data
        data = json.loads(payload)

        logger.info(
            f'Received Lemon Squeezy webhook: {data.get("meta", {}).get("event_name", "unknown")}'
        )

        # Process the webhook using the improved service method
        success = service.process_webhook_event(data)

        if success:
            return HttpResponse(status=200)
        else:
            return HttpResponse(status=500)

    except json.JSONDecodeError:
        logger.error('Invalid JSON in Lemon Squeezy webhook')
        return HttpResponse(status=400)
    except Exception as e:
        logger.error(f'Error processing Lemon Squeezy webhook: {e}')
        return HttpResponse(status=500)


@login_required
def subscription_dashboard(request):
    """Display subscription dashboard for the user's business."""
    if not hasattr(request.user, 'business') or not request.user.profile.business:
        messages.error(
            request, 'You need to be associated with a business to view subscriptions.'
        )
        return redirect('core:home')

    business = request.user.profile.business
    subscription = getattr(business, 'subscription', None)

    context = {
        'business': business,
        'subscription': subscription,
    }
    return render(request, 'payments/subscription_dashboard.html', context)


@login_required
@require_POST
def create_subscription(request):
    """Create a subscription for the user's business."""
    if not hasattr(request.user, 'business') or not request.user.profile.business:
        return JsonResponse(
            {'success': False, 'error': 'No business associated'}, status=400
        )

    business = request.user.profile.business
    plan = request.POST.get('plan', 'basic')

    # Check if subscription already exists
    if hasattr(business, 'subscription') and business.subscription.is_active:
        return JsonResponse(
            {'success': False, 'error': 'Business already has an active subscription'},
            status=400,
        )

    try:
        service = LemonSqueezyService()
        subscription_response = service.create_subscription(
            business=business, plan=plan, customer_email=request.user.email
        )

        if subscription_response and 'data' in subscription_response:
            # Create local subscription record
            subscription_data = subscription_response['data']

            subscription, created = Subscription.objects.get_or_create(
                business=business,
                defaults={
                    'lemon_squeezy_subscription_id': subscription_data['id'],
                    'plan': plan,
                    'status': Subscription.Status.ACTIVE,
                    'current_period_start': timezone.now(),
                    'current_period_end': timezone.now() + timezone.timedelta(days=30),
                },
            )

            return JsonResponse(
                {
                    'success': True,
                    'message': 'Subscription created successfully',
                    'subscription_id': str(subscription.subscription_id),
                }
            )
        else:
            return JsonResponse(
                {
                    'success': False,
                    'error': 'Failed to create subscription with Lemon Squeezy',
                },
                status=500,
            )

    except Exception as e:
        logger.error(f'Error creating subscription for business {business.id}: {e}')
        return JsonResponse(
            {
                'success': False,
                'error': 'An error occurred while creating the subscription',
            },
            status=500,
        )


@login_required
@require_POST
def cancel_subscription(request):
    """Cancel the user's business subscription."""
    if not hasattr(request.user, 'business') or not request.user.profile.business:
        return JsonResponse(
            {'success': False, 'error': 'No business associated'}, status=400
        )

    business = request.user.profile.business

    if not hasattr(business, 'subscription'):
        return JsonResponse(
            {'success': False, 'error': 'No subscription found'}, status=400
        )

    subscription = business.subscription

    try:
        service = LemonSqueezyService()

        if subscription.lemon_squeezy_subscription_id:
            # Cancel in Lemon Squeezy
            cancel_response = service.cancel_subscription(
                subscription.lemon_squeezy_subscription_id
            )

            if not cancel_response:
                return JsonResponse(
                    {
                        'success': False,
                        'error': 'Failed to cancel subscription with Lemon Squeezy',
                    },
                    status=500,
                )

        # Update local subscription
        subscription.status = Subscription.Status.CANCELLED
        subscription.save()

        return JsonResponse(
            {'success': True, 'message': 'Subscription cancelled successfully'}
        )

    except Exception as e:
        logger.error(f'Error cancelling subscription for business {business.id}: {e}')
        return JsonResponse(
            {
                'success': False,
                'error': 'An error occurred while cancelling the subscription',
            },
            status=500,
        )
