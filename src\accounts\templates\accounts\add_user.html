{% load i18n %}

<c-layouts.base>
  <c-slot name="title">{% trans "Add User" %} - Tour Business Management</c-slot>

  <c-slot name="content">
    <div class="container mx-auto px-4 py-8">
      <div class="max-w-2xl mx-auto">
        <!-- Header -->
        <div class="flex justify-between items-center mb-6">
          <div>
            <h1 class="text-3xl font-bold text-gray-900">{% trans "Add New User" %}</h1>
            <p class="text-gray-600 mt-1">{% trans "Add a new team member to your business" %}</p>
          </div>
          <a href="{% url 'accounts:user_management' %}"
            class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium">
            {% trans "Back to Users" %}
          </a>
        </div>

        <div class="bg-white shadow rounded-lg p-6">
          <form method="post" class="space-y-6">
            {% csrf_token %}

            {% if form.errors %}
            <div class="bg-red-50 border border-red-200 rounded-md p-4">
              <div class="flex">
                <div class="flex-shrink-0">
                  <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                      clip-rule="evenodd" />
                  </svg>
                </div>
                <div class="ml-3">
                  <h3 class="text-sm font-medium text-red-800">
                    {% trans "There were errors with your submission" %}
                  </h3>
                  <div class="mt-2 text-sm text-red-700">
                    <ul class="list-disc pl-5 space-y-1">
                      {% for field, errors in form.errors.items %}
                      {% for error in errors %}
                      <li>{{ field|capfirst }}: {{ error }}</li>
                      {% endfor %}
                      {% endfor %}
                    </ul>
                  </div>
                </div>
              </div>
            </div>
            {% endif %}

            <!-- Account Information -->
            <div>
              <h3 class="text-lg font-medium text-gray-900 mb-4">{% trans "Account Information" %}</h3>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label for="{{ form.username.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {% trans "Username" %} *
                  </label>
                  {{ form.username }}
                </div>

                <div>
                  <label for="{{ form.email.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {% trans "Email Address" %} *
                  </label>
                  {{ form.email }}
                </div>

                <div class="md:col-span-2">
                  <label for="{{ form.role.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {% trans "Role" %} *
                  </label>
                  {{ form.role }}
                  <p class="text-xs text-gray-500 mt-1">
                    {% trans "Business Owner: Full access to all features. Agent: Limited access for day-to-day operations." %}
                  </p>
                </div>
              </div>
            </div>

            <!-- Password Options -->
            <div>
              <h3 class="text-lg font-medium text-gray-900 mb-4">{% trans "Password Setup" %}</h3>

              <div class="bg-blue-50 border border-blue-200 rounded-md p-4 mb-4">
                <div class="flex">
                  <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd"
                        d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                        clip-rule="evenodd"></path>
                    </svg>
                  </div>
                  <div class="ml-3">
                    <h4 class="text-sm font-medium text-blue-800">
                      {% trans "Password Options" %}
                    </h4>
                    <div class="mt-2 text-sm text-blue-700">
                      <p>
                        {% trans "You can either set a password now or leave it blank to send an invitation email where the user can set their own password." %}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label for="{{ form.password1.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {% trans "Password" %}
                  </label>
                  {{ form.password1 }}
                  <p class="text-xs text-gray-500 mt-1">{{ form.password1.help_text }}</p>
                </div>

                <div>
                  <label for="{{ form.password2.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {% trans "Confirm Password" %}
                  </label>
                  {{ form.password2 }}
                </div>
              </div>
            </div>

            <!-- What Happens Next -->
            <div class="bg-gray-50 border border-gray-200 rounded-md p-4">
              <h4 class="text-sm font-medium text-gray-900 mb-2">{% trans "What happens next?" %}</h4>
              <div class="text-sm text-gray-600 space-y-1">
                <div class="flex items-start">
                  <span class="font-medium mr-2">•</span>
                  <span>{% trans "If you set a password: User can log in immediately with the provided credentials" %}</span>
                </div>
                <div class="flex items-start">
                  <span class="font-medium mr-2">•</span>
                  <span>{% trans "If you leave password blank: User receives an invitation email to set up their account" %}</span>
                </div>
                <div class="flex items-start">
                  <span class="font-medium mr-2">•</span>
                  <span>{% trans "User will be associated with your business and can access appropriate features based on their role" %}</span>
                </div>
              </div>
            </div>

            <!-- Submit Buttons -->
            <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
              <a href="{% url 'accounts:user_management' %}"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 px-6 py-2 rounded-md text-sm font-medium">
                {% trans "Cancel" %}
              </a>
              <button type="submit"
                class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md text-sm font-medium">
                {% trans "Add User" %}
              </button>
            </div>
          </form>
        </div>

        <!-- Help Section -->
        <div class="mt-8 bg-white shadow rounded-lg p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">{% trans "User Roles Explained" %}</h3>

          <div class="space-y-4">
            <div class="flex items-start">
              <div class="flex-shrink-0">
                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800">
                  {% trans "Business Owner" %}
                </span>
              </div>
              <div class="ml-3">
                <p class="text-sm text-gray-600">
                  {% trans "Full administrative access including user management, business settings, financial reports, and all tour management features." %}
                </p>
              </div>
            </div>

            <div class="flex items-start">
              <div class="flex-shrink-0">
                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                  {% trans "Agent" %}
                </span>
              </div>
              <div class="ml-3">
                <p class="text-sm text-gray-600">
                  {% trans "Access to day-to-day operations including creating quotes, managing bookings, handling client communications, and basic reporting." %}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </c-slot name="content">
</c-layouts.base>
