from django.contrib import admin
from django.utils.html import format_html
from django.utils.translation import gettext_lazy as _
from simple_history.admin import SimpleHistoryAdmin

from tours.models import TourCategory, TourEvent, TourTemplate


@admin.register(TourCategory)
class TourCategoryAdmin(SimpleHistoryAdmin):
    pass


@admin.register(TourTemplate)
class TourTemplateAdmin(SimpleHistoryAdmin):
    pass


@admin.register(TourEvent)
class TourEventAdmin(SimpleHistoryAdmin):
    """Admin configuration for TourEvent model."""

    list_display = (
        'event_title',
        'business',
        'client',
        'current_participants',
        'max_participants',
        'is_confirmed',
        'is_cancelled',
        'created_at',
    )
    list_filter = (
        'business',
        'is_confirmed',
        'is_cancelled',
        'created_at',
    )
    search_fields = (
        'title',
        'description',
        'client__first_name',
        'client__last_name',
        'client__company_name',
        'meeting_point',
    )
    readonly_fields = (
        'is_full',
        'available_spots',
        'created_at',
        'updated_at',
    )

    fieldsets = (
        (
            _('Basic Information'),
            {
                'fields': (
                    'business',
                    'client',
                    'quote',
                )
            },
        ),
        (
            _('Participants'),
            {
                'fields': (
                    'max_participants',
                    'current_participants',
                    'is_full',
                    'available_spots',
                )
            },
        ),
        (
            _('Location & Logistics'),
            {
                'fields': (
                    'meeting_point',
                    'special_instructions',
                )
            },
        ),
        (_('Pricing'), {'fields': ('price_per_person',)}),
        (
            _('Status'),
            {
                'fields': (
                    'is_confirmed',
                    'is_cancelled',
                )
            },
        ),
        (
            _('Audit Information'),
            {
                'fields': (
                    'created_by',
                    'created_at',
                    'updated_at',
                ),
                'classes': ('collapse',),
            },
        ),
    )

    def get_queryset(self, request):
        """Optimize queryset with select_related."""
        return (
            super()
            .get_queryset(request)
            .select_related('business', 'client', 'quote', 'created_by')
        )

    def event_title(self, obj):
        """Display event title."""
        return obj.event.title if obj.event else '-'

    event_title.short_description = _('Event Title')

    def is_full(self, obj):
        """Display if event is full."""
        if obj.is_full:
            return format_html(
                '<span style="color: red; font-weight: bold;">Full</span>'
            )
        return 'Available'

    is_full.short_description = _('Capacity Status')

    def available_spots(self, obj):
        """Display available spots."""
        spots = obj.available_spots
        if spots == 0:
            return format_html('<span style="color: red; font-weight: bold;">0</span>')
        elif spots <= 2:
            return format_html(
                '<span style="color: orange; font-weight: bold;">{}</span>', spots
            )
        return str(spots)

    available_spots.short_description = _('Available Spots')

    actions = ['confirm_events', 'cancel_events']

    def confirm_events(self, request, queryset):
        """Confirm selected events."""
        updated = queryset.filter(is_confirmed=False, is_cancelled=False).update(
            is_confirmed=True
        )
        self.message_user(request, f'{updated} events were confirmed.')

    confirm_events.short_description = _('Confirm selected events')

    def cancel_events(self, request, queryset):
        """Cancel selected events."""
        updated = queryset.filter(is_cancelled=False).update(
            is_cancelled=True, is_confirmed=False
        )
        self.message_user(request, f'{updated} events were cancelled.')

    cancel_events.short_description = _('Cancel selected events')
