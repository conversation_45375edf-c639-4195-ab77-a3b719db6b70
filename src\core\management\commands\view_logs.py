"""
Management command to view and analyze application logs.
"""

import os
import re
from datetime import datetime, timedelta
from django.core.management.base import BaseCommand
from django.conf import settings


class Command(BaseCommand):
    help = 'View and analyze application logs'

    def add_arguments(self, parser):
        parser.add_argument(
            '--log-type',
            choices=['all', 'audit', 'security', 'errors', 'django'],
            default='all',
            help='Type of logs to view'
        )
        parser.add_argument(
            '--lines',
            type=int,
            default=50,
            help='Number of lines to show (default: 50)'
        )
        parser.add_argument(
            '--follow',
            action='store_true',
            help='Follow log file (like tail -f)'
        )
        parser.add_argument(
            '--filter',
            type=str,
            help='Filter logs by keyword'
        )
        parser.add_argument(
            '--since',
            type=str,
            help='Show logs since date (YYYY-MM-DD format)'
        )
        parser.add_argument(
            '--level',
            choices=['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'],
            help='Filter by log level'
        )

    def handle(self, *args, **options):
        log_dir = settings.PROJECT_ROOT / "logs"
        
        if not log_dir.exists():
            self.stdout.write(
                self.style.ERROR('Log directory does not exist. No logs found.')
            )
            return

        log_files = self._get_log_files(log_dir, options['log_type'])
        
        if not log_files:
            self.stdout.write(
                self.style.WARNING(f'No log files found for type: {options["log_type"]}')
            )
            return

        for log_file in log_files:
            self._display_log_file(log_file, options)

    def _get_log_files(self, log_dir, log_type):
        """Get list of log files based on type."""
        log_files = []
        
        if log_type == 'all':
            patterns = ['*.log']
        elif log_type == 'audit':
            patterns = ['audit.log*']
        elif log_type == 'security':
            patterns = ['security.log*']
        elif log_type == 'errors':
            patterns = ['django_errors.log*']
        elif log_type == 'django':
            patterns = ['django.log*']
        else:
            patterns = [f'{log_type}.log*']

        for pattern in patterns:
            import glob
            log_files.extend(glob.glob(str(log_dir / pattern)))
        
        return sorted(log_files)

    def _display_log_file(self, log_file, options):
        """Display contents of a log file."""
        self.stdout.write(
            self.style.SUCCESS(f'\n=== {os.path.basename(log_file)} ===')
        )
        
        try:
            with open(log_file, 'r') as f:
                lines = f.readlines()
            
            # Apply filters
            filtered_lines = self._filter_lines(lines, options)
            
            # Show last N lines
            if options['lines'] and len(filtered_lines) > options['lines']:
                filtered_lines = filtered_lines[-options['lines']:]
            
            # Display lines
            for line in filtered_lines:
                self._format_and_print_line(line.strip())
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error reading {log_file}: {e}')
            )

    def _filter_lines(self, lines, options):
        """Apply filters to log lines."""
        filtered_lines = lines
        
        # Filter by date
        if options['since']:
            try:
                since_date = datetime.strptime(options['since'], '%Y-%m-%d')
                filtered_lines = [
                    line for line in filtered_lines
                    if self._line_matches_date(line, since_date)
                ]
            except ValueError:
                self.stdout.write(
                    self.style.WARNING(f'Invalid date format: {options["since"]}. Use YYYY-MM-DD')
                )
        
        # Filter by level
        if options['level']:
            filtered_lines = [
                line for line in filtered_lines
                if options['level'] in line
            ]
        
        # Filter by keyword
        if options['filter']:
            filtered_lines = [
                line for line in filtered_lines
                if options['filter'].lower() in line.lower()
            ]
        
        return filtered_lines

    def _line_matches_date(self, line, since_date):
        """Check if log line matches date filter."""
        # Try to extract date from log line
        date_patterns = [
            r'(\d{4}-\d{2}-\d{2})',  # YYYY-MM-DD
            r'(\d{2}/\d{2}/\d{4})',  # MM/DD/YYYY
        ]
        
        for pattern in date_patterns:
            match = re.search(pattern, line)
            if match:
                try:
                    if '/' in match.group(1):
                        line_date = datetime.strptime(match.group(1), '%m/%d/%Y')
                    else:
                        line_date = datetime.strptime(match.group(1), '%Y-%m-%d')
                    return line_date >= since_date
                except ValueError:
                    continue
        
        return True  # Include line if date can't be parsed

    def _format_and_print_line(self, line):
        """Format and colorize log line based on level."""
        if 'ERROR' in line or 'CRITICAL' in line:
            self.stdout.write(self.style.ERROR(line))
        elif 'WARNING' in line:
            self.stdout.write(self.style.WARNING(line))
        elif 'INFO' in line:
            self.stdout.write(line)
        elif 'DEBUG' in line:
            self.stdout.write(self.style.HTTP_INFO(line))
        else:
            self.stdout.write(line)

    def _follow_logs(self, log_files):
        """Follow log files like tail -f."""
        import time
        import select
        import sys
        
        self.stdout.write(
            self.style.SUCCESS('Following logs... Press Ctrl+C to stop')
        )
        
        file_handles = []
        for log_file in log_files:
            try:
                f = open(log_file, 'r')
                f.seek(0, 2)  # Go to end of file
                file_handles.append((f, log_file))
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'Error opening {log_file}: {e}')
                )
        
        try:
            while True:
                for f, log_file in file_handles:
                    line = f.readline()
                    if line:
                        self.stdout.write(
                            f'[{os.path.basename(log_file)}] {line.strip()}'
                        )
                time.sleep(0.1)
        except KeyboardInterrupt:
            self.stdout.write('\nStopped following logs.')
        finally:
            for f, _ in file_handles:
                f.close()


class LogAnalyzer:
    """Analyze logs for patterns and statistics."""
    
    @staticmethod
    def analyze_security_logs(log_file):
        """Analyze security logs for threats."""
        threats = {
            'failed_logins': 0,
            'suspicious_patterns': 0,
            'rate_limit_exceeded': 0,
            'permission_denied': 0,
        }
        
        try:
            with open(log_file, 'r') as f:
                for line in f:
                    if 'Login failed' in line:
                        threats['failed_logins'] += 1
                    elif 'Suspicious pattern' in line:
                        threats['suspicious_patterns'] += 1
                    elif 'Rate limit exceeded' in line:
                        threats['rate_limit_exceeded'] += 1
                    elif 'Permission denied' in line:
                        threats['permission_denied'] += 1
        except Exception:
            pass
        
        return threats
    
    @staticmethod
    def analyze_audit_logs(log_file):
        """Analyze audit logs for user activity."""
        activities = {
            'user_actions': 0,
            'data_access': 0,
            'business_operations': 0,
        }
        
        try:
            with open(log_file, 'r') as f:
                for line in f:
                    if 'User' in line and 'performed' in line:
                        activities['user_actions'] += 1
                    elif 'Data access' in line:
                        activities['data_access'] += 1
                    elif 'Business operation' in line:
                        activities['business_operations'] += 1
        except Exception:
            pass
        
        return activities
