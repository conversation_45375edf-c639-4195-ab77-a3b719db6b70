{% extends "base.html" %}

{% block title %}Subscription Dashboard{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto">
    <!-- Page Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Subscription Dashboard</h1>
        <p class="text-gray-600">Manage your business subscription and billing</p>
    </div>

    {% if subscription %}
        <!-- Current Subscription -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <div class="flex justify-between items-start mb-4">
                <div>
                    <h2 class="text-xl font-semibold text-gray-900">Current Subscription</h2>
                    <p class="text-gray-600">{{ business.name }}</p>
                </div>
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                    {% if subscription.status == 'active' %}bg-green-100 text-green-800
                    {% elif subscription.status == 'cancelled' %}bg-red-100 text-red-800
                    {% else %}bg-yellow-100 text-yellow-800{% endif %}">
                    {{ subscription.get_status_display }}
                </span>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <h3 class="text-sm font-medium text-gray-500">Plan</h3>
                    <p class="text-lg font-semibold text-gray-900">{{ subscription.get_plan_display }}</p>
                </div>
                
                {% if subscription.monthly_price %}
                    <div>
                        <h3 class="text-sm font-medium text-gray-500">Monthly Price</h3>
                        <p class="text-lg font-semibold text-gray-900">{{ subscription.monthly_price }}</p>
                    </div>
                {% endif %}
                
                {% if subscription.current_period_end %}
                    <div>
                        <h3 class="text-sm font-medium text-gray-500">
                            {% if subscription.status == 'cancelled' %}Ends On{% else %}Next Billing{% endif %}
                        </h3>
                        <p class="text-lg font-semibold text-gray-900">
                            {{ subscription.current_period_end|date:"M d, Y" }}
                        </p>
                        {% if subscription.days_until_renewal %}
                            <p class="text-sm text-gray-500">{{ subscription.days_until_renewal }} days</p>
                        {% endif %}
                    </div>
                {% endif %}
            </div>
            
            {% if subscription.is_trial %}
                <div class="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <div>
                            <p class="text-sm font-medium text-blue-900">Trial Period Active</p>
                            <p class="text-sm text-blue-700">Your trial ends on {{ subscription.trial_end|date:"M d, Y" }}</p>
                        </div>
                    </div>
                </div>
            {% endif %}
            
            <!-- Subscription Actions -->
            <div class="mt-6 pt-6 border-t border-gray-200">
                <div class="flex space-x-4">
                    {% if subscription.status == 'active' %}
                        <button onclick="cancelSubscription()" 
                                class="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-md transition-colors">
                            Cancel Subscription
                        </button>
                    {% endif %}
                    
                    <a href="#" class="bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-2 px-4 rounded-md transition-colors">
                        View Billing History
                    </a>
                </div>
            </div>
        </div>
    {% else %}
        <!-- No Subscription -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <div class="text-center">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">No Active Subscription</h2>
                <p class="text-gray-600 mb-6">
                    Subscribe to unlock all features of the Tour Business Management platform.
                </p>
                
                <!-- Subscription Plans -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
                    <!-- Basic Plan -->
                    <div class="border border-gray-200 rounded-lg p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Basic Plan</h3>
                        <p class="text-3xl font-bold text-gray-900 mb-4">$29<span class="text-sm font-normal text-gray-500">/month</span></p>
                        <ul class="text-sm text-gray-600 space-y-2 mb-6">
                            <li>• Up to 50 clients</li>
                            <li>• 100 quotes per month</li>
                            <li>• Basic reporting</li>
                            <li>• Email support</li>
                        </ul>
                        <button onclick="createSubscription('basic')" 
                                class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors">
                            Choose Basic
                        </button>
                    </div>
                    
                    <!-- Professional Plan -->
                    <div class="border-2 border-blue-500 rounded-lg p-6 relative">
                        <div class="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                            <span class="bg-blue-500 text-white px-3 py-1 rounded-full text-xs font-medium">Most Popular</span>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Professional Plan</h3>
                        <p class="text-3xl font-bold text-gray-900 mb-4">$79<span class="text-sm font-normal text-gray-500">/month</span></p>
                        <ul class="text-sm text-gray-600 space-y-2 mb-6">
                            <li>• Unlimited clients</li>
                            <li>• Unlimited quotes</li>
                            <li>• Advanced reporting</li>
                            <li>• Priority support</li>
                            <li>• Custom branding</li>
                        </ul>
                        <button onclick="createSubscription('professional')" 
                                class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors">
                            Choose Professional
                        </button>
                    </div>
                    
                    <!-- Enterprise Plan -->
                    <div class="border border-gray-200 rounded-lg p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Enterprise Plan</h3>
                        <p class="text-3xl font-bold text-gray-900 mb-4">$199<span class="text-sm font-normal text-gray-500">/month</span></p>
                        <ul class="text-sm text-gray-600 space-y-2 mb-6">
                            <li>• Everything in Professional</li>
                            <li>• Multi-user access</li>
                            <li>• API access</li>
                            <li>• Dedicated support</li>
                            <li>• Custom integrations</li>
                        </ul>
                        <button onclick="createSubscription('enterprise')" 
                                class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors">
                            Choose Enterprise
                        </button>
                    </div>
                </div>
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_scripts %}
<script>
    function createSubscription(plan) {
        if (!confirm(`Are you sure you want to subscribe to the ${plan} plan?`)) {
            return;
        }
        
        fetch('{% url "payments:create_subscription" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-CSRFToken': '{{ csrf_token }}'
            },
            body: `plan=${plan}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Subscription created successfully!');
                location.reload();
            } else {
                alert('Error: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while creating the subscription.');
        });
    }
    
    function cancelSubscription() {
        if (!confirm('Are you sure you want to cancel your subscription? This action cannot be undone.')) {
            return;
        }
        
        fetch('{% url "payments:cancel_subscription" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Subscription cancelled successfully.');
                location.reload();
            } else {
                alert('Error: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while cancelling the subscription.');
        });
    }
</script>
{% endblock %}
