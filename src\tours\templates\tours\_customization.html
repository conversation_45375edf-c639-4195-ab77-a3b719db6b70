<!-- Partial template: _customization.html -->
<div class="stepper" data-current-step="1">
  <div class="step" data-step="1" data-active>
    <h3>1. Add Extras</h3>
    {% for addon in tour.add_ons.all %}
      <div class="addon-card">
        <input type="checkbox"
               name="addons"
               value="{{ addon.id }}"
               data-price="{{ addon.price }}"
               id="addon-{{ addon.id }}">
        <label for="addon-{{ addon.id }}">
          {{ addon.name }} <span class="price">+${{ addon.price }}</span>
        </label>
      </div>
    {% endfor %}
  </div>

  <div class="step" data-step="2" hidden>
    <h3>2. Passenger Details</h3>
    <form id="passenger-form" data-booking-id="{{ booking.id }}">
      <!-- Encrypted fields handled client-side -->
      <input type="text"
             name="passport_number"
             data-encryption-field
             placeholder="*********">
      <!-- Medical notes use <textarea> with "sensitive" class -->
      <textarea name="medical_notes" class="sensitive"></textarea>
    </form>
  </div>
</div>

<script>
  {% comment %} client side encryption {% endcomment %}
  // Encrypt before submit using Web Crypto API
document.getElementById('passenger-form').addEventListener('submit', (e) => {
  const sensitiveFields = document.querySelectorAll('[data-encryption-field], .sensitive');
  sensitiveFields.forEach(field => {
    field.value = encrypt(field.value); // Your AES-GCM implementation
  });
});

</script>
