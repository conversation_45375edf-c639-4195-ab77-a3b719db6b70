<c-vars 
    invoice="None"
    show_actions="True"
    business_theme="True"
    class=""
/>

<div class="invoice-card {{ class }}" 
     {% if business_theme and invoice.business %}
     style="border-color: {{ invoice.business.primary_color }};"
     {% endif %}>
    
    <div class="invoice-header">
        <div class="invoice-title">
            <h3 class="text-lg font-semibold text-gray-900">Invoice {{ invoice.invoice_number }}</h3>
            {% if invoice.quote %}
                <span class="text-sm text-gray-500">Quote: {{ invoice.quote.quote_number }}</span>
            {% endif %}
        </div>
        <div class="invoice-status">
            <span class="status-badge status-{{ invoice.status }}">
                {{ invoice.get_status_display }}
            </span>
        </div>
    </div>
    
    <div class="invoice-details mt-4 space-y-2">
        <div class="flex justify-between">
            <span class="text-sm text-gray-600">Client:</span>
            <span class="text-sm font-medium">{{ invoice.client.display_name }}</span>
        </div>
        <div class="flex justify-between">
            <span class="text-sm text-gray-600">Amount:</span>
            <span class="text-sm font-medium">{{ invoice.total_amount }}</span>
        </div>
        <div class="flex justify-between">
            <span class="text-sm text-gray-600">Issue Date:</span>
            <span class="text-sm font-medium">{{ invoice.issue_date|date:"M d, Y" }}</span>
        </div>
        <div class="flex justify-between">
            <span class="text-sm text-gray-600">Due Date:</span>
            <span class="text-sm font-medium">{{ invoice.due_date|date:"M d, Y" }}</span>
        </div>
    </div>
    
    {% if show_actions %}
        <div class="invoice-actions mt-4 flex space-x-2">
            <a href="{% url 'invoices:detail' invoice.id %}" 
               class="btn btn-sm btn-primary">
                View Details
            </a>
            {% if invoice.status == 'draft' %}
                <button onclick="sendInvoice({{ invoice.id }})" 
                        class="btn btn-sm btn-secondary">
                    Send Invoice
                </button>
            {% endif %}
            {% if invoice.status == 'sent' or invoice.status == 'overdue' %}
                {% if invoice.payment_link %}
                    <a href="{{ invoice.payment_link.get_payment_url }}" 
                       class="btn btn-sm btn-success">
                        Pay Now
                    </a>
                {% endif %}
            {% endif %}
            {% if invoice.status == 'sent' %}
                <button onclick="markAsPaid({{ invoice.id }})" 
                        class="btn btn-sm btn-outline-success">
                    Mark as Paid
                </button>
            {% endif %}
        </div>
    {% endif %}
</div>

<style>
    .invoice-card {
        @apply border-2 border-gray-200 rounded-lg p-4 bg-white shadow-sm hover:shadow-md transition-shadow;
    }
    
    .invoice-header {
        @apply flex justify-between items-start;
    }
    
    .status-badge {
        @apply px-2 py-1 rounded-full text-xs font-medium;
    }
    
    .status-draft { 
        @apply bg-gray-100 text-gray-800; 
    }
    
    .status-sent { 
        @apply bg-blue-100 text-blue-800; 
    }
    
    .status-paid { 
        @apply bg-green-100 text-green-800; 
    }
    
    .status-overdue { 
        @apply bg-red-100 text-red-800; 
    }
    
    .status-cancelled { 
        @apply bg-gray-100 text-gray-800; 
    }
</style>
