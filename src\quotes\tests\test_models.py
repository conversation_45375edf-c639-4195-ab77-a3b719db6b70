"""
Unit tests for quotes models using pytest.
"""

from datetime import date, timedelta

import pytest
from django.contrib.auth import get_user_model
from djmoney.money import Money

from businesses.models import Business
from clients.models import Client
from quotes.models import Quote

User = get_user_model()


@pytest.mark.django_db
class TestQuoteModel:
    """Test cases for Quote model."""

    def setup_method(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            username="testuser", email="<EMAIL>", password="testpass123"
        )

        self.business = Business.objects.create(
            name="Test Tours",
            email="<EMAIL>",
            phone="+1234567890",
            address_line1="123 Test St",
            city="Test City",
            state_province="Test State",
            postal_code="12345",
            country="Test Country",
            created_by=self.user,
        )

        self.client = Client.objects.create(
            business=self.business,
            first_name="<PERSON>",
            last_name="<PERSON><PERSON>",
            email="<EMAIL>",
            phone="+1234567890",
            created_by=self.user,
        )

    def test_quote_creation(self):
        """Test quote creation with auto-generated fields."""
        quote = Quote.objects.create(
            business=self.business,
            client=self.client,
            title="Safari Adventure",
            description="3-day safari tour",
            subtotal=Money(1000, "USD"),
            tax_amount=Money(100, "USD"),
            total_amount=Money(1100, "USD"),
            created_by=self.user,
        )

        assert quote.business == self.business
        assert quote.client == self.client
        assert quote.title == "Safari Adventure"
        assert quote.status == Quote.Status.DRAFT
        assert quote.quote_number is not None
        assert quote.public_hash is not None
        assert quote.valid_until is not None

        # Check auto-generated quote number format
        assert quote.quote_number.startswith("Q2025-")

        # Check public hash length
        assert len(quote.public_hash) == 64

    def test_quote_str_representation(self):
        """Test quote string representation."""
        quote = Quote.objects.create(
            business=self.business,
            client=self.client,
            title="Safari Adventure",
            subtotal=Money(1000, "USD"),
            tax_amount=Money(100, "USD"),
            total_amount=Money(1100, "USD"),
            created_by=self.user,
        )

        expected = f"{quote.quote_number} - Safari Adventure"
        assert str(quote) == expected

    def test_quote_number_generation(self):
        """Test quote number generation is unique and sequential."""
        quote1 = Quote.objects.create(
            business=self.business,
            client=self.client,
            title="Tour 1",
            subtotal=Money(1000, "USD"),
            tax_amount=Money(100, "USD"),
            total_amount=Money(1100, "USD"),
            created_by=self.user,
        )

        quote2 = Quote.objects.create(
            business=self.business,
            client=self.client,
            title="Tour 2",
            subtotal=Money(2000, "USD"),
            tax_amount=Money(200, "USD"),
            total_amount=Money(2200, "USD"),
            created_by=self.user,
        )

        # Quote numbers should be different
        assert quote1.quote_number != quote2.quote_number

        # Should be sequential
        quote1_num = int(quote1.quote_number.split("-")[-1])
        quote2_num = int(quote2.quote_number.split("-")[-1])
        assert quote2_num == quote1_num + 1

    def test_quote_validity_properties(self):
        """Test quote validity checking properties."""
        # Create a quote that expires tomorrow
        future_date = date.today() + timedelta(days=1)
        quote = Quote.objects.create(
            business=self.business,
            client=self.client,
            title="Valid Quote",
            valid_until=future_date,
            status=Quote.Status.SENT,
            subtotal=Money(1000, "USD"),
            tax_amount=Money(100, "USD"),
            total_amount=Money(1100, "USD"),
            created_by=self.user,
        )

        assert not quote.is_expired
        assert quote.is_valid
        assert quote.can_be_approved()

        # Create an expired quote
        past_date = date.today() - timedelta(days=1)
        expired_quote = Quote.objects.create(
            business=self.business,
            client=self.client,
            title="Expired Quote",
            valid_until=past_date,
            status=Quote.Status.SENT,
            subtotal=Money(1000, "USD"),
            tax_amount=Money(100, "USD"),
            total_amount=Money(1100, "USD"),
            created_by=self.user,
        )

        assert expired_quote.is_expired
        assert not expired_quote.is_valid
        assert not expired_quote.can_be_approved()

    def test_quote_approval_conditions(self):
        """Test quote approval conditions."""
        # Draft quote cannot be approved
        draft_quote = Quote.objects.create(
            business=self.business,
            client=self.client,
            title="Draft Quote",
            status=Quote.Status.DRAFT,
            subtotal=Money(1000, "USD"),
            tax_amount=Money(100, "USD"),
            total_amount=Money(1100, "USD"),
            created_by=self.user,
        )
        assert not draft_quote.can_be_approved()

        # Sent and valid quote can be approved
        sent_quote = Quote.objects.create(
            business=self.business,
            client=self.client,
            title="Sent Quote",
            status=Quote.Status.SENT,
            valid_until=date.today() + timedelta(days=30),
            subtotal=Money(1000, "USD"),
            tax_amount=Money(100, "USD"),
            total_amount=Money(1100, "USD"),
            created_by=self.user,
        )
        assert sent_quote.can_be_approved()

        # Already approved quote cannot be approved again
        approved_quote = Quote.objects.create(
            business=self.business,
            client=self.client,
            title="Approved Quote",
            status=Quote.Status.APPROVED,
            subtotal=Money(1000, "USD"),
            tax_amount=Money(100, "USD"),
            total_amount=Money(1100, "USD"),
            created_by=self.user,
        )
        assert not approved_quote.can_be_approved()

    def test_quote_manager_methods(self):
        """Test QuoteManager methods."""
        # Create quotes with different statuses and dates
        active_quote = Quote.objects.create(
            business=self.business,
            client=self.client,
            title="Active Quote",
            status=Quote.Status.SENT,
            valid_until=date.today() + timedelta(days=30),
            subtotal=Money(1000, "USD"),
            tax_amount=Money(100, "USD"),
            total_amount=Money(1100, "USD"),
            created_by=self.user,
        )

        expired_quote = Quote.objects.create(
            business=self.business,
            client=self.client,
            title="Expired Quote",
            status=Quote.Status.SENT,
            valid_until=date.today() - timedelta(days=1),
            subtotal=Money(1000, "USD"),
            tax_amount=Money(100, "USD"),
            total_amount=Money(1100, "USD"),
            created_by=self.user,
        )

        approved_quote = Quote.objects.create(
            business=self.business,
            client=self.client,
            title="Approved Quote",
            status=Quote.Status.APPROVED,
            valid_until=date.today() + timedelta(days=30),
            subtotal=Money(1000, "USD"),
            tax_amount=Money(100, "USD"),
            total_amount=Money(1100, "USD"),
            created_by=self.user,
        )

        # Test manager methods
        active_quotes = Quote.objects.active()
        assert active_quote in active_quotes
        assert expired_quote not in active_quotes
        assert approved_quote in active_quotes  # Approved quotes are still active

        sent_quotes = Quote.objects.sent()
        assert active_quote in sent_quotes
        assert expired_quote in sent_quotes
        assert approved_quote not in sent_quotes

        approved_quotes = Quote.objects.approved()
        assert active_quote not in approved_quotes
        assert expired_quote not in approved_quotes
        assert approved_quote in approved_quotes

        business_quotes = Quote.objects.for_business(self.business)
        assert active_quote in business_quotes
        assert expired_quote in business_quotes
        assert approved_quote in business_quotes

    def test_quote_money_fields(self):
        """Test MoneyField functionality."""
        quote = Quote.objects.create(
            business=self.business,
            client=self.client,
            title="Money Test Quote",
            subtotal=Money(1000, "USD"),
            tax_amount=Money(100, "USD"),
            total_amount=Money(1100, "USD"),
            created_by=self.user,
        )

        # Test money field values
        assert quote.subtotal.amount == 1000
        assert quote.subtotal.currency.code == "USD"
        assert quote.tax_amount.amount == 100
        assert quote.total_amount.amount == 1100

        # Test money field arithmetic
        calculated_total = quote.subtotal + quote.tax_amount
        assert calculated_total == quote.total_amount

    def test_quote_audit_fields(self):
        """Test audit fields are set correctly."""
        quote = Quote.objects.create(
            business=self.business,
            client=self.client,
            title="Audit Test Quote",
            subtotal=Money(1000, "USD"),
            tax_amount=Money(100, "USD"),
            total_amount=Money(1100, "USD"),
            created_by=self.user,
        )

        assert quote.created_at is not None
        assert quote.updated_at is not None
        assert quote.created_by == self.user
        assert quote.sent_at is None
        assert quote.approved_at is None
