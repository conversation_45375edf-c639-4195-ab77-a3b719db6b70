{% load crispy_forms_tags %}

  <!-- New Template Card -->
  <div class="bg-white rounded-lg shadow p-6 border-2 border-dashed border-gray-300">
    <div class="flex items-center justify-between">
    <h2 class="text-lg font-medium text-gray-900 flex items-center">
      <span class="mr-2">🖋️</span> Create New Tour Template
    </h2>
    <button data-on-click="$_tourTemplateModal = 'closed'"
        class="cursor-pointer">
        ✕
      </button>
    </div>

    <form class="mt-4 space-y-4">
      {% csrf_token %}

      {% crispy form %}

      <div>
        <label for="id_name" class="block text-sm font-medium text-gray-700">Tour Name <span class="text-red-500">*</span></label>
        <input type="text" name="name" id="id_name" required maxlength="200"
               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500">
        <p class="mt-1 text-sm text-gray-500">e.g., "Paris Eiffel Tower Sunset Tour"</p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label for="id_base_price" class="block text-sm font-medium text-gray-700">Base Price <span class="text-red-500">*</span></label>
          <div class="mt-1 relative rounded-md shadow-sm">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none text-gray-500">$</div>
            <input type="number" name="base_price" id="id_base_price" required min="0" step="0.01"
                   class="block w-full pl-7 pr-12 border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500">
          </div>
        </div>

        <div>
          <label for="id_max_capacity" class="block text-sm font-medium text-gray-700">Max Group Size <span class="text-red-500">*</span></label>
          <input type="number" name="max_capacity" id="id_max_capacity" required min="1"
                 class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500">
        </div>

        <div>
          <label for="id_timezone" class="block text-sm font-medium text-gray-700">Timezone <span class="text-red-500">*</span></label>
          <select name="timezone" id="id_timezone" required
                  class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 rounded-md">
            {% for tz in timezones %}
            <option value="{{ tz }}"{% if tz == 'Europe/Paris' %} selected{% endif %}>{{ tz }}</option>
            {% endfor %}
          </select>
        </div>
      </div>

      <div>
        <label for="id_description" class="block text-sm font-medium text-gray-700">Description</label>
        <textarea name="description" id="id_description" rows="3"
                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"></textarea>
      </div>

      <button type="submit"
              class="w-full md:w-auto px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 cursor-pointer">
        Save
      </button>
    </form>
  </div>
