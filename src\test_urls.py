#!/usr/bin/env python
"""
Test script to verify the URL patterns work correctly.
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from django.urls import reverse
from django.test import Client
from tours.models import TourTemplate


def test_url_patterns():
    """Test the URL patterns."""
    print('Testing URL patterns...')

    client = Client()

    # Test public catalog URL
    print('\n1. Testing public catalog URL...')
    try:
        url = reverse('tours:public-catalog')
        print(f'✓ Public catalog URL: {url}')

        response = client.get(url)
        print(f'✓ Response status: {response.status_code}')
        if response.status_code == 200:
            print('✓ Public catalog page loads successfully')
        else:
            print(f'✗ Unexpected status code: {response.status_code}')
    except Exception as e:
        print(f'✗ Error testing public catalog: {e}')
        return False

    # Test public search URL
    print('\n2. Testing public search URL...')
    try:
        url = reverse('tours:public-search')
        print(f'✓ Public search URL: {url}')

        response = client.get(url)
        print(f'✓ Response status: {response.status_code}')
        if response.status_code == 200:
            print('✓ Public search page loads successfully')
        else:
            print(f'✗ Unexpected status code: {response.status_code}')
    except Exception as e:
        print(f'✗ Error testing public search: {e}')
        return False

    # Test public detail URL with existing tour
    print('\n3. Testing public detail URL...')
    try:
        tour = TourTemplate.objects.filter(is_active=True).first()
        if tour:
            url = reverse('tours:public-detail', kwargs={'pk': tour.pk})
            print(f'✓ Public detail URL: {url}')

            response = client.get(url)
            print(f'✓ Response status: {response.status_code}')
            if response.status_code == 200:
                print(f'✓ Public detail page loads successfully for tour: {tour.name}')
            else:
                print(f'✗ Unexpected status code: {response.status_code}')
        else:
            print('⚠ No active tours found to test detail URL')
    except Exception as e:
        print(f'✗ Error testing public detail: {e}')
        return False

    # Test search with parameters
    print('\n4. Testing search with parameters...')
    try:
        url = reverse('tours:public-search')
        response = client.get(url, {'q': 'adventure', 'activity': 'hiking'})
        print(f'✓ Search with parameters status: {response.status_code}')
        if response.status_code == 200:
            print('✓ Search with parameters works successfully')
        else:
            print(f'✗ Unexpected status code: {response.status_code}')
    except Exception as e:
        print(f'✗ Error testing search with parameters: {e}')
        return False

    print('\n✅ All URL patterns working correctly!')
    return True


if __name__ == '__main__':
    test_url_patterns()
