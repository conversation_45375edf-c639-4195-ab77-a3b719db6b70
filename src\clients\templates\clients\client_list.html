{% load i18n cotton %}
{% load static %}

<c-layouts.dashboard>
  <c-slot name="title">
    {% trans "Clients" %}
  </c-slot>

  <c-slot name="main">
    <div class="flex flex-col justify-between gap-4 sm:flex-row mb-4">
      <div>
        <p class="text-neutral-500">
          Manage and monitor clients
        </p>
      </div>
      <div class="flex gap-2">
        <button
          class="ring-offset-background focus-visible:outline-hidden focus-visible:ring-ring inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 h-10 px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
            class="lucide lucide-cloud-upload-icon lucide-cloud-upload">
            <path d="M12 13v8" />
            <path d="M4 14.899A7 7 0 1 1 15.71 8h1.79a4.5 4.5 0 0 1 2.5 8.242" />
            <path d="m8 17 4-4 4 4" />
          </svg>
          Upload
        </button>
        <button
          class="ring-offset-background focus-visible:outline-hidden focus-visible:ring-ring inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 h-10 px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
            class="lucide lucide-user-plus-icon lucide-user-plus">
            <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
            <circle cx="9" cy="7" r="4" />
            <line x1="19" x2="19" y1="8" y2="14" />
            <line x1="22" x2="16" y1="11" y2="11" />
          </svg>
          Add Client
        </button>
      </div>
    </div>

    <!-- Search -->
    <div
      class="flex items-center gap-4 rounded-md bg-white pl-3 outline-1 -outline-offset-1 outline-gray-300 has-[input:focus-within]:outline-2 has-[input:focus-within]:-outline-offset-2 has-[input:focus-within]:outline-blue-500 mb-4">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
        class="lucide lucide-search w-4 h-4 text-neutral-400">
        <circle cx="11" cy="11" r="8"></circle>
        <path d="m21 21-4.3-4.3"></path>
      </svg>
      <input type="search" placeholder="Search client..."
        class="block min-w-0 grow py-1.5 pr-3 pl-1 text-base text-gray-900 placeholder:text-gray-400 focus:outline-none sm:text-sm/6" />
    </div>

    {% if clients %}
    <div class="rounded-lg border text-card-foreground shadow-2xs  border-neutral-700">
      <div class="p-6 pt-0">
        <div class="overflow-x-auto">
          <table class="w-full" data-signals="{clientModal: 'close'}">
            <thead>
              <tr class="border-b border-neutral-700">
                <th class="text-left py-3 px-4 tracking-wider font-medium uppercase">
                  First Name
                </th>
                <th class="text-left py-3 px-4 tracking-wider font-medium uppercase">
                  Last Name
                </th>
                <th class="text-left py-3 px-4 tracking-wider font-medium uppercase">
                  Client Type
                </th>
                <th class="text-left py-3 px-4 font-medium tracking-wider uppercase">
                  Status
                </th>
                <th class="text-left py-3 px-4 font-medium tracking-wider uppercase">
                  LOCATION
                </th>
                <th class="text-left py-3 px-4 font-medium tracking-wider uppercase">
                  Phone Number
                </th>
                <th class="text-left py-3 px-4 font-medium tracking-wider uppercase">
                  Email
                </th>
              </tr>
            </thead>
            <tbody>
              {% for client in clients %}
              <tr class="border-b border-neutral-400  transition-colors cursor-pointer hover:bg-neutral-200"
                data-on-click="@get('{{ client.get_absolute_url }}')">
                <td class="px-4 text-sm">
                  {{ client.first_name | title }}
                </td>
                <td class="px-4 text-sm">
                  {{ client.last_name | title }}
                </td>
                <td class="px-4 text-sm">
                  {{ client.get_client_type_display }}
                </td>
                <td class="px-4">
                  <div class="flex items-center gap-2">
                    <div class="w-2 h-2 rounded-full bg-black"></div>
                    <span class="text-xs text-black uppercase tracking-wider">
                      {% if client.is_active %}
                      Active
                      {% else %}
                      Inactive
                      {% endif %}
                    </span>
                  </div>
                </td>
                <td class="px-4">
                  <div class="flex items-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                      class="lucide lucide-map-pin w-3 h-3 ">
                      <path d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z"></path>
                      <circle cx="12" cy="10" r="3"></circle>
                    </svg>
                    <span class="text-sm">Berlin</span>
                  </div>
                </td>
                <td class="px-4 text-sm font-mono">
                  {{ client.phone }}
                </td>
                <td class="px-4 text-sm font-mono">
                  {{ client.email }}
                </td>
              </tr>
              {% empty %}
              <tr>No clients</tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
    </div>
    {% endif %}

    <!-- Pagination -->
    {% if is_paginated %}
    <div class="flex justify-center mt-8">
      <nav class="flex space-x-1">
        {% if page_obj.has_previous %}
        <a href="?page=1"
          class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 hover:bg-gray-50 hover:text-gray-700">First</a>
        <a href="?page={{ page_obj.previous_page_number }}"
          class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 hover:bg-gray-50 hover:text-gray-700">Previous</a>
        {% endif %}

        {% for num in page_obj.paginator.page_range %}
        {% if page_obj.number == num %}
        <span
          class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 hover:bg-gray-50 hover:text-gray-700 bg-blue-50 border-blue-500 text-blue-600">{{ num }}</span>
        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
        <a href="?page={{ num }}"
          class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 hover:bg-gray-50 hover:text-gray-700">{{ num }}</a>
        {% endif %}
        {% endfor %}

        {% if page_obj.has_next %}
        <a href="?page={{ page_obj.next_page_number }}"
          class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 hover:bg-gray-50 hover:text-gray-700">Next</a>
        <a href="?page={{ page_obj.paginator.num_pages }}"
          class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 hover:bg-gray-50 hover:text-gray-700">Last</a>
        {% endif %}
      </nav>
    </div>
    {% endif %}

    <!-- Client Modal -->
    <div id="client-modal" data-show="$clientModal === 'open'"
      class="fixed inset-0 bg-gray-100/80 flex items-center justify-center p-4 z-50">
      <div class="rounded-lg border text-card-foreground shadow-2xs w-full max-w-2xl bg-white">
        <div class="space-y-1.5 p-6 flex flex-row items-start justify-between">
          <div>
            <h3 class="text-lg font-bold tracking-wider">
              {{ client }}
            </h3>
            <p class="text-sm font-mono">
              G-078W
            </p>
          </div>
          <button data-on-click="$clientModal = 'close'"
            class="ring-offset-background focus-visible:outline-hidden focus-visible:ring-ring inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 hover:bg-accent h-10 px-4 py-2 cursor-pointer hover:bg-blue-400 hover:text-white">
            ✕
          </button>
        </div>
        <div class="p-6 pt-0 space-y-4">
          <div class="grid grid-cols-2 gap-4">
            <div>
              <p class="text-xs tracking-wider mb-1 uppercase">STATUS</p>
              <div class="flex items-center gap-2">
                <div class="w-2 h-2 rounded-full bg-blue-400"></div>
                <span class="text-sm uppercase tracking-wider">arrived</span>
              </div>
            </div>
            <div>
              <p class="text-xs tracking-wider mb-1 uppercase">Destination</p>
              <p class="text-sm">Berlin</p>
            </div>
            <div>
              <p class="text-xs tracking-wider mb-1 uppercase">MISSIONS COMPLETED</p>
              <p class="text-sm font-mono">47</p>
            </div>
            <div>
              <p class="text-xs tracking-wider mb-1 uppercase">RISK LEVEL</p>
              <span class="text-xs px-2 py-1 rounded uppercase tracking-wider text-white bg-orange-500">high</span>
            </div>
          </div>
          <div class="flex gap-2 pt-4">
            <button
              class="ring-offset-background focus-visible:outline-hidden focus-visible:ring-ring inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 h-10 px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white">
              <i class="fa fa-eye"></i>
              View Detail
            </button>
            <button
              class="ring-offset-background focus-visible:outline-hidden focus-visible:ring-ring inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 h-10 px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="lucide lucide-file-plus2-icon lucide-file-plus-2">
                <path d="M4 22h14a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v4" />
                <path d="M14 2v4a2 2 0 0 0 2 2h4" />
                <path d="M3 15h6" />
                <path d="M6 12v6" />
              </svg>
              Create Invoice
            </button>
            <button
              class="ring-offset-background focus-visible:outline-hidden focus-visible:ring-ring inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border h-10 px-4 py-2 border-neutral-700 hover:bg-neutral-800 hover:text-white bg-transparent">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="lucide lucide-history-icon lucide-history">
                <path d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8" />
                <path d="M3 3v5h5" />
                <path d="M12 7v5l4 2" />
              </svg>
              View History
            </button>
            <button
              class="ring-offset-background focus-visible:outline-hidden focus-visible:ring-ring inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border h-10 px-4 py-2 border-neutral-700 hover:bg-neutral-800 hover:text-white bg-transparent">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="lucide lucide-message-square-text-icon lucide-message-square-text">
                <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" />
                <path d="M13 8H7" />
                <path d="M17 12H7" />
              </svg>
              Send Message
            </button>
          </div>
        </div>
      </div>
    </div>

  </c-slot>
</c-layouts.dashboard>
