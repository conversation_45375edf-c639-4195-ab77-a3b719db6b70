from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from simple_history.admin import SimpleHistoryAdmin

from .models import Client, Traveler


class TravelerInline(admin.TabularInline):
    """Inline admin for Traveler model."""

    model = Traveler
    extra = 0
    fields = (
        'first_name',
        'last_name',
        'date_of_birth',
        'gender',
        'email',
        'phone',
        'passport_number',
        'passport_expiry',
        'is_active',
    )
    readonly_fields = ('created_at', 'updated_at')


@admin.register(Client)
class ClientAdmin(SimpleHistoryAdmin):
    """Admin configuration for Client model."""

    list_display = (
        'display_name',
        'client_type',
        'email',
        'phone',
        'city',
        'country',
        'business',
        'is_active',
        'created_at',
    )
    list_filter = (
        'client_type',
        'is_active',
        'business',
        'country',
        'created_at',
    )
    search_fields = (
        'first_name',
        'last_name',
        'company_name',
        'email',
        'phone',
        'city',
    )
    readonly_fields = (
        'created_at',
        'updated_at',
    )

    fieldsets = (
        (
            _('Basic Information'),
            {
                'fields': (
                    'business',
                    'client_type',
                    'first_name',
                    'last_name',
                    'company_name',
                )
            },
        ),
        (
            _('Contact Information'),
            {
                'fields': (
                    'email',
                    'phone',
                )
            },
        ),
        (
            _('Address'),
            {
                'fields': (
                    'address_line1',
                    'address_line2',
                    'city',
                    'state_province',
                    'postal_code',
                    'country',
                )
            },
        ),
        (
            _('Additional Information'),
            {
                'fields': (
                    'notes',
                    'is_active',
                    'created_by',
                )
            },
        ),
        (
            _('Timestamps'),
            {
                'fields': (
                    'created_at',
                    'updated_at',
                ),
                'classes': ('collapse',),
            },
        ),
    )

    inlines = [TravelerInline]

    def get_queryset(self, request):
        """Optimize queryset with select_related."""
        return super().get_queryset(request).select_related('business', 'created_by')


@admin.register(Traveler)
class TravelerAdmin(SimpleHistoryAdmin):
    """Admin configuration for Traveler model."""

    list_display = (
        'full_name',
        'client',
        'age',
        'gender',
        'email',
        'passport_number',
        'passport_expiry',
        'is_active',
    )
    list_filter = (
        'gender',
        'is_active',
        'client__business',
        'passport_expiry',
        'created_at',
    )
    search_fields = (
        'first_name',
        'last_name',
        'email',
        'phone',
        'passport_number',
        'client__first_name',
        'client__last_name',
        'client__company_name',
    )
    readonly_fields = (
        'age',
        'business',
        'created_at',
        'updated_at',
    )

    fieldsets = (
        (
            _('Basic Information'),
            {
                'fields': (
                    'client',
                    'first_name',
                    'last_name',
                    'date_of_birth',
                    'age',
                    'gender',
                )
            },
        ),
        (
            _('Contact Information'),
            {
                'fields': (
                    'email',
                    'phone',
                )
            },
        ),
        (
            _('Travel Documents'),
            {
                'fields': (
                    'passport_number',
                    'passport_expiry',
                    'nationality',
                )
            },
        ),
        (
            _('Emergency Contact'),
            {
                'fields': (
                    'emergency_contact_name',
                    'emergency_contact_phone',
                    'emergency_contact_relationship',
                )
            },
        ),
        (
            _('Additional Information'),
            {
                'fields': (
                    'dietary_restrictions',
                    'medical_conditions',
                    'special_requirements',
                    'notes',
                    'is_active',
                    'created_by',
                )
            },
        ),
        (
            _('Timestamps'),
            {
                'fields': (
                    'created_at',
                    'updated_at',
                ),
                'classes': ('collapse',),
            },
        ),
    )

    def get_queryset(self, request):
        """Optimize queryset with select_related."""
        return (
            super()
            .get_queryset(request)
            .select_related('client', 'client__business', 'created_by')
        )
