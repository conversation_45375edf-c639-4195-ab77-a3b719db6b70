{% extends 'tours/tour_creation/base.html' %}

{% block content %}
<div class="bg-white rounded-lg shadow overflow-hidden">
  <div class="border-b border-gray-200 bg-indigo-50 px-6 py-4">
    <div class="flex items-center">
      <div class="h-10 w-10 rounded-full bg-indigo-100 flex items-center justify-center mr-3">
        <span class="text-indigo-800 text-xl">{{ tour.emoji }}</span>
      </div>
      <div>
        <h2 class="text-xl font-bold text-gray-900">{{ tour.name }}</h2>
        <p class="text-gray-600">
          {{ selected_date|date:"E d, Y" }} • {{ selected_date|time:"g:i A" }}
          ({{ tour.timezone }})
        </p>
      </div>
    </div>
  </div>

  <div class="grid grid-cols-1 lg:grid-cols-3">
    <!-- Group Size Selector -->
    <div class="lg:col-span-2 p-6 border-r border-gray-200">
      <h3 class="text-lg font-medium text-gray-900 mb-4">Group Size</h3>

      <div class="max-w-md">
        <div class="mb-6">
          <label class="block text-sm font-medium text-gray-700 mb-1">
            Number of Guests
            <span class="text-gray-500 text-xs">(Max {{ tour.max_capacity }})</span>
          </label>

          <div class="relative flex items-center border border-gray-300 rounded-md overflow-hidden">
            <button type="button" id="decrease-guests"
                    class="px-4 py-3 bg-gray-50 hover:bg-gray-100 text-gray-500 border-r border-gray-300 focus:outline-none">
              <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"></path>
              </svg>
            </button>

            <input type="number" id="guests-count" value="1" min="1" max="{{ tour.max_capacity }}"
                   class="w-20 text-center border-0 focus:ring-0"
                   aria-label="Number of guests">

            <button type="button" id="increase-guests"
                    class="px-4 py-3 bg-gray-50 hover:bg-gray-100 text-gray-500 border-l border-gray-300 focus:outline-none">
              <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
            </button>
          </div>

          <div class="mt-2 text-sm text-gray-500">
            <span id="capacity-warning" class="hidden text-red-600">
              ❗ Only <span id="available-count">17</span> slots available
            </span>
          </div>
        </div>

        <!-- Capacity Meter -->
        <div class="mb-6">
          <div class="flex justify-between text-sm font-medium">
            <span>Capacity</span>
            <span><span id="current-slots">1</span>/<span>{{ tour.max_capacity }}</span> slots</span>
          </div>

          <div class="mt-1 w-full bg-gray-200 rounded-full h-2.5">
            <div id="capacity-meter" class="h-2.5 rounded-full bg-capacity-high"
                 style="width: 5%;"></div>
          </div>

          <div class="mt-2 flex justify-between text-xs text-gray-500">
            <span>Low</span>
            <span>Medium</span>
            <span>High</span>
          </div>
        </div>

        <!-- Pricing Breakdown -->
        <div class="border-t border-gray-200 pt-4">
          <h4 class="font-medium text-gray-900 mb-2">Pricing Breakdown</h4>

          <div class="space-y-2">
            <div class="flex justify-between">
              <span class="text-gray-600">Base Price (x <span id="price-count">1</span>)</span>
              <span>${{ tour.base_price|floatformat:2 }}</span>
            </div>

            <div class="flex justify-between font-bold pt-2 border-t border-gray-200">
              <span>Total</span>
              <span id="total-price">${{ tour.base_price|floatformat:2 }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Action Panel -->
    <div class="border-l border-gray-200">
      <!-- Risk Alerts -->
      <div class="bg-red-50 border-l-4 border-red-400 p-4 m-4">
        <div class="flex">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3">
            <p class="text-sm font-medium text-red-800">Olympic Opening Ceremony</p>
            <p class="text-sm text-red-700">Expect city-wide traffic delays on this date</p>
          </div>
        </div>
      </div>

      <!-- Confirmation Panel -->
      <div class="m-4">
        <h3 class="font-medium text-gray-900 mb-3">Ready to Create?</h3>

        <div class="space-y-3 mb-4">
          <div class="flex items-start">
            <div class="flex-shrink-0 h-5 w-5 rounded-full bg-green-100 flex items-center justify-center mt-0.5">
              <svg class="h-3 w-3 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
            </div>
            <p class="ml-2 text-sm text-gray-700">Capacity reserved: <span id="reserved-slots">1</span>/{{ tour.max_capacity }}</p>
          </div>

          <div class="flex items-start">
            <div class="flex-shrink-0 h-5 w-5 rounded-full bg-green-100 flex items-center justify-center mt-0.5">
              <svg class="h-3 w-3 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
            </div>
            <p class="ml-2 text-sm text-gray-700">Pricing locked: $<span id="final-price">{{ tour.base_price|floatformat:2 }}</span></p>
          </div>

          <p class="text-xs text-gray-500 pl-7">
            Last inventory check: <span id="last-check">{{ now|date:"G:i" }} UTC</span>
            <button
                    hx-trigger="click"
                    hx-target="#capacity-meter"
                    class="ml-1 underline text-indigo-600 hover:text-indigo-900">Refresh</button>
          </p>
        </div>

        <button id="create-button"
                hx-include="#guests-count"
                disabled
                class="w-full px-4 py-2.5 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gray-400 cursor-not-allowed">
          Create Tour Event
        </button>

        <p class="mt-2 text-xs text-gray-500 text-center">
          ⏱️ 2-minute reservation hold applied upon creation
        </p>
      </div>
    </div>
  </div>
</div>

<script>
  document.getElementById('current-step').textContent = '3';

  // Real app would have:
  // 1. Live capacity updates via SSE/channels
  // 2. Guest count validation against available slots
  // 3. Timer for reservation hold

  // Example guest count handler:
  document.getElementById('increase-guests').addEventListener('click', function() {
    let count = parseInt(document.getElementById('guests-count').value);
    if(count < {{ tour.max_capacity }}) {
      document.getElementById('guests-count').value = count + 1;
      updateCapacityUI();
    }
  });

  function updateCapacityUI() {
    const guests = parseInt(document.getElementById('guests-count').value);
    const available = {{ available_slots }};

    // Update capacity meter
    const percentUsed = (guests / {{ tour.max_capacity }}) * 100;
    document.getElementById('capacity-meter').style.width = `${percentUsed}%`;

    // Update warnings
    if(guests > available) {
      document.getElementById('capacity-warning').classList.remove('hidden');
      document.getElementById('available-count').textContent = available;
      document.getElementById('create-button').disabled = true;
    } else {
      document.getElementById('capacity-warning').classList.add('hidden');
      document.getElementById('create-button').disabled = false;
    }

    // Update totals
    document.getElementById('current-slots').textContent = guests;
    document.getElementById('price-count').textContent = guests;
    document.getElementById('total-price').textContent = `$${(guests * {{ tour.base_price }}).toFixed(2)}`;
    document.getElementById('final-price').textContent = (guests * {{ tour.base_price }}).toFixed(2);
    document.getElementById('reserved-slots').textContent = guests;
  }
</script>
{% endblock %}
