<!DOCTYPE html>
<html lang="en">

  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- DatastarJS -->
    <script type="module" src="https://cdn.jsdelivr.net/gh/starfederation/datastar@main/bundles/datastar.js"></script>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  </head>

  <body>
    <!-- Enhanced Sidebar with Collapsible Navigation -->
    <div id="sidebar" class="bg-white shadow-lg h-full transition-all duration-300 ease-in-out" style="width: 256px;">
      <div class="flex flex-col h-full">

        <!-- Top Navigation Header -->
        <div class="flex items-center justify-between h-16 px-4 bg-primary-600 text-white">
          <div class="flex items-center">
            <a href="{% url 'core:home' %}" class="flex items-center text-xl font-bold text-gray-900">
              <i class="fas fa-route text-blue-500 mr-2"></i>
              <span class="sidebar-text">TourFlow</span>
            </a>
          </div>
        </div>

        <!-- Navigation -->
        <nav class="flex-1 px-2 py-4 space-y-1 overflow-y-auto">

          <!-- Dashboard -->
          <a href="{% url 'core:dashboard' %}"
            class="sidebar-item flex items-center px-3 py-2 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors {% if request.resolver_match.url_name == 'home' %}bg-primary-50 text-primary-700 border-r-2 border-primary-500{% endif %}"
            data-tooltip="Dashboard">
            <svg class="sidebar-icon w-5 h-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 22V12h6v10"></path>
            </svg>
            <span class="sidebar-text ml-3">Dashboard</span>
          </a>

          <!-- Business Operations Section -->
          <div class="sidebar-section">
            <div
              class="sidebar-section-header flex items-center justify-between px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider cursor-pointer"
              onclick="toggleSection('business-ops')">
              <span class="sidebar-text">Business Operations</span>
              <svg class="sidebar-text w-4 h-4 transition-transform" id="business-ops-icon" fill="none"
                stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </div>

            <div id="business-ops" class="sidebar-subsection space-y-1">
              <!-- Clients with Sub-items -->
              <div class="sidebar-menu-item">
                <div
                  class="flex items-center justify-between px-3 py-2 ml-4 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer"
                  onclick="toggleMenuItem('clients-submenu')" data-tooltip="Clients">
                  <div class="flex items-center">
                    <svg class="sidebar-icon w-5 h-5 flex-shrink-0" fill="none" stroke="currentColor"
                      viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z">
                      </path>
                    </svg>
                    <span class="sidebar-text ml-3">Clients</span>
                  </div>
                  <svg class="sidebar-text w-4 h-4 transition-transform" id="clients-submenu-icon" fill="none"
                    stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                  </svg>
                </div>
                <div id="clients-submenu" class="sidebar-submenu ml-8 space-y-1 hidden">
                  <a href="{% url 'clients:client-list' %}"
                    class="sidebar-subitem flex items-center px-3 py-1 text-sm text-gray-600 rounded hover:bg-gray-100 transition-colors"
                    data-tooltip="All Clients">
                    <i class="fas fa-list w-4 h-4 mr-2"></i>
                    <span class="sidebar-text">All Clients</span>
                  </a>
                  <a href="{% url 'clients:client-create' %}"
                    class="sidebar-subitem flex items-center px-3 py-1 text-sm text-gray-600 rounded hover:bg-gray-100 transition-colors"
                    data-tooltip="Add Client">
                    <i class="fas fa-plus w-4 h-4 mr-2"></i>
                    <span class="sidebar-text">Add Client</span>
                  </a>
                  <a href="#"
                    class="sidebar-subitem flex items-center px-3 py-1 text-sm text-gray-600 rounded hover:bg-gray-100 transition-colors"
                    data-tooltip="Client Groups">
                    <i class="fas fa-users w-4 h-4 mr-2"></i>
                    <span class="sidebar-text">Client Groups</span>
                  </a>
                </div>
              </div>

              <!-- Quotes with Sub-items -->
              <div class="sidebar-menu-item">
                <div
                  class="flex items-center justify-between px-3 py-2 ml-4 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer"
                  onclick="toggleMenuItem('quotes-submenu')" data-tooltip="Quotes">
                  <div class="flex items-center">
                    <svg class="sidebar-icon w-5 h-5 flex-shrink-0" fill="none" stroke="currentColor"
                      viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                      </path>
                    </svg>
                    <span class="sidebar-text ml-3">Quotes</span>
                  </div>
                  <svg class="sidebar-text w-4 h-4 transition-transform" id="quotes-submenu-icon" fill="none"
                    stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                  </svg>
                </div>
                <div id="quotes-submenu" class="sidebar-submenu ml-8 space-y-1 hidden">
                  <a href="{% url 'quotes:quote-list' %}"
                    class="sidebar-subitem flex items-center px-3 py-1 text-sm text-gray-600 rounded hover:bg-gray-100 transition-colors"
                    data-tooltip="All Quotes">
                    <i class="fas fa-list w-4 h-4 mr-2"></i>
                    <span class="sidebar-text">All Quotes</span>
                  </a>
                  <a href="{% url 'quotes:quote-create' %}"
                    class="sidebar-subitem flex items-center px-3 py-1 text-sm text-gray-600 rounded hover:bg-gray-100 transition-colors"
                    data-tooltip="Create Quote">
                    <i class="fas fa-plus w-4 h-4 mr-2"></i>
                    <span class="sidebar-text">Create Quote</span>
                  </a>
                  <a href="#"
                    class="sidebar-subitem flex items-center px-3 py-1 text-sm text-gray-600 rounded hover:bg-gray-100 transition-colors"
                    data-tooltip="Draft Quotes">
                    <i class="fas fa-edit w-4 h-4 mr-2"></i>
                    <span class="sidebar-text">Draft Quotes</span>
                  </a>
                  <a href="#"
                    class="sidebar-subitem flex items-center px-3 py-1 text-sm text-gray-600 rounded hover:bg-gray-100 transition-colors"
                    data-tooltip="Approved Quotes">
                    <i class="fas fa-check-circle w-4 h-4 mr-2"></i>
                    <span class="sidebar-text">Approved</span>
                  </a>
                </div>
              </div>

              <!-- Invoices with Sub-items -->
              <div class="sidebar-menu-item">
                <div
                  class="flex items-center justify-between px-3 py-2 ml-4 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer"
                  onclick="toggleMenuItem('invoices-submenu')" data-tooltip="Invoices">
                  <div class="flex items-center">
                    <svg class="sidebar-icon w-5 h-5 flex-shrink-0" fill="none" stroke="currentColor"
                      viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2">
                      </path>
                    </svg>
                    <span class="sidebar-text ml-3">Invoices</span>
                  </div>
                  <svg class="sidebar-text w-4 h-4 transition-transform" id="invoices-submenu-icon" fill="none"
                    stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                  </svg>
                </div>
                <div id="invoices-submenu" class="sidebar-submenu ml-8 space-y-1 hidden">
                  <a href="{% url 'invoices:list' %}"
                    class="sidebar-subitem flex items-center px-3 py-1 text-sm text-gray-600 rounded hover:bg-gray-100 transition-colors"
                    data-tooltip="All Invoices">
                    <i class="fas fa-list w-4 h-4 mr-2"></i>
                    <span class="sidebar-text">All Invoices</span>
                  </a>
                  <a href="{% url 'invoices:create' %}"
                    class="sidebar-subitem flex items-center px-3 py-1 text-sm text-gray-600 rounded hover:bg-gray-100 transition-colors"
                    data-tooltip="Create Invoice">
                    <i class="fas fa-plus w-4 h-4 mr-2"></i>
                    <span class="sidebar-text">Create Invoice</span>
                  </a>
                  <a href="#"
                    class="sidebar-subitem flex items-center px-3 py-1 text-sm text-gray-600 rounded hover:bg-gray-100 transition-colors"
                    data-tooltip="Pending Invoices">
                    <i class="fas fa-clock w-4 h-4 mr-2"></i>
                    <span class="sidebar-text">Pending</span>
                  </a>
                  <a href="#"
                    class="sidebar-subitem flex items-center px-3 py-1 text-sm text-gray-600 rounded hover:bg-gray-100 transition-colors"
                    data-tooltip="Paid Invoices">
                    <i class="fas fa-check w-4 h-4 mr-2"></i>
                    <span class="sidebar-text">Paid</span>
                  </a>
                </div>
              </div>

              <!-- Bookings with Sub-items -->
              <div class="sidebar-menu-item">
                <div
                  class="flex items-center justify-between px-3 py-2 ml-4 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer"
                  onclick="toggleMenuItem('bookings-submenu')">
                  <div class="flex items-center">
                    <svg class="sidebar-icon w-5 h-5 flex-shrink-0" fill="none" stroke="currentColor"
                      viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z">
                      </path>
                    </svg>
                    <span class="sidebar-text ml-3">Bookings</span>
                  </div>
                  <svg class="sidebar-text w-4 h-4 transition-transform" id="bookings-submenu-icon" fill="none"
                    stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                  </svg>
                </div>
                <div id="bookings-submenu" class="sidebar-submenu ml-8 space-y-1 hidden">
                  <a href="{% url 'bookings:dashboard' %}"
                    class="sidebar-subitem flex items-center px-3 py-1 text-sm text-gray-600 rounded hover:bg-gray-100 transition-colors"
                    data-tooltip="Bookings Dashboard">
                    <i class="fas fa-tachometer-alt w-4 h-4 mr-2"></i>
                    <span class="sidebar-text">Dashboard</span>
                  </a>
                  <a href="{% url 'bookings:booking_list' %}"
                    class="sidebar-subitem flex items-center px-3 py-1 text-sm text-gray-600 rounded hover:bg-gray-100 transition-colors"
                    data-tooltip="All Bookings">
                    <i class="fas fa-list w-4 h-4 mr-2"></i>
                    <span class="sidebar-text">All Bookings</span>
                  </a>
                  <a href="{% url 'bookings:calendar' %}"
                    class="sidebar-subitem flex items-center px-3 py-1 text-sm text-gray-600 rounded hover:bg-gray-100 transition-colors"
                    data-tooltip="Calendar View">
                    <i class="fas fa-calendar w-4 h-4 mr-2"></i>
                    <span class="sidebar-text">Calendar</span>
                  </a>
                  <a href="{% url 'tours:event_create' %}"
                    class="sidebar-subitem flex items-center px-3 py-1 text-sm text-gray-600 rounded hover:bg-gray-100 transition-colors"
                    data-tooltip="Create Event">
                    <i class="fas fa-plus w-4 h-4 mr-2"></i>
                    <span class="sidebar-text">Create Event</span>
                  </a>
                </div>
              </div>

              <!-- Payments with Sub-items -->
              <div class="sidebar-menu-item">
                <div
                  class="flex items-center justify-between px-3 py-2 ml-4 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer"
                  onclick="toggleMenuItem('payments-submenu')">
                  <div class="flex items-center">
                    <svg class="sidebar-icon w-5 h-5 flex-shrink-0" fill="none" stroke="currentColor"
                      viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1">
                      </path>
                    </svg>
                    <span class="sidebar-text ml-3">Payments</span>
                  </div>
                  <svg class="sidebar-text w-4 h-4 transition-transform" id="payments-submenu-icon" fill="none"
                    stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                  </svg>
                </div>
                <div id="payments-submenu" class="sidebar-submenu ml-8 space-y-1 hidden">
                  <a href="{% url 'payments:subscription_dashboard' %}"
                    class="sidebar-subitem flex items-center px-3 py-1 text-sm text-gray-600 rounded hover:bg-gray-100 transition-colors"
                    data-tooltip="Payment Dashboard">
                    <i class="fas fa-tachometer-alt w-4 h-4 mr-2"></i>
                    <span class="sidebar-text">Dashboard</span>
                  </a>
                  <a href="#"
                    class="sidebar-subitem flex items-center px-3 py-1 text-sm text-gray-600 rounded hover:bg-gray-100 transition-colors"
                    data-tooltip="Payment History">
                    <i class="fas fa-history w-4 h-4 mr-2"></i>
                    <span class="sidebar-text">History</span>
                  </a>
                  <a href="#"
                    class="sidebar-subitem flex items-center px-3 py-1 text-sm text-gray-600 rounded hover:bg-gray-100 transition-colors"
                    data-tooltip="Payment Methods">
                    <i class="fas fa-credit-card w-4 h-4 mr-2"></i>
                    <span class="sidebar-text">Methods</span>
                  </a>
                  <a href="#"
                    class="sidebar-subitem flex items-center px-3 py-1 text-sm text-gray-600 rounded hover:bg-gray-100 transition-colors"
                    data-tooltip="Subscription">
                    <i class="fas fa-sync w-4 h-4 mr-2"></i>
                    <span class="sidebar-text">Subscription</span>
                  </a>
                </div>
              </div>
            </div>

            <!-- Divider -->
            <div class="border-t border-gray-200 my-2"></div>

            <!-- Management Section -->
            <div class="sidebar-section">
              <div
                class="sidebar-section-header flex items-center justify-between px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider cursor-pointer"
                onclick="toggleSection('management')">
                <span class="sidebar-text">Management</span>
                <svg class="sidebar-text w-4 h-4 transition-transform" id="management-icon" fill="none"
                  stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
              </div>

              <div id="management" class="sidebar-subsection space-y-1">
                <!-- Team Management -->
                <a href="{% url 'accounts:user_management' %}"
                  class="sidebar-item flex items-center px-3 py-2 ml-4 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors"
                  data-tooltip="Team Management">
                  <svg class="sidebar-icon w-5 h-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z">
                    </path>
                  </svg>
                  <span class="sidebar-text ml-3">Team Management</span>
                </a>

                <!-- Business Settings -->
                <a href="{% url 'businesses:detail' user.profile.business.id %}"
                  class="sidebar-item flex items-center px-3 py-2 ml-4 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors"
                  data-tooltip="Business Settings">
                  <svg class="sidebar-icon w-5 h-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4">
                    </path>
                  </svg>
                  <span class="sidebar-text ml-3">Business Settings</span>
                </a>
              </div>
            </div>

            <!-- Tools Section -->
            <div class="sidebar-section">
              <div
                class="sidebar-section-header flex items-center justify-between px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider cursor-pointer"
                onclick="toggleSection('tools')">
                <span class="sidebar-text">Tools</span>
                <svg class="sidebar-text w-4 h-4 transition-transform" id="tools-icon" fill="none" stroke="currentColor"
                  viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
              </div>

              <div id="tools" class="sidebar-subsection space-y-1">
                <!-- Data Generator -->
                <a href="{% url 'data_generator:dashboard' %}"
                  class="sidebar-item flex items-center px-3 py-2 ml-4 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors"
                  data-tooltip="Data Generator">
                  <svg class="sidebar-icon w-5 h-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4">
                    </path>
                  </svg>
                  <span class="sidebar-text ml-3">Data Generator</span>
                </a>
              </div>
            </div>
        </nav>
      </div>
    </div>

    <!-- Tooltip for collapsed sidebar -->
    <div id="sidebar-tooltip"
      class="fixed z-50 px-2 py-1 text-sm text-white bg-gray-900 rounded shadow-lg pointer-events-none opacity-0 transition-opacity duration-200">
    </div>

    <!-- JavaScript for Sidebar Functionality -->
    <script>
      let sidebarCollapsed = false;
      const sidebar = document.getElementById('sidebar');
      const sidebarTitle = document.getElementById('sidebar-title');
      const sidebarLogo = document.getElementById('sidebar-logo');
      const sidebarTexts = document.querySelectorAll('.sidebar-text');
      const sidebarItems = document.querySelectorAll('.sidebar-item');
      const tooltip = document.getElementById('sidebar-tooltip');

      // Initialize section states
      const sectionStates = {
        'business-ops': true,
        'management': true,
        'tools': true
      };

      // Initialize menu item states
      const menuItemStates = {
        'clients-submenu': false,
        'quotes-submenu': false,
        'invoices-submenu': false,
        'bookings-submenu': false,
        'payments-submenu': false
      };

      function toggleSidebar() {
        sidebarCollapsed = !sidebarCollapsed;

        if (sidebarCollapsed) {
          // Collapse sidebar
          sidebar.style.width = '64px';
          sidebarTexts.forEach(text => text.style.display = 'none');

          // Hide section headers when collapsed
          document.querySelectorAll('.sidebar-section-header').forEach(header => {
            header.style.display = 'none';
          });

          // Hide subsections when collapsed
          document.querySelectorAll('.sidebar-subsection').forEach(subsection => {
            subsection.style.display = 'none';
          });

          // Hide sub-menus when collapsed
          document.querySelectorAll('.sidebar-submenu').forEach(submenu => {
            submenu.style.display = 'none';
          });

          // Enable tooltips
          enableTooltips();
        } else {
          // Expand sidebar
          sidebar.style.width = '256px';
          setTimeout(() => {
            sidebarTexts.forEach(text => text.style.display = 'inline');

            // Show section headers when expanded
            document.querySelectorAll('.sidebar-section-header').forEach(header => {
              header.style.display = 'flex';
            });

            // Restore section states
            Object.keys(sectionStates).forEach(sectionId => {
              const subsection = document.getElementById(sectionId);
              const icon = document.getElementById(sectionId + '-icon');
              if (subsection && icon) {
                if (sectionStates[sectionId]) {
                  subsection.style.display = 'block';
                  icon.style.transform = 'rotate(0deg)';
                } else {
                  subsection.style.display = 'none';
                  icon.style.transform = 'rotate(-90deg)';
                }
              }
            });

            // Restore menu item states
            Object.keys(menuItemStates).forEach(menuId => {
              const submenu = document.getElementById(menuId);
              const icon = document.getElementById(menuId + '-icon');
              if (submenu && icon) {
                if (menuItemStates[menuId]) {
                  submenu.style.display = 'block';
                  icon.style.transform = 'rotate(0deg)';
                } else {
                  submenu.style.display = 'none';
                  icon.style.transform = 'rotate(-90deg)';
                }
              }
            });
          }, 150);

          // Disable tooltips
          disableTooltips();
        }
      }

      function toggleSection(sectionId) {
        if (sidebarCollapsed) return;

        const subsection = document.getElementById(sectionId);
        const icon = document.getElementById(sectionId + '-icon');

        if (subsection && icon) {
          const isVisible = subsection.style.display !== 'none';

          if (isVisible) {
            subsection.style.display = 'none';
            icon.style.transform = 'rotate(-90deg)';
            sectionStates[sectionId] = false;
          } else {
            subsection.style.display = 'block';
            icon.style.transform = 'rotate(0deg)';
            sectionStates[sectionId] = true;
          }
        }
      }

      function toggleMenuItem(menuId) {
        if (sidebarCollapsed) return;

        const submenu = document.getElementById(menuId);
        const icon = document.getElementById(menuId + '-icon');

        if (submenu && icon) {
          const isVisible = submenu.style.display !== 'none';

          if (isVisible) {
            submenu.style.display = 'none';
            icon.style.transform = 'rotate(-90deg)';
            menuItemStates[menuId] = false;
          } else {
            submenu.style.display = 'block';
            icon.style.transform = 'rotate(0deg)';
            menuItemStates[menuId] = true;
          }
        }
      }

      function enableTooltips() {
        sidebarItems.forEach(item => {
          item.addEventListener('mouseenter', showTooltip);
          item.addEventListener('mouseleave', hideTooltip);
        });

        // Also enable tooltips for sub-items
        document.querySelectorAll('.sidebar-subitem').forEach(item => {
          item.addEventListener('mouseenter', showTooltip);
          item.addEventListener('mouseleave', hideTooltip);
        });

        // Enable tooltips for menu items with sub-menus
        document.querySelectorAll('.sidebar-menu-item > div').forEach(item => {
          item.addEventListener('mouseenter', showTooltip);
          item.addEventListener('mouseleave', hideTooltip);
        });
      }

      function disableTooltips() {
        sidebarItems.forEach(item => {
          item.removeEventListener('mouseenter', showTooltip);
          item.removeEventListener('mouseleave', hideTooltip);
        });

        document.querySelectorAll('.sidebar-subitem').forEach(item => {
          item.removeEventListener('mouseenter', showTooltip);
          item.removeEventListener('mouseleave', hideTooltip);
        });

        document.querySelectorAll('.sidebar-menu-item > div').forEach(item => {
          item.removeEventListener('mouseenter', showTooltip);
          item.removeEventListener('mouseleave', hideTooltip);
        });

        hideTooltip();
      }

      function showTooltip(event) {
        if (!sidebarCollapsed) return;

        const tooltipText = event.currentTarget.getAttribute('data-tooltip');
        if (tooltipText) {
          tooltip.textContent = tooltipText;
          tooltip.style.opacity = '1';

          const rect = event.currentTarget.getBoundingClientRect();
          tooltip.style.left = (rect.right + 8) + 'px';
          tooltip.style.top = (rect.top + rect.height / 2 - tooltip.offsetHeight / 2) + 'px';
        }
      }

      function hideTooltip() {
        tooltip.style.opacity = '0';
      }

      // Initialize sidebar state
      document.addEventListener('DOMContentLoaded', function () {
        // Set initial section states
        Object.keys(sectionStates).forEach(sectionId => {
          const subsection = document.getElementById(sectionId);
          const icon = document.getElementById(sectionId + '-icon');
          if (subsection && icon) {
            if (sectionStates[sectionId]) {
              subsection.style.display = 'block';
              icon.style.transform = 'rotate(0deg)';
            } else {
              subsection.style.display = 'none';
              icon.style.transform = 'rotate(-90deg)';
            }
          }
        });

        // Set initial menu item states
        Object.keys(menuItemStates).forEach(menuId => {
          const submenu = document.getElementById(menuId);
          const icon = document.getElementById(menuId + '-icon');
          if (submenu && icon) {
            if (menuItemStates[menuId]) {
              submenu.style.display = 'block';
              icon.style.transform = 'rotate(0deg)';
            } else {
              submenu.style.display = 'none';
              icon.style.transform = 'rotate(-90deg)';
            }
          }
        });
      });
    </script>

  </body>

</html>
