{% load static %}

<c-layouts.dashboard>
  <c-slot name="title">
    Booking {{ booking.booking_reference }}
  </c-slot>

  <c-slot name="main">
    <div class="container mx-auto px-4 py-8">
      <div class="flex justify-between items-center mb-8">
        <div>
          <h1 class="text-3xl font-bold text-gray-900">Booking {{ booking.booking_reference }}</h1>
          <p class="text-gray-600 mt-2">{{ booking.client.display_name }}</p>
        </div>
        <div class="flex space-x-3">
          {% if booking.status == 'pending' %}
          <form method="post" action="{% url 'bookings:booking_confirm' booking.pk %}" class="inline">
            {% csrf_token %}
            <button type="submit"
              class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
              Confirm Booking
            </button>
          </form>
          {% endif %}
          {% if booking.can_be_cancelled %}
          <button onclick="showCancelModal()"
            class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors">
            Cancel Booking
          </button>
          {% endif %}
          {% if booking.status == 'pending' or booking.status == 'confirmed' %}
          <a href="{% url 'bookings:booking_edit' booking.pk %}"
            class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
            Edit Booking
          </a>
          {% endif %}
          <a href="{% url 'bookings:participant_management' booking.pk %}"
            class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors">
            Manage Participants
          </a>
        </div>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Booking Information -->
        <div class="lg:col-span-2 space-y-6">
          <!-- Booking Details -->
          <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">Booking Details</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700">Reference</label>
                <p class="mt-1 text-sm text-gray-900">{{ booking.booking_reference }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700">Status</label>
                <span class="mt-1 inline-flex px-2 py-1 text-xs font-semibold rounded-full
                            {% if booking.status == 'pending' %}bg-yellow-100 text-yellow-800
                            {% elif booking.status == 'confirmed' %}bg-green-100 text-green-800
                            {% elif booking.status == 'completed' %}bg-blue-100 text-blue-800
                            {% elif booking.status == 'cancelled' %}bg-red-100 text-red-800
                            {% elif booking.status == 'no_show' %}bg-gray-100 text-gray-800
                            {% endif %}">
                  {{ booking.get_status_display }}
                </span>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700">Booking Date</label>
                <p class="mt-1 text-sm text-gray-900">{{ booking.booking_date|date:"M d, Y H:i" }}</p>
              </div>
              {% if booking.confirmation_date %}
              <div>
                <label class="block text-sm font-medium text-gray-700">Confirmation Date</label>
                <p class="mt-1 text-sm text-gray-900">{{ booking.confirmation_date|date:"M d, Y H:i" }}
                </p>
              </div>
              {% endif %}
              <div>
                <label class="block text-sm font-medium text-gray-700">Number of Participants</label>
                <p class="mt-1 text-sm text-gray-900">{{ booking.number_of_participants }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700">Created By</label>
                <p class="mt-1 text-sm text-gray-900">
                  {{ booking.created_by.get_full_name|default:booking.created_by.username }}</p>
              </div>
            </div>

            {% if booking.special_requests %}
            <div class="mt-4">
              <label class="block text-sm font-medium text-gray-700">Special Requests</label>
              <p class="mt-1 text-sm text-gray-900">{{ booking.special_requests }}</p>
            </div>
            {% endif %}

            {% if booking.internal_notes %}
            <div class="mt-4">
              <label class="block text-sm font-medium text-gray-700">Internal Notes</label>
              <p class="mt-1 text-sm text-gray-900">{{ booking.internal_notes }}</p>
            </div>
            {% endif %}
          </div>

          <!-- Event Information -->
          <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">Event Information</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700">Event Title</label>
                <p class="mt-1 text-sm text-gray-900">{{ booking.tour_event.event.title }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700">Event Type</label>
                <p class="mt-1 text-sm text-gray-900">{{ booking.tour_event.category.name }}
                </p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700">Start Time</label>
                <p class="mt-1 text-sm text-gray-900">
                  {{ booking.tour_event.event.start_time|date:"M d, Y H:i" }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700">End Time</label>
                <p class="mt-1 text-sm text-gray-900">
                  {{ booking.tour_event.event.end_time|date:"M d, Y H:i" }}</p>
              </div>
              {% if booking.tour_event.meeting_point %}
              <div class="md:col-span-2">
                <label class="block text-sm font-medium text-gray-700">Meeting Point</label>
                <p class="mt-1 text-sm text-gray-900">{{ booking.tour_event.meeting_point }}</p>
              </div>
              {% endif %}
            </div>

            {% if booking.tour_event.event.description %}
            <div class="mt-4">
              <label class="block text-sm font-medium text-gray-700">Description</label>
              <p class="mt-1 text-sm text-gray-900">{{ booking.tour_event.event.description }}</p>
            </div>
            {% endif %}
          </div>

          <!-- Client Information -->
          <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">Client Information</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700">Name</label>
                <p class="mt-1 text-sm text-gray-900">{{ booking.client.display_name }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700">Email</label>
                <p class="mt-1 text-sm text-gray-900">{{ booking.client.email }}</p>
              </div>
              {% if booking.client.phone %}
              <div>
                <label class="block text-sm font-medium text-gray-700">Phone</label>
                <p class="mt-1 text-sm text-gray-900">{{ booking.client.phone }}</p>
              </div>
              {% endif %}
              <div>
                <label class="block text-sm font-medium text-gray-700">Client Type</label>
                <p class="mt-1 text-sm text-gray-900">{{ booking.client.get_client_type_display }}</p>
              </div>
            </div>
          </div>

          <!-- Participants -->
          <div class="bg-white rounded-lg shadow p-6">
            <div class="flex justify-between items-center mb-4">
              <h2 class="text-xl font-semibold text-gray-900">Participants</h2>
              <a href="{% url 'bookings:participant_management' booking.pk %}"
                class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                Manage →
              </a>
            </div>
            {% if booking.participants.exists %}
            <div class="space-y-3">
              {% for participant in booking.participants.all %}
              <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                  <p class="font-medium text-gray-900">{{ participant.traveler.full_name }}</p>
                  {% if participant.is_primary_contact %}
                  <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                    Primary Contact
                  </span>
                  {% endif %}
                </div>
                <div class="text-right">
                  {% if participant.checked_in %}
                  <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                    Checked In
                  </span>
                  <p class="text-xs text-gray-500 mt-1">
                    {{ participant.check_in_time|date:"M d, H:i" }}
                  </p>
                  {% elif participant.no_show %}
                  <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                    No Show
                  </span>
                  {% else %}
                  <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                    Not Checked In
                  </span>
                  {% endif %}
                </div>
              </div>
              {% endfor %}
            </div>
            {% else %}
            <p class="text-gray-500 text-center py-4">No participants assigned yet</p>
            {% endif %}
          </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
          <!-- Payment Information -->
          <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Payment Information</h3>
            <div class="space-y-3">
              <div class="flex justify-between">
                <span class="text-sm text-gray-600">Total Amount:</span>
                <span class="text-sm font-medium">${{ booking.total_amount }}</span>
              </div>
              {% if booking.deposit_amount %}
              <div class="flex justify-between">
                <span class="text-sm text-gray-600">Deposit:</span>
                <span class="text-sm font-medium">${{ booking.deposit_amount }}</span>
              </div>
              {% endif %}
              <div class="flex justify-between">
                <span class="text-sm text-gray-600">Outstanding:</span>
                <span class="text-sm font-medium">${{ booking.outstanding_balance }}</span>
              </div>
              <div class="pt-2 border-t">
                <div class="flex justify-between items-center">
                  <span class="text-sm text-gray-600">Deposit Status:</span>
                  {% if booking.deposit_paid %}
                  <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                    Paid
                  </span>
                  {% else %}
                  <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                    Unpaid
                  </span>
                  {% endif %}
                </div>
                <div class="flex justify-between items-center mt-2">
                  <span class="text-sm text-gray-600">Full Payment:</span>
                  {% if booking.full_payment_received %}
                  <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                    Received
                  </span>
                  {% else %}
                  <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                    Pending
                  </span>
                  {% endif %}
                </div>
              </div>
            </div>
          </div>

          <!-- Quick Actions -->
          <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
            <div class="space-y-2">
              <a href="{% url 'bookings:booking_list' %}"
                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
                Back to Bookings
              </a>
              {% if booking.quote %}
              <a href="{% url 'quotes:quote-detail' booking.quote.pk %}"
                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
                View Quote
              </a>
              {% endif %}
              <a href="{% url 'clients:client-detail' booking.client.pk %}"
                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
                View Client
              </a>
              <a href="{% url 'tours:event_detail' booking.tour_event.pk %}"
                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
                View Event
              </a>
            </div>
          </div>

          {% if booking.status == 'cancelled' %}
          <!-- Cancellation Information -->
          <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Cancellation Details</h3>
            <div class="space-y-3">
              <div>
                <label class="block text-sm font-medium text-gray-700">Cancelled On</label>
                <p class="mt-1 text-sm text-gray-900">{{ booking.cancellation_date|date:"M d, Y H:i" }}
                </p>
              </div>
              {% if booking.cancellation_reason %}
              <div>
                <label class="block text-sm font-medium text-gray-700">Reason</label>
                <p class="mt-1 text-sm text-gray-900">{{ booking.cancellation_reason }}</p>
              </div>
              {% endif %}
              {% if booking.refund_amount %}
              <div>
                <label class="block text-sm font-medium text-gray-700">Refund Amount</label>
                <p class="mt-1 text-sm text-gray-900">${{ booking.refund_amount }}</p>
              </div>
              {% endif %}
            </div>
          </div>
          {% endif %}
        </div>
      </div>
    </div>

    <!-- Cancel Booking Modal -->
    <div id="cancelModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
      <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Cancel Booking</h3>
          <form method="post" action="{% url 'bookings:booking_cancel' booking.pk %}">
            {% csrf_token %}
            <div class="mb-4">
              <label for="reason" class="block text-sm font-medium text-gray-700 mb-2">Reason for
                cancellation</label>
              <textarea id="reason" name="reason" rows="3"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"></textarea>
            </div>
            <div class="mb-4">
              <label for="refund_amount" class="block text-sm font-medium text-gray-700 mb-2">Refund
                Amount
                (optional)</label>
              <input type="number" id="refund_amount" name="refund_amount" step="0.01"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
            </div>
            <div class="flex justify-end space-x-3">
              <button type="button" onclick="hideCancelModal()"
                class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300">
                Cancel
              </button>
              <button type="submit"
                class="px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-lg hover:bg-red-700">
                Cancel Booking
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <script>
      function showCancelModal() {
        document.getElementById('cancelModal').classList.remove('hidden');
      }

      function hideCancelModal() {
        document.getElementById('cancelModal').classList.add('hidden');
      }
    </script>
  </c-slot>
</c-layouts.dashboard>
