{% load i18n %}

<c-layout title="{{ business.name }}">
    <div class="container mx-auto px-4 py-8">
        <!-- Business Header -->
        <div class="bg-white shadow rounded-lg mb-6">
            <div class="px-6 py-6">
                <div class="flex items-start justify-between">
                    <div class="flex items-start space-x-6">
                        {% if business.logo %}
                        <img src="{{ business.get_logo_url }}" alt="{{ business.name }}" class="h-20 w-auto">
                        {% endif %}

                        <div>
                            <h1 class="text-3xl font-bold text-gray-900 mb-2">{{ business.name }}</h1>

                            {% if business.description %}
                            <p class="text-gray-600 mb-4">{{ business.description }}</p>
                            {% endif %}

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                {% if business.email %}
                                <div class="flex items-center">
                                    <svg class="w-4 h-4 mr-2 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                        <path
                                            d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z">
                                        </path>
                                        <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
                                    </svg>
                                    <a href="mailto:{{ business.email }}" class="text-blue-600 hover:text-blue-800">
                                        {{ business.email }}
                                    </a>
                                </div>
                                {% endif %}

                                {% if business.phone %}
                                <div class="flex items-center">
                                    <svg class="w-4 h-4 mr-2 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                        <path
                                            d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z">
                                        </path>
                                    </svg>
                                    <a href="tel:{{ business.phone }}" class="text-blue-600 hover:text-blue-800">
                                        {{ business.phone }}
                                    </a>
                                </div>
                                {% endif %}

                                {% if business.website %}
                                <div class="flex items-center">
                                    <svg class="w-4 h-4 mr-2 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd"
                                            d="M4.083 9h1.946c.089-1.546.383-2.97.837-4.118A6.004 6.004 0 004.083 9zM10 2a8 8 0 100 16 8 8 0 000-16zm0 2c-.076 0-.232.032-.465.262-.238.234-.497.623-.737 1.182-.389.907-.673 2.142-.766 3.556h3.936c-.093-1.414-.377-2.649-.766-3.556-.24-.559-.499-.948-.737-1.182C10.232 4.032 10.076 4 10 4zm3.971 5c-.089-1.546-.383-2.97-.837-4.118A6.004 6.004 0 0115.917 9h-1.946zm-2.003 2H8.032c.093 1.414.377 2.649.766 3.556.24.559.499.948.737 ************.389.262.465.262.076 0 .232-.032.465-.262.238-.234.497-.623.737-1.182.389-.907.673-2.142.766-3.556zm1.166 4.118c.454-1.147.748-2.572.837-4.118h1.946a6.004 6.004 0 01-2.783 4.118zm-6.268 0C6.412 13.97 6.118 12.546 6.03 11H4.083a6.004 6.004 0 002.783 4.118z"
                                            clip-rule="evenodd"></path>
                                    </svg>
                                    <a href="{{ business.website }}" target="_blank"
                                        class="text-blue-600 hover:text-blue-800">
                                        {{ business.website|slice:"8:" }}
                                    </a>
                                </div>
                                {% endif %}

                                {% if business.full_address %}
                                <div class="flex items-start md:col-span-2">
                                    <svg class="w-4 h-4 mr-2 mt-0.5 text-gray-400" fill="currentColor"
                                        viewBox="0 0 20 20">
                                        <path fill-rule="evenodd"
                                            d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
                                            clip-rule="evenodd"></path>
                                    </svg>
                                    <span class="text-gray-600">{{ business.full_address }}</span>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="flex space-x-2">
                        <a href="{% url 'businesses:edit' business.pk %}" class="btn btn-primary">
                            {% trans "Edit Business" %}
                        </a>
                        <a href="{% url 'businesses:dashboard' business.pk %}" class="btn btn-secondary">
                            {% trans "Dashboard" %}
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Business Theme Preview -->
        {% if business.primary_color or business.secondary_color %}
        <div class="bg-white shadow rounded-lg mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-medium text-gray-900">{% trans "Brand Colors" %}</h2>
            </div>
            <div class="px-6 py-4">
                <div class="flex space-x-4">
                    <div class="text-center">
                        <div class="w-16 h-16 rounded-lg border border-gray-200 mb-2"
                            style="background-color: {{ business.primary_color }};"></div>
                        <span class="text-sm text-gray-600">{% trans "Primary" %}</span>
                        <div class="text-xs text-gray-500">{{ business.primary_color }}</div>
                    </div>
                    <div class="text-center">
                        <div class="w-16 h-16 rounded-lg border border-gray-200 mb-2"
                            style="background-color: {{ business.secondary_color }};"></div>
                        <span class="text-sm text-gray-600">{% trans "Secondary" %}</span>
                        <div class="text-xs text-gray-500">{{ business.secondary_color }}</div>
                    </div>
                    <div class="text-center">
                        <div class="w-16 h-16 rounded-lg border border-gray-200 mb-2"
                            style="background-color: {{ business.accent_color }};"></div>
                        <span class="text-sm text-gray-600">{% trans "Accent" %}</span>
                        <div class="text-xs text-gray-500">{{ business.accent_color }}</div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Quick Actions -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <a href="{% url 'clients:client-list' %}?business={{ business.pk }}"
                class="bg-white p-6 rounded-lg shadow hover:shadow-lg transition-shadow">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-8 w-8 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-medium text-gray-900">{% trans "Clients" %}</h3>
                        <p class="text-sm text-gray-500">{% trans "Manage your clients" %}</p>
                    </div>
                </div>
            </a>

            <a href="{% url 'quotes:quote-list' %}?business={{ business.pk }}"
                class="bg-white p-6 rounded-lg shadow hover:shadow-lg transition-shadow">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-medium text-gray-900">{% trans "Quotes" %}</h3>
                        <p class="text-sm text-gray-500">{% trans "Create and manage quotes" %}</p>
                    </div>
                </div>
            </a>

            <a href="{% url 'invoices:list' %}?business={{ business.pk }}"
                class="bg-white p-6 rounded-lg shadow hover:shadow-lg transition-shadow">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-8 w-8 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-medium text-gray-900">{% trans "Invoices" %}</h3>
                        <p class="text-sm text-gray-500">{% trans "Manage invoices and payments" %}</p>
                    </div>
                </div>
            </a>

            <a href="{% url 'bookings:list' %}?business={{ business.pk }}"
                class="bg-white p-6 rounded-lg shadow hover:shadow-lg transition-shadow">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-8 w-8 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-medium text-gray-900">{% trans "Bookings" %}</h3>
                        <p class="text-sm text-gray-500">{% trans "Schedule and manage tours" %}</p>
                    </div>
                </div>
            </a>
        </div>

        <!-- Business Information -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-medium text-gray-900">{% trans "Business Information" %}</h2>
            </div>
            <div class="px-6 py-4">
                <dl class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">{% trans "Created" %}</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ business.created_at|date:"F d, Y" }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">{% trans "Last Updated" %}</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ business.updated_at|date:"F d, Y" }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">{% trans "Status" %}</dt>
                        <dd class="mt-1">
                            {% if business.is_active %}
                            <span
                                class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                {% trans "Active" %}
                            </span>
                            {% else %}
                            <span
                                class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                {% trans "Inactive" %}
                            </span>
                            {% endif %}
                        </dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">{% trans "Created By" %}</dt>
                        <dd class="mt-1 text-sm text-gray-900">
                            {{ business.created_by.get_full_name|default:business.created_by.username }}</dd>
                    </div>
                </dl>
            </div>
        </div>
    </div>
</c-layout>
