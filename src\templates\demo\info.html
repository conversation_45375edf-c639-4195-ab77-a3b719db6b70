<c-layout title="Demo Information - TourFlow" show_nav="True" show_footer="True">
    <!-- Debug: Demo info template is loading -->
    <div class="max-w-4xl mx-auto space-y-8 p-6">
        <!-- Header -->
        <div class="text-center">
            <h1 class="text-4xl font-bold text-gray-900 mb-4">
                <i class="fas fa-rocket text-blue-500 mr-3"></i>
                Try TourFlow Demo
            </h1>
            <p class="text-xl text-gray-600 max-w-2xl mx-auto">
                Explore all features of our tour business management platform with real sample data.
                No registration required - start your {{ demo_duration_hours|default:"2" }}-hour demo session instantly.
            </p>
        </div>

        <!-- Demo Features -->
        <div class="bg-white rounded-lg shadow-lg p-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">What You'll Experience</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                {% for feature in features %}
                <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0">
                        <i class="fas fa-check-circle text-green-500 text-lg mt-1"></i>
                    </div>
                    <div>
                        <p class="text-gray-700">{{ feature }}</p>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>

        <!-- How It Works -->
        <div class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">How Demo Mode Works</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="text-center">
                    <div class="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-2xl font-bold text-white">1</span>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Instant Setup</h3>
                    <p class="text-gray-600">
                        Click "Start Demo" to instantly create a sample tour business with realistic data including
                        clients,
                        quotes, and bookings.
                    </p>
                </div>

                <div class="text-center">
                    <div class="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-2xl font-bold text-white">2</span>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Full Access</h3>
                    <p class="text-gray-600">
                        Explore all features with complete functionality. Create new quotes, manage bookings, generate
                        invoices - everything works!
                    </p>
                </div>

                <div class="text-center">
                    <div class="w-16 h-16 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-2xl font-bold text-white">3</span>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Auto Cleanup</h3>
                    <p class="text-gray-600">
                        After {{ demo_duration_hours }} hours, your demo session automatically expires and all demo data
                        is
                        securely deleted.
                    </p>
                </div>
            </div>
        </div>

        <!-- Demo Benefits -->
        <div class="bg-white rounded-lg shadow-lg p-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">Why Try Our Demo?</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-3">
                        <i class="fas fa-eye text-blue-500 mr-2"></i>
                        See Real Functionality
                    </h3>
                    <p class="text-gray-600 mb-4">
                        Unlike static screenshots or videos, our demo gives you hands-on experience with actual working
                        features and realistic business data.
                    </p>

                    <h3 class="text-lg font-semibold text-gray-900 mb-3">
                        <i class="fas fa-shield-alt text-green-500 mr-2"></i>
                        No Risk, No Commitment
                    </h3>
                    <p class="text-gray-600">
                        No email required, no credit card needed. Just click and start exploring. Your demo session is
                        completely isolated and secure.
                    </p>
                </div>

                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-3">
                        <i class="fas fa-clock text-purple-500 mr-2"></i>
                        Generous Time Limit
                    </h3>
                    <p class="text-gray-600 mb-4">
                        {{ demo_duration_hours }} hours is plenty of time to thoroughly explore all features, create
                        test
                        data, and understand how TourFlow can benefit your business.
                    </p>

                    <h3 class="text-lg font-semibold text-gray-900 mb-3">
                        <i class="fas fa-database text-yellow-500 mr-2"></i>
                        Rich Sample Data
                    </h3>
                    <p class="text-gray-600">
                        Start with pre-populated clients, quotes, bookings, and invoices so you can immediately see how
                        everything works together.
                    </p>
                </div>
            </div>
        </div>

        <!-- Call to Action -->
        <div class="bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg p-8 text-center">
            <h2 class="text-3xl font-bold mb-4">Ready to Explore?</h2>
            <p class="text-xl text-blue-100 mb-6">
                Start your free demo session now and discover how TourFlow can transform your tour business operations.
            </p>

            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <button onclick="startDemo()"
                    class="bg-white text-blue-600 px-8 py-4 rounded-lg font-semibold hover:bg-blue-50 transition-colors">
                    <i class="fas fa-rocket mr-2"></i>
                    Start {{ demo_duration_hours }}-Hour Demo
                </button>

                <a href="{% url 'accounts:register' %}"
                    class="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors">
                    <i class="fas fa-user-plus mr-2"></i>
                    Create Free Account
                </a>
            </div>

            <p class="text-sm text-blue-200 mt-4">
                <i class="fas fa-info-circle mr-1"></i>
                Demo sessions automatically expire after {{ demo_duration_hours }} hours for security
            </p>
        </div>

        <!-- FAQ -->
        <div class="bg-white rounded-lg shadow-lg p-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">Frequently Asked Questions</h2>
            <div class="space-y-6">
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Is the demo fully functional?</h3>
                    <p class="text-gray-600">
                        Yes! The demo includes all features of TourFlow with real functionality. You can create, edit,
                        and
                        delete data just like in the full version.
                    </p>
                </div>

                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">What happens to my demo data?</h3>
                    <p class="text-gray-600">
                        All demo data is automatically deleted when your session expires after {{ demo_duration_hours }}
                        hours. This ensures your privacy and keeps our system clean.
                    </p>
                </div>

                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Can I extend my demo session?</h3>
                    <p class="text-gray-600">
                        Demo sessions have a fixed {{ demo_duration_hours }}-hour limit for security reasons. If you
                        need
                        more time, you can create a free account to continue exploring.
                    </p>
                </div>

                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Do I need to provide any personal information?
                    </h3>
                    <p class="text-gray-600">
                        No! The demo requires no registration, email, or personal information. Just click "Start Demo"
                        and
                        begin exploring immediately.
                    </p>
                </div>
            </div>
        </div>
    </div>

    <script>
        function startDemo() {
            // Show loading state
            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Creating Demo...';
            button.disabled = true;

            fetch('{% url "start_demo" %}', {
                method: 'POST',
                headers: {
                    'X-CSRFToken': '{{ csrf_token }}',
                    'Content-Type': 'application/json'
                }
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Show success message briefly then redirect
                        button.innerHTML = '<i class="fas fa-check mr-2"></i>Demo Created!';
                        button.classList.remove('text-blue-600', 'hover:bg-blue-50');
                        button.classList.add('text-green-600', 'bg-green-50');

                        setTimeout(() => {
                            window.location.href = data.redirect_url;
                        }, 1000);
                    } else {
                        alert('Error starting demo: ' + data.error);
                        button.innerHTML = originalText;
                        button.disabled = false;
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Failed to start demo session. Please try again.');
                    button.innerHTML = originalText;
                    button.disabled = false;
                });
        }
    </script>
</c-layout>
