{% load i18n %}

<c-layouts.base>
  <c-slot name="title">
    {% trans "Bulk Upload Users" %} - Tour Business Management
  </c-slot>

  <c-slot name="content">
    <div class="container mx-auto px-4 py-8">
      <div class="max-w-4xl mx-auto">
        <!-- Header -->
        <div class="flex justify-between items-center mb-6">
          <div>
            <h1 class="text-3xl font-bold text-gray-900">{% trans "Bulk Upload Users" %}</h1>
            <p class="text-gray-600 mt-1">{% trans "Upload multiple users at once using an Excel file" %}
            </p>
          </div>
          <a href="{% url 'accounts:user_management' %}"
            class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium">
            {% trans "Back to Users" %}
          </a>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <!-- Upload Form -->
          <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">{% trans "Upload Excel File" %}</h3>

            <form method="post" enctype="multipart/form-data" class="space-y-6">
              {% csrf_token %}

              {% if form.errors %}
              <div class="bg-red-50 border border-red-200 rounded-md p-4">
                <div class="flex">
                  <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fill-rule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                        clip-rule="evenodd" />
                    </svg>
                  </div>
                  <div class="ml-3">
                    <h3 class="text-sm font-medium text-red-800">
                      {% trans "There were errors with your submission" %}
                    </h3>
                    <div class="mt-2 text-sm text-red-700">
                      <ul class="list-disc pl-5 space-y-1">
                        {% for field, errors in form.errors.items %}
                        {% for error in errors %}
                        <li>{{ field|capfirst }}: {{ error }}</li>
                        {% endfor %}
                        {% endfor %}
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
              {% endif %}

              <div>
                <label for="{{ form.excel_file.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                  {% trans "Excel File" %} *
                </label>
                {{ form.excel_file }}
                <p class="text-xs text-gray-500 mt-1">{{ form.excel_file.help_text }}</p>
              </div>

              <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
                <div class="flex">
                  <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd"
                        d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                        clip-rule="evenodd"></path>
                    </svg>
                  </div>
                  <div class="ml-3">
                    <h4 class="text-sm font-medium text-blue-800">
                      {% trans "Before uploading" %}
                    </h4>
                    <div class="mt-2 text-sm text-blue-700">
                      <ul class="list-disc pl-5 space-y-1">
                        <li>{% trans "Download the template file to ensure correct format" %}
                        </li>
                        <li>{% trans "Fill in all required columns: username, email, role" %}
                        </li>
                        <li>{% trans "Make sure all usernames and emails are unique" %}</li>
                        <li>{% trans "Role must be either 'agent' or 'business_owner'" %}</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>

              <div class="flex justify-end space-x-3">
                <a href="{% url 'accounts:user_management' %}"
                  class="bg-gray-300 hover:bg-gray-400 text-gray-800 px-6 py-2 rounded-md text-sm font-medium">
                  {% trans "Cancel" %}
                </a>
                <button type="submit"
                  class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-md text-sm font-medium">
                  {% trans "Upload Users" %}
                </button>
              </div>
            </form>
          </div>

          <!-- Instructions -->
          <div class="space-y-6">
            <!-- Download Template -->
            <div class="bg-white shadow rounded-lg p-6">
              <h3 class="text-lg font-medium text-gray-900 mb-4">{% trans "Download Template" %}</h3>

              <p class="text-sm text-gray-600 mb-4">
                {% trans "Download the Excel template to ensure your file has the correct format and column headers." %}
              </p>

              <a href="{% url 'accounts:download_template' %}"
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                <svg class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                {% trans "Download Template" %}
              </a>
            </div>

            <!-- Required Columns -->
            <div class="bg-white shadow rounded-lg p-6">
              <h3 class="text-lg font-medium text-gray-900 mb-4">{% trans "Required Columns" %}</h3>

              <div class="space-y-3">
                <div class="flex items-start">
                  <div class="flex-shrink-0">
                    <span
                      class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      username
                    </span>
                  </div>
                  <div class="ml-3">
                    <p class="text-sm text-gray-600">
                      {% trans "Unique username for login (e.g., john.doe)" %}</p>
                  </div>
                </div>

                <div class="flex items-start">
                  <div class="flex-shrink-0">
                    <span
                      class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      email
                    </span>
                  </div>
                  <div class="ml-3">
                    <p class="text-sm text-gray-600">{% trans "Valid email address for the user" %}
                    </p>
                  </div>
                </div>

                <div class="flex items-start">
                  <div class="flex-shrink-0">
                    <span
                      class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      role
                    </span>
                  </div>
                  <div class="ml-3">
                    <p class="text-sm text-gray-600">
                      {% trans "Either 'agent' or 'business_owner'" %}</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Process Information -->
            <div class="bg-white shadow rounded-lg p-6">
              <h3 class="text-lg font-medium text-gray-900 mb-4">{% trans "What Happens After Upload" %}
              </h3>

              <div class="space-y-3">
                <div class="flex items-start">
                  <div class="flex-shrink-0">
                    <div class="flex items-center justify-center w-6 h-6 bg-green-100 rounded-full">
                      <span class="text-xs font-medium text-green-600">1</span>
                    </div>
                  </div>
                  <div class="ml-3">
                    <p class="text-sm text-gray-600">
                      {% trans "File is validated for correct format and data" %}</p>
                  </div>
                </div>

                <div class="flex items-start">
                  <div class="flex-shrink-0">
                    <div class="flex items-center justify-center w-6 h-6 bg-green-100 rounded-full">
                      <span class="text-xs font-medium text-green-600">2</span>
                    </div>
                  </div>
                  <div class="ml-3">
                    <p class="text-sm text-gray-600">
                      {% trans "User accounts are created for valid entries" %}</p>
                  </div>
                </div>

                <div class="flex items-start">
                  <div class="flex-shrink-0">
                    <div class="flex items-center justify-center w-6 h-6 bg-green-100 rounded-full">
                      <span class="text-xs font-medium text-green-600">3</span>
                    </div>
                  </div>
                  <div class="ml-3">
                    <p class="text-sm text-gray-600">
                      {% trans "Invitation emails are sent to all new users" %}</p>
                  </div>
                </div>

                <div class="flex items-start">
                  <div class="flex-shrink-0">
                    <div class="flex items-center justify-center w-6 h-6 bg-green-100 rounded-full">
                      <span class="text-xs font-medium text-green-600">4</span>
                    </div>
                  </div>
                  <div class="ml-3">
                    <p class="text-sm text-gray-600">
                      {% trans "Users can set their passwords and complete profiles" %}</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- File Requirements -->
            <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4">
              <div class="flex">
                <div class="flex-shrink-0">
                  <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd"
                      d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                      clip-rule="evenodd" />
                  </svg>
                </div>
                <div class="ml-3">
                  <h4 class="text-sm font-medium text-yellow-800">
                    {% trans "File Requirements" %}
                  </h4>
                  <div class="mt-2 text-sm text-yellow-700">
                    <ul class="list-disc pl-5 space-y-1">
                      <li>{% trans "Excel format (.xlsx or .xls)" %}</li>
                      <li>{% trans "Maximum file size: 5MB" %}</li>
                      <li>{% trans "First row must contain column headers" %}</li>
                      <li>{% trans "No empty rows between data" %}</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </c-slot name="content">
</c-layouts.base>
