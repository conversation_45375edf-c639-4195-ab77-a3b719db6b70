<c-vars
    client="None"
    show_actions="True"
    show_details="False"
    class=""
/>

<div class="client-card {{ class }}">
    <div class="client-header">
        <div class="client-avatar">
            {% if client.avatar %}
                <img src="{{ client.avatar.url }}"
                     alt="{{ client.display_name }}"
                     class="w-12 h-12 rounded-full object-cover">
            {% else %}
                <div class="w-12 h-12 rounded-full bg-gray-300 flex items-center justify-center">
                    <span class="text-gray-600 font-medium text-lg">
                        {{ client.first_name|first }}{{ client.last_name|first }}
                    </span>
                </div>
            {% endif %}
        </div>

        <div class="client-info flex-1">
            <h3 class="text-lg font-semibold text-gray-900">
                {{ client.display_name }}
            </h3>

            <div class="client-contact text-sm text-gray-600 space-y-1">
                {% if client.email %}
                    <div class="flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
                            <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
                        </svg>
                        <a href="mailto:{{ client.email }}" class="hover:text-blue-600">
                            {{ client.email }}
                        </a>
                    </div>
                {% endif %}

                {% if client.phone %}
                    <div class="flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"></path>
                        </svg>
                        <a href="tel:{{ client.phone }}" class="hover:text-blue-600">
                            {{ client.phone }}
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>

        <div class="client-status">
            {% if client.is_active %}
                <span class="status-badge bg-green-100 text-green-800">Active</span>
            {% else %}
                <span class="status-badge bg-gray-100 text-gray-800">Inactive</span>
            {% endif %}
        </div>
    </div>

    {% if show_details %}
        <div class="client-details mt-4 pt-4 border-t border-gray-200">
            <div class="grid grid-cols-2 gap-4 text-sm">
                {% if client.nationality %}
                    <div>
                        <span class="text-gray-600">Nationality:</span>
                        <span class="font-medium">{{ client.nationality }}</span>
                    </div>
                {% endif %}

                {% if client.date_of_birth %}
                    <div>
                        <span class="text-gray-600">Age:</span>
                        <span class="font-medium">{{ client.age }} years</span>
                    </div>
                {% endif %}

                {% if client.emergency_contact_name %}
                    <div>
                        <span class="text-gray-600">Emergency Contact:</span>
                        <span class="font-medium">{{ client.emergency_contact_name }}</span>
                    </div>
                {% endif %}

                {% if client.travel_preferences %}
                    <div class="col-span-2">
                        <span class="text-gray-600">Preferences:</span>
                        <span class="font-medium">{{ client.travel_preferences|truncatewords:10 }}</span>
                    </div>
                {% endif %}
            </div>
        </div>
    {% endif %}

    {% if show_actions %}
        <div class="client-actions mt-4 flex space-x-2">
            <a href=""
               class="btn btn-sm btn-primary">
                View Profile
            </a>
            <a href=""
               class="btn btn-sm btn-secondary">
                Create Quote
            </a>
            <a href=""
               class="btn btn-sm btn-outline-primary">
                Edit
            </a>
        </div>
    {% endif %}
</div>

<style>
    .client-card {
        @apply border border-gray-200 rounded-lg p-4 bg-white shadow-sm hover:shadow-md transition-shadow;
    }

    .client-header {
        @apply flex items-start space-x-4;
    }

    .status-badge {
        @apply px-2 py-1 rounded-full text-xs font-medium;
    }

    .client-contact a {
        @apply transition-colors duration-200;
    }
</style>
