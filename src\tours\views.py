from datetime import timed<PERSON>ta

from django.contrib import messages
from django.contrib.auth.mixins import LoginRequiredMixin
from django.db.models import Q
from django.shortcuts import get_object_or_404, redirect, render
from django.urls import reverse_lazy
from django.utils import timezone
from django.views import generic

from core.business_mixins import BusinessAccessMixin
from tours.forms import (
    TourCategoryForm,
    TourEventForm,
    TourSearchForm,
    TourTemplateForm,
)
from tours.models import (
    GuideAssignment,
    TourCategory,
    TourEvent,
    TourInstance,
    TourTemplate,
)

# Additional imports for search functionality


# tour category views
class CategoryCreateView(generic.CreateView):
    model = TourCategory
    template_name = 'tours/category_form.html'
    form_class = TourCategoryForm


class CategoryUpdateView(generic.UpdateView):
    model = TourCategory
    template_name = 'tours/category_form.html'
    form_class = TourCategoryForm


category_update = CategoryUpdateView.as_view()


category_create = CategoryCreateView.as_view()


def category_list(request):
    categories = TourCategory.objects.all()
    return render(
        request,
        'tours/category_list.html',
        {'categories': categories},
    )


def category_detail(request, pk):
    category = TourCategory.objects.get(pk=pk)
    return render(
        request,
        'tours/category_detail.html',
        {'category': category},
    )


# Tour Templates
def tour_template_list(request):
    templates = TourTemplate.objects.all()
    return render(
        request,
        'tours/tour_templates/tour_template_list.html',
        {'templates': templates},
    )


def tour_template_create(request):
    form = TourTemplateForm()

    if request.method == 'POST':
        form = TourTemplateForm(request.POST)
        if form.is_valid():
            form.instance.created_by = request.user
            form.save()
            messages.success(request, 'Tour Template create successfully.')
            return redirect('tours:tour-template-list')

    return render(
        request,
        'tours/tour_templates/tour_template_form.html',
        {'form': form},
    )


def tour_template_detail(request, pk):
    template = get_object_or_404(TourTemplate, pk=pk)
    return render(
        request,
        'tours/tour_templates/tour_template_detail.html',
        {'template': template},
    )


def tour_template_update(request, pk):
    template = get_object_or_404(TourTemplate, pk=pk)
    form = TourTemplateForm(instance=template)

    if request.method == 'POST':
        # update logic
        form = TourTemplateForm(instance=template, data=request.POST)
        if form.is_valid:
            form.instance.created_by = request.user
            form.save()
            messages.success(request, 'Tour Template updated successfully.')
            return redirect('tours:tour-template-list')

    return render(
        request,
        'tours/tour_templates/tour_template_form.html',
        {'form': form},
    )


# tours views
def tour_list(request):
    tours = TourEvent.objects.all()
    return render(
        request,
        'tours/tour_list.html',
        {'tours': tours},
    )


def tour_detail(request, pk):
    tour = TourEvent.objects.get(pk=pk)
    return render(
        request,
        'tours/tour_detail.html',
        {'tour': tour},
    )


def tour_create(request):
    tours = TourInstance.objects.filter(tour_template__is_active=True)
    template_form = TourTemplateForm()
    return render(
        request,
        'tours/tour_creation/step1_select_template.html',
        {'tours': tours, 'form': template_form},
    )


def set_tour_date(request):
    return render(
        request,
        'tours/tour_creation/step2_set_dates.html',
    )


def capacity_step(request):
    return render(
        request,
        'tours/tour_creation/step3_capacity.html',
    )


def confirmation_step(request):
    return render(
        request,
        'tours/tour_creation/step4_confirm.html',
    )


class TourEventListView(LoginRequiredMixin, generic.ListView):
    """List view for tour events."""

    model = TourEvent
    template_name = 'bookings/tour_event_list.html'
    context_object_name = 'events'
    paginate_by = 20

    def get_queryset(self):
        """Filter events based on user's business access."""
        queryset = TourEvent.objects.all()

        # Filter by user's accessible businesses
        # if not self.request.user.is_superuser:
        #     accessible_businesses = Business.objects.filter(
        #         Q(created_by=self.request.user) | Q(users=self.request.user)
        #     ).distinct()
        #     queryset = queryset.filter(business__in=accessible_businesses)

        return queryset.order_by('created_at')


class TourEventDetailView(LoginRequiredMixin, generic.DetailView):
    """Detail view for a tour event."""

    model = TourEvent
    template_name = 'bookings/tour_event_detail.html'
    context_object_name = 'event'

    def get_object(self):
        """Get event and check permissions."""
        event = get_object_or_404(TourEvent, pk=self.kwargs['pk'])

        # Check if user has access to this event's business
        if not self.request.user.is_superuser:
            if (
                event.business.created_by != self.request.user
                and self.request.user not in event.business.users.all()
            ):
                raise PermissionError("You don't have access to this event")

        return event


class TourEventCreateView(BusinessAccessMixin, generic.CreateView):
    """Create view for tour events."""

    model = TourEvent
    form_class = TourEventForm
    template_name = 'bookings/tour_event_form.html'

    def get_form_kwargs(self):
        """Pass user to form."""
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        return kwargs

    def form_valid(self, form):
        """Handle successful form submission."""
        form.instance.created_by = self.request.user

        # Set the business to the user's business if not already set
        if not form.instance.business:
            form.instance.business = self.get_user_business()

        response = super().form_valid(form)

        # Get the title from the form data since the event should now be created
        event_title = form.cleaned_data.get('title', 'Event')
        messages.success(
            self.request,
            f'Tour event "{event_title}" created successfully!',
        )
        return response

    def get_success_url(self):
        return reverse_lazy('tours:event_detail', kwargs={'pk': self.object.pk})


# Tour Search and Discovery Views


class TourSearchView(generic.TemplateView):
    """
    Main tour search and discovery page with Datastar-powered filtering.
    """

    template_name = 'tours/search.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Get all active tour templates for initial display
        tours = TourTemplate.objects.filter(is_active=True)

        # Apply business filtering if user is logged in
        if self.request.user.is_authenticated and not self.request.user.is_superuser:
            from businesses.models import Business

            accessible_businesses = Business.objects.filter(
                Q(created_by=self.request.user) | Q(users=self.request.user)
            ).distinct()
            tours = tours.filter(business__in=accessible_businesses)

        # Apply search filters
        tours = self.apply_search_filters(tours)

        # Add availability information
        tours = self.add_availability_info(tours)

        context.update(
            {
                'tours': tours,
                'search_form': TourSearchForm(),
                'total_tours': tours.count(),
            }
        )

        return context

    def apply_search_filters(self, tours):
        """Apply search filters based on GET parameters."""
        # Destination filter
        destination = self.request.GET.get('destination')
        if destination:
            tours = tours.filter(destination__icontains=destination)

        # Activity filter
        activity = self.request.GET.get('activity')
        if activity:
            tours = tours.filter(activity_type=activity)

        # Duration filter
        duration = self.request.GET.get('duration')
        if duration:
            if duration == '1-3':
                tours = tours.filter(duration_days__gte=1, duration_days__lte=3)
            elif duration == '4-7':
                tours = tours.filter(duration_days__gte=4, duration_days__lte=7)
            elif duration == '8+':
                tours = tours.filter(duration_days__gte=8)

        # General search query
        q = self.request.GET.get('q')
        if q:
            tours = tours.filter(
                Q(name__icontains=q)
                | Q(description__icontains=q)
                | Q(destination__icontains=q)
            )

        return tours

    def add_availability_info(self, tours):
        """Add availability information to tour queryset."""
        # Get upcoming tour instances for each template
        today = timezone.now().date()
        future_date = today + timedelta(days=365)  # Look ahead 1 year

        for tour in tours:
            # Get upcoming instances
            upcoming_instances = TourInstance.objects.filter(
                tour_template=tour,
                date__gte=today,
                date__lte=future_date,
                is_operational=True,
            ).order_by('date')[:5]

            tour.upcoming_instances = upcoming_instances
            tour.next_available_date = (
                upcoming_instances.first().date if upcoming_instances else None
            )

            # Calculate total available spots across upcoming instances
            total_spots = 0
            min_spots = float('inf')
            for instance in upcoming_instances:
                available = instance.available_spots_with_reservations()
                total_spots += available
                if available < min_spots:
                    min_spots = available

            tour.total_available_spots = total_spots
            tour.min_available_spots = min_spots if min_spots != float('inf') else 0
            tour.has_urgent_availability = min_spots < 3 and min_spots > 0

        return tours


def tour_search_api(request):
    """
    API endpoint for real-time tour search with Datastar SSE.
    Returns filtered tour results as HTML fragments.
    """
    from django.db.models import Q
    from django.http import HttpResponse
    from django.template.loader import render_to_string

    # Get search parameters
    search = request.GET.get('search', '').strip()
    destination = request.GET.get('destination', '').strip()
    activity_type = request.GET.get('activity_type', '')
    difficulty_level = request.GET.get('difficulty_level', '')
    min_duration = request.GET.get('min_duration')
    max_duration = request.GET.get('max_duration')
    min_price = request.GET.get('min_price')
    max_price = request.GET.get('max_price')
    is_guided = request.GET.get('is_guided') == 'on'
    meals_included = request.GET.get('meals_included') == 'on'
    accommodation_included = request.GET.get('accommodation_included') == 'on'
    equipment_provided = request.GET.get('equipment_provided') == 'on'

    # Start with all active tours
    tours = TourTemplate.objects.filter(is_active=True)

    # Apply business filtering if user is logged in
    if request.user.is_authenticated and not request.user.is_superuser:
        from businesses.models import Business

        accessible_businesses = Business.objects.filter(
            Q(created_by=request.user) | Q(users=request.user)
        ).distinct()
        tours = tours.filter(business__in=accessible_businesses)

    # Apply search filters
    if search:
        tours = tours.filter(
            Q(name__icontains=search)
            | Q(description__icontains=search)
            | Q(destination__icontains=search)
        )

    if destination:
        tours = tours.filter(destination__icontains=destination)

    if activity_type:
        tours = tours.filter(activity_type=activity_type)

    if difficulty_level:
        tours = tours.filter(difficulty_level=difficulty_level)

    if min_duration:
        try:
            tours = tours.filter(duration_days__gte=int(min_duration))
        except ValueError:
            pass

    if max_duration:
        try:
            tours = tours.filter(duration_days__lte=int(max_duration))
        except ValueError:
            pass

    if min_price:
        try:
            tours = tours.filter(base_price__gte=float(min_price))
        except (ValueError, TypeError):
            pass

    if max_price:
        try:
            tours = tours.filter(base_price__lte=float(max_price))
        except (ValueError, TypeError):
            pass

    # Apply feature filters
    if is_guided:
        tours = tours.filter(is_guided=True)

    if meals_included:
        tours = tours.filter(meals_included=True)

    if accommodation_included:
        tours = tours.filter(accommodation_included=True)

    if equipment_provided:
        tours = tours.filter(equipment_provided=True)

    # Add availability information
    tours = add_availability_info_to_queryset(tours)

    # Limit results
    tours = tours[:20]

    # Render the tour cards as HTML fragment
    html = render_to_string(
        'tours/partials/tour_cards.html',
        {
            'tours': tours,
            'total_results': tours.count(),
        },
        request=request,
    )

    # Return SSE response for Datastar
    response = HttpResponse(html, content_type='text/html')
    response['Cache-Control'] = 'no-cache'
    return response


# Public Tour Catalog Views


class PublicTourCatalogView(generic.TemplateView):
    """
    Public tour catalog page accessible without authentication.
    Shows all active tours with search and filtering capabilities.
    """

    template_name = 'tours/public_catalog.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Get all active tour templates (no business filtering for public view)
        tours = (
            TourTemplate.objects.filter(is_active=True)
            .select_related('business', 'category')
            .order_by('name')
        )

        # Add availability information
        tours = self.add_availability_info(tours)

        context.update(
            {
                'tours': tours[:12],  # Limit initial results
                'search_form': TourSearchForm(),
                'total_tours': tours.count(),
                'featured_destinations': self.get_featured_destinations(),
                'popular_activities': self.get_popular_activities(),
            }
        )

        return context

    def add_availability_info(self, tours):
        """Add availability information to tour queryset."""
        today = timezone.now().date()
        future_date = today + timedelta(days=365)  # Look ahead 1 year

        for tour in tours:
            # Get upcoming tour instances
            upcoming_instances = TourInstance.objects.filter(
                tour_template=tour,
                date__gte=today,
                date__lte=future_date,
                is_operational=True,
            ).order_by('date')[:5]

            tour.upcoming_instances = upcoming_instances
            tour.next_available_date = (
                upcoming_instances.first().date if upcoming_instances else None
            )

            # Calculate total available spots across upcoming instances
            total_spots = 0
            min_spots = float('inf')
            for instance in upcoming_instances:
                available = instance.available_spots_with_reservations()
                total_spots += available
                if available < min_spots:
                    min_spots = available

            tour.total_available_spots = total_spots
            tour.min_available_spots = min_spots if min_spots != float('inf') else 0
            tour.has_urgent_availability = min_spots < 3 and min_spots > 0

        return tours

    def get_featured_destinations(self):
        """Get featured destinations for the catalog."""
        return (
            TourTemplate.objects.filter(is_active=True)
            .values_list('destination', flat=True)
            .distinct()[:6]
        )

    def get_popular_activities(self):
        """Get popular activity types."""
        return TourTemplate.ActivityType.choices[:6]


class PublicTourDetailView(generic.DetailView):
    """
    Public tour detail page accessible without authentication.
    Shows detailed tour information with availability and booking options.
    """

    model = TourTemplate
    template_name = 'tours/public_detail.html'
    context_object_name = 'tour'
    pk_url_kwarg = 'pk'

    def get_queryset(self):
        """Only show active tours."""
        return TourTemplate.objects.filter(is_active=True).select_related(
            'business', 'category'
        )

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        tour = context['tour']

        # Get upcoming tour instances with availability
        today = timezone.now().date()
        future_date = today + timedelta(days=365)

        upcoming_instances = TourInstance.objects.filter(
            tour_template=tour,
            date__gte=today,
            date__lte=future_date,
            is_operational=True,
        ).order_by('date')

        # Create availability info for each instance
        instance_data = []
        for instance in upcoming_instances:
            available_spots = instance.available_spots_with_reservations()
            guides = GuideAssignment.objects.filter(
                tour_instance=instance
            ).select_related('guide')

            instance_data.append(
                {
                    'instance': instance,
                    'available_spots': available_spots,
                    'is_urgent': available_spots < 3 and available_spots > 0,
                    'is_sold_out': available_spots <= 0,
                    'guides': guides,
                }
            )

        # Get related tours (same activity type or destination)
        related_tours = (
            TourTemplate.objects.filter(is_active=True)
            .filter(
                Q(activity_type=tour.activity_type)
                | Q(destination__icontains=tour.destination.split(',')[0])
            )
            .exclude(id=tour.id)[:4]
        )

        context.update(
            {
                'instance_data': instance_data[:10],  # Show next 10 dates
                'related_tours': related_tours,
                'can_book': upcoming_instances.filter(
                    date__gte=today
                    + timedelta(days=1)  # Must book at least 1 day in advance
                ).exists(),
            }
        )

        return context


def add_availability_info_to_queryset(tours):
    """Helper function to add availability information to tour queryset."""
    today = timezone.now().date()
    future_date = today + timedelta(days=365)

    for tour in tours:
        # Get upcoming instances
        upcoming_instances = TourInstance.objects.filter(
            tour_template=tour,
            date__gte=today,
            date__lte=future_date,
            is_operational=True,
        ).order_by('date')[:5]

        tour.upcoming_instances = upcoming_instances
        tour.next_available_date = (
            upcoming_instances.first().date if upcoming_instances else None
        )
        tour.available_spots = sum(
            instance.available_spots for instance in upcoming_instances
        )

        # Calculate urgency (spots left)
        total_spots = sum(instance.max_capacity for instance in upcoming_instances)
        tour.spots_urgency = (
            'high'
            if tour.available_spots < 3
            else 'medium'
            if tour.available_spots < 6
            else 'low'
        )

    return tours
