"""
Integration tests for quotes views.
"""

import json
from datetime import date, timedelta

from django.contrib.auth import get_user_model
from django.test import Client, TestCase
from django.urls import reverse
from djmoney.money import Money

from businesses.models import Business
from clients.models import Client as ClientModel
from invoices.models import Invoice
from quotes.models import Quote

User = get_user_model()


class QuoteViewsTest(TestCase):
    """Test cases for quote views."""

    def setUp(self):
        """Set up test data."""
        self.client = Client()

        # Create test user and business
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            role=Profile.Role.BUSINESS_OWNER,
        )

        self.business = Business.objects.create(
            name='Test Tours',
            email='<EMAIL>',
            phone='+1234567890',
            address_line1='123 Test St',
            city='Test City',
            state_province='Test State',
            postal_code='12345',
            country='Test Country',
            created_by=self.user,
        )

        self.user.profile.business = self.business
        self.user.save()

        # Create test client
        self.test_client = ClientModel.objects.create(
            business=self.business,
            first_name='<PERSON>',
            last_name='Doe',
            email='<EMAIL>',
            phone='+1234567890',
            created_by=self.user,
        )

        # Create test quote
        self.quote = Quote.objects.create(
            business=self.business,
            client=self.test_client,
            title='Safari Adventure',
            description='3-day safari tour',
            status=Quote.Status.SENT,
            valid_until=date.today() + timedelta(days=30),
            subtotal=Money(1000, 'USD'),
            tax_amount=Money(100, 'USD'),
            total_amount=Money(1100, 'USD'),
            created_by=self.user,
        )

    def test_public_quote_view(self):
        """Test public quote view."""
        url = reverse(
            'quotes:quote-public-view', kwargs={'hash': self.quote.public_hash}
        )
        response = self.client.get(url)

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.quote.title)
        self.assertContains(response, self.quote.quote_number)
        self.assertContains(response, str(self.quote.total_amount))

    def test_public_quote_view_invalid_hash(self):
        """Test public quote view with invalid hash."""
        url = reverse('quotes:quote-public-view', kwargs={'hash': 'invalid-hash'})
        response = self.client.get(url)

        self.assertEqual(response.status_code, 404)

    def test_approve_quote_success(self):
        """Test successful quote approval."""
        url = reverse('quotes:approve', kwargs={'hash': self.quote.public_hash})
        response = self.client.post(url)

        self.assertEqual(response.status_code, 200)

        data = json.loads(response.content)
        self.assertTrue(data['success'])
        self.assertEqual(data['quote_number'], self.quote.quote_number)

        # Refresh quote from database
        self.quote.refresh_from_db()
        self.assertEqual(self.quote.status, Quote.Status.APPROVED)
        self.assertIsNotNone(self.quote.approved_at)

    def test_approve_quote_invalid_status(self):
        """Test quote approval with invalid status."""
        # Set quote to draft status
        self.quote.status = Quote.Status.DRAFT
        self.quote.save()

        url = reverse('quotes:approve', kwargs={'hash': self.quote.public_hash})
        response = self.client.post(url)

        self.assertEqual(response.status_code, 400)

        data = json.loads(response.content)
        self.assertFalse(data['success'])

    def test_approve_quote_expired(self):
        """Test quote approval when expired."""
        # Set quote to expired
        self.quote.valid_until = date.today() - timedelta(days=1)
        self.quote.save()

        url = reverse('quotes:approve', kwargs={'hash': self.quote.public_hash})
        response = self.client.post(url)

        self.assertEqual(response.status_code, 400)

        data = json.loads(response.content)
        self.assertFalse(data['success'])

    def test_quote_detail_authenticated(self):
        """Test quote detail view for authenticated users."""
        self.client.login(username='testuser', password='testpass123')

        url = reverse('quotes:quote-detail', kwargs={'quote_id': self.quote.id})
        response = self.client.get(url)

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.quote.title)
        self.assertContains(response, self.quote.quote_number)

    def test_quote_detail_unauthenticated(self):
        """Test quote detail view for unauthenticated users."""
        url = reverse('quotes:quote-detail', kwargs={'quote_id': self.quote.id})
        response = self.client.get(url)

        # Should redirect to login
        self.assertEqual(response.status_code, 302)

    def test_create_invoice_from_quote_success(self):
        """Test successful invoice creation from quote."""
        # First approve the quote
        self.quote.status = Quote.Status.APPROVED
        self.quote.save()

        self.client.login(username='testuser', password='testpass123')

        url = reverse('quotes:quote-create_invoice', kwargs={'quote_id': self.quote.id})
        response = self.client.post(url)

        self.assertEqual(response.status_code, 200)

        data = json.loads(response.content)
        self.assertTrue(data['success'])
        self.assertIn('invoice_number', data)
        self.assertIn('invoice_id', data)

        # Check that invoice was created
        self.assertTrue(hasattr(self.quote, 'invoice'))
        invoice = self.quote.invoice
        self.assertEqual(invoice.business, self.business)
        self.assertEqual(invoice.client, self.test_client)
        self.assertEqual(invoice.total_amount, self.quote.total_amount)

    def test_create_invoice_from_quote_not_approved(self):
        """Test invoice creation from non-approved quote."""
        self.client.login(username='testuser', password='testpass123')

        url = reverse('quotes:quote-create_invoice', kwargs={'quote_id': self.quote.id})
        response = self.client.post(url)

        self.assertEqual(response.status_code, 400)

        data = json.loads(response.content)
        self.assertFalse(data['success'])

    def test_create_invoice_from_quote_already_exists(self):
        """Test invoice creation when invoice already exists."""
        # First approve the quote and create an invoice
        self.quote.status = Quote.Status.APPROVED
        self.quote.save()

        Invoice.objects.from_quote(self.quote)

        self.client.login(username='testuser', password='testpass123')

        url = reverse('quotes:quote-create_invoice', kwargs={'quote_id': self.quote.id})
        response = self.client.post(url)

        self.assertEqual(response.status_code, 400)

        data = json.loads(response.content)
        self.assertFalse(data['success'])
        self.assertIn('already exists', data['error'])

    def test_create_invoice_unauthorized(self):
        """Test invoice creation without proper authorization."""
        # Create another user from different business
        other_user = User.objects.create_user(
            username='otheruser', email='<EMAIL>', password='testpass123'
        )

        self.quote.status = Quote.Status.APPROVED
        self.quote.save()

        self.client.login(username='otheruser', password='testpass123')

        url = reverse('quotes:quote-create_invoice', kwargs={'quote_id': self.quote.id})
        response = self.client.post(url)

        self.assertEqual(response.status_code, 403)

        data = json.loads(response.content)
        self.assertFalse(data['success'])
