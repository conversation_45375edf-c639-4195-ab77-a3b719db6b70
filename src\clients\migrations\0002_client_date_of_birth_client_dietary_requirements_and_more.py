# Generated by Django 5.2.4 on 2025-08-13 07:52

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ('clients', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='client',
            name='date_of_birth',
            field=models.DateField(blank=True, help_text='Date of birth', null=True),
        ),
        migrations.AddField(
            model_name='client',
            name='dietary_requirements',
            field=models.TextField(
                blank=True,
                help_text='Dietary requirements and restrictions (e.g., vegetarian, allergies)',
            ),
        ),
        migrations.AddField(
            model_name='client',
            name='emergency_contact_email',
            field=models.EmailField(
                blank=True, help_text='Emergency contact email address', max_length=254
            ),
        ),
        migrations.AddField(
            model_name='client',
            name='emergency_contact_name',
            field=models.CharField(
                blank=True, help_text='Emergency contact full name', max_length=200
            ),
        ),
        migrations.AddField(
            model_name='client',
            name='emergency_contact_phone',
            field=models.CharField(
                blank=True,
                help_text='Emergency contact phone number',
                max_length=20,
                validators=[
                    django.core.validators.RegexValidator(
                        message="Phone number must be entered in the format: '+999999999'. Up to 15 digits allowed.",
                        regex='^\\+?1?\\d{9,15}$',
                    )
                ],
            ),
        ),
        migrations.AddField(
            model_name='client',
            name='emergency_contact_relationship',
            field=models.CharField(
                blank=True,
                help_text='Relationship to emergency contact (e.g., "Father", "Spouse")',
                max_length=100,
            ),
        ),
        migrations.AddField(
            model_name='client',
            name='medical_conditions',
            field=models.TextField(
                blank=True,
                help_text='Medical conditions and allergies relevant to travel',
            ),
        ),
        migrations.AddField(
            model_name='client',
            name='nationality',
            field=models.CharField(
                blank=True, help_text='Nationality/citizenship', max_length=100
            ),
        ),
        migrations.AddField(
            model_name='client',
            name='passport_expiry_date',
            field=models.DateField(
                blank=True, help_text='Passport expiry date', null=True
            ),
        ),
        migrations.AddField(
            model_name='client',
            name='passport_number',
            field=models.CharField(
                blank=True,
                help_text='Passport number for international travel',
                max_length=20,
            ),
        ),
    ]
