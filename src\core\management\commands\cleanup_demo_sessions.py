"""
Management command to clean up expired demo sessions.
This should be run periodically via cron job or task scheduler.
"""

import logging
from django.core.management.base import BaseCommand
from django.utils import timezone

from core.demo import DemoCleanupService

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Clean up expired demo sessions and associated data'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be cleaned up without actually deleting anything',
        )
        
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Show detailed output of cleanup process',
        )

    def handle(self, *args, **options):
        """Handle the cleanup command."""
        dry_run = options['dry_run']
        verbose = options['verbose']
        
        if verbose:
            self.stdout.write(
                self.style.SUCCESS(f'Starting demo session cleanup at {timezone.now()}')
            )
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('DRY RUN MODE - No data will actually be deleted')
            )
        
        try:
            if dry_run:
                # In dry run mode, we would analyze what would be cleaned up
                # For now, just show a message
                self.stdout.write(
                    self.style.WARNING('Dry run mode not fully implemented yet')
                )
                return
            
            # Perform actual cleanup
            cleaned_count = DemoCleanupService.cleanup_expired_sessions()
            
            if cleaned_count > 0:
                self.stdout.write(
                    self.style.SUCCESS(
                        f'Successfully cleaned up {cleaned_count} expired demo sessions'
                    )
                )
            else:
                if verbose:
                    self.stdout.write(
                        self.style.SUCCESS('No expired demo sessions found to clean up')
                    )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error during cleanup: {str(e)}')
            )
            logger.error(f'Demo cleanup command failed: {str(e)}')
            raise
        
        if verbose:
            self.stdout.write(
                self.style.SUCCESS(f'Demo session cleanup completed at {timezone.now()}')
            )
