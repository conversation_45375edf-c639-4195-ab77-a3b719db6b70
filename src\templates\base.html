{% load static %}
<!DOCTYPE html>
<html lang="en">

    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>{% block title %}Tour Business Management{% endblock %}</title>

        <!-- Tailwind CSS -->
         <link rel="stylesheet" href="{% static "css/styles.css" %}">


        <!-- DatastarJS -->
        <script type="module"
            src="https://cdn.jsdelivr.net/gh/starfederation/datastar@main/bundles/datastar.js"></script>

        <!-- Business Theme CSS -->
        {% if request.user.is_authenticated and request.user.profile.business %}
        <style>
            {
                    {
                    request.user.profile.business.get_theme_css
                }
            }
        </style>
        {% endif %}

        {% block extra_head %}{% endblock %}
    </head>

    <body class="bg-gray-50 min-h-screen">
        <!-- Navigation -->
        <nav class="bg-white shadow-sm border-b">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex items-center">
                        <a href="/" class="text-xl font-bold text-gray-900">
                            Tour Business Management
                        </a>
                    </div>

                    <div class="flex items-center space-x-4">
                        {% if user.is_authenticated %}
                        <span class="text-gray-700">Welcome, {{ user.get_full_name|default:user.username }}</span>
                        {% if user.profile.business %}
                        <span class="text-sm text-gray-500">({{ user.profile.business.name }})</span>
                        {% endif %}
                        <a href="{% url 'payments:subscription_dashboard' %}"
                            class="text-purple-600 hover:text-purple-800">Subscription</a>
                        <a href="/admin/" class="text-blue-600 hover:text-blue-800">Admin</a>
                        <a href="{% url 'accounts:logout' %}" class="text-red-600 hover:text-red-800">Logout</a>
                        {% else %}
                        <a href="{% url 'accounts:login' %}" class="text-blue-600 hover:text-blue-800">Login</a>
                        <a href="{% url 'accounts:register' %}" class="text-green-600 hover:text-green-800">Register</a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            {% block content %}{% endblock %}
        </main>

        <!-- Footer -->
        <footer class="bg-white border-t mt-auto">
            <div class="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8">
                <p class="text-center text-gray-500 text-sm">
                    © 2025 Tour Business Management System. Built with Django, Tailwind CSS, and DatastarJS.
                </p>
            </div>
        </footer>

        {% block extra_scripts %}{% endblock %}
    </body>

</html>
