{% load static %}
<!DOCTYPE html>
<html lang="en">

    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
        <title>{% block title %}Tour Business Management{% endblock %}</title>

        <!-- Tailwind CSS -->
        <link rel="stylesheet" href="{% static "css/styles.css" %}">

        <!-- DatastarJS -->
        <script type="module"
            src="https://cdn.jsdelivr.net/gh/starfederation/datastar@main/bundles/datastar.js"></script>

        <!-- Font Awesome for icons -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

        <!-- Business Theme CSS -->
        {% if request.user.is_authenticated and request.user.profile.business %}
        <style>
            {{ request.user.profile.business.get_theme_css }}
        </style>
        {% endif %}

        <!-- Mobile-first custom styles -->
        <style>
            /* Mobile-first navigation enhancements */
            .mobile-menu-backdrop {
                background: rgba(0, 0, 0, 0.5);
                backdrop-filter: blur(4px);
            }

            .mobile-menu-slide {
                transform: translateX(-100%);
                transition: transform 0.3s ease-in-out;
            }

            .mobile-menu-slide.open {
                transform: translateX(0);
            }

            /* Touch-friendly button sizes */
            .touch-target {
                min-height: 44px;
                min-width: 44px;
            }

            /* Focus states for accessibility */
            .focus-ring:focus {
                outline: 2px solid #3B82F6;
                outline-offset: 2px;
            }

            /* Smooth transitions */
            .transition-smooth {
                transition: all 0.2s ease-in-out;
            }
        </style>

        {% block extra_head %}{% endblock %}
    </head>

    <body class="bg-gray-50 min-h-screen flex flex-col">
        <!-- Mobile Navigation -->
        <nav class="bg-white shadow-sm border-b sticky top-0 z-50">
            <div class="max-w-7xl mx-auto px-3 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center h-16">
                    <!-- Logo/Brand - Mobile optimized -->
                    <div class="flex items-center">
                        <a href="/" class="flex items-center text-lg sm:text-xl font-bold text-gray-900 focus-ring rounded-md px-2 py-1">
                            <i class="fas fa-route text-blue-500 mr-2"></i>
                            <span class="hidden xs:inline">Tour Business</span>
                            <span class="xs:hidden">TourFlow</span>
                        </a>
                    </div>

                    <!-- Desktop Navigation -->
                    <div class="hidden md:flex items-center space-x-4">
                        {% if user.is_authenticated %}
                        <span class="text-gray-700 text-sm lg:text-base">Welcome, {{ user.get_full_name|default:user.username }}</span>
                        {% if user.profile.business %}
                        <span class="text-xs lg:text-sm text-gray-500">({{ user.profile.business.name }})</span>
                        {% endif %}
                        <a href="{% url 'payments:subscription_dashboard' %}"
                            class="text-purple-600 hover:text-purple-800 transition-smooth focus-ring rounded px-2 py-1">Subscription</a>
                        <a href="/admin/" class="text-blue-600 hover:text-blue-800 transition-smooth focus-ring rounded px-2 py-1">Admin</a>
                        <a href="{% url 'accounts:logout' %}" class="text-red-600 hover:text-red-800 transition-smooth focus-ring rounded px-2 py-1">Logout</a>
                        {% else %}
                        <a href="{% url 'accounts:login' %}" class="text-blue-600 hover:text-blue-800 transition-smooth focus-ring rounded px-3 py-2">Login</a>
                        <a href="{% url 'accounts:register' %}" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-smooth focus-ring">Register</a>
                        {% endif %}
                    </div>

                    <!-- Mobile menu button -->
                    <div class="md:hidden">
                        <button type="button"
                                id="mobile-menu-button"
                                class="touch-target flex items-center justify-center text-gray-700 hover:text-gray-900 focus-ring rounded-lg transition-smooth"
                                aria-label="Toggle mobile menu"
                                aria-expanded="false">
                            <i class="fas fa-bars text-xl" id="menu-icon"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Mobile menu overlay -->
            <div id="mobile-menu-backdrop" class="fixed inset-0 mobile-menu-backdrop hidden z-40"></div>

            <!-- Mobile menu -->
            <div id="mobile-menu" class="fixed top-0 left-0 h-full w-80 max-w-sm bg-white shadow-xl mobile-menu-slide z-50 overflow-y-auto">
                <div class="p-4">
                    <!-- Mobile menu header -->
                    <div class="flex items-center justify-between mb-6">
                        <div class="flex items-center">
                            <i class="fas fa-route text-blue-500 mr-2"></i>
                            <span class="text-lg font-bold text-gray-900">TourFlow</span>
                        </div>
                        <button type="button"
                                id="mobile-menu-close"
                                class="touch-target flex items-center justify-center text-gray-500 hover:text-gray-700 focus-ring rounded-lg"
                                aria-label="Close mobile menu">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>

                    <!-- Mobile menu content -->
                    {% if user.is_authenticated %}
                    <div class="space-y-1">
                        <a href="{% url 'core:dashboard' %}" class="flex items-center px-3 py-3 text-gray-700 hover:bg-gray-100 rounded-lg transition-smooth touch-target">
                            <i class="fas fa-tachometer-alt mr-3 w-5"></i>
                            Dashboard
                        </a>

                        {% if user.profile.business or user.is_demo_user %}
                        <a href="{% url 'clients:client-list' %}" class="flex items-center px-3 py-3 text-gray-700 hover:bg-gray-100 rounded-lg transition-smooth touch-target">
                            <i class="fas fa-users mr-3 w-5"></i>
                            Clients
                        </a>
                        <a href="{% url 'quotes:quote-list' %}" class="flex items-center px-3 py-3 text-gray-700 hover:bg-gray-100 rounded-lg transition-smooth touch-target">
                            <i class="fas fa-file-invoice-dollar mr-3 w-5"></i>
                            Quotes
                        </a>
                        <a href="{% url 'invoices:list' %}" class="flex items-center px-3 py-3 text-gray-700 hover:bg-gray-100 rounded-lg transition-smooth touch-target">
                            <i class="fas fa-receipt mr-3 w-5"></i>
                            Invoices
                        </a>
                        <a href="{% url 'bookings:booking_list' %}" class="flex items-center px-3 py-3 text-gray-700 hover:bg-gray-100 rounded-lg transition-smooth touch-target">
                            <i class="fas fa-calendar-check mr-3 w-5"></i>
                            Bookings
                        </a>
                        {% endif %}

                        <div class="border-t border-gray-200 my-4"></div>

                        <a href="{% url 'accounts:profile' %}" class="flex items-center px-3 py-3 text-gray-700 hover:bg-gray-100 rounded-lg transition-smooth touch-target">
                            <i class="fas fa-user mr-3 w-5"></i>
                            Profile
                        </a>

                        {% if user.profile.business %}
                        <a href="{% url 'payments:subscription_dashboard' %}" class="flex items-center px-3 py-3 text-purple-600 hover:bg-purple-50 rounded-lg transition-smooth touch-target">
                            <i class="fas fa-credit-card mr-3 w-5"></i>
                            Subscription
                        </a>
                        {% endif %}

                        {% if user.is_staff %}
                        <a href="/admin/" class="flex items-center px-3 py-3 text-blue-600 hover:bg-blue-50 rounded-lg transition-smooth touch-target">
                            <i class="fas fa-cog mr-3 w-5"></i>
                            Admin
                        </a>
                        {% endif %}

                        <a href="{% url 'accounts:logout' %}" class="flex items-center px-3 py-3 text-red-600 hover:bg-red-50 rounded-lg transition-smooth touch-target">
                            <i class="fas fa-sign-out-alt mr-3 w-5"></i>
                            Logout
                        </a>
                    </div>
                    {% else %}
                    <div class="space-y-3">
                        <a href="{% url 'accounts:login' %}" class="block w-full text-center px-4 py-3 text-blue-600 border border-blue-600 rounded-lg hover:bg-blue-50 transition-smooth touch-target">
                            Login
                        </a>
                        <a href="{% url 'accounts:register' %}" class="block w-full text-center px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-smooth touch-target">
                            Register
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="flex-1 max-w-7xl mx-auto w-full py-4 px-3 sm:py-6 sm:px-6 lg:px-8">
            <!-- Messages -->
            {% if messages %}
            <div class="mb-4 sm:mb-6">
                {% for message in messages %}
                <div class="p-3 sm:p-4 mb-3 rounded-lg {% if message.tags == 'error' %}bg-red-50 text-red-800 border border-red-200{% elif message.tags == 'success' %}bg-green-50 text-green-800 border border-green-200{% elif message.tags == 'warning' %}bg-yellow-50 text-yellow-800 border border-yellow-200{% else %}bg-blue-50 text-blue-800 border border-blue-200{% endif %}">
                    {{ message }}
                </div>
                {% endfor %}
            </div>
            {% endif %}

            {% block content %}{% endblock %}
        </main>

        <!-- Footer -->
        <footer class="bg-white border-t mt-auto">
            <div class="max-w-7xl mx-auto py-4 px-3 sm:px-6 lg:px-8">
                <p class="text-center text-gray-500 text-xs sm:text-sm">
                    © 2025 Tour Business Management System. Built with Django, Tailwind CSS, and DatastarJS.
                </p>
            </div>
        </footer>

        <!-- Mobile menu JavaScript -->
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const mobileMenuButton = document.getElementById('mobile-menu-button');
                const mobileMenu = document.getElementById('mobile-menu');
                const mobileMenuBackdrop = document.getElementById('mobile-menu-backdrop');
                const mobileMenuClose = document.getElementById('mobile-menu-close');
                const menuIcon = document.getElementById('menu-icon');

                function openMobileMenu() {
                    mobileMenu.classList.add('open');
                    mobileMenuBackdrop.classList.remove('hidden');
                    mobileMenuButton.setAttribute('aria-expanded', 'true');
                    menuIcon.classList.remove('fa-bars');
                    menuIcon.classList.add('fa-times');
                    document.body.style.overflow = 'hidden';
                }

                function closeMobileMenu() {
                    mobileMenu.classList.remove('open');
                    mobileMenuBackdrop.classList.add('hidden');
                    mobileMenuButton.setAttribute('aria-expanded', 'false');
                    menuIcon.classList.remove('fa-times');
                    menuIcon.classList.add('fa-bars');
                    document.body.style.overflow = '';
                }

                // Toggle mobile menu
                mobileMenuButton.addEventListener('click', function() {
                    if (mobileMenu.classList.contains('open')) {
                        closeMobileMenu();
                    } else {
                        openMobileMenu();
                    }
                });

                // Close mobile menu
                mobileMenuClose.addEventListener('click', closeMobileMenu);
                mobileMenuBackdrop.addEventListener('click', closeMobileMenu);

                // Close menu when clicking on links
                const mobileMenuLinks = mobileMenu.querySelectorAll('a');
                mobileMenuLinks.forEach(link => {
                    link.addEventListener('click', closeMobileMenu);
                });

                // Handle escape key
                document.addEventListener('keydown', function(event) {
                    if (event.key === 'Escape' && mobileMenu.classList.contains('open')) {
                        closeMobileMenu();
                    }
                });

                // Handle swipe gestures (basic implementation)
                let startX = 0;
                let currentX = 0;
                let isDragging = false;

                mobileMenu.addEventListener('touchstart', function(e) {
                    startX = e.touches[0].clientX;
                    isDragging = true;
                });

                mobileMenu.addEventListener('touchmove', function(e) {
                    if (!isDragging) return;
                    currentX = e.touches[0].clientX;
                    const diffX = startX - currentX;

                    if (diffX > 0) {
                        mobileMenu.style.transform = `translateX(-${diffX}px)`;
                    }
                });

                mobileMenu.addEventListener('touchend', function(e) {
                    if (!isDragging) return;
                    isDragging = false;

                    const diffX = startX - currentX;
                    if (diffX > 100) {
                        closeMobileMenu();
                    }

                    mobileMenu.style.transform = '';
                });
            });
        </script>

        {% block extra_scripts %}{% endblock %}
    </body>

</html>
