<c-layouts.dashboard>
  <c-slot name="title">
    Calendar - {{ month_name }} {{ year }}
  </c-slot>

  <c-slot name="main">
    <div class="container mx-auto px-4 py-8">
      <!-- Calendar Header -->
      <div class="flex justify-between items-center mb-8">
        <div>
          <h1 class="text-3xl font-bold text-gray-900">Event Calendar</h1>
          <p class="text-gray-600 mt-2">{{ month_name }} {{ year }}</p>
        </div>
        <div class="flex space-x-3">
          <a href="{% url 'bookings:calendar_week' %}"
            class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
            Week View
          </a>
          <a href="{% url 'bookings:calendar_day' %}"
            class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
            Day View
          </a>
          <a href="{% url 'tours:event_create' %}"
            class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
            New Event
          </a>
        </div>
      </div>

      <!-- Calendar Navigation -->
      <div class="flex justify-between items-center mb-6">
        <a href="{% url 'bookings:calendar_month' prev_year prev_month %}"
          class="flex items-center px-4 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
          </svg>
          Previous Month
        </a>

        <div class="flex items-center space-x-4">
          <a href="{% url 'bookings:calendar' %}" class="text-blue-600 hover:text-blue-800 font-medium">
            Today
          </a>
          <h2 class="text-xl font-semibold text-gray-900">{{ month_name }} {{ year }}</h2>
        </div>

        <a href="{% url 'bookings:calendar_month' next_year next_month %}"
          class="flex items-center px-4 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors">
          Next Month
          <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
          </svg>
        </a>
      </div>

      <!-- Calendar Grid -->
      <div class="bg-white rounded-lg shadow overflow-hidden">
        <!-- Weekday Headers -->
        <div class="grid grid-cols-7 bg-gray-50 border-b border-gray-200">
          {% for weekday in weekday_names %}
          <div class="p-4 text-center text-sm font-medium text-gray-700 border-r border-gray-200 last:border-r-0">
            {{ weekday }}
          </div>
          {% endfor %}
        </div>

        <!-- Calendar Days -->
        {% for week in month_days %}
        <div class="grid grid-cols-7 border-b border-gray-200 last:border-b-0">
          {% for day in week %}
          {% if day == 0 %}
          <!-- Empty cell for days from other months -->
          <div class="h-32 bg-gray-50 border-r border-gray-200 last:border-r-0"></div>
          {% else %}
          {% with current_date=year|add:"-01-01"|date:"Y-m-d" %}
          {% with day_date=year|stringformat:"s"|add:"-"|add:month|stringformat:"02d"|add:"-"|add:day|stringformat:"02d"|date:"Y-m-d" %}
          <div
            class="h-32 border-r border-gray-200 last:border-r-0 relative {% if day_date == today|date:'Y-m-d' %}bg-blue-50{% else %}bg-white hover:bg-gray-50{% endif %} transition-colors cursor-pointer"
            data-on-click="windo.location.href={% url 'bookings:calendar_day' %}">
            <!-- Day Number -->
            <div class="p-2">
              <span class="text-sm font-medium
                 {% if day_date == today|date:'Y-m-d' %}text-blue-600{% else %}text-gray-900{% endif %}">
                {{ day }}
              </span>
            </div>

            <!-- Events for this day -->
            {% with day_date|date:"Y-m-d" as date_key %}
            {% for event in events_by_date|default_if_none:""|dictsort:"start_time" %}
            {% if event.start_time.date|date:"Y-m-d" == date_key %}
            <div class="mx-1 mb-1">
              <div
                class="text-xs p-1 rounded truncate {% if event.event_type == 'tour' %}bg-green-100 text-green-800 {% elif event.event_type == 'meeting' %}bg-blue-100 text-blue-800 {% elif event.event_type == 'consultation' %}bg-yellow-100 text-yellow-800 {% elif event.event_type == 'maintenance' %}bg-red-100 text-red-800 {% elif event.event_type == 'training' %}bg-purple-100 text-purple-800 {% else %}bg-gray-100 text-gray-800{% endif %}"
                title="{{ event.event.title }} - {{ event.start_time|time:'H:i' }}">
                <span class="font-medium">{{ event.start_time|time:"H:i" }}</span>
                {{ event.event.title|truncatechars:15 }}
              </div>
            </div>
            {% endif %}
            {% endfor %}
            {% endwith %}

            <!-- More events indicator -->
            {% with day_date|date:"Y-m-d" as date_key %}
            {% with events_by_date|default_if_none:""|length as event_count %}
            {% if event_count > 3 %}
            <div class="absolute bottom-1 right-1">
              <span class="text-xs text-gray-500 bg-gray-200 px-1 rounded">
                +{{ event_count|add:"-3" }} more
              </span>
            </div>
            {% endif %}
            {% endwith %}
            {% endwith %}
          </div>
          {% endwith %}
          {% endwith %}
          {% endif %}
          {% endfor %}
        </div>
        {% endfor %}
      </div>

      <!-- Legend -->
      <div class="mt-6 bg-white rounded-lg shadow p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Event Types</h3>
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
          <div class="flex items-center">
            <div class="w-4 h-4 bg-green-100 border border-green-200 rounded mr-2"></div>
            <span class="text-sm text-gray-700">Tours</span>
          </div>
          <div class="flex items-center">
            <div class="w-4 h-4 bg-blue-100 border border-blue-200 rounded mr-2"></div>
            <span class="text-sm text-gray-700">Meetings</span>
          </div>
          <div class="flex items-center">
            <div class="w-4 h-4 bg-yellow-100 border border-yellow-200 rounded mr-2"></div>
            <span class="text-sm text-gray-700">Consultations</span>
          </div>
          <div class="flex items-center">
            <div class="w-4 h-4 bg-red-100 border border-red-200 rounded mr-2"></div>
            <span class="text-sm text-gray-700">Maintenance</span>
          </div>
          <div class="flex items-center">
            <div class="w-4 h-4 bg-purple-100 border border-purple-200 rounded mr-2"></div>
            <span class="text-sm text-gray-700">Training</span>
          </div>
          <div class="flex items-center">
            <div class="w-4 h-4 bg-gray-100 border border-gray-200 rounded mr-2"></div>
            <span class="text-sm text-gray-700">Other</span>
          </div>
        </div>
      </div>

      <!-- Quick Stats -->
      <div class="mt-6 grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center">
            <div class="p-2 bg-green-100 rounded-lg">
              <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z">
                </path>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">This Month</p>
              <p class="text-2xl font-semibold text-gray-900">
                {% with events_by_date.values|length as total_events %}{{ total_events }}{% endwith %}
                Events
              </p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center">
            <div class="p-2 bg-blue-100 rounded-lg">
              <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Today</p>
              <p class="text-2xl font-semibold text-gray-900">
                {% with today|date:"Y-m-d" as today_key %}
                {% with events_by_date|default_if_none:""|length as today_events %}{{ today_events }}{% endwith %}
                {% endwith %} Events
              </p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center">
            <div class="p-2 bg-purple-100 rounded-lg">
              <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2">
                </path>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Quick Actions</p>
              <div class="mt-2">
                <a href="{% url 'tours:event_create' %}" class="text-sm text-blue-600 hover:text-blue-800">
                  Create Event →
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <style>
      /* Custom styles for calendar */
      .calendar-day {
        min-height: 120px;
      }

      .event-item {
        font-size: 0.75rem;
        line-height: 1rem;
        margin-bottom: 2px;
      }

      .event-item:hover {
        transform: scale(1.02);
        transition: transform 0.1s ease-in-out;
      }
    </style>
  </c-slot>
</c-layouts.dashboard>
