from datetime import date, timedelta

from django.core.exceptions import ValidationError
from django.db import models
from django.utils.translation import gettext_lazy as _
from djmoney.models.fields import MoneyField

from core.mixins import UserAuditModel


class InvoiceQuerySet(models.QuerySet):
    """Custom QuerySet for Invoice model with chainable methods."""

    def for_business(self, business):
        """Filter invoices by business."""
        return self.filter(business=business)

    def by_status(self, status):
        """Filter invoices by status."""
        return self.filter(status=status)

    def paid(self):
        """Return paid invoices."""
        return self.filter(status='paid')

    def overdue(self):
        """Return overdue invoices."""
        from datetime import date

        return self.filter(
            status__in=['sent', 'overdue'],
            due_date__lt=date.today(),
        )


class InvoiceManager(models.Manager):
    """Optimized manager for Invoice model."""

    def get_queryset(self):
        """Return optimized queryset."""
        return InvoiceQuerySet(self.model, using=self._db).select_related(
            'business', 'client', 'quote', 'created_by'
        )

    def for_business(self, business):
        """Filter invoices by business."""
        return self.get_queryset().for_business(business)

    def by_status(self, status):
        """Filter invoices by status."""
        return self.get_queryset().by_status(status)

    def paid(self):
        """Return paid invoices."""
        return self.get_queryset().paid()

    def overdue(self):
        """Return overdue invoices."""
        return self.get_queryset().overdue()

    def from_quote(self, quote):
        """Create invoice from approved quote."""
        if quote.status != quote.Status.APPROVED:
            raise ValidationError('Can only create invoices from approved quotes')

        return self.create(
            business=quote.business,
            client=quote.client,
            quote=quote,
            subtotal=quote.subtotal,
            tax_amount=quote.tax_amount,
            total_amount=quote.total_amount,
            terms_conditions=quote.terms_conditions,
            payment_terms=quote.payment_terms,
            created_by=quote.created_by,
        )


class Invoice(UserAuditModel):
    """
    Invoice model for billing clients.
    Can only be created from approved quotes.
    """

    class Status(models.TextChoices):
        DRAFT = 'draft', _('Draft')
        SENT = 'sent', _('Sent')
        PAID = 'paid', _('Paid')
        OVERDUE = 'overdue', _('Overdue')
        CANCELLED = 'cancelled', _('Cancelled')

    # Relationships
    business = models.ForeignKey(
        'businesses.Business',
        on_delete=models.CASCADE,
        related_name='invoices',
        help_text=_('Business this invoice belongs to'),
    )
    client = models.ForeignKey(
        'clients.Client',
        on_delete=models.CASCADE,
        related_name='invoices',
        help_text=_('Client this invoice is for'),
    )
    quote = models.OneToOneField(
        'quotes.Quote',
        on_delete=models.PROTECT,
        related_name='invoice',
        help_text=_('Quote this invoice was created from'),
    )

    # Invoice Information
    # todo: set default unique invoice number
    invoice_number = models.CharField(
        max_length=50,
        unique=True,
        help_text=_('Unique invoice number'),
    )

    # Status and Dates
    status = models.CharField(
        max_length=20,
        choices=Status.choices,
        default=Status.DRAFT,
        help_text=_('Current status of the invoice'),
    )
    issue_date = models.DateField(
        default=date.today,
        verbose_name=_('Invoice issue date'),
    )
    due_date = models.DateField(
        verbose_name=_('Invoice due date'),
    )

    # Financial Information
    subtotal = MoneyField(
        max_digits=14,
        decimal_places=2,
        default_currency='USD',
        help_text=_('Subtotal amount before taxes'),
    )
    tax_amount = MoneyField(
        max_digits=14,
        decimal_places=2,
        default_currency='USD',
        help_text=_('Tax amount'),
    )
    total_amount = MoneyField(
        max_digits=14,
        decimal_places=2,
        default_currency='USD',
        help_text=_('Total amount including taxes'),
    )

    # Terms and Conditions
    terms_conditions = models.TextField(
        blank=True,
        help_text=_('Terms and conditions for this invoice'),
    )
    payment_terms = models.TextField(
        blank=True,
        help_text=_('Payment terms and schedule'),
    )

    # Internal Notes
    internal_notes = models.TextField(
        blank=True,
        help_text=_('Internal notes (not visible to client)'),
    )

    sent_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text=_('When the invoice was sent to the client'),
    )
    paid_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text=_('When the invoice was paid'),
    )

    # Custom manager
    objects = InvoiceManager()

    class Meta:
        db_table = 'invoices_invoice'
        verbose_name = _('Invoice')
        verbose_name_plural = _('Invoices')
        unique_together = [['business', 'invoice_number']]
        indexes = [
            models.Index(fields=['business', 'status']),
            models.Index(fields=['business', 'client']),
            models.Index(fields=['due_date']),
        ]

    def __str__(self):
        return f'{self.invoice_number} - {self.client.display_name}'

    def save(self, *args, **kwargs):
        # Generate invoice number if not set
        if not self.invoice_number:
            self.invoice_number = self.generate_invoice_number()

        # Set default due date if not set
        if not self.due_date:
            self.due_date = self.issue_date + timedelta(days=30)

        super().save(*args, **kwargs)

    def generate_invoice_number(self):
        """Generate a unique invoice number for this business."""
        from datetime import datetime

        year = datetime.now().year

        # Get the next sequential number for this business and year
        last_invoice = (
            Invoice.objects.filter(
                business=self.business, invoice_number__startswith=f'INV{year}'
            )
            .order_by('-invoice_number')
            .first()
        )

        if last_invoice:
            try:
                last_num = int(last_invoice.invoice_number.split('-')[-1])
                next_num = last_num + 1
            except (ValueError, IndexError):
                next_num = 1
        else:
            next_num = 1

        return f'INV{year}-{next_num:04d}'

    @property
    def is_overdue(self):
        """Check if the invoice is overdue."""
        return (
            self.status in [self.Status.SENT, self.Status.OVERDUE]
            and date.today() > self.due_date
        )

    def mark_as_paid(self):
        """Mark the invoice as paid."""
        from django.utils import timezone

        self.status = self.Status.PAID
        self.paid_at = timezone.now()
        self.save()
