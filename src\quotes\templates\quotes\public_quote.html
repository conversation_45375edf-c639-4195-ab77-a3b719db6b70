<c-layouts.base>

  {% block title %}Quote {{ quote.quote_number }}{% endblock %}

  {% block content %}
  <div class="max-w-4xl mx-auto">
    <!-- Quote Header -->
    <div class="bg-white rounded-lg shadow p-6 mb-6">
      <div class="flex justify-between items-start mb-4">
        <div>
          <h1 class="text-3xl font-bold text-gray-900">{{ quote.title }}</h1>
          <p class="text-lg text-gray-600">Quote #{{ quote.quote_number }}</p>
        </div>
        <div class="text-right">
          <p class="text-sm text-gray-500">Status</p>
          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                    {% if quote.status == 'approved' %}bg-green-100 text-green-800
                    {% elif quote.status == 'sent' %}bg-blue-100 text-blue-800
                    {% else %}bg-gray-100 text-gray-800{% endif %}">
            {{ quote.get_status_display }}
          </span>
        </div>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <h3 class="text-lg font-semibold text-gray-900 mb-2">Quote Details</h3>
          <dl class="space-y-2">
            <div class="flex justify-between">
              <dt class="text-sm text-gray-500">Subtotal:</dt>
              <dd class="text-sm font-medium text-gray-900">{{ quote.subtotal }}</dd>
            </div>
            <div class="flex justify-between">
              <dt class="text-sm text-gray-500">Tax:</dt>
              <dd class="text-sm font-medium text-gray-900">{{ quote.tax_amount }}</dd>
            </div>
            <div class="flex justify-between border-t pt-2">
              <dt class="text-base font-semibold text-gray-900">Total:</dt>
              <dd class="text-base font-semibold text-gray-900">{{ quote.total_amount }}</dd>
            </div>
          </dl>
        </div>

        <div>
          <h3 class="text-lg font-semibold text-gray-900 mb-2">Validity</h3>
          <p class="text-sm text-gray-600">Valid until: {{ quote.valid_until|date:"F d, Y" }}</p>
          {% if is_expired %}
          <p class="text-sm text-red-600 font-medium mt-1">This quote has expired</p>
          {% endif %}
        </div>
      </div>

      {% if quote.description %}
      <div class="mt-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-2">Description</h3>
        <p class="text-gray-600">{{ quote.description|linebreaks }}</p>
      </div>
      {% endif %}

      {% if can_approve %}
      <div class="mt-6 pt-6 border-t">
        <button onclick="approveQuote()"
          class="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-md transition-colors">
          Approve Quote
        </button>
      </div>
      {% endif %}
    </div>
  </div>
  {% endblock %}

  {% block extra_scripts %}
  <script>
    function approveQuote() {
      if (!confirm('Are you sure you want to approve this quote?')) {
        return;
      }

      fetch('{% url "quotes:approve" quote.public_hash %}', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      })
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            alert('Quote approved successfully!');
            location.reload();
          } else {
            alert('Error: ' + data.error);
          }
        })
        .catch(error => {
          console.error('Error:', error);
          alert('An error occurred while approving the quote.');
        });
    }
  </script>
  {% endblock %}
</c-layouts.base>
