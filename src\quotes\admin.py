from django.contrib import admin
from django.urls import reverse
from django.utils.html import format_html
from django.utils.translation import gettext_lazy as _
from simple_history.admin import SimpleHistoryAdmin

from .models import Quote


@admin.register(Quote)
class QuoteAdmin(SimpleHistoryAdmin):
    """Admin configuration for Quote model."""

    list_display = (
        'quote_number',
        'title',
        'client',
        'business',
        'status',
        'total_amount',
        'valid_until',
        'is_expired',
        'created_at',
    )
    list_filter = (
        'status',
        'business',
        'valid_until',
        'created_at',
        'sent_at',
        'approved_at',
    )
    search_fields = (
        'quote_number',
        'title',
        'description',
        'client__first_name',
        'client__last_name',
        'client__company_name',
        'public_hash',
    )
    readonly_fields = (
        'quote_number',
        'public_hash',
        'is_expired',
        'is_valid',
        'can_be_approved',
        'public_url',
        'created_at',
        'updated_at',
        'sent_at',
        'approved_at',
    )

    fieldsets = (
        (
            _('Basic Information'),
            {
                'fields': (
                    'business',
                    'client',
                    'quote_number',
                    'title',
                    'description',
                )
            },
        ),
        (
            _('Financial Information'),
            {
                'fields': (
                    'subtotal',
                    'tax_amount',
                    'total_amount',
                )
            },
        ),
        (
            _('Status & Validity'),
            {
                'fields': (
                    'status',
                    'valid_until',
                    'is_expired',
                    'is_valid',
                    'can_be_approved',
                )
            },
        ),
        (
            _('Public Access'),
            {
                'fields': (
                    'public_hash',
                    'public_url',
                )
            },
        ),
        (
            _('Terms & Conditions'),
            {
                'fields': (
                    'terms_conditions',
                    'payment_terms',
                    'notes',
                ),
                'classes': ('collapse',),
            },
        ),
        (
            _('Audit Information'),
            {
                'fields': (
                    'created_by',
                    'created_at',
                    'updated_at',
                    'sent_at',
                    'approved_at',
                ),
                'classes': ('collapse',),
            },
        ),
    )

    def get_queryset(self, request):
        """Optimize queryset with select_related."""
        return (
            super()
            .get_queryset(request)
            .select_related('business', 'client', 'created_by')
        )

    def public_url(self, obj):
        """Display public URL for the quote."""
        if obj.public_hash:
            url = reverse('quotes:quote-public-view', kwargs={'hash': obj.public_hash})
            return format_html(
                '<a href="{}" target="_blank">{}</a>', url, _('View Public Quote')
            )
        return '-'

    public_url.short_description = _('Public URL')

    def is_expired(self, obj):
        """Display if quote is expired."""
        return obj.is_expired

    is_expired.boolean = True
    is_expired.short_description = _('Expired')

    actions = ['mark_as_sent', 'mark_as_approved']

    def mark_as_sent(self, request, queryset):
        """Mark selected quotes as sent."""
        from django.utils import timezone

        updated = queryset.filter(status=Quote.Status.DRAFT).update(
            status=Quote.Status.SENT, sent_at=timezone.now()
        )
        self.message_user(request, f'{updated} quotes were marked as sent.')

    mark_as_sent.short_description = _('Mark selected quotes as sent')

    def mark_as_approved(self, request, queryset):
        """Mark selected quotes as approved."""
        from django.utils import timezone

        updated = queryset.filter(status=Quote.Status.SENT).update(
            status=Quote.Status.APPROVED, approved_at=timezone.now()
        )
        self.message_user(request, f'{updated} quotes were marked as approved.')

    mark_as_approved.short_description = _('Mark selected quotes as approved')
