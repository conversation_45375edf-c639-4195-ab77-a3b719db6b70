"""
Admin configuration for payments app.
"""

from django.contrib import admin
from django.urls import reverse
from django.utils.html import format_html
from simple_history.admin import SimpleHistoryAdmin

from .models import Payment, PaymentLink, Subscription


@admin.register(Payment)
class PaymentAdmin(SimpleHistoryAdmin):
    """Admin interface for Payment model."""

    list_display = [
        'payment_id',
        'invoice_link',
        'amount',
        'payment_method',
        'status',
        'payment_date',
        'created_at',
    ]
    list_filter = ['status', 'payment_method', 'payment_processor', 'created_at']
    search_fields = [
        'payment_id',
        'invoice__invoice_number',
        'invoice__client__first_name',
        'invoice__client__last_name',
        'reference_number',
        'external_payment_id',
    ]
    readonly_fields = ['payment_id', 'created_at', 'updated_at']

    fieldsets = (
        (
            'Payment Information',
            {'fields': ('payment_id', 'invoice', 'amount', 'payment_method', 'status')},
        ),
        (
            'External Integration',
            {
                'fields': (
                    'payment_processor',
                    'external_payment_id',
                    'lemon_squeezy_order_id',
                    'lemon_squeezy_checkout_id',
                )
            },
        ),
        ('Payment Details', {'fields': ('payment_date', 'reference_number', 'notes')}),
        (
            'Audit Information',
            {
                'fields': ('created_by', 'created_at', 'updated_at'),
                'classes': ('collapse',),
            },
        ),
    )

    def invoice_link(self, obj):
        """Display clickable link to invoice."""
        if obj.invoice:
            url = reverse('admin:invoices_invoice_change', args=[obj.invoice.pk])
            return format_html('<a href="{}">{}</a>', url, obj.invoice.invoice_number)
        return '-'

    invoice_link.short_description = 'Invoice'

    def get_queryset(self, request):
        """Optimize queryset with select_related."""
        return (
            super()
            .get_queryset(request)
            .select_related(
                'invoice', 'invoice__client', 'invoice__business', 'created_by'
            )
        )


@admin.register(PaymentLink)
class PaymentLinkAdmin(SimpleHistoryAdmin):
    """Admin interface for PaymentLink model."""

    list_display = [
        'link_id',
        'invoice_link',
        'status',
        'expires_at',
        'access_count',
        'created_at',
    ]
    list_filter = ['status', 'expires_at', 'created_at']
    search_fields = [
        'link_id',
        'invoice__invoice_number',
        'invoice__client__first_name',
        'invoice__client__last_name',
        'lemon_squeezy_checkout_id',
    ]
    readonly_fields = [
        'link_id',
        'access_count',
        'last_accessed_at',
        'created_at',
        'updated_at',
    ]

    fieldsets = (
        (
            'Link Information',
            {'fields': ('link_id', 'invoice', 'status', 'expires_at')},
        ),
        (
            'Lemon Squeezy Integration',
            {'fields': ('lemon_squeezy_checkout_url', 'lemon_squeezy_checkout_id')},
        ),
        ('Usage Tracking', {'fields': ('access_count', 'last_accessed_at')}),
        (
            'Audit Information',
            {
                'fields': ('created_by', 'created_at', 'updated_at'),
                'classes': ('collapse',),
            },
        ),
    )

    def invoice_link(self, obj):
        """Display clickable link to invoice."""
        if obj.invoice:
            url = reverse('admin:invoices_invoice_change', args=[obj.invoice.pk])
            return format_html('<a href="{}">{}</a>', url, obj.invoice.invoice_number)
        return '-'

    invoice_link.short_description = 'Invoice'

    def get_queryset(self, request):
        """Optimize queryset with select_related."""
        return (
            super()
            .get_queryset(request)
            .select_related(
                'invoice', 'invoice__client', 'invoice__business', 'created_by'
            )
        )


@admin.register(Subscription)
class SubscriptionAdmin(SimpleHistoryAdmin):
    """Admin interface for Subscription model."""

    list_display = [
        'subscription_id',
        'business_link',
        'plan',
        'status',
        'current_period_end',
        'monthly_price',
        'created_at',
    ]
    list_filter = ['status', 'plan', 'current_period_end', 'created_at']
    search_fields = [
        'subscription_id',
        'business__name',
        'lemon_squeezy_subscription_id',
        'lemon_squeezy_customer_id',
    ]
    readonly_fields = ['subscription_id', 'created_at', 'updated_at']

    fieldsets = (
        (
            'Subscription Information',
            {'fields': ('subscription_id', 'business', 'plan', 'status')},
        ),
        (
            'Lemon Squeezy Integration',
            {'fields': ('lemon_squeezy_subscription_id', 'lemon_squeezy_customer_id')},
        ),
        (
            'Billing Information',
            {
                'fields': (
                    'monthly_price',
                    'current_period_start',
                    'current_period_end',
                    'trial_end',
                )
            },
        ),
        (
            'Audit Information',
            {'fields': ('created_at', 'updated_at'), 'classes': ('collapse',)},
        ),
    )

    def business_link(self, obj):
        """Display clickable link to business."""
        if obj.business:
            url = reverse('admin:businesses_business_change', args=[obj.business.pk])
            return format_html('<a href="{}">{}</a>', url, obj.business.name)
        return '-'

    business_link.short_description = 'Business'

    def get_queryset(self, request):
        """Optimize queryset with select_related."""
        return super().get_queryset(request).select_related('business')
