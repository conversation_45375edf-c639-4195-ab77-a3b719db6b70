"""
Middleware for the Tour Business Management system.
Provides request logging, security monitoring, and audit trails.
"""

import logging
import time

from django.contrib.auth import get_user_model
from django.contrib.auth.signals import (
    user_logged_in,
    user_logged_out,
    user_login_failed,
)
from django.db import transaction
from django.dispatch import receiver
from django.utils import timezone
from django.utils.deprecation import MiddlewareMixin

# Import demo module after User is defined to avoid circular imports
from .demo import DemoSession
from .logging_utils import SecurityLogger, audit_logger, get_client_ip

User = get_user_model()

logger = logging.getLogger('tour_business')
security_logger = logging.getLogger('tour_business.security')


class RequestLoggingMiddleware(MiddlewareMixin):
    """Middleware to log HTTP requests and responses."""

    def process_request(self, request):
        """Log incoming requests."""
        request.start_time = time.time()

        # Log request details
        log_data = {
            'method': request.method,
            'path': request.path,
            'ip_address': get_client_ip(request),
            'user_agent': request.META.get('HTTP_USER_AGENT', ''),
            'user': request.user.username
            if request.user.is_authenticated
            else 'anonymous',
            'timestamp': timezone.now().isoformat(),
        }

        # Log query parameters for GET requests (excluding sensitive data)
        if request.method == 'GET' and request.GET:
            safe_params = {}
            for key, value in request.GET.items():
                # Exclude sensitive parameters
                if key.lower() not in ['password', 'token', 'secret', 'key']:
                    safe_params[key] = value
            if safe_params:
                log_data['query_params'] = safe_params

        logger.debug(f'Request: {request.method} {request.path}', extra=log_data)

    def process_response(self, request, response):
        """Log response details."""
        if hasattr(request, 'start_time'):
            duration = time.time() - request.start_time

            log_data = {
                'method': request.method,
                'path': request.path,
                'status_code': response.status_code,
                'duration_ms': round(duration * 1000, 2),
                'user': request.user.username
                if request.user.is_authenticated
                else 'anonymous',
                'ip_address': get_client_ip(request),
                'timestamp': timezone.now().isoformat(),
            }

            # Log level based on status code
            if response.status_code >= 500:
                logger.error(
                    f'Response: {request.method} {request.path} - {response.status_code}',
                    extra=log_data,
                )
            elif response.status_code >= 400:
                logger.warning(
                    f'Response: {request.method} {request.path} - {response.status_code}',
                    extra=log_data,
                )
            else:
                logger.info(
                    f'Response: {request.method} {request.path} - {response.status_code}',
                    extra=log_data,
                )

        return response

    def process_exception(self, request, exception):
        """Log exceptions."""
        log_data = {
            'method': request.method,
            'path': request.path,
            'user': request.user.username
            if request.user.is_authenticated
            else 'anonymous',
            'ip_address': get_client_ip(request),
            'exception_type': type(exception).__name__,
            'exception_message': str(exception),
            'timestamp': timezone.now().isoformat(),
        }

        logger.error(
            f'Exception in {request.method} {request.path}: {exception}',
            extra=log_data,
            exc_info=True,
        )


class SecurityMiddleware(MiddlewareMixin):
    """Middleware for security monitoring and logging."""

    def process_request(self, request):
        """Monitor for suspicious activities."""
        # Check for suspicious patterns
        self._check_suspicious_patterns(request)

        # Log admin access
        if request.path.startswith('/admin/') and request.user.is_authenticated:
            security_logger.info(
                f'Admin access: {request.user.username} accessed {request.path}',
                extra={
                    'user': request.user.username,
                    'path': request.path,
                    'ip_address': get_client_ip(request),
                    'timestamp': timezone.now().isoformat(),
                },
            )

    def _check_suspicious_patterns(self, request):
        """Check for suspicious request patterns."""
        suspicious_patterns = [
            'script',
            'javascript:',
            'vbscript:',
            'onload=',
            'onerror=',
            '<script',
            '</script>',
            'eval(',
            'document.cookie',
            'union select',
            'drop table',
            'insert into',
            'delete from',
            '../',
            '..\\',
            '/etc/passwd',
            '/etc/shadow',
        ]

        # Check URL and query parameters
        full_url = request.get_full_path().lower()
        for pattern in suspicious_patterns:
            if pattern in full_url:
                security_logger.warning(
                    f'Suspicious pattern detected in URL: {pattern}',
                    extra={
                        'pattern': pattern,
                        'url': request.get_full_path(),
                        'ip_address': get_client_ip(request),
                        'user_agent': request.META.get('HTTP_USER_AGENT', ''),
                        'user': request.user.username
                        if request.user.is_authenticated
                        else 'anonymous',
                        'timestamp': timezone.now().isoformat(),
                    },
                )
                break


class AuditTrailMiddleware(MiddlewareMixin):
    """Middleware to create audit trails for data modifications."""

    def process_view(self, request, view_func, view_args, view_kwargs):
        """Log view access for audit purposes."""
        if request.user.is_authenticated:
            # Log access to sensitive views
            sensitive_views = [
                'user_management',
                'add_user',
                'edit_user',
                'remove_user',
                'business_detail',
                'business_update',
                'invoice_create',
                'invoice_update',
                'invoice_delete',
                'quote_create',
                'quote_update',
                'quote_delete',
                'booking_create',
                'booking_update',
                'booking_delete',
                'payment_create',
                'payment_update',
            ]

            view_name = getattr(view_func, '__name__', 'unknown')
            if any(sensitive in view_name.lower() for sensitive in sensitive_views):
                audit_logger.info(
                    f'Sensitive view access: {request.user.username} accessed {view_name}',
                    extra={
                        'user': request.user.username,
                        'view': view_name,
                        'path': request.path,
                        'method': request.method,
                        'ip_address': get_client_ip(request),
                        'timestamp': timezone.now().isoformat(),
                    },
                )


# Signal handlers for authentication events
@receiver(user_logged_in)
def log_user_login(sender, request, user, **kwargs):
    """Log successful user login."""
    SecurityLogger.log_login_attempt(
        username=user.username, success=True, request=request
    )


@receiver(user_logged_out)
def log_user_logout(sender, request, user, **kwargs):
    """Log user logout."""
    if user:
        security_logger.info(
            f'User logout: {user.username}',
            extra={
                'user': user.username,
                'ip_address': get_client_ip(request),
                'timestamp': timezone.now().isoformat(),
            },
        )


@receiver(user_login_failed)
def log_failed_login(sender, credentials, request, **kwargs):
    """Log failed login attempts."""
    username = credentials.get('username', 'unknown')
    SecurityLogger.log_login_attempt(
        username=username,
        success=False,
        request=request,
        failure_reason='Invalid credentials',
    )


class DemoSessionMiddleware(MiddlewareMixin):
    """Middleware to handle demo sessions for anonymous users."""

    def process_request(self, request):
        """Process demo session for each request."""
        # Skip if user is authenticated (not a demo session)
        if request.user.is_authenticated:
            return None

        # Initialize demo session
        demo_session = DemoSession(request)
        request.demo_session = demo_session

        # Check if demo session exists and is expired
        if demo_session.is_demo_session and demo_session.is_expired:
            logger.info('Demo session expired, cleaning up')
            demo_session.cleanup_demo_session()

        return None

    def process_view(self, request, view_func, view_args, view_kwargs):
        """Process view for demo sessions."""
        # Skip if user is authenticated
        if request.user.is_authenticated:
            return None

        # Skip for certain paths that don't need demo session
        skip_paths = [
            '/admin/',
            '/static/',
            '/media/',
            '/favicon.ico',
            '/__debug__/',
            '/accounts/login/',
            '/accounts/register/',
        ]
        if any(request.path.startswith(path) for path in skip_paths):
            return None

        # For demo-enabled paths, ensure demo session exists
        demo_session = getattr(request, 'demo_session', None)
        if demo_session and not demo_session.is_demo_session:
            # This is a request that could benefit from demo mode
            # We'll handle this in the views that need it
            pass

        return None


class RateLimitingMiddleware(MiddlewareMixin):
    """Simple rate limiting middleware for security."""

    def __init__(self, get_response):
        self.get_response = get_response
        self.request_counts = {}  # In production, use Redis or database
        super().__init__(get_response)

    def process_request(self, request):
        """Check rate limits."""
        ip = get_client_ip(request)
        current_time = time.time()

        # Clean old entries (older than 1 hour)
        cutoff_time = current_time - 3600
        self.request_counts = {
            k: v
            for k, v in self.request_counts.items()
            if v['last_request'] > cutoff_time
        }

        # Check current IP
        if ip in self.request_counts:
            count_data = self.request_counts[ip]

            # Reset count if more than 1 minute has passed
            if current_time - count_data['last_request'] > 60:
                count_data['count'] = 1
            else:
                count_data['count'] += 1

            count_data['last_request'] = current_time

            # Log if too many requests
            if count_data['count'] > 100:  # 100 requests per minute
                security_logger.warning(
                    f'Rate limit exceeded for IP: {ip}',
                    extra={
                        'ip_address': ip,
                        'request_count': count_data['count'],
                        'path': request.path,
                        'user_agent': request.META.get('HTTP_USER_AGENT', ''),
                        'timestamp': timezone.now().isoformat(),
                    },
                )
        else:
            self.request_counts[ip] = {'count': 1, 'last_request': current_time}


class AtomicRequestMiddleware:
    """Wrap all POST requests in transactions"""

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        if request.method == 'POST':
            with transaction.atomic():
                return self.get_response(request)
            return self.get_response(request)
