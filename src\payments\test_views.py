"""
Pytest tests for payment views.
"""
import pytest
import json
import hmac
import hashlib
from unittest.mock import patch, Mock
from django.urls import reverse
from django.test import Client
from django.conf import settings

from payments.models import Payment


@pytest.mark.django_db
class TestPaymentViews:
    """Test cases for payment views."""
    
    def test_payment_list_view_authenticated(self, business_authenticated_client, payment):
        """Test payment list view for authenticated business owner."""
        url = reverse('payments:list')
        response = business_authenticated_client.get(url)
        
        assert response.status_code == 200
        assert payment.invoice.invoice_number in response.content.decode()
    
    def test_payment_list_view_unauthenticated(self, api_client):
        """Test payment list view for unauthenticated user."""
        url = reverse('payments:list')
        response = api_client.get(url)
        
        # Should redirect to login
        assert response.status_code == 302
    
    def test_payment_detail_view(self, business_authenticated_client, payment):
        """Test payment detail view."""
        url = reverse('payments:detail', kwargs={'pk': payment.pk})
        response = business_authenticated_client.get(url)
        
        assert response.status_code == 200
        assert str(payment.amount) in response.content.decode()
    
    @patch('payments.services.LemonSqueezyService.create_checkout')
    def test_create_payment_link_success(self, mock_create_checkout, business_authenticated_client, invoice):
        """Test successful payment link creation."""
        mock_create_checkout.return_value = {
            'data': {
                'id': 'checkout_123',
                'attributes': {
                    'url': 'https://checkout.lemonsqueezy.com/checkout_123'
                }
            }
        }
        
        url = reverse('payments:create_link')
        data = {
            'invoice': invoice.id,
            'expires_at': '2024-12-31 23:59:59'
        }
        response = business_authenticated_client.post(url, data)
        
        assert response.status_code == 302  # Redirect after success
    
    @patch('payments.services.LemonSqueezyService.create_checkout')
    def test_create_payment_link_failure(self, mock_create_checkout, business_authenticated_client, invoice):
        """Test payment link creation failure."""
        mock_create_checkout.return_value = None  # Simulate API failure
        
        url = reverse('payments:create_link')
        data = {
            'invoice': invoice.id,
            'expires_at': '2024-12-31 23:59:59'
        }
        response = business_authenticated_client.post(url, data)
        
        # Should show error message
        assert response.status_code == 200
        assert 'error' in response.context or 'form' in response.context


@pytest.mark.django_db
class TestLemonSqueezyWebhookView:
    """Test cases for Lemon Squeezy webhook view."""
    
    def create_webhook_signature(self, payload, secret):
        """Helper to create valid webhook signature."""
        return hmac.new(
            secret.encode('utf-8'),
            payload.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
    
    def test_webhook_get_method_not_allowed(self, api_client):
        """Test webhook endpoint rejects GET requests."""
        url = reverse('payments:lemon_squeezy_webhook')
        response = api_client.get(url)
        
        assert response.status_code == 405
    
    @patch('payments.services.LemonSqueezyService.process_webhook_event')
    def test_webhook_valid_signature(self, mock_process_event, api_client):
        """Test webhook with valid signature."""
        mock_process_event.return_value = True
        
        payload = json.dumps({
            'meta': {'event_name': 'order_created'},
            'data': {'id': 'test_123'}
        })
        
        secret = 'test_webhook_secret'
        signature = self.create_webhook_signature(payload, secret)
        
        with patch.object(settings, 'LEMON_SQUEEZY_WEBHOOK_SECRET', secret):
            url = reverse('payments:lemon_squeezy_webhook')
            response = api_client.post(
                url,
                data=payload,
                content_type='application/json',
                HTTP_X_SIGNATURE=signature
            )
        
        assert response.status_code == 200
        mock_process_event.assert_called_once()
    
    def test_webhook_invalid_signature(self, api_client):
        """Test webhook with invalid signature."""
        payload = json.dumps({
            'meta': {'event_name': 'order_created'},
            'data': {'id': 'test_123'}
        })
        
        url = reverse('payments:lemon_squeezy_webhook')
        response = api_client.post(
            url,
            data=payload,
            content_type='application/json',
            HTTP_X_SIGNATURE='invalid_signature'
        )
        
        assert response.status_code == 401
    
    def test_webhook_no_signature(self, api_client):
        """Test webhook without signature header."""
        payload = json.dumps({
            'meta': {'event_name': 'order_created'},
            'data': {'id': 'test_123'}
        })
        
        url = reverse('payments:lemon_squeezy_webhook')
        response = api_client.post(
            url,
            data=payload,
            content_type='application/json'
        )
        
        assert response.status_code == 401
    
    def test_webhook_invalid_json(self, api_client):
        """Test webhook with invalid JSON payload."""
        payload = 'invalid json'
        secret = 'test_webhook_secret'
        signature = self.create_webhook_signature(payload, secret)
        
        with patch.object(settings, 'LEMON_SQUEEZY_WEBHOOK_SECRET', secret):
            url = reverse('payments:lemon_squeezy_webhook')
            response = api_client.post(
                url,
                data=payload,
                content_type='application/json',
                HTTP_X_SIGNATURE=signature
            )
        
        assert response.status_code == 400
    
    @patch('payments.services.LemonSqueezyService.process_webhook_event')
    def test_webhook_processing_failure(self, mock_process_event, api_client):
        """Test webhook when event processing fails."""
        mock_process_event.return_value = False
        
        payload = json.dumps({
            'meta': {'event_name': 'order_created'},
            'data': {'id': 'test_123'}
        })
        
        secret = 'test_webhook_secret'
        signature = self.create_webhook_signature(payload, secret)
        
        with patch.object(settings, 'LEMON_SQUEEZY_WEBHOOK_SECRET', secret):
            url = reverse('payments:lemon_squeezy_webhook')
            response = api_client.post(
                url,
                data=payload,
                content_type='application/json',
                HTTP_X_SIGNATURE=signature
            )
        
        assert response.status_code == 500
    
    @patch('payments.services.LemonSqueezyService.process_webhook_event')
    def test_webhook_processing_exception(self, mock_process_event, api_client):
        """Test webhook when event processing raises exception."""
        mock_process_event.side_effect = Exception('Processing error')
        
        payload = json.dumps({
            'meta': {'event_name': 'order_created'},
            'data': {'id': 'test_123'}
        })
        
        secret = 'test_webhook_secret'
        signature = self.create_webhook_signature(payload, secret)
        
        with patch.object(settings, 'LEMON_SQUEEZY_WEBHOOK_SECRET', secret):
            url = reverse('payments:lemon_squeezy_webhook')
            response = api_client.post(
                url,
                data=payload,
                content_type='application/json',
                HTTP_X_SIGNATURE=signature
            )
        
        assert response.status_code == 500


@pytest.mark.webhook
class TestWebhookEventProcessing:
    """Test webhook event processing end-to-end."""
    
    @pytest.mark.django_db
    def test_order_created_webhook_end_to_end(self, api_client, invoice):
        """Test complete order_created webhook processing."""
        payload = json.dumps({
            'meta': {'event_name': 'order_created'},
            'data': {
                'id': 'order_123',
                'attributes': {
                    'id': 'order_123',
                    'total': 1650,
                    'order_number': 'ORD-123',
                    'custom': {'invoice_id': str(invoice.id)}
                }
            }
        })
        
        secret = 'test_webhook_secret'
        signature = hmac.new(
            secret.encode('utf-8'),
            payload.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        
        with patch.object(settings, 'LEMON_SQUEEZY_WEBHOOK_SECRET', secret):
            url = reverse('payments:lemon_squeezy_webhook')
            response = api_client.post(
                url,
                data=payload,
                content_type='application/json',
                HTTP_X_SIGNATURE=signature
            )
        
        assert response.status_code == 200
        
        # Verify payment was created
        payment = Payment.objects.filter(
            invoice=invoice,
            lemon_squeezy_order_id='order_123'
        ).first()
        assert payment is not None
        assert payment.status == Payment.Status.COMPLETED
    
    @pytest.mark.django_db
    def test_subscription_created_webhook_end_to_end(self, api_client, business):
        """Test complete subscription_created webhook processing."""
        payload = json.dumps({
            'meta': {'event_name': 'subscription_created'},
            'data': {
                'id': 'sub_123',
                'attributes': {
                    'id': 'sub_123',
                    'customer_id': 'cust_123',
                    'status': 'active',
                    'custom': {'business_id': str(business.id)}
                }
            }
        })
        
        secret = 'test_webhook_secret'
        signature = hmac.new(
            secret.encode('utf-8'),
            payload.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        
        with patch.object(settings, 'LEMON_SQUEEZY_WEBHOOK_SECRET', secret):
            url = reverse('payments:lemon_squeezy_webhook')
            response = api_client.post(
                url,
                data=payload,
                content_type='application/json',
                HTTP_X_SIGNATURE=signature
            )
        
        assert response.status_code == 200
        
        # Verify subscription was created
        from payments.models import Subscription
        subscription = Subscription.objects.filter(
            business=business,
            lemon_squeezy_subscription_id='sub_123'
        ).first()
        assert subscription is not None
        assert subscription.status == Subscription.Status.ACTIVE
