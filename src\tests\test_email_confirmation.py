"""
Tests for email confirmation functionality using Django Herald.
"""
import pytest
import uuid
from unittest.mock import patch, MagicMock
from django.test import TestCase, override_settings
from django.core import mail
from django.urls import reverse
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timed<PERSON><PERSON>

from core.notifications import EmailConfirmationNotification, UserInvitationNotification
from accounts.forms import CustomUserCreationForm
from accounts.views import RegisterView

User = get_user_model()


@pytest.mark.django_db
class TestEmailConfirmationNotification:
    """Test the EmailConfirmationNotification class."""

    def test_notification_initialization(self, user):
        """Test that notification is properly initialized."""
        confirmation_url = "http://example.com/confirm/test-token"
        notification = EmailConfirmationNotification(user, confirmation_url)
        
        assert notification.to_emails == [user.email]
        assert notification.context['user'] == user
        assert notification.context['confirmation_url'] == confirmation_url
        assert 'site_name' in notification.context
        assert 'company_name' in notification.context
        assert 'email_signature' in notification.context

    def test_notification_with_no_email_user(self):
        """Test notification with user that has no email."""
        user = User.objects.create_user(
            username='noemail',
            email='',
            password='testpass123'
        )
        confirmation_url = "http://example.com/confirm/test-token"
        notification = EmailConfirmationNotification(user, confirmation_url)
        
        assert notification.to_emails == []

    @override_settings(EMAIL_BACKEND='django.core.mail.backends.locmem.EmailBackend')
    def test_notification_send(self, user):
        """Test that notification sends email correctly."""
        confirmation_url = "http://example.com/confirm/test-token"
        notification = EmailConfirmationNotification(user, confirmation_url)
        
        # Clear any existing emails
        mail.outbox = []
        
        # Send the notification
        notification.send()
        
        # Check that email was sent
        assert len(mail.outbox) == 1
        email = mail.outbox[0]
        assert user.email in email.to
        assert 'Confirm your email address' in email.subject
        assert confirmation_url in email.body


@pytest.mark.django_db
class TestUserRegistrationEmailFlow:
    """Test the complete user registration and email confirmation flow."""

    @override_settings(EMAIL_BACKEND='django.core.mail.backends.locmem.EmailBackend')
    def test_registration_sends_confirmation_email(self, api_client):
        """Test that user registration sends confirmation email."""
        # Clear any existing emails
        mail.outbox = []
        
        # Register a new user
        registration_data = {
            'username': 'newuser',
            'email': '<EMAIL>',
            'password1': 'complexpassword123',
            'password2': 'complexpassword123'
        }
        
        response = api_client.post(reverse('accounts:register'), registration_data)
        
        # Check that user was created
        assert User.objects.filter(username='newuser').exists()
        user = User.objects.get(username='newuser')
        
        # Check that user is not active (needs email confirmation)
        assert not user.is_active
        assert user.email_confirmation_token is not None
        assert user.email_confirmation_sent_at is not None
        
        # Check that confirmation email was sent
        assert len(mail.outbox) == 1
        email = mail.outbox[0]
        assert '<EMAIL>' in email.to
        assert 'Confirm your email address' in email.subject
        
        # Check that confirmation URL is in email
        assert 'confirm-email' in email.body
        assert str(user.email_confirmation_token) in email.body

    def test_user_generate_confirmation_token(self, user):
        """Test that user can generate confirmation token."""
        # Initially no token
        assert user.email_confirmation_token is None
        assert user.email_confirmation_sent_at is None
        
        # Generate token
        token = user.generate_confirmation_token()
        
        # Check token was generated and saved
        assert token is not None
        assert user.email_confirmation_token == token
        assert user.email_confirmation_sent_at is not None
        
        # Refresh from database
        user.refresh_from_db()
        assert user.email_confirmation_token == token

    def test_email_confirmation_view_valid_token(self, api_client, user):
        """Test email confirmation with valid token."""
        # Generate confirmation token
        token = user.generate_confirmation_token()
        user.is_active = False
        user.save()
        
        # Confirm email
        response = api_client.get(reverse('accounts:confirm_email', kwargs={'token': str(token)}))
        
        # Check user is now active
        user.refresh_from_db()
        assert user.is_active
        assert user.email_confirmed_at is not None
        
        # Check redirect
        assert response.status_code == 302

    def test_email_confirmation_view_invalid_token(self, api_client, user):
        """Test email confirmation with invalid token."""
        invalid_token = uuid.uuid4()
        
        response = api_client.get(reverse('accounts:confirm_email', kwargs={'token': str(invalid_token)}))
        
        # Check user is still not active
        user.refresh_from_db()
        assert not user.is_active
        assert user.email_confirmed_at is None

    def test_email_confirmation_view_expired_token(self, api_client, user):
        """Test email confirmation with expired token."""
        # Generate token and make it expired
        token = user.generate_confirmation_token()
        user.email_confirmation_sent_at = timezone.now() - timedelta(days=8)  # Expired
        user.is_active = False
        user.save()
        
        response = api_client.get(reverse('accounts:confirm_email', kwargs={'token': str(token)}))
        
        # Check user is still not active
        user.refresh_from_db()
        assert not user.is_active
        assert user.email_confirmed_at is None


@pytest.mark.django_db
class TestCustomUserCreationForm:
    """Test the CustomUserCreationForm email functionality."""

    @override_settings(EMAIL_BACKEND='django.core.mail.backends.locmem.EmailBackend')
    def test_form_send_confirmation_email(self, user):
        """Test that form can send confirmation email."""
        # Clear any existing emails
        mail.outbox = []
        
        confirmation_url = "http://example.com/confirm/test-token"
        CustomUserCreationForm.send_confirmation_email(user, confirmation_url)
        
        # Check that email was sent
        assert len(mail.outbox) == 1
        email = mail.outbox[0]
        assert user.email in email.to
        assert confirmation_url in email.body

    def test_form_validation(self):
        """Test form validation."""
        form_data = {
            'username': 'testuser',
            'email': '<EMAIL>',
            'password1': 'complexpassword123',
            'password2': 'complexpassword123'
        }
        form = CustomUserCreationForm(data=form_data)
        assert form.is_valid()

    def test_form_password_mismatch(self):
        """Test form validation with password mismatch."""
        form_data = {
            'username': 'testuser',
            'email': '<EMAIL>',
            'password1': 'complexpassword123',
            'password2': 'differentpassword123'
        }
        form = CustomUserCreationForm(data=form_data)
        assert not form.is_valid()
        assert 'password2' in form.errors


@pytest.mark.django_db
class TestEmailBackendIntegration:
    """Test email backend integration with different configurations."""

    @override_settings(
        EMAIL_BACKEND='django.core.mail.backends.smtp.EmailBackend',
        EMAIL_HOST='localhost',
        EMAIL_PORT=1025,
        EMAIL_USE_TLS=False
    )
    @patch('smtplib.SMTP')
    def test_smtp_backend_configuration(self, mock_smtp, user):
        """Test SMTP backend configuration (for MailHog)."""
        mock_smtp_instance = MagicMock()
        mock_smtp.return_value = mock_smtp_instance
        
        confirmation_url = "http://example.com/confirm/test-token"
        notification = EmailConfirmationNotification(user, confirmation_url)
        
        try:
            notification.send()
            # If we get here, the SMTP configuration is correct
            assert True
        except Exception as e:
            # Expected in test environment without actual SMTP server
            assert 'Connection refused' in str(e) or 'Name or service not known' in str(e)

    @override_settings(EMAIL_BACKEND='django.core.mail.backends.console.EmailBackend')
    def test_console_backend(self, user, capsys):
        """Test console email backend."""
        confirmation_url = "http://example.com/confirm/test-token"
        notification = EmailConfirmationNotification(user, confirmation_url)
        
        notification.send()
        
        # Capture console output
        captured = capsys.readouterr()
        assert user.email in captured.out
        assert confirmation_url in captured.out

    @override_settings(EMAIL_BACKEND='django.core.mail.backends.locmem.EmailBackend')
    def test_locmem_backend(self, user):
        """Test local memory email backend."""
        mail.outbox = []
        
        confirmation_url = "http://example.com/confirm/test-token"
        notification = EmailConfirmationNotification(user, confirmation_url)
        
        notification.send()
        
        assert len(mail.outbox) == 1
        email = mail.outbox[0]
        assert user.email in email.to


@pytest.mark.django_db
class TestEmailTemplates:
    """Test email template rendering."""

    def test_email_confirmation_template_context(self, user):
        """Test that email confirmation template has correct context."""
        confirmation_url = "http://example.com/confirm/test-token"
        notification = EmailConfirmationNotification(user, confirmation_url)
        
        context = notification.context
        required_keys = ['user', 'confirmation_url', 'site_name', 'company_name', 'email_signature']
        
        for key in required_keys:
            assert key in context
        
        assert context['user'] == user
        assert context['confirmation_url'] == confirmation_url

    @override_settings(EMAIL_BACKEND='django.core.mail.backends.locmem.EmailBackend')
    def test_email_template_rendering(self, user):
        """Test that email templates render correctly."""
        mail.outbox = []
        
        confirmation_url = "http://example.com/confirm/test-token"
        notification = EmailConfirmationNotification(user, confirmation_url)
        notification.send()
        
        assert len(mail.outbox) == 1
        email = mail.outbox[0]
        
        # Check that template variables were rendered
        assert user.username in email.body or user.email in email.body
        assert confirmation_url in email.body
        assert 'confirm' in email.body.lower()


@pytest.mark.django_db
class TestUserInvitationEmail:
    """Test user invitation email functionality."""

    @override_settings(EMAIL_BACKEND='django.core.mail.backends.locmem.EmailBackend')
    def test_user_invitation_notification(self, user, business, business_owner):
        """Test user invitation notification."""
        mail.outbox = []
        
        invitation_url = "http://example.com/invite/test-token"
        notification = UserInvitationNotification(
            user=user,
            business=business,
            invitation_url=invitation_url,
            invited_by=business_owner
        )
        
        notification.send()
        
        assert len(mail.outbox) == 1
        email = mail.outbox[0]
        assert user.email in email.to
        assert business.name in email.subject
        assert invitation_url in email.body
