{% load i18n %}

<c-layouts.base>
  <c-slot name="title">
    {% trans "Password Reset Sent" %}
  </c-slot>

  <c-slot name="content">
    <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div class="max-w-md w-full space-y-8">
        <div class="text-center">
          <div class="mx-auto h-12 w-auto flex justify-center">
            <svg class="h-12 w-12 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
          </div>
          <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
            {% trans "Check your email" %}
          </h2>
          <p class="mt-2 text-center text-sm text-gray-600">
            {% trans "We've sent you a password reset link. Please check your email and follow the instructions to reset your password." %}
          </p>
        </div>

        <div class="bg-green-50 border border-green-200 rounded-md p-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                  clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-green-800">
                {% trans "Email sent successfully" %}
              </h3>
              <div class="mt-2 text-sm text-green-700">
                <p>{% trans "If you don't see the email in your inbox, please check your spam folder." %}</p>
              </div>
            </div>
          </div>
        </div>

        <div class="text-center space-y-4">
          <a href="{% url 'accounts:login' %}"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
            {% trans "Back to Login" %}
          </a>

          <p class="text-sm text-gray-600">
            {% trans "Didn't receive the email?" %}
            <a href="{% url 'accounts:password_reset' %}" class="font-medium text-blue-600 hover:text-blue-500">
              {% trans "Try again" %}
            </a>
          </p>
        </div>
      </div>
    </div>
  </c-slot>
</c-layouts.base>
