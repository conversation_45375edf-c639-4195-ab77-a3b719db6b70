<c-layouts.dashboard>
	<c-slot name="title">
		{% trans '{{ event.event.title }} - Event Details' %}
	</c-slot>

	<c-slot name="main">
		<div class="container mx-auto px-4 py-8">
			<div class="max-w-6xl mx-auto">
				<!-- Header -->
				<div class="flex justify-between items-start mb-8">
					<div>
						<h1 class="text-3xl font-bold text-gray-900">{{ event.event.title }}</h1>
						<p class="text-gray-600 mt-2">{{ event.get_event_type_display }}</p>
					</div>
					<div class="flex space-x-3">
						<a href="{% url 'bookings:calendar' %}"
							class="text-gray-600 hover:text-gray-800 px-4 py-2 border border-gray-300 rounded-lg">
							← Back to Calendar
						</a>
						<a href="{% url 'tours:event_create' %}"
							class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
							Create New Event
						</a>
					</div>
				</div>

				<div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
					<!-- Main Event Details -->
					<div class="lg:col-span-2 space-y-6">
						<!-- Event Information Card -->
						<div class="bg-white rounded-lg shadow p-6">
							<h2 class="text-xl font-semibold text-gray-900 mb-4">Event Information</h2>

							<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
								<div>
									<label class="block text-sm font-medium text-gray-700">Event Type</label>
									<p class="mt-1 text-sm text-gray-900">{{ event.get_event_type_display }}</p>
								</div>

								<div>
									<label class="block text-sm font-medium text-gray-700">Business</label>
									<p class="mt-1 text-sm text-gray-900">{{ event.business.name }}</p>
								</div>

								{% if event.client %}
								<div>
									<label class="block text-sm font-medium text-gray-700">Client</label>
									<p class="mt-1 text-sm text-gray-900">
										<a href="{% url 'clients:client-detail' event.client.pk %}"
											class="text-blue-600 hover:text-blue-800">
											{{ event.client.get_full_name }}
										</a>
									</p>
								</div>
								{% endif %}

								{% if event.quote %}
								<div>
									<label class="block text-sm font-medium text-gray-700">Related Quote</label>
									<p class="mt-1 text-sm text-gray-900">
										<a href="{% url 'quotes:quote-detail' event.quote.id %}" class="text-blue-600 hover:text-blue-800">
											{{ event.quote.quote_number }}
										</a>
									</p>
								</div>
								{% endif %}
							</div>

							{% if event.event.description %}
							<div class="mt-4">
								<label class="block text-sm font-medium text-gray-700">Description</label>
								<p class="mt-1 text-sm text-gray-900">{{ event.event.description }}</p>
							</div>
							{% endif %}
						</div>

						<!-- Schedule Information -->
						<div class="bg-white rounded-lg shadow p-6">
							<h2 class="text-xl font-semibold text-gray-900 mb-4">Schedule</h2>

							<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
								{% if event.start_time %}
								<div>
									<label class="block text-sm font-medium text-gray-700">Start Date & Time</label>
									<p class="mt-1 text-sm text-gray-900">
										{{ event.start_time|date:"F j, Y" }} at {{ event.start_time|time:"g:i A" }}
									</p>
								</div>
								{% endif %}

								{% if event.end_time %}
								<div>
									<label class="block text-sm font-medium text-gray-700">End Date & Time</label>
									<p class="mt-1 text-sm text-gray-900">
										{{ event.end_time|date:"F j, Y" }} at {{ event.end_time|time:"g:i A" }}
									</p>
								</div>
								{% endif %}

								{% if event.meeting_point %}
								<div class="md:col-span-2">
									<label class="block text-sm font-medium text-gray-700">Meeting Point</label>
									<p class="mt-1 text-sm text-gray-900">{{ event.meeting_point }}</p>
								</div>
								{% endif %}
							</div>
						</div>

						<!-- Capacity and Pricing -->
						<div class="bg-white rounded-lg shadow p-6">
							<h2 class="text-xl font-semibold text-gray-900 mb-4">Capacity & Pricing</h2>

							<div class="grid grid-cols-1 md:grid-cols-3 gap-4">
								<div>
									<label class="block text-sm font-medium text-gray-700">Max Participants</label>
									<p class="mt-1 text-sm text-gray-900">{{ event.max_participants }}</p>
								</div>

								<div>
									<label class="block text-sm font-medium text-gray-700">Current Participants</label>
									<p class="mt-1 text-sm text-gray-900">{{ event.current_participants }}</p>
								</div>

								<div>
									<label class="block text-sm font-medium text-gray-700">Available Spots</label>
									<p class="mt-1 text-sm text-gray-900 {% if event.available_spots == 0 %}text-red-600{% endif %}">
										{{ event.available_spots }}
									</p>
								</div>

								{% if event.price_per_person %}
								<div>
									<label class="block text-sm font-medium text-gray-700">Price per Person</label>
									<p class="mt-1 text-sm text-gray-900">${{ event.price_per_person }}</p>
								</div>
								{% endif %}
							</div>
						</div>

						{% if event.special_instructions %}
						<!-- Special Instructions -->
						<div class="bg-white rounded-lg shadow p-6">
							<h2 class="text-xl font-semibold text-gray-900 mb-4">Special Instructions</h2>
							<p class="text-sm text-gray-900">{{ event.special_instructions }}</p>
						</div>
						{% endif %}
					</div>

					<!-- Sidebar -->
					<div class="space-y-6">
						<!-- Status Card -->
						<div class="bg-white rounded-lg shadow p-6">
							<h3 class="text-lg font-semibold text-gray-900 mb-4">Status</h3>

							<div class="space-y-3">
								<div class="flex items-center justify-between">
									<span class="text-sm text-gray-700">Confirmed</span>
									<span
										class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                {% if event.is_confirmed %}bg-green-100 text-green-800{% else %}bg-yellow-100 text-yellow-800{% endif %}">
										{% if event.is_confirmed %}Yes{% else %}Pending{% endif %}
									</span>
								</div>

								<div class="flex items-center justify-between">
									<span class="text-sm text-gray-700">Cancelled</span>
									<span
										class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                {% if event.is_cancelled %}bg-red-100 text-red-800{% else %}bg-green-100 text-green-800{% endif %}">
										{% if event.is_cancelled %}Yes{% else %}No{% endif %}
									</span>
								</div>

								<div class="flex items-center justify-between">
									<span class="text-sm text-gray-700">Capacity</span>
									<span
										class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                {% if event.is_full %}bg-red-100 text-red-800{% else %}bg-green-100 text-green-800{% endif %}">
										{% if event.is_full %}Full{% else %}Available{% endif %}
									</span>
								</div>
							</div>
						</div>

						<!-- Quick Actions -->
						<div class="bg-white rounded-lg shadow p-6">
							<h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>

							<div class="space-y-3">
								<a href="{% url 'bookings:booking_create' %}?tour_event={{ event.pk }}"
									class="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-center block">
									Create Booking
								</a>

								<a href="#"
									class="w-full bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors text-center block">
									Edit Event
								</a>

								{% if not event.is_cancelled %}
								<button class="w-full bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors">
									Cancel Event
								</button>
								{% endif %}
							</div>
						</div>

						<!-- Event Metadata -->
						<div class="bg-white rounded-lg shadow p-6">
							<h3 class="text-lg font-semibold text-gray-900 mb-4">Event Details</h3>

							<div class="space-y-2 text-sm">
								<div class="flex justify-between">
									<span class="text-gray-700">Created</span>
									<span class="text-gray-900">{{ event.created_at|date:"M j, Y" }}</span>
								</div>

								<div class="flex justify-between">
									<span class="text-gray-700">Updated</span>
									<span class="text-gray-900">{{ event.updated_at|date:"M j, Y" }}</span>
								</div>

								{% if event.created_by %}
								<div class="flex justify-between">
									<span class="text-gray-700">Created by</span>
									<span
										class="text-gray-900">{{ event.created_by.get_full_name|default:event.created_by.username }}</span>
								</div>
								{% endif %}
							</div>
						</div>
					</div>
				</div>

				<!-- Related Bookings -->
				{% if event.bookings.exists %}
				<div class="mt-8">
					<div class="bg-white rounded-lg shadow">
						<div class="px-6 py-4 border-b border-gray-200">
							<h2 class="text-xl font-semibold text-gray-900">Related Bookings</h2>
						</div>

						<div class="overflow-x-auto">
							<table class="min-w-full divide-y divide-gray-200">
								<thead class="bg-gray-50">
									<tr>
										<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
											Booking Reference
										</th>
										<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
											Client
										</th>
										<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
											Participants
										</th>
										<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
											Status
										</th>
										<th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
											Actions
										</th>
									</tr>
								</thead>
								<tbody class="bg-white divide-y divide-gray-200">
									{% for booking in event.bookings.all %}
									<tr>
										<td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
											<a href="{% url 'bookings:booking_detail' booking.pk %}"
												class="text-blue-600 hover:text-blue-800">
												{{ booking.booking_reference }}
											</a>
										</td>
										<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
											{{ booking.client.get_full_name }}
										</td>
										<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
											{{ booking.number_of_participants }}
										</td>
										<td class="px-6 py-4 whitespace-nowrap">
											<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        {% if booking.status == 'confirmed' %}bg-green-100 text-green-800
                                        {% elif booking.status == 'pending' %}bg-yellow-100 text-yellow-800
                                        {% elif booking.status == 'cancelled' %}bg-red-100 text-red-800
                                        {% else %}bg-gray-100 text-gray-800{% endif %}">
												{{ booking.get_status_display }}
											</span>
										</td>
										<td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
											<a href="{% url 'bookings:booking_detail' booking.pk %}"
												class="text-blue-600 hover:text-blue-800">
												View
											</a>
										</td>
									</tr>
									{% endfor %}
								</tbody>
							</table>
						</div>
					</div>
				</div>
				{% endif %}
			</div>
		</div>
	</c-slot>
</c-layouts.dashboard>
