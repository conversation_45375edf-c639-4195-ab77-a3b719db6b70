# Generated by Django 5.2.4 on 2025-08-12 11:35

import django.core.validators
import django.db.models.deletion
import djmoney.models.fields
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('businesses', '0001_initial'),
        ('clients', '0001_initial'),
        ('quotes', '0001_initial'),
        ('tours', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Booking',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, help_text='Unique identifier for this record', primary_key=True, serialize=False, unique=True)),
                ('booking_reference', models.CharField(help_text='Unique booking reference number', max_length=20, unique=True)),
                ('number_of_participants', models.PositiveSmallIntegerField(default=1, help_text='Number of participants for this booking', validators=[django.core.validators.MinValueValidator(1)])),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('confirmed', 'Confirmed'), ('cancelled', 'Cancelled'), ('completed', 'Completed'), ('no_show', 'No Show')], default='pending', help_text='Current status of the booking', max_length=20)),
                ('payment_status', models.CharField(choices=[('unpaid', 'Unpaid'), ('deposit_paid', 'Deposit Paid'), ('fully_paid', 'Fully Paid'), ('refunded', 'Refunded')], default='unpaid', max_length=20)),
                ('booking_date', models.DateTimeField(auto_now_add=True, help_text='Date and time when booking was made')),
                ('confirmation_date', models.DateTimeField(blank=True, help_text='Date and time when booking was confirmed', null=True)),
                ('total_amount_currency', djmoney.models.fields.CurrencyField(choices=[('AUD', 'Australian Dollar'), ('BRL', 'Brazilian Real'), ('GBP', 'British Pound'), ('CAD', 'Canadian Dollar'), ('CNY', 'Chinese Yuan'), ('EUR', 'Euro'), ('INR', 'Indian Rupee'), ('JPY', 'Japanese Yen'), ('MXN', 'Mexican Peso'), ('CHF', 'Swiss Franc'), ('TZS', 'Tanzanian Shilling'), ('USD', 'US Dollar')], default='TZS', editable=False, max_length=3)),
                ('total_amount', djmoney.models.fields.MoneyField(decimal_places=2, help_text='Total amount for this booking', max_digits=10)),
                ('deposit_amount_currency', djmoney.models.fields.CurrencyField(choices=[('AUD', 'Australian Dollar'), ('BRL', 'Brazilian Real'), ('GBP', 'British Pound'), ('CAD', 'Canadian Dollar'), ('CNY', 'Chinese Yuan'), ('EUR', 'Euro'), ('INR', 'Indian Rupee'), ('JPY', 'Japanese Yen'), ('MXN', 'Mexican Peso'), ('CHF', 'Swiss Franc'), ('TZS', 'Tanzanian Shilling'), ('USD', 'US Dollar')], default='TZS', editable=False, max_length=3, null=True)),
                ('deposit_amount', djmoney.models.fields.MoneyField(blank=True, decimal_places=2, help_text='Deposit amount paid', max_digits=10, null=True)),
                ('deposit_paid', models.BooleanField(default=False, help_text='Whether deposit has been paid')),
                ('full_payment_received', models.BooleanField(default=False, help_text='Whether full payment has been received')),
                ('special_requests', models.TextField(blank=True, help_text='Special requests or notes for this booking')),
                ('internal_notes', models.TextField(blank=True, help_text='Internal notes (not visible to client)')),
                ('cancellation_date', models.DateTimeField(blank=True, help_text='Date and time when booking was cancelled', null=True)),
                ('cancellation_reason', models.TextField(blank=True, help_text='Reason for cancellation')),
                ('refund_amount_currency', djmoney.models.fields.CurrencyField(choices=[('AUD', 'Australian Dollar'), ('BRL', 'Brazilian Real'), ('GBP', 'British Pound'), ('CAD', 'Canadian Dollar'), ('CNY', 'Chinese Yuan'), ('EUR', 'Euro'), ('INR', 'Indian Rupee'), ('JPY', 'Japanese Yen'), ('MXN', 'Mexican Peso'), ('CHF', 'Swiss Franc'), ('TZS', 'Tanzanian Shilling'), ('USD', 'US Dollar')], default='TZS', editable=False, max_length=3, null=True)),
                ('refund_amount', djmoney.models.fields.MoneyField(blank=True, decimal_places=2, help_text='Amount refunded upon cancellation', max_digits=10, null=True)),
                ('business', models.ForeignKey(help_text='Business this booking belongs to', on_delete=django.db.models.deletion.CASCADE, related_name='bookings', to='businesses.business')),
                ('client', models.ForeignKey(help_text='Client making the booking', on_delete=django.db.models.deletion.PROTECT, related_name='bookings', to='clients.client')),
                ('created_by', models.ForeignKey(help_text='User who created this record', on_delete=django.db.models.deletion.PROTECT, related_name='%(class)ss_created', to=settings.AUTH_USER_MODEL)),
                ('quote', models.ForeignKey(blank=True, help_text='Associated quote for this booking', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='bookings', to='quotes.quote')),
                ('tour_instance', models.ForeignKey(blank=True, help_text='Tour event being booked', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='bookings', to='tours.tourinstance')),
            ],
            options={
                'verbose_name': 'Booking',
                'verbose_name_plural': 'Bookings',
                'db_table': 'bookings_booking',
            },
        ),
        migrations.CreateModel(
            name='BookingParticipant',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, help_text='Unique identifier for this record', primary_key=True, serialize=False, unique=True)),
                ('is_primary_contact', models.BooleanField(default=False, help_text='Whether this participant is the primary contact')),
                ('special_requirements', models.TextField(blank=True, help_text='Special requirements for this participant')),
                ('checked_in', models.BooleanField(default=False, help_text='Whether participant has checked in')),
                ('check_in_time', models.DateTimeField(blank=True, help_text='Time when participant checked in', null=True)),
                ('no_show', models.BooleanField(default=False, help_text='Whether participant was a no-show')),
                ('booking', models.ForeignKey(help_text='Booking this participant belongs to', on_delete=django.db.models.deletion.CASCADE, related_name='participants', to='bookings.booking')),
                ('created_by', models.ForeignKey(help_text='User who created this record', on_delete=django.db.models.deletion.PROTECT, related_name='%(class)ss_created', to=settings.AUTH_USER_MODEL)),
                ('traveler', models.ForeignKey(help_text='Traveler participating in this booking', on_delete=django.db.models.deletion.CASCADE, related_name='booking_participants', to='clients.traveler')),
            ],
            options={
                'verbose_name': 'Booking Participant',
                'verbose_name_plural': 'Booking Participants',
                'db_table': 'bookings_booking_participant',
            },
        ),
        migrations.AddIndex(
            model_name='booking',
            index=models.Index(fields=['business', 'status'], name='bookings_bo_busines_621743_idx'),
        ),
        migrations.AddIndex(
            model_name='booking',
            index=models.Index(fields=['business', 'client'], name='bookings_bo_busines_9f8c9e_idx'),
        ),
        migrations.AddIndex(
            model_name='booking',
            index=models.Index(fields=['business', 'booking_date'], name='bookings_bo_busines_efa1a2_idx'),
        ),
        migrations.AddIndex(
            model_name='booking',
            index=models.Index(fields=['booking_reference'], name='bookings_bo_booking_8a7545_idx'),
        ),
        migrations.AddIndex(
            model_name='booking',
            index=models.Index(fields=['created_at'], name='bookings_bo_created_1720a2_idx'),
        ),
        migrations.AddIndex(
            model_name='bookingparticipant',
            index=models.Index(fields=['booking', 'is_primary_contact'], name='bookings_bo_booking_c29eb3_idx'),
        ),
        migrations.AddIndex(
            model_name='bookingparticipant',
            index=models.Index(fields=['booking', 'checked_in'], name='bookings_bo_booking_6a0858_idx'),
        ),
        migrations.AddIndex(
            model_name='bookingparticipant',
            index=models.Index(fields=['created_at'], name='bookings_bo_created_622514_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='bookingparticipant',
            unique_together={('booking', 'traveler')},
        ),
    ]
