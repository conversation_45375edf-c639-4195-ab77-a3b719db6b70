"""
Data generation services using model_bakery for creating realistic dummy data.
"""

import random
from datetime import timed<PERSON><PERSON>
from decimal import Decimal

from django.utils import timezone
from faker import Faker
from model_bakery import baker
from model_bakery.recipe import Recipe, seq

# Import app-specific data generators
from accounts.data_generation import AccountsDataGenerator
from accounts.models import User
from bookings.models import Booking, BookingParticipant
from businesses.data_generation import BusinessesDataGenerator
from businesses.models import Business
from clients.data_generation import ClientsDataGenerator
from clients.models import Client, Traveler
from invoices.data_generation import InvoicesDataGenerator
from invoices.models import Invoice
from payments.data_generation import PaymentsDataGenerator
from quotes.data_generation import QuotesDataGenerator
from quotes.models import Quote
from templates_manager.data_generation import TemplatesDataGenerator
from tours.bakers import TourCategoryGenerator
from tours.models import Occurrence, TourCategory, TourEvent

# Initialize Faker for realistic data generation
fake = Faker()


# Model Bakery Recipes for consistent data generation
user_recipe = Recipe(
    User,
    username=seq('user_'),
    email=fake.email,
    first_name=fake.first_name,
    last_name=fake.last_name,
    is_active=True,
    is_staff=False,  # Only for Django admin access
    is_superuser=False,
)

business_recipe = Recipe(
    Business,
    name=fake.company,
    description=fake.catch_phrase,
    email=fake.company_email,
    phone=fake.phone_number,
    website=fake.url,
    address_line1=fake.street_address,
    address_line2=fake.secondary_address,
    city=fake.city,
    state_province=fake.state,
    postal_code=fake.postcode,
    country=fake.country,
)

client_recipe = Recipe(
    Client,
    first_name=fake.first_name,
    last_name=fake.last_name,
    email=fake.email,
    phone=fake.phone_number,
    address_line1=fake.street_address,
    city=fake.city,
    country=fake.country,
)

traveler_recipe = Recipe(
    Traveler,
    first_name=fake.first_name,
    last_name=fake.last_name,
    email=fake.email,
    phone=fake.phone_number,
    date_of_birth=fake.date_of_birth,
    passport_number=fake.bothify(text='??#######'),
    nationality=fake.country,
    emergency_contact_name=fake.name,
    emergency_contact_phone=fake.phone_number,
)


tour_event_recipe = Recipe(
    TourEvent,
    max_participants=fake.random_int(min=2, max=20),
    current_participants=0,
    meeting_point=fake.address,
    special_instructions=fake.text,
    price_per_person=fake.pydecimal(left_digits=3, right_digits=2, positive=True),
    is_confirmed=fake.boolean,
)

quote_recipe = Recipe(
    Quote,
    title=fake.catch_phrase,
    description=fake.text,
    valid_until=fake.future_date,
)

booking_recipe = Recipe(
    Booking,
    special_requests=fake.text,
    deposit_paid=fake.boolean,
    full_payment_received=fake.boolean,
)

tour_category_recipe = Recipe(
    TourCategory,
)


class DataGeneratorService:
    """Service for generating dummy data using model_bakery."""

    def __init__(self):
        self.created_objects = {
            'users': [],
            'profiles': [],
            'businesses': [],
            'clients': [],
            'travelers': [],
            'bookings': [],
            'quotes': [],
            'invoices': [],
            'payments': [],
            'pdf_templates': [],
            'tour_categories': [],
            'tours': [],
        }

        # Initialize app-specific generators
        self.accounts_generator = AccountsDataGenerator()
        self.businesses_generator = BusinessesDataGenerator()
        self.clients_generator = ClientsDataGenerator()
        self.quotes_generator = QuotesDataGenerator()
        self.invoices_generator = InvoicesDataGenerator()
        self.payments_generator = PaymentsDataGenerator()
        self.templates_generator = TemplatesDataGenerator()
        self.tour_categories_generator = TourCategoryGenerator()

    def clear_all_data(self):
        """Clear all data from the database (except superusers)."""
        # Import models that might not be imported yet
        from accounts.models import Profile
        from payments.models import Payment
        from templates_manager.models import PDFTemplate

        # Clear in reverse dependency order
        Payment.objects.all().delete()
        BookingParticipant.objects.all().delete()
        Booking.objects.all().delete()
        TourEvent.objects.all().delete()
        Occurrence.objects.all().delete()
        Invoice.objects.all().delete()
        Quote.objects.all().delete()
        Traveler.objects.all().delete()
        Client.objects.all().delete()

        # Clear user.profile.business relationships before deleting businesses
        User.objects.filter(is_superuser=False).update(business=None)
        Business.objects.all().delete()
        Profile.objects.all().delete()

        # Delete all PDF templates (including system templates) to avoid foreign key issues
        PDFTemplate.objects.all().delete()

        # Delete non-superuser users
        User.objects.filter(is_superuser=False).delete()

        # Reset created objects tracking
        self.created_objects = {key: [] for key in self.created_objects.keys()}

    def generate_users(self, count=5):
        """Generate users with realistic data using model_bakery."""
        users = self.accounts_generator.generate_users(count)
        self.created_objects['users'].extend(users)
        return users

    def generate_profiles(self, users=None):
        """Generate profiles for users."""
        profiles = self.accounts_generator.update_profiles(users)
        self.created_objects['profiles'].extend(profiles)
        return profiles

    def generate_businesses(self, count=3, users=None):
        """Generate businesses with different types using model_bakery."""
        if not users:
            users = self.created_objects['users']

        businesses = self.businesses_generator.generate_businesses(count, users)
        self.created_objects['businesses'].extend(businesses)
        return businesses

    def generate_clients(self, count=10, businesses=None):
        """Generate clients for businesses using model_bakery."""
        if not businesses:
            businesses = self.created_objects['businesses']

        clients = self.clients_generator.generate_clients(count, businesses)
        self.created_objects['clients'].extend(clients)
        return clients

    def generate_travelers(self, count=20, clients=None):
        """Generate travelers for clients using model_bakery."""
        if not clients:
            clients = self.created_objects['clients']

        travelers = self.clients_generator.generate_travelers(count, clients)
        self.created_objects['travelers'].extend(travelers)
        return travelers

    def generate_events(self, count=15, businesses=None, clients=None):
        """Generate tour events using model_bakery."""
        if not businesses:
            businesses = self.created_objects['businesses']
        if not clients:
            clients = self.created_objects['clients']

        tours = []

        # Event title templates by type
        event_titles = {
            TourEvent.EventType.TOUR: [
                'Big Five Safari Experience',
                'Serengeti Wildlife Tour',
                'Masai Mara Adventure',
                'Cultural Village Visit',
                'Mountain Hiking Expedition',
                'City Walking Tour',
            ],
            TourEvent.EventType.MEETING: [
                'Client Consultation',
                'Trip Planning Session',
                'Welcome Briefing',
                'Safety Orientation',
                'Departure Meeting',
                'Group Introduction',
            ],
            TourEvent.EventType.CONSULTATION: [
                'Travel Planning Consultation',
                'Custom Itinerary Design',
                'Budget Discussion',
                'Special Requirements Review',
                'Travel Insurance Briefing',
            ],
            TourEvent.EventType.MAINTENANCE: [
                'Vehicle Maintenance',
                'Equipment Check',
                'Facility Inspection',
                'Safety Equipment Review',
                'Communication Systems Test',
            ],
            TourEvent.EventType.TRAINING: [
                'Guide Training Session',
                'Safety Protocol Training',
                'Customer Service Workshop',
                'Emergency Response Training',
                'Wildlife Knowledge Update',
            ],
        }

        # Valid event types for TourEvent
        valid_event_types = [
            TourEvent.EventType.TOUR,
            TourEvent.EventType.MEETING,
            TourEvent.EventType.CONSULTATION,
            TourEvent.EventType.MAINTENANCE,
            TourEvent.EventType.TRAINING,
        ]

        for i in range(count):
            business = random.choice(businesses)
            client = random.choice(
                clients + [None, None]
            )  # Some events without clients
            tour_event_type = random.choice(valid_event_types)

            # Create Custom Event with realistic data
            start_time = fake.future_datetime(
                end_date='+90d', tzinfo=timezone.get_current_timezone()
            )
            # Ensure start time is during business hours
            start_time = start_time.replace(
                hour=random.randint(8, 18), minute=random.choice([0, 30])
            )
            end_time = start_time + timedelta(hours=random.randint(2, 8))

            event = baker.make(
                Event,
                title=random.choice(event_titles[tour_event_type]),
                description=fake.text(max_nb_chars=200),
                event_type=tour_event_type,  # Use TourEvent.EventType directly
                created_by=business.created_by,
            )

            # Create Occurrence
            baker.make(
                Occurrence,
                event=event,
                start_time=start_time,
                end_time=end_time,
                created_by=business.created_by,
            )

            # Create TourEvent with realistic data
            tour_event = baker.make(
                TourEvent,
                business=business,
                event=event,
                event_type=tour_event_type,
                client=client,
                max_participants=fake.random_int(min=2, max=20),
                current_participants=0,
                meeting_point=fake.address(),
                special_instructions=fake.text(max_nb_chars=150),
                price_per_person=Decimal(str(fake.random_int(min=50, max=500))),
                is_confirmed=fake.boolean(chance_of_getting_true=70),
                created_by=business.created_by,
            )

            tours.append(tour_event)

        self.created_objects['tours'].extend(tours)
        return tours

    def generate_quotes(self, count=12, businesses=None, clients=None):
        """Generate quotes using model_bakery."""
        if not businesses:
            businesses = self.created_objects['businesses']
        if not clients:
            clients = self.created_objects['clients']

        quotes = self.quotes_generator.generate_quotes(count, businesses, clients)
        self.created_objects['quotes'].extend(quotes)
        return quotes

    def generate_bookings(self, count=20, events=None, clients=None, travelers=None):
        """Generate bookings with participants using model_bakery."""
        if not events:
            events = self.created_objects['events']
        if not clients:
            clients = self.created_objects['clients']
        if not travelers:
            travelers = self.created_objects['travelers']

        bookings = []

        for i in range(count):
            event = random.choice(events)
            # Filter clients by business
            business_clients = [c for c in clients if c.business == event.business]

            if not business_clients:
                continue

            client = random.choice(business_clients)

            # Check capacity
            max_participants = min(4, event.available_spots)
            if max_participants <= 0:
                continue
            participants_count = fake.random_int(min=1, max=max_participants)

            # Calculate realistic pricing
            total_amount = event.price_per_person * participants_count
            deposit_percentage = Decimal(
                str(fake.random_int(min=20, max=50) / 100)
            )  # 20-50%
            deposit_amount = total_amount * deposit_percentage

            booking = baker.make(
                Booking,
                business=event.business,
                client=client,
                tour_event=event,
                number_of_participants=participants_count,
                status=random.choice(list(Booking.Status)),
                total_amount=total_amount,
                deposit_amount=deposit_amount,
                deposit_paid=fake.boolean(chance_of_getting_true=80),
                full_payment_received=fake.boolean(chance_of_getting_true=60),
                special_requests=fake.text(max_nb_chars=100)
                if fake.boolean(chance_of_getting_true=30)
                else '',
                created_by=event.business.created_by,
            )

            # Update event participant count
            event.current_participants += participants_count
            event.save()

            # Add participants
            client_travelers = [t for t in travelers if t.client == client]
            if client_travelers:
                selected_travelers = random.sample(
                    client_travelers, min(participants_count, len(client_travelers))
                )

                for j, traveler in enumerate(selected_travelers):
                    baker.make(
                        BookingParticipant,
                        booking=booking,
                        traveler=traveler,
                        is_primary_contact=(j == 0),
                        special_requirements=fake.text(max_nb_chars=100)
                        if fake.boolean(chance_of_getting_true=20)
                        else '',
                        checked_in=fake.boolean(chance_of_getting_true=70)
                        if booking.status == Booking.Status.COMPLETED
                        else False,
                        no_show=fake.boolean(chance_of_getting_true=5)
                        if booking.status == Booking.Status.COMPLETED
                        else False,
                    )

            bookings.append(booking)

        self.created_objects['bookings'].extend(bookings)
        return bookings

    def generate_invoices(self, count=8, quotes=None):
        """Generate invoices from quotes using model_bakery."""
        if not quotes:
            quotes = self.created_objects['quotes']

        invoices = self.invoices_generator.generate_invoices(count, quotes)
        self.created_objects['invoices'].extend(invoices)
        return invoices

    def generate_payments(self, count=20, invoices=None):
        """Generate payments for invoices using model_bakery."""
        if not invoices:
            invoices = self.created_objects['invoices']

        payments = self.payments_generator.generate_payments(count, invoices)
        self.created_objects['payments'].extend(payments)
        return payments

    def generate_pdf_templates(self, count=12, businesses=None, users=None):
        """Generate PDF templates using model_bakery."""
        if not businesses:
            businesses = self.created_objects['businesses']
        if not users:
            users = self.created_objects['users']

        templates = self.templates_generator.generate_pdf_templates(
            count, businesses, users
        )
        self.created_objects['pdf_templates'].extend(templates)
        return templates

    def generate_complete_dataset(self, scale='medium'):
        """Generate a complete dataset with all related objects."""
        scales = {
            'small': {
                'users': 3,
                'businesses': 1,
                'clients': 5,
                'travelers': 8,
                'events': 6,
                'quotes': 4,
                'bookings': 8,
                'invoices': 3,
                'payments': 5,
                'pdf_templates': 6,
            },
            'medium': {
                'users': 5,
                'businesses': 2,
                'clients': 10,
                'travelers': 20,
                'events': 12,
                'quotes': 8,
                'bookings': 15,
                'invoices': 5,
                'payments': 12,
                'pdf_templates': 10,
            },
            'large': {
                'users': 8,
                'businesses': 3,
                'clients': 20,
                'travelers': 40,
                'events': 25,
                'quotes': 15,
                'bookings': 30,
                'invoices': 10,
                'payments': 25,
                'pdf_templates': 15,
            },
        }

        config = scales.get(scale, scales['medium'])

        # Generate in dependency order
        users = self.generate_users(config['users'])
        businesses = self.generate_businesses(config['businesses'], users)
        clients = self.generate_clients(config['clients'], businesses)
        travelers = self.generate_travelers(config['travelers'], clients)
        events = self.generate_events(config['events'], businesses, clients)
        quotes = self.generate_quotes(config['quotes'], businesses, clients)
        bookings = self.generate_bookings(
            config['bookings'], events, clients, travelers
        )
        invoices = self.generate_invoices(config['invoices'], quotes)
        payments = self.generate_payments(config['payments'], invoices)
        pdf_templates = self.generate_pdf_templates(
            config['pdf_templates'], businesses, users
        )

        return {
            'users': len(users),
            'businesses': len(businesses),
            'clients': len(clients),
            'travelers': len(travelers),
            'tours': len(tours),
            'quotes': len(quotes),
            'bookings': len(bookings),
            'invoices': len(invoices),
            'payments': len(payments),
            'pdf_templates': len(pdf_templates),
        }

    def get_generation_summary(self):
        """Get summary of generated objects."""
        return {key: len(objects) for key, objects in self.created_objects.items()}
