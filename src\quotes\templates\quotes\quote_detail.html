{% extends "base.html" %}

{% block title %}Quote {{ quote.quote_number }} - Internal{% endblock %}

{% block content %}
<div class="max-w-6xl mx-auto">
    <!-- Breadcrumb -->
    <nav class="flex mb-6" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3">
            <li class="inline-flex items-center">
                <a href="/" class="text-gray-700 hover:text-gray-900">Home</a>
            </li>
            <li>
                <div class="flex items-center">
                    <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd"
                            d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                            clip-rule="evenodd"></path>
                    </svg>
                    <a href="/admin/quotes/quote/" class="ml-1 text-gray-700 hover:text-gray-900 md:ml-2">Quotes</a>
                </div>
            </li>
            <li aria-current="page">
                <div class="flex items-center">
                    <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd"
                            d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                            clip-rule="evenodd"></path>
                    </svg>
                    <span class="ml-1 text-gray-500 md:ml-2">{{ quote.quote_number }}</span>
                </div>
            </li>
        </ol>
    </nav>

    <!-- Quote Header -->
    <div class="bg-white rounded-lg shadow p-6 mb-6">
        <div class="flex justify-between items-start mb-4">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">{{ quote.title }}</h1>
                <p class="text-lg text-gray-600">Quote #{{ quote.quote_number }}</p>
                <p class="text-sm text-gray-500">Client: {{ quote.client.display_name }}</p>
            </div>
            <div class="text-right">
                <p class="text-sm text-gray-500">Status</p>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                    {% if quote.status == 'approved' %}bg-green-100 text-green-800
                    {% elif quote.status == 'sent' %}bg-blue-100 text-blue-800
                    {% else %}bg-gray-100 text-gray-800{% endif %}">
                    {{ quote.get_status_display }}
                </span>
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Financial Details</h3>
                <dl class="space-y-2">
                    <div class="flex justify-between">
                        <dt class="text-sm text-gray-500">Subtotal:</dt>
                        <dd class="text-sm font-medium text-gray-900">{{ quote.subtotal }}</dd>
                    </div>
                    <div class="flex justify-between">
                        <dt class="text-sm text-gray-500">Tax:</dt>
                        <dd class="text-sm font-medium text-gray-900">{{ quote.tax_amount }}</dd>
                    </div>
                    <div class="flex justify-between border-t pt-2">
                        <dt class="text-base font-semibold text-gray-900">Total:</dt>
                        <dd class="text-base font-semibold text-gray-900">{{ quote.total_amount }}</dd>
                    </div>
                </dl>
            </div>

            <div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Validity & Dates</h3>
                <dl class="space-y-2">
                    <div class="flex justify-between">
                        <dt class="text-sm text-gray-500">Created:</dt>
                        <dd class="text-sm text-gray-900">{{ quote.created_at|date:"M d, Y" }}</dd>
                    </div>
                    <div class="flex justify-between">
                        <dt class="text-sm text-gray-500">Valid until:</dt>
                        <dd class="text-sm text-gray-900">{{ quote.valid_until|date:"M d, Y" }}</dd>
                    </div>
                    {% if is_expired %}
                    <p class="text-sm text-red-600 font-medium">This quote has expired</p>
                    {% endif %}
                </dl>
            </div>

            <div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Actions</h3>
                <div class="space-y-2">
                    <a href="{% url 'quotes:quote-public-view' quote.public_hash %}" target="_blank"
                        class="inline-flex items-center text-sm text-blue-600 hover:text-blue-800">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                        </svg>
                        View Public Quote
                    </a>

                    {% if quote.status == 'approved' and not has_invoice %}
                    <button onclick="createInvoice()"
                        class="block w-full bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-md transition-colors text-sm">
                        Create Invoice
                    </button>
                    {% endif %}
                </div>
            </div>
        </div>

        {% if quote.description %}
        <div class="mt-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Description</h3>
            <p class="text-gray-600">{{ quote.description|linebreaks }}</p>
        </div>
        {% endif %}
    </div>

    <!-- Invoice Information -->
    {% if has_invoice %}
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <div class="flex items-center">
            <svg class="w-6 h-6 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                </path>
            </svg>
            <div>
                <h3 class="text-lg font-semibold text-blue-900">Invoice Created</h3>
                <p class="text-blue-700">
                    Invoice <a href="{% url 'invoices:detail' invoice.id %}"
                        class="font-medium underline">{{ invoice.invoice_number }}</a>
                    has been created for this quote.
                </p>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_scripts %}
<script>
    function createInvoice() {
        if (!confirm('Are you sure you want to create an invoice for this quote?')) {
            return;
        }

        fetch('{% url "quotes:quote-create_invoice" quote.id %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Invoice created successfully!');
                    location.reload();
                } else {
                    alert('Error: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while creating the invoice.');
            });
    }
</script>
{% endblock %}
