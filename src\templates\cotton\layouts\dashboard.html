{% load i18n %}

<c-layouts.base>
  <c-slot name="title">{{ title }} - Tour Business Management</c-slot>

  <c-slot name="content">
    <div class="flex h-screen">
      <!-- Left Sidebar -->
      <c-sidebar />

      <!-- Main Content -->
      <div class="flex-1 flex flex-col overflow-hidden">
        <!-- Top Navigation Bar -->
        <header class="bg-white shadow-sm border-b h-16 flex items-center justify-between px-6">
          <div class="flex items-center">
            <button id="sidebar-toggle"
              class=" mr-2 p-2 rounded-lg text-blue-700 hover:text-white hover:bg-blue-700 transition-colors"
              onclick="toggleSidebar()" title="Toggle Sidebar">
              <i class="fa-solid fa-bars"></i>
            </button>
            <h1 class="text-xl font-semibold text-gray-900">
              {{ title|default:"Dashboard" }}
            </h1>
          </div>

          <!-- Top Navigation Actions -->
          <div class="flex items-center space-x-4">
            <!-- Notifications -->
            <button class="p-2 text-gray-400 hover:text-gray-600 transition-colors" title="Notifications">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M15 17h5l-5 5v-5zM10.5 3.5a6 6 0 0 1 6 6v2l1.5 3h-15l1.5-3v-2a6 6 0 0 1 6-6z"></path>
              </svg>
            </button>

            <!-- User Menu -->
            {% if user.is_authenticated %}
            <div class="relative">
              <button class="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 transition-colors"
                onclick="toggleUserMenu()">
                <div class="w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center">
                  <span
                    class="text-white text-sm font-medium">{{ user.get_full_name|default:user.username|slice:":1"|upper }}</span>
                </div>
                <span class="text-sm font-medium text-gray-700">{{ user.get_full_name|default:user.username }}</span>
                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
              </button>

              <!-- User Dropdown -->
              <div id="user-menu"
                class="hidden absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
                <div class="py-1">
                  <a href="{% url 'accounts:profile' %}"
                    class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                    <i class="fas fa-user mr-2"></i>Profile
                  </a>
                  {% if user.is_staff %}
                  <a href="/admin/" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                    <i class="fas fa-cog mr-2"></i>Admin
                  </a>
                  {% endif %}
                  <div class="border-t border-gray-100"></div>
                  <form action="{% url 'accounts:logout' %}" method="post" class="block">
                    {% csrf_token %}
                    <button type="submit" class="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100">
                      <i class="fas fa-sign-out-alt mr-2"></i>Logout
                    </button>
                  </form>
                </div>
              </div>
            </div>
            {% endif %}
          </div>
        </header>

        <!-- Page Content -->
        <main class="flex-1 overflow-y-auto p-6 bg-gray-50">
          {{ main }}
        </main>
      </div>
    </div>

    <!-- JavaScript for Top Navigation -->
    <script>
      function toggleUserMenu() {
        const menu = document.getElementById('user-menu');
        menu.classList.toggle('hidden');
      }

      // Close user menu when clicking outside
      document.addEventListener('click', function (event) {
        const userMenu = document.getElementById('user-menu');
        const userButton = event.target.closest('[onclick="toggleUserMenu()"]');

        if (!userButton && userMenu && !userMenu.contains(event.target)) {
          userMenu.classList.add('hidden');
        }
      });
    </script>
  </c-slot>
</c-layouts.base>
