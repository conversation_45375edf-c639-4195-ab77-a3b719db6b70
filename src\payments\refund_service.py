from django.utils import timezone
from bookings.models import Booking
from payments.services import PaymentProcessor


class RefundService:
    """Handles policy compliant refunds"""
    def __init__(self, booking):
        self.booking = booking
        self.rules = booking.tour_instance.tour_template.refund_rules

    def process_refund(self, strategy='AUTO'):
        """Main Refund orchestration"""
        if self.booking.payment_status == Booking.PaymentStatus.UNPAID:
            return {'amount': 0, 'reason': 'No payment received'}
        eligible_amount = self._calculate_eligible_amount()
        eligible_amount <=0 :
            raise RefundPolicyError('No refund eligible per policy')

        # process actual refundi via payment service
        refund_id = self._excecute_refund(eligible_amount)

        # record in the system
        Refund.objects.create(booking=self.booking,amount=eligible_amount,transaction_id=refund_id,policy_snapshot=self.rules)

        return {'amount': eligible_amount, 'transaction_id': refund_id}

    def _calculate_eligible_amount(self):
        """Core refund policy engine"""
        days_before_tour = (self.booking.tour_instance.date - timezone.now().date()).days

        # default: full refund if > 14 days out
        if days_before_tour > 14:
            return self.booking.total_paid

        # apply tiered policy
        for tier in self.rules.get('tiers', []):
            if days_before_tour >= tier['min_days']:
                return self.booking.total_paid * tier['refund_percentage'] / 100

        return 0 # default to no refund

    def _execute_refund(self, amount):
        """Delegate to payment service"""
        payment = self.booking.payments.order_by('-payment_date').first()
        processor = PaymentProcessor(method=payment.payment_method)
        return processor.refund(payment.transaction_id, amount)
