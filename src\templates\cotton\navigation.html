{% load i18n %}

<nav class="bg-white shadow-sm border-b sticky top-0 z-50 {{ class }}">
  <div class="max-w-7xl mx-auto px-3 sm:px-6 lg:px-8">
    <div class="flex justify-between items-center h-16">
      <!-- Logo/Brand - Mobile optimized -->
      <div class="flex items-center">
        <a href="{% url 'core:home' %}" class="flex items-center text-lg sm:text-xl font-bold text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-md px-2 py-1 transition-all duration-200">
          <i class="fas fa-route text-blue-500 mr-2"></i>
          <span class="hidden xs:inline">TourFlow</span>
          <span class="xs:hidden">TF</span>
        </a>
      </div>

      <!-- Desktop Navigation Links -->
      {% if user.is_authenticated %}
      <div class="hidden md:flex items-center space-x-4 lg:space-x-6">
        <a href="{% url 'core:dashboard' %}"
          class="flex items-center px-3 py-2 text-sm lg:text-base text-gray-700 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 {% if request.resolver_match.url_name == 'dashboard' or request.resolver_match.url_name == 'core:home' %}text-blue-600 bg-blue-50 font-medium{% endif %}">
          <i class="fas fa-tachometer-alt mr-2 w-4"></i>
          Dashboard
        </a>

        {% if user.profile.business or user.is_demo_user %}
        <a href="{% url 'clients:client-list' %}"
          class="flex items-center px-3 py-2 text-sm lg:text-base text-gray-700 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 {% if 'clients' in request.resolver_match.namespace %}text-blue-600 bg-blue-50 font-medium{% endif %}">
          <i class="fas fa-users mr-2 w-4"></i>
          Clients
        </a>
        <a href="{% url 'quotes:quote-list' %}"
          class="flex items-center px-3 py-2 text-sm lg:text-base text-gray-700 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 {% if 'quotes' in request.resolver_match.namespace %}text-blue-600 bg-blue-50 font-medium{% endif %}">
          <i class="fas fa-file-invoice-dollar mr-2 w-4"></i>
          Quotes
        </a>
        <a href="{% url 'invoices:list' %}"
          class="flex items-center px-3 py-2 text-sm lg:text-base text-gray-700 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 {% if 'invoices' in request.resolver_match.namespace %}text-blue-600 bg-blue-50 font-medium{% endif %}">
          <i class="fas fa-receipt mr-2 w-4"></i>
          Invoices
        </a>
        <a href="{% url 'bookings:booking_list' %}"
          class="flex items-center px-3 py-2 text-sm lg:text-base text-gray-700 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 {% if 'bookings' in request.resolver_match.namespace %}text-blue-600 bg-blue-50 font-medium{% endif %}">
          <i class="fas fa-calendar-check mr-2 w-4"></i>
          Bookings
        </a>

        {% if user.profile.role == 'business_owner' and not user.is_demo_user %}
        <a href="{% url 'accounts:user_management' %}"
          class="flex items-center px-3 py-2 text-sm lg:text-base text-gray-700 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
          <i class="fas fa-users-cog mr-2 w-4"></i>
          Team
        </a>
        <a href="{% url 'businesses:detail' user.profile.business.id %}"
          class="flex items-center px-3 py-2 text-sm lg:text-base text-gray-700 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
          <i class="fas fa-building mr-2 w-4"></i>
          Business
        </a>
        {% endif %}
        {% endif %}
      </div>
      {% endif %}

      <!-- User Menu -->
      <div class="flex items-center space-x-2 sm:space-x-4">
        {% if user.is_authenticated %}
        <!-- Desktop User Dropdown -->
        <div class="hidden md:block relative">
          <button type="button" onclick="toggleUserMenu()"
            class="flex items-center px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 min-h-[44px]">
            <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center mr-2 flex-shrink-0">
              <span class="text-white text-sm font-medium">
                {{ user.first_name.0|default:user.username.0|upper }}
              </span>
            </div>
            <span class="mr-2 text-sm lg:text-base hidden lg:inline">
              {{ user.first_name|default:user.username }}
            </span>
            {% if user.is_demo_user %}
            <span class="bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded-full mr-2 hidden xl:inline">DEMO</span>
            {% endif %}
            <svg class="w-4 h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </button>

          <div id="user-menu" class="hidden absolute right-0 mt-2 w-56 bg-white rounded-lg shadow-lg py-1 z-50 border border-gray-200">
            <!-- User Info -->
            <div class="px-4 py-3 border-b border-gray-100">
              <p class="text-sm font-medium text-gray-900 truncate">
                {{ user.profile.get_full_name|default:user.username|title }}
              </p>
              {% if show_business_info and user.profile.business %}
              <p class="text-xs text-gray-500 truncate">{{ user.profile.business.name }}</p>
              {% endif %}
              {% if user.is_demo_user %}
              <p class="text-xs text-purple-600 font-medium">Demo Session Active</p>
              {% endif %}
            </div>

            <!-- Menu Items -->
            <a href="{% url 'accounts:profile' %}"
              class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-100 transition-colors min-h-[44px]">
              <i class="fas fa-user mr-3 w-4 flex-shrink-0"></i>
              Profile
            </a>

            {% if user.profile.business and not user.is_demo_user %}
            <a href="{% url 'payments:subscription_dashboard' %}"
              class="flex items-center px-4 py-3 text-sm text-purple-600 hover:bg-purple-50 transition-colors min-h-[44px]">
              <i class="fas fa-credit-card mr-3 w-4 flex-shrink-0"></i>
              Subscription
            </a>
            {% endif %}

            {% if user.is_staff %}
            <a href="/admin/" class="flex items-center px-4 py-3 text-sm text-blue-600 hover:bg-blue-50 transition-colors min-h-[44px]">
              <i class="fas fa-cog mr-3 w-4 flex-shrink-0"></i>
              Admin
            </a>
            {% endif %}

            {% if user.is_demo_user %}
            <div class="border-t border-gray-100"></div>
            <button onclick="endDemo()"
              class="flex items-center w-full px-4 py-3 text-sm text-orange-600 hover:bg-orange-50 transition-colors min-h-[44px]">
              <i class="fas fa-stop-circle mr-3 w-4 flex-shrink-0"></i>
              End Demo Session
            </button>
            {% endif %}

            <div class="border-t border-gray-100"></div>
            <form action="{% url 'accounts:logout' %}" method="post" class="block">
              {% csrf_token %}
              <button type="submit" class="flex items-center w-full px-4 py-3 text-sm text-red-600 hover:bg-red-50 transition-colors min-h-[44px]">
                <i class="fas fa-sign-out-alt mr-3 w-4 flex-shrink-0"></i>
                Logout
              </button>
            </form>
          </div>
        </div>
        {% else %}
        <div class="hidden md:flex items-center space-x-3">
          <a href="{% url 'accounts:login' %}" class="px-4 py-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 min-h-[44px] flex items-center">Login</a>
          <a href="{% url 'accounts:register' %}" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 min-h-[44px] flex items-center">Register</a>
        </div>
        {% endif %}
      </div>

      <!-- Mobile menu button -->
      <div class="md:hidden">
        <button type="button"
                id="mobile-menu-button"
                class="flex items-center justify-center w-10 h-10 text-gray-700 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                onclick="toggleMobileMenu()"
                aria-label="Toggle mobile menu"
                aria-expanded="false">
          <i class="fas fa-bars text-lg" id="mobile-menu-icon"></i>
        </button>
      </div>
    </div>

    <!-- Mobile menu overlay -->
    <div id="mobile-menu-overlay" class="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm hidden z-40 md:hidden"></div>

    <!-- Mobile menu -->
    <div id="mobile-menu" class="fixed top-0 left-0 h-full w-80 max-w-sm bg-white shadow-xl transform -translate-x-full transition-transform duration-300 ease-in-out z-50 overflow-y-auto md:hidden">
      <div class="p-4">
        <!-- Mobile menu header -->
        <div class="flex items-center justify-between mb-6">
          <div class="flex items-center">
            <i class="fas fa-route text-blue-500 mr-2"></i>
            <span class="text-lg font-bold text-gray-900">TourFlow</span>
          </div>
          <button type="button"
                  id="mobile-menu-close"
                  class="flex items-center justify-center w-10 h-10 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                  aria-label="Close mobile menu">
            <i class="fas fa-times text-lg"></i>
          </button>
        </div>

        <!-- Mobile menu content -->
        {% if user.is_authenticated %}
        <!-- User info section -->
        <div class="mb-6 p-3 bg-gray-50 rounded-lg">
          <div class="flex items-center">
            <div class="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
              <span class="text-white text-sm font-medium">
                {{ user.first_name.0|default:user.username.0|upper }}
              </span>
            </div>
            <div class="flex-1 min-w-0">
              <p class="text-sm font-medium text-gray-900 truncate">
                {{ user.profile.get_full_name|default:user.username|title }}
              </p>
              {% if user.profile.business %}
              <p class="text-xs text-gray-500 truncate">{{ user.profile.business.name }}</p>
              {% endif %}
              {% if user.is_demo_user %}
              <span class="inline-block bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded-full mt-1">DEMO</span>
              {% endif %}
            </div>
          </div>
        </div>

        <div class="space-y-1">
          <a href="{% url 'core:dashboard' %}" class="flex items-center px-3 py-3 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors min-h-[44px] {% if request.resolver_match.url_name == 'dashboard' or request.resolver_match.url_name == 'core:home' %}bg-blue-50 text-blue-600{% endif %}">
            <i class="fas fa-tachometer-alt mr-3 w-5 flex-shrink-0"></i>
            Dashboard
          </a>

          {% if user.profile.business or user.is_demo_user %}
          <a href="{% url 'clients:client-list' %}" class="flex items-center px-3 py-3 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors min-h-[44px] {% if 'clients' in request.resolver_match.namespace %}bg-blue-50 text-blue-600{% endif %}">
            <i class="fas fa-users mr-3 w-5 flex-shrink-0"></i>
            Clients
          </a>
          <a href="{% url 'quotes:quote-list' %}" class="flex items-center px-3 py-3 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors min-h-[44px] {% if 'quotes' in request.resolver_match.namespace %}bg-blue-50 text-blue-600{% endif %}">
            <i class="fas fa-file-invoice-dollar mr-3 w-5 flex-shrink-0"></i>
            Quotes
          </a>
          <a href="{% url 'invoices:list' %}" class="flex items-center px-3 py-3 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors min-h-[44px] {% if 'invoices' in request.resolver_match.namespace %}bg-blue-50 text-blue-600{% endif %}">
            <i class="fas fa-receipt mr-3 w-5 flex-shrink-0"></i>
            Invoices
          </a>
          <a href="{% url 'bookings:booking_list' %}" class="flex items-center px-3 py-3 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors min-h-[44px] {% if 'bookings' in request.resolver_match.namespace %}bg-blue-50 text-blue-600{% endif %}">
            <i class="fas fa-calendar-check mr-3 w-5 flex-shrink-0"></i>
            Bookings
          </a>

          {% if user.profile.role == 'business_owner' and not user.is_demo_user %}
          <a href="{% url 'accounts:user_management' %}" class="flex items-center px-3 py-3 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors min-h-[44px]">
            <i class="fas fa-users-cog mr-3 w-5 flex-shrink-0"></i>
            Team
          </a>
          <a href="{% url 'businesses:detail' user.profile.business.id %}" class="flex items-center px-3 py-3 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors min-h-[44px]">
            <i class="fas fa-building mr-3 w-5 flex-shrink-0"></i>
            Business
          </a>
          {% endif %}
          {% endif %}

          <div class="border-t border-gray-200 my-4"></div>

          <a href="{% url 'accounts:profile' %}" class="flex items-center px-3 py-3 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors min-h-[44px]">
            <i class="fas fa-user mr-3 w-5 flex-shrink-0"></i>
            Profile
          </a>

          {% if user.profile.business and not user.is_demo_user %}
          <a href="{% url 'payments:subscription_dashboard' %}" class="flex items-center px-3 py-3 text-purple-600 hover:bg-purple-50 rounded-lg transition-colors min-h-[44px]">
            <i class="fas fa-credit-card mr-3 w-5 flex-shrink-0"></i>
            Subscription
          </a>
          {% endif %}

          {% if user.is_staff %}
          <a href="/admin/" class="flex items-center px-3 py-3 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors min-h-[44px]">
            <i class="fas fa-cog mr-3 w-5 flex-shrink-0"></i>
            Admin
          </a>
          {% endif %}

          {% if user.is_demo_user %}
          <button onclick="endDemo()" class="flex items-center w-full px-3 py-3 text-orange-600 hover:bg-orange-50 rounded-lg transition-colors min-h-[44px]">
            <i class="fas fa-stop-circle mr-3 w-5 flex-shrink-0"></i>
            End Demo Session
          </button>
          {% endif %}

          <form action="{% url 'accounts:logout' %}" method="post" class="block">
            {% csrf_token %}
            <button type="submit" class="flex items-center w-full px-3 py-3 text-red-600 hover:bg-red-50 rounded-lg transition-colors min-h-[44px]">
              <i class="fas fa-sign-out-alt mr-3 w-5 flex-shrink-0"></i>
              Logout
            </button>
          </form>
        </div>
        {% else %}
        <div class="space-y-3">
          <a href="{% url 'accounts:login' %}" class="flex items-center justify-center w-full px-4 py-3 text-blue-600 border border-blue-600 rounded-lg hover:bg-blue-50 transition-colors min-h-[44px]">
            Login
          </a>
          <a href="{% url 'accounts:register' %}" class="flex items-center justify-center w-full px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors min-h-[44px]">
            Register
          </a>
        </div>
        {% endif %}
      </div>
    </div>
  </div>
</nav>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Mobile menu elements
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');
    const mobileMenuOverlay = document.getElementById('mobile-menu-overlay');
    const mobileMenuClose = document.getElementById('mobile-menu-close');
    const mobileMenuIcon = document.getElementById('mobile-menu-icon');

    // Mobile menu functions
    function openMobileMenu() {
      if (mobileMenu && mobileMenuOverlay) {
        mobileMenu.style.transform = 'translateX(0)';
        mobileMenuOverlay.classList.remove('hidden');
        mobileMenuButton.setAttribute('aria-expanded', 'true');
        mobileMenuIcon.classList.remove('fa-bars');
        mobileMenuIcon.classList.add('fa-times');
        document.body.style.overflow = 'hidden';
      }
    }

    function closeMobileMenu() {
      if (mobileMenu && mobileMenuOverlay) {
        mobileMenu.style.transform = 'translateX(-100%)';
        mobileMenuOverlay.classList.add('hidden');
        mobileMenuButton.setAttribute('aria-expanded', 'false');
        mobileMenuIcon.classList.remove('fa-times');
        mobileMenuIcon.classList.add('fa-bars');
        document.body.style.overflow = '';
      }
    }

    function toggleMobileMenu() {
      const isOpen = mobileMenu && mobileMenu.style.transform === 'translateX(0px)';
      if (isOpen) {
        closeMobileMenu();
      } else {
        openMobileMenu();
      }
    }

    // Event listeners for mobile menu
    if (mobileMenuButton) {
      mobileMenuButton.addEventListener('click', toggleMobileMenu);
    }
    if (mobileMenuClose) {
      mobileMenuClose.addEventListener('click', closeMobileMenu);
    }
    if (mobileMenuOverlay) {
      mobileMenuOverlay.addEventListener('click', closeMobileMenu);
    }

    // Close menu when clicking on links
    if (mobileMenu) {
      const mobileMenuLinks = mobileMenu.querySelectorAll('a');
      mobileMenuLinks.forEach(link => {
        link.addEventListener('click', closeMobileMenu);
      });
    }

    // Handle escape key
    document.addEventListener('keydown', function(event) {
      if (event.key === 'Escape') {
        closeMobileMenu();
        // Also close user menu if open
        const userMenu = document.getElementById('user-menu');
        if (userMenu && !userMenu.classList.contains('hidden')) {
          userMenu.classList.add('hidden');
        }
      }
    });

    // Touch gestures for mobile menu
    let startX = 0;
    let currentX = 0;
    let isDragging = false;

    if (mobileMenu) {
      mobileMenu.addEventListener('touchstart', function(e) {
        startX = e.touches[0].clientX;
        isDragging = true;
      });

      mobileMenu.addEventListener('touchmove', function(e) {
        if (!isDragging) return;
        currentX = e.touches[0].clientX;
        const diffX = startX - currentX;

        if (diffX > 0) {
          const translateX = Math.min(diffX, mobileMenu.offsetWidth);
          mobileMenu.style.transform = `translateX(-${translateX}px)`;
        }
      });

      mobileMenu.addEventListener('touchend', function(e) {
        if (!isDragging) return;
        isDragging = false;

        const diffX = startX - currentX;
        if (diffX > 100) {
          closeMobileMenu();
        } else {
          mobileMenu.style.transform = 'translateX(0)';
        }
      });
    }
  });

  function toggleUserMenu() {
    const menu = document.getElementById('user-menu');
    if (menu) {
      menu.classList.toggle('hidden');
    }
  }

  // Close dropdowns when clicking outside
  document.addEventListener('click', function (event) {
    const userMenu = document.getElementById('user-menu');
    const userButton = event.target.closest('[onclick="toggleUserMenu()"]');

    if (!userButton && userMenu && !userMenu.contains(event.target)) {
      userMenu.classList.add('hidden');
    }
  });

  // Demo session functions
  function endDemo() {
    if (confirm('Are you sure you want to end your demo session? All demo data will be removed.')) {
      fetch('/demo/end/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
      })
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            window.location.href = data.redirect_url || '/';
          } else {
            alert('Error ending demo: ' + data.error);
          }
        })
        .catch(error => {
          console.error('Error:', error);
          alert('Failed to end demo session');
        });
    }
  }

  // Quick tour function for navigation
  function startQuickTour() {
    if (typeof introJs !== 'undefined') {
      introJs().setOptions({
        steps: [
          {
            element: '.navbar-brand',
            intro: "Welcome to TourFlow! This is your tour business management platform.",
            position: 'bottom'
          },
          {
            element: '[href*="dashboard"]',
            intro: "Your dashboard shows key business metrics and recent activity.",
            position: 'bottom'
          },
          {
            element: '[href*="clients"]',
            intro: "Manage all your customer profiles and contact information here.",
            position: 'bottom'
          },
          {
            element: '[href*="quotes"]',
            intro: "Create and track professional quotes for your tour packages.",
            position: 'bottom'
          },
          {
            element: '[href*="bookings"]',
            intro: "Schedule and manage tour bookings with your calendar system.",
            position: 'bottom'
          },
          {
            element: '[href*="invoices"]',
            intro: "Generate invoices and track payments for your business.",
            position: 'bottom'
          }
        ],
        nextLabel: 'Next →',
        prevLabel: '← Back',
        skipLabel: 'Skip',
        doneLabel: 'Got it!',
        showProgress: true
      }).start();
    } else {
      console.log('Intro.js not loaded');
    }
  }
</script>
