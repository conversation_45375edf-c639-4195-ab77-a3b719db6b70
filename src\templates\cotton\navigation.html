{% load i18n %}

<nav class="bg-white shadow-sm border-b {{ class }}">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex justify-between items-center h-16">
      <!-- Logo/Brand -->
      <div class="flex items-center">
        <a href="{% url 'core:home' %}" class="flex items-center text-xl font-bold text-gray-900">
          <i class="fas fa-route text-blue-500 mr-2"></i>
          TourFlow
        </a>
      </div>

      <!-- Navigation Links -->
      {% if user.is_authenticated %}
      <div class="hidden md:flex items-center space-x-6">
        <a href="{% url 'core:dashboard' %}"
          class="flex items-center text-gray-700 hover:text-blue-600 transition-colors {% if request.resolver_match.url_name == 'dashboard' or request.resolver_match.url_name == 'core:home' %}text-blue-600 font-medium{% endif %}">
          <i class="fas fa-tachometer-alt mr-1"></i>
          Dashboard
        </a>

        {% if user.profile.business or user.is_demo_user %}
        <a href="{% url 'clients:client-list' %}"
          class="flex items-center text-gray-700 hover:text-blue-600 transition-colors {% if 'clients' in request.resolver_match.namespace %}text-blue-600 font-medium{% endif %}">
          <i class="fas fa-users mr-1"></i>
          Clients
        </a>
        <a href="{% url 'quotes:quote-list' %}"
          class="flex items-center text-gray-700 hover:text-blue-600 transition-colors {% if 'quotes' in request.resolver_match.namespace %}text-blue-600 font-medium{% endif %}">
          <i class="fas fa-file-invoice-dollar mr-1"></i>
          Quotes
        </a>
        <a href="{% url 'invoices:list' %}"
          class="flex items-center text-gray-700 hover:text-blue-600 transition-colors {% if 'invoices' in request.resolver_match.namespace %}text-blue-600 font-medium{% endif %}">
          <i class="fas fa-receipt mr-1"></i>
          Invoices
        </a>
        <a href="{% url 'bookings:booking_list' %}"
          class="flex items-center text-gray-700 hover:text-blue-600 transition-colors {% if 'bookings' in request.resolver_match.namespace %}text-blue-600 font-medium{% endif %}">
          <i class="fas fa-calendar-check mr-1"></i>
          Bookings
        </a>

        {% if user.profile.role == 'business_owner' and not user.is_demo_user %}
        <a href="{% url 'accounts:user_management' %}"
          class="flex items-center text-gray-700 hover:text-blue-600 transition-colors">
          <i class="fas fa-users-cog mr-1"></i>
          Team
        </a>
        <a href="{% url 'businesses:detail' user.profile.business.id %}"
          class="flex items-center text-gray-700 hover:text-blue-600 transition-colors">
          <i class="fas fa-building mr-1"></i>
          Business
        </a>
        {% endif %}
        {% endif %}
      </div>
      {% endif %}

      <!-- User Menu -->
      <div class="flex items-center space-x-4">
        {% if user.is_authenticated %}
        <!-- User Dropdown -->
        <div class="relative">
          <button type="button" onclick="toggleUserMenu()"
            class=" flex items-center text-gray-700 hover:text-blue-600 focus:outline-none transition-colors">
            <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center mr-2">
              <span class="text-white text-sm font-medium">
                {{ user.first_name.0|default:user.username.0|upper }}
              </span>
            </div>
            <span class="mr-2">
              {{ user.first_name|default:user.username }}
            </span>
            {% if user.is_demo_user %}
            <span class="bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded-full mr-2">DEMO</span>
            {% endif %}
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </button>

          <div id="user-menu" class="hidden absolute right-0 mt-2 w-56 bg-white rounded-md shadow-lg py-1 z-50 border">
            <!-- User Info -->
            <div class="px-4 py-3 border-b border-gray-100">
              <p class="text-sm font-medium text-gray-900">
                {{ user.profile.get_full_name|default:user.username|title }}
              </p>
              {% if show_business_info and user.profile.business %}
              <p class="text-xs text-gray-500">{{ user.profile.business.name }}</p>
              {% endif %}
              {% if user.is_demo_user %}
              <p class="text-xs text-purple-600 font-medium">Demo Session Active</p>
              {% endif %}
            </div>

            <!-- Menu Items -->
            <a href="{% url 'accounts:profile' %}"
              class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
              <i class="fas fa-user mr-3 w-4"></i>
              Profile
            </a>

            {% if user.profile.business and not user.is_demo_user %}
            <a href="{% url 'payments:subscription_dashboard' %}"
              class="flex items-center px-4 py-2 text-sm text-purple-600 hover:bg-gray-100">
              <i class="fas fa-credit-card mr-3 w-4"></i>
              Subscription
            </a>
            {% endif %}

            {% if user.is_staff %}
            <a href="/admin/" class="flex items-center px-4 py-2 text-sm text-blue-600 hover:bg-gray-100">
              <i class="fas fa-cog mr-3 w-4"></i>
              Admin
            </a>
            {% endif %}

            {% if user.is_demo_user %}
            <div class="border-t border-gray-100"></div>
            <button onclick="endDemo()"
              class="flex items-center w-full px-4 py-2 text-sm text-orange-600 hover:bg-gray-100">
              <i class="fas fa-stop-circle mr-3 w-4"></i>
              End Demo Session
            </button>
            {% endif %}

            <div class="border-t border-gray-100"></div>
            <form action="{% url 'accounts:logout' %}" method="post" class="block">
              {% csrf_token %}
              <button type="submit" class="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-gray-100">
                <i class="fas fa-sign-out-alt mr-3 w-4"></i>
                Logout
              </button>
            </form>
          </div>
        </div>
        {% else %}
        <div class="flex items-center space-x-3">
          <a href="{% url 'accounts:login' %}" class="text-blue-600 hover:text-blue-800">Login</a>
          <a href="{% url 'accounts:register' %}" class="btn btn-primary">Register</a>
        </div>
        {% endif %}
      </div>

      <!-- Mobile menu button -->
      <div class="md:hidden">
        <button type="button" class="text-gray-700 hover:text-gray-900" onclick="toggleMobileMenu()">
          <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
          </svg>
        </button>
      </div>
    </div>

    <!-- Mobile menu -->
    <div id="mobile-menu" class="hidden md:hidden pb-4">
      {% if user.is_authenticated %}
      <div class="space-y-2">
        <a href="{% url 'core:dashboard' %}" class="block text-gray-700 hover:text-gray-900 py-2">Dashboard</a>

        {% if user.profile.business or user.is_demo_user %}
        <a href="{% url 'clients:client-list' %}" class="flex items-center text-gray-700 hover:text-blue-600 py-2">
          <i class="fas fa-users mr-2"></i>
          Clients
        </a>
        <a href="{% url 'quotes:quote-list' %}" class="flex items-center text-gray-700 hover:text-blue-600 py-2">
          <i class="fas fa-file-invoice-dollar mr-2"></i>
          Quotes
        </a>
        <a href="{% url 'invoices:list' %}" class="flex items-center text-gray-700 hover:text-blue-600 py-2">
          <i class="fas fa-receipt mr-2"></i>
          Invoices
        </a>
        <a href="{% url 'bookings:booking_list' %}" class="flex items-center text-gray-700 hover:text-blue-600 py-2">
          <i class="fas fa-calendar-check mr-2"></i>
          Bookings
        </a>

        {% if user.profile.role == 'business_owner' %}
        <a href="{% url 'accounts:user_management' %}" class="block text-gray-700 hover:text-gray-900 py-2">Team</a>
        <a href="{% url 'businesses:detail' user.profile.business.id %}"
          class="block text-gray-700 hover:text-gray-900 py-2">Business</a>
        {% endif %}
        {% endif %}

        <div class="border-t pt-2 mt-2">
          <a href="{% url 'accounts:profile' %}" class="block text-gray-600 hover:text-gray-800 py-2">Profile</a>

          {% if user.profile.business %}
          <a href="{% url 'payments:subscription_dashboard' %}"
            class="block text-purple-600 hover:text-purple-800 py-2">Subscription</a>
          {% endif %}

          {% if user.is_staff %}
          <a href="/admin/" class="block text-blue-600 hover:text-blue-800 py-2">Admin</a>
          {% endif %}

          <a href="{% url 'accounts:logout' %}" class="block text-red-600 hover:text-red-800 py-2">Logout</a>
        </div>
      </div>
      {% endif %}
    </div>
  </div>
</nav>

<script>
  function toggleMobileMenu() {
    const menu = document.getElementById('mobile-menu');
    menu.classList.toggle('hidden');
  }

  function toggleUserMenu() {
    const menu = document.getElementById('user-menu');
    menu.classList.toggle('hidden');
  }

  // Close dropdowns when clicking outside
  document.addEventListener('click', function (event) {
    const userMenu = document.getElementById('user-menu');
    const userButton = event.target.closest('[onclick="toggleUserMenu()"]');

    if (!userButton && userMenu && !userMenu.contains(event.target)) {
      userMenu.classList.add('hidden');
    }
  });

  // Demo session functions
  function endDemo() {
    if (confirm('Are you sure you want to end your demo session? All demo data will be removed.')) {
      fetch('/demo/end/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
      })
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            window.location.href = data.redirect_url || '/';
          } else {
            alert('Error ending demo: ' + data.error);
          }
        })
        .catch(error => {
          console.error('Error:', error);
          alert('Failed to end demo session');
        });
    }
  }

  // Quick tour function for navigation
  function startQuickTour() {
    if (typeof introJs !== 'undefined') {
      introJs().setOptions({
        steps: [
          {
            element: '.navbar-brand',
            intro: "Welcome to TourFlow! This is your tour business management platform.",
            position: 'bottom'
          },
          {
            element: '[href*="dashboard"]',
            intro: "Your dashboard shows key business metrics and recent activity.",
            position: 'bottom'
          },
          {
            element: '[href*="clients"]',
            intro: "Manage all your customer profiles and contact information here.",
            position: 'bottom'
          },
          {
            element: '[href*="quotes"]',
            intro: "Create and track professional quotes for your tour packages.",
            position: 'bottom'
          },
          {
            element: '[href*="bookings"]',
            intro: "Schedule and manage tour bookings with your calendar system.",
            position: 'bottom'
          },
          {
            element: '[href*="invoices"]',
            intro: "Generate invoices and track payments for your business.",
            position: 'bottom'
          }
        ],
        nextLabel: 'Next →',
        prevLabel: '← Back',
        skipLabel: 'Skip',
        doneLabel: 'Got it!',
        showProgress: true
      }).start();
    } else {
      console.log('Intro.js not loaded');
    }
  }
</script>
