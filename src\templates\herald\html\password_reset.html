{% extends "herald/html/base_email.html" %}
{% load i18n %}

{% block title %}{% trans "Password Reset Request" %} - {{ site_name }}{% endblock %}

{% block header %}
    <h2 style="color: #dc3545; margin: 10px 0;">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor" style="vertical-align: middle; margin-right: 8px;">
            <path d="M18 8h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2zM12 17c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zM15.1 8H8.9V6c0-1.71 1.39-3.1 3.1-3.1 1.71 0 3.1 1.39 3.1 3.1v2z"/>
        </svg>
        {% trans "Password Reset Request" %}
    </h2>
{% endblock %}

{% block content %}
    <p>{% trans "Hello" %} <strong>{{ user.get_full_name|default:user.username }}</strong>,</p>

    <p>{% trans "We received a request to reset the password for your account on" %} <strong>{{ site_name }}</strong>. {% trans "If this was you, click the button below to reset your password:" %}</p>

    <div style="text-align: center; margin: 30px 0;">
        <a href="{{ reset_url }}" class="button" style="color: white; text-decoration: none; font-weight: bold; font-size: 16px;">
            🔑 {% trans "Reset My Password" %}
        </a>
    </div>

    <div class="info-box">
        <p><strong>{% trans "Account Details:" %}</strong></p>
        <ul style="margin: 10px 0; padding-left: 20px;">
            <li><strong>{% trans "Username:" %}</strong> {{ user.get_username }}</li>
            <li><strong>{% trans "Email:" %}</strong> {{ email }}</li>
            <li><strong>{% trans "Request Time:" %}</strong> {% now "F j, Y \a\t g:i A T" %}</li>
        </ul>
    </div>

    <div class="warning-box">
        <p><strong>⚠️ {% trans "Security Notice:" %}</strong></p>
        <ul style="margin: 10px 0; padding-left: 20px;">
            <li>{% trans "This password reset link will expire in 24 hours for security reasons." %}</li>
            <li>{% trans "If you didn't request this password reset, you can safely ignore this email." %}</li>
            <li>{% trans "Your current password will remain unchanged until you complete the reset process." %}</li>
        </ul>
    </div>

    <p>{% trans "If the button above doesn't work, you can copy and paste the following link into your browser:" %}</p>
    <div style="background-color: #f8f9fa; padding: 15px; border-radius: 4px; border: 1px solid #dee2e6; margin: 15px 0;">
        <code style="word-break: break-all; font-family: 'Courier New', monospace; font-size: 14px;">{{ reset_url }}</code>
    </div>

    <div class="info-box">
        <p><strong>💡 {% trans "Need Help?" %}</strong></p>
        <p>{% trans "If you're having trouble with the password reset process or didn't request this change, please contact our support team immediately." %}</p>
    </div>

    <p>{% trans "Thank you for using" %} {{ site_name }}!</p>
{% endblock %}

{% block footer %}
    <p>{{ email_signature|linebreaks }}</p>
    <p>
        <small>
            {% trans "This is an automated security email from" %} {{ site_name }}.<br>
            {% trans "For security reasons, please do not reply to this email." %}<br>
            {% trans "If you need assistance, please contact our support team." %}
        </small>
    </p>
{% endblock %}
