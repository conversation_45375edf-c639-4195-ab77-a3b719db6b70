# API Documentation

The Tour Business Management System provides a comprehensive REST API for integration with external systems and mobile applications.

## Authentication

### Token Authentication

The API uses token-based authentication. Include the token in the Authorization header:

```http
Authorization: Token your-api-token-here
```

### Obtaining a Token

```http
POST /api/auth/token/
Content-Type: application/json

{
    "username": "your-username",
    "password": "your-password"
}
```

Response:
```json
{
    "token": "your-api-token-here",
    "user_id": 1,
    "email": "<EMAIL>"
}
```

## Base URL

All API endpoints are prefixed with `/api/v1/`

## Common Response Format

### Success Response
```json
{
    "success": true,
    "data": { ... },
    "message": "Operation completed successfully"
}
```

### Error Response
```json
{
    "success": false,
    "error": "Error message",
    "details": { ... }
}
```

### Pagination
```json
{
    "count": 100,
    "next": "http://api.example.com/api/v1/clients/?page=3",
    "previous": "http://api.example.com/api/v1/clients/?page=1",
    "results": [ ... ]
}
```

## Endpoints

### Businesses

#### List Businesses
```http
GET /api/v1/businesses/
```

#### Get Business Details
```http
GET /api/v1/businesses/{id}/
```

#### Create Business
```http
POST /api/v1/businesses/
Content-Type: application/json

{
    "name": "Adventure Tours Inc",
    "email": "<EMAIL>",
    "phone": "******-123-4567",
    "address": "123 Adventure St",
    "city": "Adventure City",
    "state_province": "AC",
    "country": "US",
    "postal_code": "12345",
    "website": "https://adventuretours.com",
    "description": "Premium adventure tour experiences"
}
```

### Clients

#### List Clients
```http
GET /api/v1/clients/
```

Query parameters:
- `search`: Search by name or email
- `business`: Filter by business ID
- `is_active`: Filter by active status

#### Get Client Details
```http
GET /api/v1/clients/{id}/
```

#### Create Client
```http
POST /api/v1/clients/
Content-Type: application/json

{
    "business": 1,
    "first_name": "John",
    "last_name": "Doe",
    "email": "<EMAIL>",
    "phone": "******-987-6543",
    "date_of_birth": "1985-06-15",
    "nationality": "US",
    "passport_number": "*********",
    "emergency_contact_name": "Jane Doe",
    "emergency_contact_phone": "******-987-6544",
    "dietary_restrictions": "Vegetarian",
    "medical_conditions": "None",
    "travel_preferences": "Adventure tours, hiking"
}
```

#### Update Client
```http
PUT /api/v1/clients/{id}/
PATCH /api/v1/clients/{id}/
```

### Quotes

#### List Quotes
```http
GET /api/v1/quotes/
```

Query parameters:
- `business`: Filter by business ID
- `client`: Filter by client ID
- `status`: Filter by status (draft, sent, approved, expired)
- `created_after`: Filter by creation date

#### Get Quote Details
```http
GET /api/v1/quotes/{id}/
```

#### Create Quote
```http
POST /api/v1/quotes/
Content-Type: application/json

{
    "business": 1,
    "client": 1,
    "title": "7-Day Adventure Package",
    "description": "Complete adventure tour package including hiking, rafting, and camping",
    "subtotal": "1500.00",
    "tax_amount": "150.00",
    "total_amount": "1650.00",
    "currency": "USD",
    "valid_until": "2024-12-31",
    "terms_conditions": "Payment required within 30 days of approval"
}
```

#### Send Quote
```http
POST /api/v1/quotes/{id}/send/
```

#### Approve Quote (Public)
```http
POST /api/v1/quotes/public/{public_hash}/approve/
```

### Invoices

#### List Invoices
```http
GET /api/v1/invoices/
```

Query parameters:
- `business`: Filter by business ID
- `client`: Filter by client ID
- `status`: Filter by status (draft, sent, paid, overdue)
- `due_date_before`: Filter by due date

#### Get Invoice Details
```http
GET /api/v1/invoices/{id}/
```

#### Create Invoice
```http
POST /api/v1/invoices/
Content-Type: application/json

{
    "business": 1,
    "client": 1,
    "quote": 1,
    "subtotal": "1500.00",
    "tax_amount": "150.00",
    "total_amount": "1650.00",
    "currency": "USD",
    "issue_date": "2024-01-15",
    "due_date": "2024-02-15",
    "payment_terms": "Net 30",
    "terms_conditions": "Payment due within 30 days"
}
```

#### Send Invoice
```http
POST /api/v1/invoices/{id}/send/
```

#### Mark Invoice as Paid
```http
POST /api/v1/invoices/{id}/mark-paid/
```

### Payments

#### List Payments
```http
GET /api/v1/payments/
```

#### Get Payment Details
```http
GET /api/v1/payments/{id}/
```

#### Create Payment Link
```http
POST /api/v1/payments/links/
Content-Type: application/json

{
    "invoice": 1,
    "expires_at": "2024-12-31T23:59:59Z"
}
```

### Bookings

#### List Tour Events
```http
GET /api/v1/bookings/events/
```

Query parameters:
- `business`: Filter by business ID
- `start_date_after`: Filter by start date
- `is_confirmed`: Filter by confirmation status

#### Get Tour Event Details
```http
GET /api/v1/bookings/events/{id}/
```

#### Create Tour Event
```http
POST /api/v1/bookings/events/
Content-Type: application/json

{
    "business": 1,
    "event": {
        "title": "Mountain Hiking Adventure",
        "description": "Full day mountain hiking experience",
        "start_time": "2024-06-15T08:00:00Z",
        "end_time": "2024-06-15T18:00:00Z"
    },
    "max_participants": 12,
    "price_per_person": "150.00",
    "currency": "USD",
    "meeting_point": "Adventure Center Parking Lot",
    "requirements": "Good physical condition required"
}
```

## Webhooks

### Lemon Squeezy Webhooks

The system handles webhooks from Lemon Squeezy for payment processing:

```http
POST /api/v1/payments/webhook/lemon-squeezy/
X-Signature: sha256=webhook-signature
Content-Type: application/json

{
    "meta": {
        "event_name": "order_created"
    },
    "data": {
        "id": "order-id",
        "attributes": { ... }
    }
}
```

Supported events:
- `order_created`: Payment completed
- `subscription_created`: Subscription started
- `subscription_cancelled`: Subscription cancelled

## Error Codes

| Code | Description |
|------|-------------|
| 400 | Bad Request - Invalid data |
| 401 | Unauthorized - Invalid token |
| 403 | Forbidden - Insufficient permissions |
| 404 | Not Found - Resource doesn't exist |
| 429 | Too Many Requests - Rate limit exceeded |
| 500 | Internal Server Error |

## Rate Limiting

API requests are limited to:
- 1000 requests per hour for authenticated users
- 100 requests per hour for unauthenticated users

Rate limit headers:
```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1640995200
```

## SDKs and Examples

### Python SDK

```python
import requests

class TourBusinessAPI:
    def __init__(self, base_url, token):
        self.base_url = base_url
        self.headers = {
            'Authorization': f'Token {token}',
            'Content-Type': 'application/json'
        }
    
    def get_clients(self, business_id=None):
        params = {'business': business_id} if business_id else {}
        response = requests.get(
            f'{self.base_url}/api/v1/clients/',
            headers=self.headers,
            params=params
        )
        return response.json()
    
    def create_quote(self, quote_data):
        response = requests.post(
            f'{self.base_url}/api/v1/quotes/',
            headers=self.headers,
            json=quote_data
        )
        return response.json()

# Usage
api = TourBusinessAPI('https://your-domain.com', 'your-token')
clients = api.get_clients(business_id=1)
```

### JavaScript SDK

```javascript
class TourBusinessAPI {
    constructor(baseUrl, token) {
        this.baseUrl = baseUrl;
        this.headers = {
            'Authorization': `Token ${token}`,
            'Content-Type': 'application/json'
        };
    }
    
    async getClients(businessId = null) {
        const params = businessId ? `?business=${businessId}` : '';
        const response = await fetch(`${this.baseUrl}/api/v1/clients/${params}`, {
            headers: this.headers
        });
        return response.json();
    }
    
    async createQuote(quoteData) {
        const response = await fetch(`${this.baseUrl}/api/v1/quotes/`, {
            method: 'POST',
            headers: this.headers,
            body: JSON.stringify(quoteData)
        });
        return response.json();
    }
}

// Usage
const api = new TourBusinessAPI('https://your-domain.com', 'your-token');
const clients = await api.getClients(1);
```

## Testing

Use the provided test endpoints to verify your integration:

```http
GET /api/v1/health/
```

Response:
```json
{
    "status": "healthy",
    "version": "1.0.0",
    "timestamp": "2024-01-15T10:30:00Z"
}
```

## Support

For API support:
- Documentation: This guide
- Postman Collection: Available in `/docs/postman/`
- Support Email: <EMAIL>
