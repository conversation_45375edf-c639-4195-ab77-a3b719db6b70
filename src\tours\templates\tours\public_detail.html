{% extends "base.html" %}
{% load static %}
{% block title %}{{ tour.name }} - Alpine Adventures{% endblock %}
{% block content %}
    <div class="min-h-screen bg-gray-50">
        <!-- Hero Section -->
        <div class="bg-gradient-to-r from-blue-600 to-green-600 text-white">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
                <div class="flex items-center mb-4">
                    <a href="{% url 'tours:public-catalog' %}"
                       class="text-blue-200 hover:text-white mr-4">← Back to Tours</a>
                </div>
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
                    <div>
                        <h1 class="text-4xl md:text-5xl font-bold mb-4">{{ tour.name }}</h1>
                        <p class="text-xl text-blue-100 mb-6">{{ tour.description }}</p>
                        <div class="flex flex-wrap gap-4 text-sm">
                            <span class="bg-blue-500 bg-opacity-50 px-3 py-1 rounded-full">📍 {{ tour.destination }}</span>
                            <span class="bg-blue-500 bg-opacity-50 px-3 py-1 rounded-full">⏱️ {{ tour.duration_days }} days</span>
                            <span class="bg-blue-500 bg-opacity-50 px-3 py-1 rounded-full">🏔️ {{ tour.get_difficulty_level_display }}</span>
                            <span class="bg-blue-500 bg-opacity-50 px-3 py-1 rounded-full">👥 Max {{ tour.max_group_size }} people</span>
                        </div>
                    </div>
                    <div class="bg-white bg-opacity-10 rounded-lg p-6">
                        <h3 class="text-xl font-semibold mb-4">Quick Booking</h3>
                        {% if can_book %}
                            <p class="text-blue-100 mb-4">Ready to book your adventure?</p>
                            <a href="#booking-section"
                               class="block w-full bg-orange-500 hover:bg-orange-600 text-white text-center py-3 px-6 rounded-lg font-semibold transition-colors">
                                Book Now
                            </a>
                        {% else %}
                            <p class="text-blue-200 mb-4">No upcoming dates available</p>
                            <button disabled
                                    class="block w-full bg-gray-400 text-white text-center py-3 px-6 rounded-lg font-semibold cursor-not-allowed">
                                Currently Unavailable
                            </button>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        <!-- Main Content -->
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Main Content -->
                <div class="lg:col-span-2">
                    <!-- Tour Overview -->
                    <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                        <h2 class="text-2xl font-bold text-gray-900 mb-4">Tour Overview</h2>
                        <p class="text-gray-700 leading-relaxed">{{ tour.description }}</p>
                    </div>
                    <!-- Itinerary -->
                    {% if tour.itinerary %}
                        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                            <h2 class="text-2xl font-bold text-gray-900 mb-4">Itinerary</h2>
                            <div class="prose max-w-none">{{ tour.itinerary|linebreaks }}</div>
                        </div>
                    {% endif %}
                    <!-- Inclusions & Exclusions -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                        {% if tour.inclusions %}
                            <div class="bg-white rounded-lg shadow-md p-6">
                                <h3 class="text-xl font-bold text-gray-900 mb-4 flex items-center">✅ What's Included</h3>
                                <div class="text-gray-700">{{ tour.inclusions|linebreaks }}</div>
                            </div>
                        {% endif %}
                        {% if tour.exclusions %}
                            <div class="bg-white rounded-lg shadow-md p-6">
                                <h3 class="text-xl font-bold text-gray-900 mb-4 flex items-center">❌ What's Not Included</h3>
                                <div class="text-gray-700">{{ tour.exclusions|linebreaks }}</div>
                            </div>
                        {% endif %}
                    </div>
                </div>
                <!-- Sidebar -->
                <div class="lg:col-span-1">
                    <!-- Available Dates -->
                    <div id="booking-section" class="bg-white rounded-lg shadow-md p-6 mb-6">
                        <h3 class="text-xl font-bold text-gray-900 mb-4">Available Dates</h3>
                        {% if instance_data %}
                            <div class="space-y-4">
                                {% for data in instance_data %}
                                    <div class="border border-gray-200 rounded-lg p-4">
                                        <div class="flex justify-between items-start mb-2">
                                            <div>
                                                <p class="font-semibold text-gray-900">{{ data.instance.date|date:"M d, Y" }}</p>
                                                {% if data.guides %}
                                                    <p class="text-sm text-gray-600">
                                                        Guide:
                                                        {% for guide in data.guides %}
                                                            {{ guide.guide.get_full_name }}
                                                            {% if not forloop.last %},{% endif %}
                                                        {% endfor %}
                                                    </p>
                                                {% endif %}
                                            </div>
                                            {% if data.is_sold_out %}
                                                <span class="bg-red-100 text-red-800 text-xs font-semibold px-2 py-1 rounded-full">Sold Out</span>
                                            {% elif data.is_urgent %}
                                                <span class="bg-orange-100 text-orange-800 text-xs font-semibold px-2 py-1 rounded-full">
                                                    Only {{ data.available_spots }} left!
                                                </span>
                                            {% else %}
                                                <span class="bg-green-100 text-green-800 text-xs font-semibold px-2 py-1 rounded-full">
                                                    {{ data.available_spots }} spots
                                                </span>
                                            {% endif %}
                                        </div>
                                        {% if not data.is_sold_out %}
                                            <a href="{% url 'bookings:booking-wizard' %}?tour_instance={{ data.instance.pk }}"
                                               class="block w-full bg-blue-600 hover:bg-blue-700 text-white text-center py-2 px-4 rounded-lg font-semibold transition-colors">
                                                Book This Date
                                            </a>
                                        {% else %}
                                            <button disabled
                                                    class="block w-full bg-gray-300 text-gray-500 text-center py-2 px-4 rounded-lg font-semibold cursor-not-allowed">
                                                Sold Out
                                            </button>
                                        {% endif %}
                                    </div>
                                {% endfor %}
                            </div>
                        {% else %}
                            <p class="text-gray-600">No upcoming dates available. Please check back later.</p>
                        {% endif %}
                    </div>
                    <!-- Tour Details -->
                    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                        <h3 class="text-xl font-bold text-gray-900 mb-4">Tour Details</h3>
                        <div class="space-y-3 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Duration:</span>
                                <span class="font-semibold">{{ tour.duration_days }} days</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Difficulty:</span>
                                <span class="font-semibold">{{ tour.get_difficulty_level_display }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Group Size:</span>
                                <span class="font-semibold">{{ tour.min_group_size }}-{{ tour.max_group_size }} people</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Activity:</span>
                                <span class="font-semibold">{{ tour.get_activity_type_display }}</span>
                            </div>
                        </div>
                    </div>
                    <!-- Contact Info -->
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <h3 class="text-xl font-bold text-gray-900 mb-4">Questions?</h3>
                        <p class="text-gray-600 mb-4">Contact {{ tour.business.name }} for more information.</p>
                        <div class="space-y-2 text-sm">
                            <p class="flex items-center">
                                📧 <span class="ml-2">{{ tour.business.email }}</span>
                            </p>
                            {% if tour.business.phone %}
                                <p class="flex items-center">
                                    📞 <span class="ml-2">{{ tour.business.phone }}</span>
                                </p>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            <!-- Related Tours -->
            {% if related_tours %}
                <div class="mt-12">
                    <h2 class="text-3xl font-bold text-gray-900 mb-8">You Might Also Like</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        {% for related_tour in related_tours %}
                            <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow overflow-hidden">
                                <div class="h-32 bg-gradient-to-r from-blue-400 to-green-400 flex items-center justify-center">
                                    <span class="text-white text-3xl">
                                        {% if related_tour.activity_type == 'hiking' %}
                                            🥾
                                        {% elif related_tour.activity_type == 'cycling' %}
                                            🚴
                                        {% elif related_tour.activity_type == 'wildlife' %}
                                            🦁
                                        {% elif related_tour.activity_type == 'cultural' %}
                                            🏛️
                                        {% elif related_tour.activity_type == 'adventure' %}
                                            🧗
                                        {% else %}
                                            🌟
                                        {% endif %}
                                    </span>
                                </div>
                                <div class="p-4">
                                    <h3 class="font-bold text-gray-900 mb-2">{{ related_tour.name }}</h3>
                                    <p class="text-gray-600 text-sm mb-3">📍 {{ related_tour.destination }}</p>
                                    <a href="{% url 'tours:public-detail' related_tour.pk %}"
                                       class="text-blue-600 hover:text-blue-800 font-semibold text-sm">
                                        View Details →
                                    </a>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
{% endblock %}
