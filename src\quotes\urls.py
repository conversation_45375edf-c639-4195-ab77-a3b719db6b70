"""
URL configuration for quotes app.
"""

from django.urls import path

from . import views

app_name = 'quotes'

urlpatterns = [
    # Quote list and CRUD operations
    path(
        '',
        views.QuoteListView.as_view(),
        name='quote-list',
    ),
    path(
        'create/',
        views.QuoteCreateView.as_view(),
        name='quote-create',
    ),
    path(
        '<uuid:pk>/edit/',
        views.QuoteUpdateView.as_view(),
        name='quote-edit',
    ),
    # Public quote view (accessible via hash)
    path(
        'public/<str:hash>/',
        views.PublicQuoteView.as_view(),
        name='quote-public-view',
    ),
    # Quote approval (public)
    path(
        'public/<str:hash>/approve/',
        views.approve_quote,
        name='quote-approve',
    ),
    # Internal quote views (require login)
    path(
        '<uuid:quote_id>/',
        views.quote_detail,
        name='quote-detail',
    ),
    path(
        '<uuid:quote_id>/create-invoice/',
        views.create_invoice_from_quote,
        name='quote-create-invoice',
    ),
]
