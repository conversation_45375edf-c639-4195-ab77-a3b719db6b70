"""
URL configuration for bookings app.
"""

from django.urls import path

from . import views

app_name = 'bookings'

urlpatterns = [
    # Dashboard
    path(
        '',
        views.BookingDashboardView.as_view(),
        name='dashboard',
    ),
    # Calendar views
    path(
        'calendar/',
        views.MonthlyCalendarView.as_view(),
        name='calendar',
    ),
    path(
        'calendar/<int:year>/',
        views.MonthlyCalendarView.as_view(),
        name='calendar_year',
    ),
    path(
        'calendar/<int:year>/<int:month>/',
        views.MonthlyCalendarView.as_view(),
        name='calendar_month',
    ),
    path(
        'calendar/week/',
        views.WeeklyCalendarView.as_view(),
        name='calendar_week',
    ),
    path(
        'calendar/week/<int:year>/<int:month>/<int:day>/',
        views.WeeklyCalendarView.as_view(),
        name='calendar_week_date',
    ),
    path(
        'calendar/day/',
        views.DailyCalendarView.as_view(),
        name='calendar_day',
    ),
    path(
        'calendar/day/<int:year>/<int:month>/<int:day>/',
        views.DailyCalendarView.as_view(),
        name='calendar_day_date',
    ),
    # Bookings
    path(
        'bookings/',
        views.BookingListView.as_view(),
        name='booking_list',
    ),
    path(
        'create/',
        views.BookingCreateView.as_view(),
        name='booking_create',
    ),
    path(
        '<uuid:pk>/',
        views.BookingDetailView.as_view(),
        name='booking_detail',
    ),
    path(
        '<uuid:pk>/edit/',
        views.BookingUpdateView.as_view(),
        name='booking_edit',
    ),
    path(
        '<uuid:pk>/confirm/',
        views.BookingConfirmView.as_view(),
        name='booking_confirm',
    ),
    path(
        '<uuid:pk>/cancel/',
        views.BookingCancelView.as_view(),
        name='booking_cancel',
    ),
    path(
        '<uuid:pk>/participants/',
        views.ParticipantManagementView.as_view(),
        name='participant_management',
    ),
]
