import uuid

from django.conf import settings
from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from phonenumber_field.phonenumber import PhoneNumber
from core.mixins import BaseModel, TimeAuditModel


class User(BaseModel, AbstractUser):
    """
    Custom user model extending Django's AbstractUser.
    Supports multi-tenant business structure with role-based access.
    """

    # Demo user flag
    is_demo_user = models.BooleanField(
        default=False,
        help_text=_('Whether this is a temporary demo user'),
    )

    # Email confirmation fields
    email_confirmed = models.BooleanField(
        default=False, help_text=_('Whether email has been confirmed')
    )
    email_confirmation_token = models.UUIDField(
        default=uuid.uuid4, editable=False, help_text=_('Token for email confirmation')
    )
    email_confirmation_sent_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text=_(
            'When confirmation email was sent',
        ),
    )

    # Profile completion tracking
    profile_completed = models.Bo<PERSON>anField(
        default=False,
        help_text=_(
            'Whether user has completed their profile',
        ),
    )
    onboarding_completed = models.BooleanField(
        default=False,
        help_text=_(
            'Whether user has completed onboarding',
        ),
    )
    is_active = models.BooleanField(default=True)

    class Meta:
        indexes = [
            models.Index(fields=['email']),
            models.Index(fields=['is_active']),
        ]

    def __str__(self):
        return f'{self.get_display_name()}'

    @property
    def full_name(self):
        """Return the user's full name from profile."""
        if hasattr(self, 'profile'):
            return self.profile.get_full_name()
        return self.username

    def get_display_name(self):
        """Get the best available display name for the user."""
        if hasattr(self, 'profile'):
            full_name = self.profile.get_full_name()
            if full_name:
                return full_name
        return self.username

    def save(self, *args, **kwargs):
        """Override save method for any custom logic."""
        # Note: is_staff is now only used for Django admin access
        # Business functionality is controlled by business membership and roles
        super().save(*args, **kwargs)

    def generate_confirmation_token(self):
        """Generate a new email confirmation token."""
        self.email_confirmation_token = uuid.uuid4()
        self.email_confirmation_sent_at = timezone.now()
        self.save(
            update_fields=['email_confirmation_token', 'email_confirmation_sent_at']
        )
        return self.email_confirmation_token

    def confirm_email(self):
        """Mark email as confirmed and activate user."""
        self.email_confirmed = True
        self.is_active = True
        # Set profile role to administrator for first registration
        if not self.profile.role or self.profile.role == self.profile.Role.CUSTOMER:
            self.profile.role = self.profile.Role.BUSINESS_OWNER
            self.profile.save()
        self.save(
            update_fields=['email_confirmed', 'is_active'],
        )

    def needs_onboarding(self):
        """Check if user needs to complete onboarding."""
        return (
            self.email_confirmed
            and not self.onboarding_completed
            and self.profile.role == self.profile.Role.BUSINESS_OWNER
        )

    def needs_profile_completion(self):
        """Check if user needs to complete their profile."""
        return self.email_confirmed and not self.profile_completed

    def has_business_access(self, business):
        """Check if user has access to a specific business."""
        if self.profile.role == self.profile.Role.BUSINESS_OWNER:
            return (
                hasattr(self.profile, 'business') and self.profile.business == business
            )
        elif self.profile.role == self.profile.Role.AGENT:
            return (
                hasattr(self.profile, 'business') and self.profile.business == business
            )
        return False


class Profile(TimeAuditModel):
    """
    Extended profile information for users.
    """

    class Role(models.TextChoices):
        BUSINESS_OWNER = 'business_owner', _('Business Owner')
        AGENT = 'agent', _('Agent')
        CUSTOMER = 'customer', _('Customer')

    class Department(models.TextChoices):
        SALES = 'sales', _('Sales')
        MARKETING = 'marketing', _('Marketing')

    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        related_name='profile',
        help_text=_('User this profile belongs to'),
    )

    # Business relationship
    business = models.ForeignKey(
        'businesses.Business',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='profiles',
        help_text=_('Business this profile belongs to'),
    )
    role = models.CharField(
        max_length=20,
        choices=Role.choices,
        default=Role.CUSTOMER,
        help_text=_('User role in the system'),
    )
    department = models.CharField(
        max_length=100,
        choices=Department.choices,
        blank=True,
        default=Department.SALES,
        help_text=_('Department'),
    )
    date_joined = models.DateTimeField(auto_now_add=True)

    first_name = models.CharField(
        max_length=30,
        blank=True,
        help_text=_('First name'),
    )
    last_name = models.CharField(
        max_length=30,
        blank=True,
        help_text=_('Last name'),
    )
    phone = models.CharField(
        max_length=20,
        blank=True,
        help_text=_('Contact phone number'),
    )
    avatar = models.ImageField(
        upload_to='avatars/',
        blank=True,
        null=True,
        help_text=_('Profile picture'),
    )

    # Preferences
    timezone = models.CharField(
        max_length=50,
        default=settings.TIME_ZONE,
        help_text=_("User's timezone"),
    )
    language = models.CharField(
        max_length=10,
        default='en',
        help_text=_('Preferred language'),
    )

    def __str__(self):
        return f'{self.user.get_full_name() or self.user.username} Profile'

    def is_complete(self):
        """Check if profile has minimum required information."""
        required_fields = [
            self.first_name,
            self.last_name,
            self.phone,
        ]
        return all(field.strip() for field in required_fields if field)

    def get_full_name(self):
        """Return the user's full name from profile."""
        if self.first_name and self.last_name:
            return f'{self.first_name} {self.last_name}'
        return self.first_name or self.last_name or ''
