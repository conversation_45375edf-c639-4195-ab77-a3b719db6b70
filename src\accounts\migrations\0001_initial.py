# Generated by Django 5.2.4 on 2025-08-12 11:35

import django.contrib.auth.models
import django.contrib.auth.validators
import django.utils.timezone
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='Profile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('role', models.CharField(choices=[('business_owner', 'Business Owner'), ('agent', 'Agent'), ('customer', 'Customer')], default='customer', help_text='User role in the system', max_length=20)),
                ('department', models.CharField(blank=True, choices=[('sales', 'Sales'), ('marketing', 'Marketing')], default='sales', help_text='Department', max_length=100)),
                ('date_joined', models.DateTimeField(auto_now_add=True)),
                ('first_name', models.CharField(blank=True, help_text='First name', max_length=30)),
                ('last_name', models.CharField(blank=True, help_text='Last name', max_length=30)),
                ('phone', models.CharField(blank=True, help_text='Contact phone number', max_length=20)),
                ('avatar', models.ImageField(blank=True, help_text='Profile picture', null=True, upload_to='avatars/')),
                ('timezone', models.CharField(default='Africa/Dar_es_Salaam', help_text="User's timezone", max_length=50)),
                ('language', models.CharField(default='en', help_text='Preferred language', max_length=10)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='User',
            fields=[
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('username', models.CharField(error_messages={'unique': 'A user with that username already exists.'}, help_text='Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.', max_length=150, unique=True, validators=[django.contrib.auth.validators.UnicodeUsernameValidator()], verbose_name='username')),
                ('first_name', models.CharField(blank=True, max_length=150, verbose_name='first name')),
                ('last_name', models.CharField(blank=True, max_length=150, verbose_name='last name')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='email address')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, help_text='Unique identifier for this record', primary_key=True, serialize=False, unique=True)),
                ('is_demo_user', models.BooleanField(default=False, help_text='Whether this is a temporary demo user')),
                ('email_confirmed', models.BooleanField(default=False, help_text='Whether email has been confirmed')),
                ('email_confirmation_token', models.UUIDField(default=uuid.uuid4, editable=False, help_text='Token for email confirmation')),
                ('email_confirmation_sent_at', models.DateTimeField(blank=True, help_text='When confirmation email was sent', null=True)),
                ('profile_completed', models.BooleanField(default=False, help_text='Whether user has completed their profile')),
                ('onboarding_completed', models.BooleanField(default=False, help_text='Whether user has completed onboarding')),
                ('is_active', models.BooleanField(default=True)),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.group', verbose_name='groups')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.permission', verbose_name='user permissions')),
            ],
            managers=[
                ('objects', django.contrib.auth.models.UserManager()),
            ],
        ),
    ]
