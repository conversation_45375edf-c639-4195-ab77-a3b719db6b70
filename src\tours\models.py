from datetime import <PERSON><PERSON><PERSON>

from django.core.validators import MinValueValidator
from django.db import models
from django.forms import ValidationError
from django.urls import reverse_lazy
from django.utils.translation import gettext_lazy as _
from djmoney.models.fields import MoneyField

from core.mixins import BaseModel, UserAuditModel


class TourCategory(BaseModel):
    """Organize tours by type (e.g., Adventure, Cultural)"""

    name = models.CharField(_('name'), max_length=64, unique=True)
    icon = models.CharField(_('icon'), max_length=32, blank=True)

    class Meta:
        verbose_name = _('Tour Category')
        verbose_name_plural = _('Tour Categories')

    def __str__(self):
        return self.name

    def get_absolute_url(self):
        return reverse_lazy(
            'tours:category-detail',
            kwargs={'pk': self.pk},
        )


class TourTemplate(UserAuditModel):
    """Master record for a tour type (e.g., 'Grand Canyon Hike')"""

    class DifficultyLevel(models.TextChoices):
        EASY = 'easy', _('Easy')
        MODERATE = 'moderate', _('Moderate')
        INTERMEDIATE = 'intermediate', _('Intermediate')
        CHALLENGING = 'challenging', _('Challenging')
        EXPERT = 'expert', _('Expert')

    class ActivityType(models.TextChoices):
        HIKING = 'hiking', _('Hiking')
        CULTURAL = 'cultural', _('Cultural')
        ADVENTURE = 'adventure', _('Adventure')
        WILDLIFE = 'wildlife', _('Wildlife')
        PHOTOGRAPHY = 'photography', _('Photography')
        CYCLING = 'cycling', _('Cycling')
        WATER_SPORTS = 'water_sports', _('Water Sports')
        CLIMBING = 'climbing', _('Climbing')
        SKIING = 'skiing', _('Skiing')
        OTHER = 'other', _('Other')

    # Business relationship (for multi-tenancy)
    business = models.ForeignKey(
        'businesses.Business',
        on_delete=models.CASCADE,
        related_name='tour_templates',
        help_text=_('Business this tour template belongs to'),
        null=True,  # Allow null for existing data
        blank=True,
    )

    name = models.CharField(_('name'), max_length=255)
    category = models.ForeignKey(TourCategory, on_delete=models.PROTECT)
    description = models.TextField(blank=True, null=True)

    # Search and filtering fields
    destination = models.CharField(
        _('destination'),
        max_length=255,
        help_text=_('Primary destination (e.g., "Swiss Alps", "Grand Canyon")'),
        blank=True,
    )
    activity_type = models.CharField(
        _('activity type'),
        max_length=20,
        choices=ActivityType.choices,
        default=ActivityType.HIKING,
        help_text=_('Primary activity type'),
    )
    difficulty_level = models.CharField(
        _('difficulty level'),
        max_length=20,
        choices=DifficultyLevel.choices,
        default=DifficultyLevel.MODERATE,
        help_text=_('Physical difficulty level'),
    )

    # Capacity and duration
    max_group_size = models.PositiveSmallIntegerField(
        validators=[MinValueValidator(1)],
        help_text=_('Max number of participants per departure'),
    )
    min_group_size = models.PositiveSmallIntegerField(
        validators=[MinValueValidator(1)],
        default=1,
        help_text=_('Min number of participants required to operate'),
    )
    duration_days = models.PositiveSmallIntegerField(default=1)

    # Tour features
    is_guided = models.BooleanField(
        default=True,
        help_text=_('Whether the tour includes a guide'),
    )
    meals_included = models.BooleanField(
        default=False,
        help_text=_('Whether meals are included'),
    )
    accommodation_included = models.BooleanField(
        default=False,
        help_text=_('Whether accommodation is included'),
    )
    equipment_provided = models.BooleanField(
        default=False,
        help_text=_('Whether equipment is provided'),
    )

    # Pricing
    base_price = MoneyField(
        max_digits=10,
        decimal_places=2,
        default_currency='USD',
        help_text=_('Base price per person'),
        null=True,
        blank=True,
    )

    # flexible pricing (avoids schema changes for new price tiers)
    pricing_rule = models.JSONField(
        default=dict,
        help_text=_(
            'e.g., {"base": 100, "child_discount": 0.8, "group_min": 4, "group_rate": 90}'
        ),
    )

    # Additional details
    inclusions = models.TextField(
        blank=True,
        help_text=_('What is included in the tour price'),
    )
    exclusions = models.TextField(
        blank=True,
        help_text=_('What is not included in the tour price'),
    )
    itinerary = models.JSONField(
        default=dict,
        help_text=_('Day-by-day itinerary details'),
    )

    is_active = models.BooleanField(default=True)

    class Meta:
        verbose_name = _('Tour Template')
        verbose_name_plural = _('Tour Templates')
        unique_together = [['business', 'name']]  # Unique name per business
        indexes = [
            models.Index(fields=['business', 'destination']),
            models.Index(fields=['business', 'activity_type']),
            models.Index(fields=['business', 'difficulty_level']),
            models.Index(fields=['business', 'duration_days']),
            models.Index(fields=['business', 'is_active']),
        ]

    def __str__(self):
        return self.name

    def get_absolute_url(self):
        return reverse_lazy(
            'tours:tour-template-detail',
            kwargs={'pk': self.pk},
        )

    @property
    def avg_rating(self):
        return 5

    @property
    def review_count(self):
        return 37

    @property
    def effective_base_price(self):
        """Get the effective base price from base_price or pricing_rule."""
        if self.base_price:
            return self.base_price
        return self.pricing_rule.get('base', 0)


class TourAddon(UserAuditModel):
    """Add-ons and extras available for tours (e.g., equipment rental, meals)"""

    class AddonType(models.TextChoices):
        EQUIPMENT = 'equipment', _('Equipment')
        MEAL = 'meal', _('Meal')
        ACCOMMODATION = 'accommodation', _('Accommodation')
        TRANSPORT = 'transport', _('Transport')
        INSURANCE = 'insurance', _('Insurance')
        ACTIVITY = 'activity', _('Activity')
        OTHER = 'other', _('Other')

    # Business relationship (for multi-tenancy)
    business = models.ForeignKey(
        'businesses.Business',
        on_delete=models.CASCADE,
        related_name='tour_addons',
        help_text=_('Business this addon belongs to'),
    )

    name = models.CharField(
        _('name'),
        max_length=255,
        help_text=_('Name of the addon (e.g., "Carbon-fiber trekking poles")'),
    )
    description = models.TextField(
        blank=True,
        help_text=_('Detailed description of the addon'),
    )
    addon_type = models.CharField(
        _('addon type'),
        max_length=20,
        choices=AddonType.choices,
        default=AddonType.EQUIPMENT,
    )

    # Pricing
    price = MoneyField(
        max_digits=10,
        decimal_places=2,
        default_currency='USD',
        help_text=_('Price for this addon'),
    )

    # Availability
    is_available = models.BooleanField(
        default=True,
        help_text=_('Whether this addon is currently available'),
    )
    max_quantity = models.PositiveSmallIntegerField(
        default=1,
        help_text=_('Maximum quantity per booking (0 = unlimited)'),
    )

    # Tour compatibility
    compatible_tours = models.ManyToManyField(
        TourTemplate,
        blank=True,
        related_name='available_addons',
        help_text=_('Tours this addon is available for (empty = all tours)'),
    )

    class Meta:
        verbose_name = _('Tour Addon')
        verbose_name_plural = _('Tour Addons')
        unique_together = [['business', 'name']]
        indexes = [
            models.Index(fields=['business', 'addon_type']),
            models.Index(fields=['business', 'is_available']),
        ]

    def __str__(self):
        return f'{self.name} (+{self.price})'

    def is_compatible_with_tour(self, tour_template):
        """Check if this addon is compatible with the given tour template."""
        if not self.compatible_tours.exists():
            # If no specific tours are set, it's compatible with all tours
            return True
        return self.compatible_tours.filter(pk=tour_template.pk).exists()


class TourResource(BaseModel):
    """Physical resources required (e.g., Vehicles, Guides)"""

    RESOURCE_TYPES = [
        ('GUIDE', _('Tour Guide')),
        ('VEHICLE', _('Vehicle')),
        ('EQUIPMENT', _('Specialized Equipment')),
    ]

    name = models.CharField(max_length=100)
    resource_type = models.CharField(max_length=20, choices=RESOURCE_TYPES)
    max_capacity = models.PositiveSmallIntegerField(
        validators=[MinValueValidator(1)],
        help_text=_('How many participants this resource can handle simultineously'),
    )

    def __str__(self):
        return f'{self.get_resource_type_display()}: {self.name}'

    def get_absolute_url(self):
        return reverse_lazy(
            'tours:tour-detail',
            kwargs={'pk': self.pk},
        )


class TourSchedule(BaseModel):
    """Recurring tour dates (e.g., 'Every Saturday')"""

    tour_template = models.ForeignKey(
        'TourTemplate', on_delete=models.CASCADE, related_name='schedules'
    )
    start_date = models.DateField()
    end_date = models.DateField()
    recurrence_rule = models.CharField()
    # resource allocation
    required_resources = models.ManyToManyField(
        TourResource,
        through='ResourceRequirement',
        help_text=_('Links resources to this schedule'),
    )

    class Meta:
        verbose_name = _('Tour Schedule')
        verbose_name_plural = _('Tour Schedules')

    def __str__(self):
        return f'{self.tour_template} ({self.start_date} to {self.end_date})'


class ResourceRequirement(BaseModel):
    schedule = models.ForeignKey(TourSchedule, on_delete=models.CASCADE)
    resource = models.ForeignKey(TourResource, on_delete=models.CASCADE)
    quantity = models.PositiveSmallIntegerField(default=1)

    class Meta:
        unique_together = ('schedule', 'resource')


class TourInstance(BaseModel):
    """Concrete tour occurrence (e.g., 'Grand Canyon Hike on 2025-10-05)"""

    tour_template = models.ForeignKey(
        'TourTemplate', on_delete=models.PROTECT, related_name='tour_instances'
    )
    schedule = models.ForeignKey(
        TourSchedule, on_delete=models.SET_NULL, null=True, blank=True
    )
    date = models.DateField()
    # real-time capacity management
    max_capacity = models.PositiveSmallIntegerField()
    blocked_spots = models.PositiveSmallIntegerField(
        default=0,
        validators=[MinValueValidator(0)],
        help_text=_('Sports held for internal use/VIPs'),
    )
    is_operational = models.BooleanField(
        default=True,
        help_text=_('False = cancelled due to low sign-ups/weather'),
    )

    class Meta:
        unique_together = ['tour_template', 'date']
        indexes = [
            models.Index(fields=['date']),
            models.Index(fields=['is_operational']),
        ]

    def __str__(self):
        return f'{self.tour_template} on {self.date}'

    @property
    def available_spots(self):
        """Critical for preventing overbooking"""
        confirmed_participants = (
            # Booking.objects.filter(
            #     status=Booking.Status.CONFIRMED,
            #     tour_instance=self,
            # ).aggregate(total=models.Sum('num_participants'))['total']
            # or 0
            0
        )
        return self.max_capacity - self.blocked_spots - confirmed_participants

    def clean(self):
        if self.blocked_spots > self.max_capacity:
            raise ValidationError(
                _('Blocked spots cannot exceed max capacity'),
            )

    def reserve_spots(self, session_key, spots=1):
        """
        Reserve spots temporarily for a session.
        Returns True if successful, False if not enough spots available.
        """
        # Clean up expired reservations first
        TemporaryReservation.cleanup_expired()

        # Check if enough spots are available
        if self.available_spots_with_reservations() < spots:
            return False

        # Create or update existing reservation for this session
        reservation, created = TemporaryReservation.objects.get_or_create(
            session_key=session_key,
            tour_instance=self,
            defaults={'spots_reserved': spots},
        )

        if not created:
            # Update existing reservation
            reservation.spots_reserved = spots
            reservation.save()

        return True

    def release_spots(self, session_key):
        """Release temporarily reserved spots for a session."""
        TemporaryReservation.objects.filter(
            session_key=session_key, tour_instance=self
        ).delete()

    def available_spots_with_reservations(self):
        """
        Get available spots considering temporary reservations.
        This is the real-time availability that should be displayed to users.
        """
        # Clean up expired reservations first
        TemporaryReservation.cleanup_expired()

        # Get confirmed bookings
        confirmed_participants = (
            # Booking.objects.filter(
            #     status=Booking.Status.CONFIRMED,
            #     tour_instance=self,
            # ).aggregate(total=models.Sum('number_of_participants'))['total']
            # or 0
            0  # TODO: Implement when Booking model is enhanced
        )

        # Get active temporary reservations
        from django.utils import timezone

        temp_reserved = (
            TemporaryReservation.objects.filter(
                tour_instance=self, expires_at__gt=timezone.now()
            ).aggregate(total=models.Sum('spots_reserved'))['total']
            or 0
        )

        return (
            self.max_capacity
            - self.blocked_spots
            - confirmed_participants
            - temp_reserved
        )


class Occurrence(UserAuditModel):
    """
    Stores timing information for tour events.
    """

    tour = models.ForeignKey(
        'TourEvent',
        on_delete=models.CASCADE,
        related_name='occurrence',
        help_text=_('Associated tour'),
    )

    start_time = models.DateTimeField(
        help_text=_('Start time of the occurrence'),
    )
    end_time = models.DateTimeField(
        help_text=_('End time of the occurrence'),
    )

    class Meta:
        verbose_name = _('Occurrence')
        verbose_name_plural = _('Occurrences')
        indexes = [
            models.Index(fields=['start_time']),
            models.Index(fields=['end_time']),
            models.Index(fields=['tour', 'start_time']),
        ]
        ordering = ['start_time']

    def __str__(self):
        return f'{self.tour.title} - {self.start_time.strftime("%Y-%m-%d %H:%M")}'


class TourEvent(UserAuditModel):
    """
    Represents scheduled tours.
    """

    title = models.CharField(
        max_length=200,
        help_text=_('Tour Event title'),
    )
    description = models.TextField(
        blank=True,
        help_text=_('Tour Event description'),
    )

    # Business relationship (for multi-tenancy)
    business = models.ForeignKey(
        'businesses.Business',
        on_delete=models.CASCADE,
        related_name='tour_events',
        help_text=_('Business this event belongs to'),
    )

    # Event details
    category = models.ForeignKey(
        TourCategory,
        on_delete=models.DO_NOTHING,
        related_name='tour_events',
    )

    # Related entities
    client = models.ForeignKey(
        'clients.Client',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='tour_events',
        help_text=_('Client associated with this tour'),
    )
    quote = models.ForeignKey(
        'quotes.Quote',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='tour_events',
        help_text=_('Quote associated with this event'),
    )

    # Event details
    max_participants = models.PositiveIntegerField(
        default=1,
        help_text=_('Maximum number of participants'),
    )
    current_participants = models.PositiveIntegerField(
        default=0,
        help_text=_('Current number of participants'),
    )
    blocked_spots = models.PositiveIntegerField(default=0)  # for internal reservations

    # Location and logistics
    meeting_point = models.CharField(
        max_length=255,
        blank=True,
        help_text=_('Meeting point for the event'),
    )
    special_instructions = models.TextField(
        blank=True,
        help_text=_('Special instructions for the event'),
    )

    # Pricing
    price_per_person = MoneyField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        help_text=_('Price per person for this event'),
    )

    # Status
    is_confirmed = models.BooleanField(
        default=False,
        help_text=_('Whether the event is confirmed'),
    )
    is_cancelled = models.BooleanField(
        default=False,
        help_text=_('Whether the event is cancelled'),
    )

    class Meta:
        verbose_name = _('Tour Event')
        verbose_name_plural = _('Tour Events')
        indexes = [
            models.Index(fields=['business', 'client']),
            models.Index(fields=['business', 'is_confirmed']),
        ]

    def __str__(self):
        return f'{self.title} - {self.category.name}'

    @property
    def is_full(self):
        """Check if the event is at capacity."""
        return self.current_participants >= self.max_participants

    @property
    def available_spots(self):
        """Get the number of available spots."""
        return max(0, self.max_participants - self.current_participants)

    def can_add_participants(self, count=1):
        """Check if we can add the specified number of participants."""
        return self.current_participants + count <= self.max_participants

    # @property
    # def start_time(self):
    #     """Get the start time from the first occurrence."""
    #     if not self.event:
    #         return None
    #     occurrence = self.event.occurrence.first()
    #     return occurrence.start_time if occurrence else None

    # @property
    # def end_time(self):
    #     """Get the end time from the first occurrence."""
    #     if not self.event:
    #         return None
    #     occurrence = self.event.occurrence.first()
    #     return occurrence.end_time if occurrence else None

    # def get_or_create_occurrence(self, start_time, end_time):
    #     """Get or create an occurrence for this event."""
    #     if not self.event:
    #         return None

    #     occurrence = self.event.occurrence.first()
    #     if occurrence:
    #         occurrence.start_time = start_time
    #         occurrence.end_time = end_time
    #         occurrence.save()
    #     else:
    #         occurrence = Occurrence.objects.create(
    #             event=self.event, start_time=start_time, end_time=end_time
    #         )
    #     return occurrence


class TemporaryReservation(BaseModel):
    """
    Temporary reservation model for holding spots during the booking process.
    Automatically expires after 15 minutes to prevent indefinite holds.
    """

    session_key = models.CharField(
        max_length=40,
        help_text=_('Session key for the user making the reservation'),
    )
    tour_instance = models.ForeignKey(
        TourInstance,
        on_delete=models.CASCADE,
        related_name='temporary_reservations',
        help_text=_('Tour instance being reserved'),
    )
    spots_reserved = models.PositiveSmallIntegerField(
        default=1,
        validators=[MinValueValidator(1)],
        help_text=_('Number of spots temporarily reserved'),
    )
    expires_at = models.DateTimeField(
        help_text=_('When this reservation expires'),
    )

    class Meta:
        verbose_name = _('Temporary Reservation')
        verbose_name_plural = _('Temporary Reservations')
        indexes = [
            models.Index(fields=['session_key']),
            models.Index(fields=['tour_instance', 'expires_at']),
            models.Index(fields=['expires_at']),
        ]

    def __str__(self):
        return (
            f'Temp reservation for {self.tour_instance} - {self.spots_reserved} spots'
        )

    def save(self, *args, **kwargs):
        """Set expiration time if not provided."""
        if not self.expires_at:
            from django.utils import timezone

            self.expires_at = timezone.now() + timedelta(minutes=15)
        super().save(*args, **kwargs)

    @property
    def is_expired(self):
        """Check if this reservation has expired."""
        from django.utils import timezone

        return timezone.now() > self.expires_at

    @classmethod
    def cleanup_expired(cls):
        """Remove all expired reservations."""
        from django.utils import timezone

        expired_count = cls.objects.filter(expires_at__lt=timezone.now()).count()
        cls.objects.filter(expires_at__lt=timezone.now()).delete()
        return expired_count


class GuideAssignment(UserAuditModel):
    """
    Assignment of guides to tour instances.
    Supports multiple guides per tour with different roles.
    """

    class Role(models.TextChoices):
        LEAD_GUIDE = 'lead_guide', _('Lead Guide')
        ASSISTANT_GUIDE = 'assistant_guide', _('Assistant Guide')
        SPECIALIST = 'specialist', _('Specialist')
        TRAINEE = 'trainee', _('Trainee')

    tour_instance = models.ForeignKey(
        TourInstance,
        on_delete=models.CASCADE,
        related_name='guide_assignments',
        help_text=_('Tour instance for this assignment'),
    )
    guide = models.ForeignKey(
        'accounts.User',
        on_delete=models.CASCADE,
        related_name='guide_assignments',
        help_text=_('Guide assigned to this tour'),
    )
    role = models.CharField(
        max_length=20,
        choices=Role.choices,
        default=Role.LEAD_GUIDE,
        help_text=_('Role of the guide for this tour'),
    )
    notes = models.TextField(
        blank=True,
        help_text=_('Additional notes about this assignment'),
    )

    class Meta:
        verbose_name = _('Guide Assignment')
        verbose_name_plural = _('Guide Assignments')
        unique_together = ['tour_instance', 'guide', 'role']
        indexes = [
            models.Index(fields=['tour_instance', 'role']),
            models.Index(fields=['guide']),
        ]

    def __str__(self):
        return f'{self.guide.get_full_name()} - {self.get_role_display()} for {self.tour_instance}'
