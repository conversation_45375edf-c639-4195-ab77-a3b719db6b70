{% extends "base.html" %}

{% block title %}Invoice {{ invoice.invoice_number }}{% endblock %}

{% block content %}
<div class="max-w-6xl mx-auto">
  <!-- Breadcrumb -->
  <nav class="flex mb-6" aria-label="Breadcrumb">
    <ol class="inline-flex items-center space-x-1 md:space-x-3">
      <li class="inline-flex items-center">
        <a href="{% url 'core:home' %}" class="text-gray-700 hover:text-gray-900">
          Home
        </a>
      </li>
      <li>
        <div class="flex items-center">
          <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd"
              d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
              clip-rule="evenodd"></path>
          </svg>
          <a href="/admin/invoices/invoice/" class="ml-1 text-gray-700 hover:text-gray-900 md:ml-2">Invoices</a>
        </div>
      </li>
      <li aria-current="page">
        <div class="flex items-center">
          <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd"
              d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
              clip-rule="evenodd"></path>
          </svg>
          <span class="ml-1 text-gray-500 md:ml-2">{{ invoice.invoice_number }}</span>
        </div>
      </li>
    </ol>
  </nav>

  <!-- Overdue Alert -->
  {% if is_overdue %}
  <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
    <div class="flex items-center">
      <svg class="w-6 h-6 text-red-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
          d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z">
        </path>
      </svg>
      <div>
        <h3 class="text-lg font-semibold text-red-900">Invoice Overdue</h3>
        <p class="text-red-700">This invoice is past its due date and requires immediate attention.</p>
      </div>
    </div>
  </div>
  {% endif %}

  <!-- Invoice Header -->
  <div class="bg-white rounded-lg shadow p-6 mb-6">
    <div class="flex justify-between items-start mb-4">
      <div>
        <h1 class="text-3xl font-bold text-gray-900">Invoice {{ invoice.invoice_number }}</h1>
        <p class="text-lg text-gray-600">Client: {{ invoice.client.display_name }}</p>
        {% if invoice.quote %}
        <p class="text-sm text-gray-500">
          Related Quote:
          <a href="{% url 'quotes:quote-detail' invoice.quote.id %}" class="text-blue-600 hover:text-blue-800">
            {{ invoice.quote.quote_number }}
          </a>
        </p>
        {% endif %}
      </div>
      <div class="text-right">
        <p class="text-sm text-gray-500">Status</p>
        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                    {% if invoice.status == 'paid' %}bg-green-100 text-green-800
                    {% elif invoice.status == 'sent' %}bg-blue-100 text-blue-800
                    {% elif invoice.status == 'overdue' %}bg-red-100 text-red-800
                    {% else %}bg-gray-100 text-gray-800{% endif %}">
          {{ invoice.get_status_display }}
        </span>
      </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <div>
        <h3 class="text-lg font-semibold text-gray-900 mb-2">Financial Details</h3>
        <dl class="space-y-2">
          <div class="flex justify-between">
            <dt class="text-sm text-gray-500">Subtotal:</dt>
            <dd class="text-sm font-medium text-gray-900">{{ invoice.subtotal }}</dd>
          </div>
          <div class="flex justify-between">
            <dt class="text-sm text-gray-500">Tax:</dt>
            <dd class="text-sm font-medium text-gray-900">{{ invoice.tax_amount }}</dd>
          </div>
          <div class="flex justify-between border-t pt-2">
            <dt class="text-base font-semibold text-gray-900">Total:</dt>
            <dd class="text-base font-semibold text-gray-900">{{ invoice.total_amount }}</dd>
          </div>
        </dl>
      </div>

      <div>
        <h3 class="text-lg font-semibold text-gray-900 mb-2">Important Dates</h3>
        <dl class="space-y-2">
          <div class="flex justify-between">
            <dt class="text-sm text-gray-500">Issue Date:</dt>
            <dd class="text-sm text-gray-900">{{ invoice.issue_date|date:"M d, Y" }}</dd>
          </div>
          <div class="flex justify-between">
            <dt class="text-sm text-gray-500">Due Date:</dt>
            <dd class="text-sm text-gray-900 {% if is_overdue %}text-red-600 font-medium{% endif %}">
              {{ invoice.due_date|date:"M d, Y" }}
            </dd>
          </div>
          {% if invoice.sent_at %}
          <div class="flex justify-between">
            <dt class="text-sm text-gray-500">Sent:</dt>
            <dd class="text-sm text-gray-900">{{ invoice.sent_at|date:"M d, Y" }}</dd>
          </div>
          {% endif %}
          {% if invoice.paid_at %}
          <div class="flex justify-between">
            <dt class="text-sm text-gray-500">Paid:</dt>
            <dd class="text-sm text-green-600 font-medium">{{ invoice.paid_at|date:"M d, Y" }}</dd>
          </div>
          {% endif %}
        </dl>
      </div>

      <div>
        <h3 class="text-lg font-semibold text-gray-900 mb-2">Actions</h3>
        <div class="space-y-2">
          {% if invoice.status == 'draft' %}
          <button onclick="sendInvoice()"
            class="block w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors text-sm">
            Send Invoice
          </button>
          {% endif %}

          {% if can_mark_paid %}
          <button onclick="markPaid()"
            class="block w-full bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-md transition-colors text-sm">
            Mark as Paid
          </button>
          {% endif %}

          {% if invoice.status in 'sent,overdue' %}
          <button onclick="createPaymentLink()"
            class="block w-full bg-purple-600 hover:bg-purple-700 text-white font-medium py-2 px-4 rounded-md transition-colors text-sm">
            Create Payment Link
          </button>
          {% endif %}

          <a href="/admin/invoices/invoice/{{ invoice.id }}/change/"
            class="block w-full text-center bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-2 px-4 rounded-md transition-colors text-sm">
            Edit Invoice
          </a>
        </div>
      </div>
    </div>

    {% if invoice.terms_conditions %}
    <div class="mt-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-2">Terms & Conditions</h3>
      <p class="text-gray-600">{{ invoice.terms_conditions|linebreaks }}</p>
    </div>
    {% endif %}

    {% if invoice.payment_terms %}
    <div class="mt-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-2">Payment Terms</h3>
      <p class="text-gray-600">{{ invoice.payment_terms|linebreaks }}</p>
    </div>
    {% endif %}
  </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
  function sendInvoice() {
    if (!confirm('Are you sure you want to send this invoice to the client?')) {
      return;
    }

    fetch('{% url "invoices:send" invoice.id %}', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      }
    })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          alert('Invoice sent successfully!');
          location.reload();
        } else {
          alert('Error: ' + data.error);
        }
      })
      .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while sending the invoice.');
      });
  }

  function markPaid() {
    if (!confirm('Are you sure you want to mark this invoice as paid?')) {
      return;
    }

    fetch('{% url "invoices:mark_paid" invoice.id %}', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      }
    })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          alert('Invoice marked as paid!');
          location.reload();
        } else {
          alert('Error: ' + data.error);
        }
      })
      .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while marking the invoice as paid.');
      });
  }

  function createPaymentLink() {
    if (!confirm('Are you sure you want to create a payment link for this invoice?')) {
      return;
    }

    fetch('{% url "payments:create_link" invoice.id %}', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': '{{ csrf_token }}'
      }
    })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          alert('Payment link created successfully!');
          if (data.payment_url) {
            if (confirm('Would you like to copy the payment link to clipboard?')) {
              navigator.clipboard.writeText(window.location.origin + data.payment_url).then(() => {
                alert('Payment link copied to clipboard!');
              });
            }
          }
        } else {
          alert('Error: ' + data.error);
        }
      })
      .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while creating the payment link.');
      });
  }
</script>
{% endblock %}
