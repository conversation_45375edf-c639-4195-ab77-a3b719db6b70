"""
Authentication views for the accounts app.
"""

import logging

from django import forms
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib.auth.views import <PERSON>ginView, LogoutView
from django.core.exceptions import PermissionDenied
from django.shortcuts import redirect
from django.urls import reverse, reverse_lazy
from django.views.generic import CreateView, TemplateView, UpdateView, View

from core.logging_utils import AuditLogger
from core.notifications import (
    WelcomeBusinessOwnerNotification,
)

from . import forms
from .models import Profile, User

logger = logging.getLogger('accounts')


class CustomLoginView(LoginView):
    """Custom login view with enhanced styling."""

    template_name = 'accounts/login.html'
    redirect_authenticated_user = True

    def get_success_url(self):
        """Redirect to appropriate page after login."""
        # Check if user needs onboarding
        if hasattr(self.request, 'user') and self.request.user.is_authenticated:
            user = self.request.user

            # If user needs onboarding, redirect to onboarding
            if user.needs_onboarding() or user.needs_profile_completion():
                return reverse_lazy('accounts:onboarding')

        return self.get_redirect_url() or reverse_lazy('core:dashboard')

    def form_valid(self, form):
        """Add success message on login."""
        messages.success(
            self.request,
            f'Welcome back, {form.get_user().get_full_name() or form.get_user().username}!',
        )
        return super().form_valid(form)


class CustomLogoutView(LogoutView):
    """Custom logout view."""

    def dispatch(self, request, *args, **kwargs):
        """Add logout message."""
        if request.user.is_authenticated:
            messages.info(request, 'You have been logged out successfully.')
        return super().dispatch(request, *args, **kwargs)


class RegisterView(CreateView):
    """User registration view."""

    model = User
    form_class = forms.CustomUserCreationForm
    template_name = 'accounts/register.html'
    success_url = reverse_lazy('accounts:login')

    def form_valid(self, form):
        """Handle successful registration and send confirmation email."""
        response = super().form_valid(form)
        user = self.object

        # Generate confirmation token and send email
        token = user.generate_confirmation_token()
        confirmation_url = self.request.build_absolute_uri(
            reverse(
                'accounts:confirm_email',
                kwargs={'token': str(token)},
            )
        )

        # Send confirmation email
        form.send_confirmation_email(user, confirmation_url)

        messages.success(
            self.request,
            f'Account created successfully! Please check your email ({user.email}) for a confirmation link to activate your account.',
        )

        return response

    def get_context_data(self, **kwargs):
        """Add additional context."""
        context = super().get_context_data(**kwargs)
        context['title'] = 'Create Account'
        return context


def email_confirmation_view(request, token):
    """View to handle email confirmation."""
    user = User.objects.get(
        email_confirmation_token=token,
        email_confirmed=False,
        is_active=False,
    )
    if user is not None:
        user.confirm_email()
        # assign profile a role of BUSINESS_OWNER
        user.profile.role = Profile.Role.BUSINESS_OWNER
        user.profile.save()

        login_url = request.build_absolute_uri(reverse('accounts:login'))

        WelcomeBusinessOwnerNotification(user, login_url).send()

        messages.success(
            request,
            'Email confirmed successfully! Your account is now active. Please log in to complete your setup.',
        )

        return redirect('accounts:login')


class ProfileUpdateView(LoginRequiredMixin, UpdateView):
    """View for updating user profile."""

    model = Profile
    form_class = forms.ProfileForm
    template_name = 'accounts/profile_form.html'
    extra_context = {'title': 'Update Profile'}
    success_url = reverse_lazy('accounts:profile')

    def get_object(self):
        """Get or create profile for current user."""
        profile, created = Profile.objects.get_or_create(user=self.request.user)
        return profile

    def form_valid(self, form):
        """Handle successful form submission."""
        messages.success(self.request, 'Profile updated successfully!')

        # Mark profile as completed if it meets requirements
        if form.instance.is_complete():
            self.request.user.profile_completed = True
            self.request.user.save(update_fields=['profile_completed'])

        return super().form_valid(form)


class ProfileDetailView(LoginRequiredMixin, TemplateView):
    """View for displaying user profile."""

    template_name = 'accounts/profile_detail.html'

    def get_context_data(self, **kwargs):
        """Add profile to context."""
        context = super().get_context_data(**kwargs)
        profile, created = Profile.objects.get_or_create(user=self.request.user)
        context['profile'] = profile
        context['title'] = 'My Profile'
        return context


class OnboardingProfileView(LoginRequiredMixin, UpdateView):
    """Simplified profile form for onboarding."""

    model = Profile
    form_class = forms.OnboardingProfileForm
    template_name = 'accounts/onboarding_profile.html'
    success_url = reverse_lazy('accounts:onboarding_business')
    extra_context = {'title': 'Complete Your Profile'}

    def get_object(self):
        """Get or create profile for current user."""
        profile, created = Profile.objects.get_or_create(user=self.request.user)
        return profile

    def form_valid(self, form):
        """Handle successful form submission."""
        messages.success(
            self.request,
            'Profile information saved!',
        )

        # Mark profile as completed
        self.request.user.profile_completed = True
        self.request.user.save(update_fields=['profile_completed'])

        return super().form_valid(form)


class OnboardingBusinessView(LoginRequiredMixin, TemplateView):
    """Business setup step in onboarding."""

    template_name = 'accounts/onboarding_business.html'

    def get_context_data(self, **kwargs):
        """Add business and form to context."""
        context = super().get_context_data(**kwargs)
        context['business'] = self.request.user.profile.business
        context['title'] = 'Set Up Your Business'

        # If no business exists, provide a form to create one
        if not context['business']:
            context['business_form'] = forms.OnboardingBusinessForm()

        return context

    def post(self, request, *args, **kwargs):
        """Handle business creation or setup completion."""
        user = request.user

        # If user doesn't have a business, create one
        if not user.profile.business:
            form = forms.OnboardingBusinessForm(request.POST)
            if form.is_valid():
                business = form.save(commit=False)
                business.created_by = user
                business.save()

                # Associate user with the business
                user.profile.business = business
                user.profile.save(update_fields=['business'])

                messages.success(
                    request,
                    f'Business "{business.name}" created successfully!',
                )
            else:
                # Return form with errors
                context = self.get_context_data(**kwargs)
                context['business_form'] = form
                return self.render_to_response(context)

        # Mark onboarding as completed
        user.onboarding_completed = True
        user.save(update_fields=['onboarding_completed'])

        messages.success(request, 'Welcome! Your account setup is complete.')
        return redirect('core:dashboard')


class OnboardingWelcomeView(LoginRequiredMixin, TemplateView):
    """Welcome step in onboarding process."""

    template_name = 'accounts/onboarding_welcome.html'

    def get_context_data(self, **kwargs):
        """Add additional context."""
        context = super().get_context_data(**kwargs)
        context['title'] = 'Welcome to Tour Business Management'
        return context


@login_required
def check_onboarding_status(request):
    """Check if user needs onboarding and redirect appropriately."""
    user = request.user

    # If user hasn't confirmed email, redirect to login
    if not user.email_confirmed:
        messages.warning(request, 'Please confirm your email address first.')
        return redirect('accounts:login')

    # If profile not completed, redirect to profile onboarding
    if not user.profile_completed:
        return redirect('accounts:onboarding_profile')

    # If onboarding not completed, redirect to business setup
    if (
        not user.onboarding_completed
        and user.profile.role == Profile.Role.BUSINESS_OWNER
    ):
        return redirect('accounts:onboarding_business')

    # Onboarding complete, redirect to dashboard
    return redirect(reverse_lazy('core:dashboard'))


class UserManagementView(LoginRequiredMixin, TemplateView):
    """View for managing business users."""

    template_name = 'accounts/user_management.html'

    def get_context_data(self, **kwargs):
        """Add users and forms to context."""
        context = super().get_context_data(**kwargs)

        # Only business owners can manage users
        if self.request.user.profile.role != User.profile.Role.BUSINESS_OWNER:
            raise PermissionDenied('Only business owners can manage users')

        business = self.request.user.profile.business
        if not business:
            raise PermissionDenied('No business associated with user')

        # Get all users in the business
        users = User.objects.filter(business=business).select_related('profile')

        context.update(
            {
                'title': 'Manage Users',
                'business': business,
                'users': users,
                'add_user_form': forms.AddUserForm(business=business),
                'bulk_upload_form': forms.BulkUserUploadForm(),
            }
        )
        return context


class EditUserView(LoginRequiredMixin, UpdateView):
    """View for editing users in business."""

    model = User
    form_class = forms.UserAccountForm
    template_name = 'accounts/edit_user.html'
    success_url = reverse_lazy('accounts:user_management')

    def get_queryset(self):
        """Limit to users in the same business."""
        if self.request.user.profile.role != User.profile.Role.BUSINESS_OWNER:
            raise PermissionDenied('Only business owners can edit users')

        business = self.request.user.profile.business
        if not business:
            raise PermissionDenied('No business associated with user')

        return User.objects.filter(business=business)

    def form_valid(self, form):
        """Handle successful user update."""
        user = form.instance

        # Log the user update
        AuditLogger.log_user_action(
            user=self.request.user,
            action='update',
            resource='user',
            resource_id=str(user.id),
            details={
                'updated_user': user.username,
                'role': user.profile.role,
                'is_active': user.is_active,
            },
            request=self.request,
        )

        messages.success(self.request, f'User "{user.username}" updated successfully!')
        return super().form_valid(form)


class RemoveUserView(LoginRequiredMixin, View):
    """View for removing users from business."""

    def post(self, request, pk):
        """Handle user removal."""
        if request.user.profile.role != Profile.Role.BUSINESS_OWNER:
            raise PermissionDenied('Only business owners can remove users')

        business = request.user.profile.business
        if not business:
            raise PermissionDenied('No business associated with user')

        try:
            user = User.objects.get(pk=pk, business=business)

            # Prevent removing self
            if user == request.user:
                messages.error(request, 'You cannot remove yourself.')
                return redirect('accounts:user_management')

            username = user.username
            user_id = str(user.id)

            # Log the user removal
            AuditLogger.log_user_action(
                user=request.user,
                action='delete',
                resource='user',
                resource_id=user_id,
                details={
                    'removed_user': username,
                    'removed_user_role': user.profile.role,
                },
                request=request,
            )

            user.delete()

            messages.success(request, f'User "{username}" removed successfully!')
        except User.DoesNotExist:
            messages.error(request, 'User not found.')

        return redirect('accounts:user_management')


class ResendInviteView(LoginRequiredMixin, View):
    """View for resending user invitations."""

    def post(self, request, pk):
        """Handle invitation resend."""
        if request.user.profile.role != Profile.Role.BUSINESS_OWNER:
            raise PermissionDenied('Only business owners can resend invitations')

        business = request.user.profile.business
        if not business:
            raise PermissionDenied('No business associated with user')

        try:
            user = User.objects.get(pk=pk, business=business)

            if user.is_active:
                messages.warning(request, f'User "{user.username}" is already active.')
                return redirect('accounts:user_management')

            # Generate new invitation token and send email
            token = user.generate_confirmation_token()
            invitation_url = request.build_absolute_uri(
                reverse('accounts:user_invitation', kwargs={'token': str(token)})
            )

            from core.notifications import UserInvitationNotification

            notification = UserInvitationNotification(
                user, business, invitation_url, request.user
            )
            notification.send()

            messages.success(
                request, f'Invitation resent to "{user.username}" successfully!'
            )
        except User.DoesNotExist:
            messages.error(request, 'User not found.')
        except Exception:
            messages.error(request, 'Error sending invitation email.')

        return redirect('accounts:user_management')


class UserActivityView(LoginRequiredMixin, TemplateView):
    """View for displaying user activity and analytics."""

    template_name = 'accounts/user_activity.html'

    def get_context_data(self, **kwargs):
        """Add user activity data to context."""
        context = super().get_context_data(**kwargs)

        # Only business owners can view user activity
        if self.request.user.profile.role != Profile.Role.BUSINESS_OWNER:
            raise PermissionDenied('Only business owners can view user activity')

        business = self.request.user.profile.business
        if not business:
            raise PermissionDenied('No business associated with user')

        # Get all users in the business
        users = User.objects.filter(business=business).select_related('profile')

        # Calculate activity statistics
        total_users = users.count()
        active_users = users.filter(is_active=True).count()
        pending_users = users.filter(is_active=False).count()

        # Recent activity (users who logged in within last 30 days)
        from datetime import timedelta

        from django.utils import timezone

        thirty_days_ago = timezone.now() - timedelta(days=30)
        recent_active_users = users.filter(last_login__gte=thirty_days_ago).count()

        # Role distribution
        role_stats = {}
        for role_choice in Profile.Role.choices:
            role_key = role_choice[0]
            role_label = role_choice[1]
            count = users.filter(role=role_key).count()
            role_stats[role_label] = count

        context.update(
            {
                'title': 'User Activity',
                'business': business,
                'users': users.order_by('-last_login'),
                'total_users': total_users,
                'active_users': active_users,
                'pending_users': pending_users,
                'recent_active_users': recent_active_users,
                'role_stats': role_stats,
            }
        )
        return context


class AddUserView(LoginRequiredMixin, CreateView):
    """View for adding individual users to business."""

    model = User
    form_class = forms.AddUserForm
    template_name = 'accounts/add_user.html'
    success_url = reverse_lazy('accounts:user_management')

    def get_form_kwargs(self):
        """Add business to form kwargs."""
        kwargs = super().get_form_kwargs()
        kwargs['business'] = self.request.user.profile.business
        return kwargs

    def form_valid(self, form):
        """Handle successful user creation."""
        user = form.save()

        # Send invitation email if no password was set
        if not form.cleaned_data.get('password1'):
            try:
                # Generate invitation token and send email
                token = user.generate_confirmation_token()
                invitation_url = self.request.build_absolute_uri(
                    reverse('accounts:user_invitation', kwargs={'token': str(token)})
                )

                from core.notifications import UserInvitationNotification

                notification = UserInvitationNotification(
                    user,
                    self.request.user.profile.business,
                    invitation_url,
                    self.request.user,
                )
                notification.send()

                messages.success(
                    self.request,
                    f'User {user.username} added successfully! An invitation email has been sent to {user.email}.',
                )
            except Exception:
                messages.warning(
                    self.request,
                    f'User {user.username} added but there was an error sending the invitation email.',
                )
        else:
            messages.success(
                self.request,
                f'User {user.username} added successfully with password set.',
            )

        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        """Add additional context."""
        context = super().get_context_data(**kwargs)
        context['title'] = 'Add User'
        return context


class UserInvitationView(TemplateView):
    """View to handle user invitation acceptance."""

    template_name = 'accounts/user_invitation.html'

    def get(self, request, token):
        """Handle invitation acceptance."""
        try:
            # Find user with this token
            user = User.objects.get(
                email_confirmation_token=token, email_confirmed=False, is_active=False
            )

            context = {
                'user': user,
                'business': user.profile.business,
                'token': token,
                'title': 'Accept Invitation',
            }
            return self.render_to_response(context)

        except User.DoesNotExist:
            messages.error(
                request,
                'Invalid or expired invitation link. Please contact your business administrator.',
            )
            return redirect('accounts:login')

    def post(self, request, token):
        """Handle password setup and account activation."""
        try:
            user = User.objects.get(
                email_confirmation_token=token, email_confirmed=False, is_active=False
            )

            password1 = request.POST.get('password1')
            password2 = request.POST.get('password2')

            if not password1 or not password2:
                messages.error(request, 'Please provide both password fields.')
                return redirect('accounts:user_invitation', token=token)

            if password1 != password2:
                messages.error(request, "Passwords don't match.")
                return redirect('accounts:user_invitation', token=token)

            if len(password1) < 8:
                messages.error(request, 'Password must be at least 8 characters long.')
                return redirect('accounts:user_invitation', token=token)

            # Set password and activate user
            user.set_password(password1)
            user.is_active = True
            user.email_confirmed = True
            user.save(update_fields=['password', 'is_active', 'email_confirmed'])

            # Create profile for the user
            Profile.objects.get_or_create(user=user)

            messages.success(
                request,
                'Welcome! Your account has been activated. Please complete your profile.',
            )

            # Log the user in
            from django.contrib.auth import login

            login(request, user)

            # Redirect to profile completion
            return redirect('accounts:onboarding_profile')

        except User.DoesNotExist:
            messages.error(
                request,
                'Invalid or expired invitation link. Please contact your business administrator.',
            )
            return redirect('accounts:login')
        except Exception:
            messages.error(
                request,
                'An error occurred while setting up your account. Please try again.',
            )
            return redirect('accounts:user_invitation', token=token)


class BulkUserUploadView(LoginRequiredMixin, TemplateView):
    """View for bulk user upload via Excel."""

    template_name = 'accounts/bulk_upload.html'

    def get_context_data(self, **kwargs):
        """Add form to context."""
        context = super().get_context_data(**kwargs)
        context.update(
            {
                'title': 'Bulk Upload Users',
                'form': forms.BulkUserUploadForm(),
            }
        )
        return context

    def post(self, request, *args, **kwargs):
        """Handle Excel file upload and processing."""
        form = forms.BulkUserUploadForm(request.POST, request.FILES)

        if form.is_valid():
            try:
                excel_file = form.cleaned_data['excel_file']
                results = self.process_excel_file(
                    excel_file, request.user.profile.business
                )

                if results['success_count'] > 0:
                    messages.success(
                        request,
                        f'Successfully processed {results["success_count"]} users. '
                        f'{results["error_count"]} errors occurred.',
                    )
                else:
                    messages.error(
                        request,
                        f'No users were processed successfully. {results["error_count"]} errors occurred.',
                    )

                # Show detailed results
                if results['errors']:
                    for error in results['errors'][:5]:  # Show first 5 errors
                        messages.warning(request, error)

                return redirect('accounts:user_management')

            except Exception as e:
                messages.error(request, f'Error processing file: {str(e)}')

        context = self.get_context_data(**kwargs)
        context['form'] = form
        return self.render_to_response(context)

    def process_excel_file(self, excel_file, business):
        """Process uploaded Excel file and create users."""
        import pandas as pd

        from core.notifications import UserInvitationNotification

        results = {'success_count': 0, 'error_count': 0, 'errors': []}

        try:
            # Read Excel file
            df = pd.read_excel(excel_file)

            # Validate required columns
            required_columns = ['username', 'email', 'role']
            missing_columns = [col for col in required_columns if col not in df.columns]

            if missing_columns:
                raise ValueError(
                    f'Missing required columns: {", ".join(missing_columns)}'
                )

            # Process each row
            for index, row in df.iterrows():
                try:
                    # Validate data
                    username = str(row['username']).strip()
                    email = str(row['email']).strip()
                    role = str(row['role']).strip().lower()

                    if not username or not email:
                        results['errors'].append(
                            f'Row {index + 2}: Username and email are required'
                        )
                        results['error_count'] += 1
                        continue

                    # Validate role
                    if role not in ['agent', 'business_owner']:
                        results['errors'].append(
                            f"Row {index + 2}: Invalid role '{role}'. Must be 'agent' or 'business_owner'"
                        )
                        results['error_count'] += 1
                        continue

                    # Check if user already exists
                    if User.objects.filter(username=username).exists():
                        results['errors'].append(
                            f"Row {index + 2}: Username '{username}' already exists"
                        )
                        results['error_count'] += 1
                        continue

                    if User.objects.filter(email=email).exists():
                        results['errors'].append(
                            f"Row {index + 2}: Email '{email}' already exists"
                        )
                        results['error_count'] += 1
                        continue

                    # Create user
                    user = User.objects.create(
                        username=username,
                        email=email,
                        role=role,
                        is_active=False,
                        email_confirmed=False,
                        business=business,
                    )

                    # Generate invitation token and send email
                    token = user.generate_confirmation_token()
                    invitation_url = self.request.build_absolute_uri(
                        reverse(
                            'accounts:user_invitation', kwargs={'token': str(token)}
                        )
                    )

                    notification = UserInvitationNotification(
                        user, business, invitation_url, self.request.user
                    )
                    notification.send()

                    results['success_count'] += 1

                except Exception as e:
                    results['errors'].append(f'Row {index + 2}: {str(e)}')
                    results['error_count'] += 1

        except Exception as e:
            raise ValueError(f'Error reading Excel file: {str(e)}')

        return results


@login_required
def download_user_template(request):
    """Download Excel template for bulk user upload."""
    import pandas as pd
    from django.http import HttpResponse

    # Create sample data
    data = {
        'username': ['john.doe', 'jane.smith', 'mike.wilson'],
        'email': [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
        ],
        'role': ['agent', 'agent', 'business_owner'],
        'notes': ['Sample agent user', 'Another sample agent', 'Sample business owner'],
    }

    df = pd.DataFrame(data)

    # Create HTTP response with Excel content
    response = HttpResponse(
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    response['Content-Disposition'] = 'attachment; filename="user_upload_template.xlsx"'

    # Write Excel file to response
    with pd.ExcelWriter(response, engine='openpyxl') as writer:
        df.to_excel(writer, index=False, sheet_name='Users')

        # Add instructions sheet
        instructions = pd.DataFrame(
            {
                'Instructions': [
                    '1. Fill in the required columns: username, email, role',
                    '2. Role must be either "agent" or "business_owner"',
                    '3. Username must be unique across the system',
                    '4. Email must be valid and unique',
                    '5. Notes column is optional',
                    '6. Save the file and upload it through the bulk upload form',
                    '',
                    'Required Columns:',
                    '- username: Unique username for login',
                    '- email: Valid email address',
                    '- role: Either "agent" or "business_owner"',
                    '',
                    'Optional Columns:',
                    '- notes: Additional information about the user',
                ]
            }
        )
        instructions.to_excel(writer, index=False, sheet_name='Instructions')

    return response
