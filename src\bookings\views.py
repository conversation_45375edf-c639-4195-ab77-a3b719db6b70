"""
Views for bookings app.
"""

import calendar
from datetime import date, datetime, timedelta

from django.contrib import messages
from django.contrib.auth.mixins import LoginRequiredMixin
from django.db.models import Q, Sum
from django.http import Http404
from django.shortcuts import get_object_or_404, redirect, render
from django.urls import reverse
from django.views.generic import (
    CreateView,
    DetailView,
    ListView,
    TemplateView,
    UpdateView,
)

from clients.models import Traveler
from core.business_mixins import BusinessAccessMixin
from tours.models import TourEvent

from .models import Booking, BookingParticipant

# Calendar Views


class CalendarView(BusinessAccessMixin, TemplateView):
    """Base calendar view for tour events."""

    def get_events_for_period(self, start_date, end_date):
        """Get tour events for a specific period."""
        accessible_businesses = self.get_accessible_businesses()

        # Get tour events within the date range using occurrences
        # Filter for events that have occurrences within the date range
        tour_events = (
            TourEvent.objects.filter(
                business__in=accessible_businesses,
                event__occurrence__start_time__date__gte=start_date,
                event__occurrence__start_time__date__lte=end_date,
            )
            .select_related('event', 'business', 'client')
            .prefetch_related('event__occurrence')
            .distinct()
            .order_by('event__occurrence__start_time')
        )

        # Filter out events that don't have valid start times
        valid_events = []
        for event in tour_events:
            if event.start_time is not None:
                valid_events.append(event)

        return valid_events


class MonthlyCalendarView(CalendarView):
    """Monthly calendar view for tour events."""

    template_name = 'bookings/calendar_month.html'

    def get(self, request, year=None, month=None):
        """Handle GET request for monthly calendar."""
        # Default to current month if not specified
        if year is None or month is None:
            today = date.today()
            year = today.year
            month = today.month
        else:
            year = int(year)
            month = int(month)

        # Validate month and year
        if month < 1 or month > 12:
            raise Http404('Invalid month')

        # Calculate calendar boundaries
        cal = calendar.Calendar(firstweekday=0)  # Monday = 0
        month_days = cal.monthdayscalendar(year, month)

        # Get first and last day of the calendar view (including partial weeks)
        first_week = month_days[0]
        last_week = month_days[-1]

        # Find actual start and end dates
        start_date = date(year, month, 1)
        if first_week[0] == 0:  # First week starts with previous month
            # Find the first non-zero day
            for day in first_week:
                if day != 0:
                    start_date = date(year, month, day)
                    break
        else:
            start_date = date(year, month, first_week[0])

        # Calculate end date
        end_date = date(year, month, max(day for day in last_week if day != 0))

        # Extend to include full weeks
        start_date = start_date - timedelta(days=start_date.weekday())
        end_date = end_date + timedelta(days=(6 - end_date.weekday()))

        # Get events for the period
        events = self.get_events_for_period(start_date, end_date)

        # Group events by date
        events_by_date = {}
        for event in events:
            # Use the TourEvent's start_time property
            if event.start_time:
                event_date = event.start_time.date()
                if event_date not in events_by_date:
                    events_by_date[event_date] = []
                events_by_date[event_date].append(event)

        # Calculate navigation dates
        prev_month = month - 1 if month > 1 else 12
        prev_year = year if month > 1 else year - 1
        next_month = month + 1 if month < 12 else 1
        next_year = year if month < 12 else year + 1

        context = {
            'year': year,
            'month': month,
            'month_name': calendar.month_name[month],
            'month_days': month_days,
            'events_by_date': events_by_date,
            'today': date.today(),
            'prev_month': prev_month,
            'prev_year': prev_year,
            'next_month': next_month,
            'next_year': next_year,
            'weekday_names': ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
        }

        return render(request, self.template_name, context)


class WeeklyCalendarView(CalendarView):
    """Weekly calendar view for tour events."""

    template_name = 'bookings/calendar_week.html'

    def get(self, request, year=None, month=None, day=None):
        """Handle GET request for weekly calendar."""
        # Default to current week if not specified
        if year is None or month is None or day is None:
            today = date.today()
            year = today.year
            month = today.month
            day = today.day
        else:
            year = int(year)
            month = int(month)
            day = int(day)

        try:
            target_date = date(year, month, day)
        except ValueError:
            raise Http404('Invalid date')

        # Calculate week boundaries (Monday to Sunday)
        start_of_week = target_date - timedelta(days=target_date.weekday())
        end_of_week = start_of_week + timedelta(days=6)

        # Generate week days
        week_days = []
        current_date = start_of_week
        while current_date <= end_of_week:
            week_days.append(current_date)
            current_date += timedelta(days=1)

        # Get events for the week
        events = self.get_events_for_period(start_of_week, end_of_week)

        # Group events by date and time
        events_by_date = {}
        for event in events:
            # Use the TourEvent's start_time property
            if event.start_time:
                event_date = event.start_time.date()
                if event_date not in events_by_date:
                    events_by_date[event_date] = []
                events_by_date[event_date].append(event)

        # Sort events by time for each date
        def get_event_start_time(event):
            if event.start_time:
                return event.start_time.time()
            else:
                return datetime.min.time()

        for date_key in events_by_date:
            events_by_date[date_key].sort(key=get_event_start_time)

        # Calculate navigation dates
        prev_week = start_of_week - timedelta(days=7)
        next_week = start_of_week + timedelta(days=7)

        context = {
            'target_date': target_date,
            'start_of_week': start_of_week,
            'end_of_week': end_of_week,
            'week_days': week_days,
            'events_by_date': events_by_date,
            'today': date.today(),
            'prev_week': prev_week,
            'next_week': next_week,
            'time_slots': self.generate_time_slots(),
        }

        return render(request, self.template_name, context)

    def generate_time_slots(self):
        """Generate time slots for the weekly view."""
        slots = []
        start_hour = 8  # 8 AM
        end_hour = 20  # 8 PM

        for hour in range(start_hour, end_hour + 1):
            for minute in [0, 30]:
                time_obj = datetime.strptime(f'{hour:02d}:{minute:02d}', '%H:%M').time()
                slots.append(time_obj)

        return slots


class DailyCalendarView(CalendarView):
    """Daily calendar view for tour events."""

    template_name = 'bookings/calendar_day.html'

    def get(self, request, year=None, month=None, day=None):
        """Handle GET request for daily calendar."""
        # Default to today if not specified
        if year is None or month is None or day is None:
            target_date = date.today()
        else:
            try:
                target_date = date(int(year), int(month), int(day))
            except ValueError:
                raise Http404('Invalid date')

        # Get events for the day
        events = self.get_events_for_period(target_date, target_date)

        # Sort events by time
        def get_event_start_time(event):
            if event.start_time:
                return event.start_time.time()
            else:
                return datetime.min.time()

        events = sorted(events, key=get_event_start_time)

        # Calculate navigation dates
        prev_day = target_date - timedelta(days=1)
        next_day = target_date + timedelta(days=1)

        context = {
            'target_date': target_date,
            'events': events,
            'today': date.today(),
            'prev_day': prev_day,
            'next_day': next_day,
            'time_slots': self.generate_time_slots(),
        }

        return render(request, self.template_name, context)

    def generate_time_slots(self):
        """Generate time slots for the daily view."""
        slots = []
        start_hour = 6  # 6 AM
        end_hour = 22  # 10 PM

        for hour in range(start_hour, end_hour + 1):
            for minute in [0, 30]:
                time_obj = datetime.strptime(f'{hour:02d}:{minute:02d}', '%H:%M').time()
                slots.append(time_obj)

        return slots


# Booking Views


class BookingListView(BusinessAccessMixin, ListView):
    """List view for bookings."""

    model = Booking
    template_name = 'bookings/booking_list.html'
    context_object_name = 'bookings'
    paginate_by = 20

    def get_queryset(self):
        """Filter bookings based on user's business access."""
        queryset = Booking.objects.select_related(
            'client', 'tour_event', 'business'
        ).prefetch_related('participants__traveler')

        # Filter by user's accessible businesses using the mixin
        queryset = self.filter_by_accessible_businesses(queryset)

        # Filter by status if provided
        status = self.request.GET.get('status')
        if status:
            queryset = queryset.filter(status=status)

        # Search functionality
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(booking_reference__icontains=search)
                | Q(client__first_name__icontains=search)
                | Q(client__last_name__icontains=search)
                | Q(client__company_name__icontains=search)
            )

        return queryset.order_by('-booking_date')

    def get_context_data(self, **kwargs):
        """Add additional context."""
        context = super().get_context_data(**kwargs)
        context['status_choices'] = Booking.Status.choices
        context['current_status'] = self.request.GET.get('status', '')
        context['search_query'] = self.request.GET.get('search', '')
        return context


class BookingDetailView(LoginRequiredMixin, DetailView):
    """Detail view for a booking."""

    model = Booking
    template_name = 'bookings/booking_detail.html'
    context_object_name = 'booking'

    def get_object(self):
        """Get booking and check permissions."""
        booking = get_object_or_404(
            Booking.objects.select_related(
                'client', 'tour_event', 'business', 'quote'
            ).prefetch_related('participants__traveler'),
            pk=self.kwargs['pk'],
        )

        # Check if user has access to this booking's business
        if not self.request.user.is_superuser:
            if (
                booking.business.created_by != self.request.user
                and self.request.user not in booking.business.users.all()
            ):
                raise PermissionError("You don't have access to this booking")

        return booking


class BookingCreateView(LoginRequiredMixin, CreateView):
    """Create view for bookings."""

    model = Booking
    template_name = 'bookings/booking_form.html'
    fields = [
        'business',
        'client',
        'tour_event',
        'number_of_participants',
        'total_amount',
        'deposit_amount',
        'special_requests',
    ]

    def get_form(self, form_class=None):
        """Limit choices to user's accessible businesses."""
        form = super().get_form(form_class)

        if not self.request.user.is_superuser:
            from businesses.models import Business
            from clients.models import Client

            accessible_businesses = Business.objects.filter(
                Q(created_by=self.request.user) | Q(users=self.request.user)
            ).distinct()

            form.fields['business'].queryset = accessible_businesses
            form.fields['client'].queryset = Client.objects.filter(
                business__in=accessible_businesses
            )
            form.fields['tour_event'].queryset = TourEvent.objects.filter(
                business__in=accessible_businesses
            )

        return form

    def form_valid(self, form):
        """Handle successful form submission."""
        form.instance.created_by = self.request.user

        # Check if tour event has capacity
        tour_event = form.instance.tour_event
        if not tour_event.can_add_participants(form.instance.number_of_participants):
            messages.error(
                self.request,
                f'Not enough capacity. Only {tour_event.available_spots} spots available.',
            )
            return self.form_invalid(form)

        response = super().form_valid(form)

        # Update tour event participant count
        tour_event.current_participants += form.instance.number_of_participants
        tour_event.save()

        messages.success(
            self.request,
            f'Booking {form.instance.booking_reference} created successfully!',
        )
        return response

    def get_success_url(self):
        return reverse('bookings:booking_detail', kwargs={'pk': self.object.pk})


class BookingUpdateView(LoginRequiredMixin, UpdateView):
    """Update view for bookings."""

    model = Booking
    template_name = 'bookings/booking_form.html'
    fields = [
        'number_of_participants',
        'total_amount',
        'deposit_amount',
        'deposit_paid',
        'full_payment_received',
        'special_requests',
        'internal_notes',
    ]

    def get_object(self):
        """Get booking and check permissions."""
        booking = get_object_or_404(Booking, pk=self.kwargs['pk'])

        # Check if user has access to this booking's business
        if not self.request.user.is_superuser:
            if (
                booking.business.created_by != self.request.user
                and self.request.user not in booking.business.users.all()
            ):
                raise PermissionError("You don't have access to this booking")

        return booking

    def form_valid(self, form):
        """Handle successful form submission."""
        old_participants = self.object.number_of_participants
        new_participants = form.instance.number_of_participants
        participant_diff = new_participants - old_participants

        # Check capacity if increasing participants
        if participant_diff > 0:
            tour_event = self.object.tour_event
            if not tour_event.can_add_participants(participant_diff):
                messages.error(
                    self.request,
                    f'Not enough capacity. Only {tour_event.available_spots} spots available.',
                )
                return self.form_invalid(form)

        response = super().form_valid(form)

        # Update tour event participant count
        if participant_diff != 0:
            tour_event = self.object.tour_event
            tour_event.current_participants += participant_diff
            tour_event.save()

        messages.success(
            self.request,
            f'Booking {self.object.booking_reference} updated successfully!',
        )
        return response

    def get_success_url(self):
        return reverse('bookings:booking_detail', kwargs={'pk': self.object.pk})


class BookingConfirmView(LoginRequiredMixin, TemplateView):
    """View to confirm a booking."""

    def post(self, request, *args, **kwargs):
        booking = get_object_or_404(Booking, pk=kwargs['pk'])

        # Check permissions
        if not request.user.is_superuser:
            if (
                booking.business.created_by != request.user
                and request.user not in booking.business.users.all()
            ):
                raise PermissionError("You don't have access to this booking")

        if booking.confirm_booking(user=request.user):
            messages.success(request, f'Booking {booking.booking_reference} confirmed!')
        else:
            messages.error(request, 'Booking could not be confirmed.')

        return redirect('bookings:booking_detail', pk=booking.pk)


class BookingCancelView(LoginRequiredMixin, TemplateView):
    """View to cancel a booking."""

    def post(self, request, *args, **kwargs):
        booking = get_object_or_404(Booking, pk=kwargs['pk'])

        # Check permissions
        if not request.user.is_superuser:
            if (
                booking.business.created_by != request.user
                and request.user not in booking.business.users.all()
            ):
                raise PermissionError("You don't have access to this booking")

        reason = request.POST.get('reason', '')
        refund_amount = request.POST.get('refund_amount')

        if refund_amount:
            try:
                refund_amount = float(refund_amount)
            except ValueError:
                refund_amount = None

        if booking.cancel_booking(
            reason=reason, refund_amount=refund_amount, user=request.user
        ):
            # Update tour event participant count
            tour_event = booking.tour_event
            tour_event.current_participants -= booking.number_of_participants
            tour_event.save()

            messages.success(request, f'Booking {booking.booking_reference} cancelled!')
        else:
            messages.error(request, 'Booking could not be cancelled.')

        return redirect('bookings:booking_detail', pk=booking.pk)


class BookingDashboardView(LoginRequiredMixin, TemplateView):
    """Dashboard view showing booking statistics and recent activity."""

    template_name = 'bookings/dashboard.html'

    def get_context_data(self, **kwargs):
        """Add dashboard statistics."""
        context = super().get_context_data(**kwargs)

        # Get user's accessible businesses
        # if self.request.user.is_superuser:
        #     accessible_businesses = Business.objects.all()
        # else:
        #     accessible_businesses = Business.objects.filter(
        #         Q(created_by=self.request.user) | Q(users=self.request.user)
        #     ).distinct()

        # Get bookings for accessible businesses
        bookings = Booking.objects.all()

        # Statistics
        context['total_bookings'] = bookings.count()
        context['pending_bookings'] = bookings.filter(
            status=Booking.Status.PENDING
        ).count()
        context['confirmed_bookings'] = bookings.filter(
            status=Booking.Status.CONFIRMED
        ).count()
        context['completed_bookings'] = bookings.filter(
            status=Booking.Status.COMPLETED
        ).count()
        context['cancelled_bookings'] = bookings.filter(
            status=Booking.Status.CANCELLED
        ).count()

        # Revenue statistics
        confirmed_bookings = bookings.filter(
            status__in=[Booking.Status.CONFIRMED, Booking.Status.COMPLETED]
        )
        context['total_revenue'] = (
            confirmed_bookings.aggregate(total=Sum('total_amount'))['total'] or 0
        )
        context['deposits_collected'] = (
            confirmed_bookings.filter(deposit_paid=True).aggregate(
                total=Sum('deposit_amount')
            )['total']
            or 0
        )

        # Recent bookings
        context['recent_bookings'] = bookings.select_related(
            'client', 'tour_event'
        ).order_by('-booking_date')[:10]

        # Upcoming events
        context['upcoming_events'] = (
            TourEvent.objects.filter(is_cancelled=False)
            .select_related('event')
            .prefetch_related('event__occurrence')
            .order_by('event__occurrence__start_time')[:10]
        )

        return context


class ParticipantManagementView(LoginRequiredMixin, DetailView):
    """View to manage participants for a booking."""

    model = Booking
    template_name = 'bookings/participant_management.html'
    context_object_name = 'booking'

    def get_object(self):
        """Get booking and check permissions."""
        booking = get_object_or_404(
            Booking.objects.select_related('client', 'business').prefetch_related(
                'participants__traveler', 'client__travelers'
            ),
            pk=self.kwargs['pk'],
        )

        # Check permissions
        if not self.request.user.is_superuser:
            if (
                booking.business.created_by != self.request.user
                and self.request.user not in booking.business.users.all()
            ):
                raise PermissionError("You don't have access to this booking")

        return booking

    def get_context_data(self, **kwargs):
        """Add available travelers."""
        context = super().get_context_data(**kwargs)

        # Get travelers for this client that aren't already participants
        existing_participant_ids = self.object.participants.values_list(
            'traveler_id', flat=True
        )
        context['available_travelers'] = self.object.client.travelers.exclude(
            id__in=existing_participant_ids
        )

        return context

    def post(self, request, *args, **kwargs):
        """Handle participant management actions."""
        booking = self.get_object()
        action = request.POST.get('action')

        if action == 'add_participant':
            traveler_id = request.POST.get('traveler_id')
            try:
                traveler = Traveler.objects.get(id=traveler_id, client=booking.client)
                participant, created = BookingParticipant.objects.get_or_create(
                    booking=booking, traveler=traveler
                )
                if created:
                    messages.success(request, f'Added {traveler.full_name} to booking.')
                else:
                    messages.info(
                        request, f'{traveler.full_name} is already a participant.'
                    )
            except Traveler.DoesNotExist:
                messages.error(request, 'Invalid traveler selected.')

        elif action == 'remove_participant':
            participant_id = request.POST.get('participant_id')
            try:
                participant = BookingParticipant.objects.get(
                    id=participant_id, booking=booking
                )
                traveler_name = participant.traveler.full_name
                participant.delete()
                messages.success(request, f'Removed {traveler_name} from booking.')
            except BookingParticipant.DoesNotExist:
                messages.error(request, 'Invalid participant selected.')

        elif action == 'check_in':
            participant_id = request.POST.get('participant_id')
            try:
                participant = BookingParticipant.objects.get(
                    id=participant_id, booking=booking
                )
                if participant.check_in():
                    messages.success(
                        request,
                        f'{participant.traveler.full_name} checked in successfully.',
                    )
                else:
                    messages.info(
                        request,
                        f'{participant.traveler.full_name} was already checked in.',
                    )
            except BookingParticipant.DoesNotExist:
                messages.error(request, 'Invalid participant selected.')

        elif action == 'mark_no_show':
            participant_id = request.POST.get('participant_id')
            try:
                participant = BookingParticipant.objects.get(
                    id=participant_id, booking=booking
                )
                if participant.mark_no_show():
                    messages.success(
                        request, f'{participant.traveler.full_name} marked as no-show.'
                    )
                else:
                    messages.info(
                        request,
                        f'{participant.traveler.full_name} was already checked in.',
                    )
            except BookingParticipant.DoesNotExist:
                messages.error(request, 'Invalid participant selected.')

        return redirect('bookings:participant_management', pk=booking.pk)
