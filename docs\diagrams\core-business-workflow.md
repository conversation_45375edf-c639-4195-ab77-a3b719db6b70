# Tour Management SaaS - Core Business Workflow

This diagram illustrates the complete end-to-end business process from lead generation to tour completion.

## Diagram

```mermaid
flowchart TD
    %% Lead Generation and Client Management
    A[Lead Inquiry] --> B[Create Client Profile]
    B --> C[Add Traveler Information]
    C --> D[Assess Requirements]

    %% Quote Creation Process
    D --> E[Create Quote]
    E --> F[Add Quote Items/Services]
    F --> G[Calculate Pricing]
    G --> H[Set Terms & Conditions]
    H --> I[Generate Public Quote Link]
    I --> J[Send Quote to Client]

    %% Quote Review and Approval
    J --> K{Client Reviews Quote}
    K -->|Needs Changes| L[Modify Quote]
    L --> G
    K -->|Rejects| M[Quote Rejected]
    K -->|Approves| N[Quote Approved]

    %% Booking Creation
    N --> O[Create Booking]
    O --> P[Schedule Tour Event]
    P --> Q[Add Booking Participants]
    Q --> R[Confirm Booking Details]

    %% Invoice and Payment
    R --> S[Generate Invoice]
    S --> T[Create Payment Link]
    T --> U[Send Invoice to Client]
    U --> V{Payment Received?}
    V -->|No| W[Send Payment Reminder]
    W --> V
    V -->|Yes| X[Mark Invoice as Paid]

    %% Tour Execution
    X --> Y[Tour Event Day]
    Y --> Z[Check-in Participants]
    Z --> AA[Conduct Tour]
    AA --> BB[Mark Booking Complete]

    %% Post-Tour Activities
    BB --> CC[Generate Receipt]
    CC --> DD[Send Completion Confirmation]
    DD --> EE[Request Feedback]
    EE --> FF[Archive Booking]

    %% Exception Handling
    M --> GG[Archive Quote]
    K -->|Expires| HH[Mark Quote Expired]
    HH --> GG

    %% Cancellation Flow
    R --> II{Cancellation Request?}
    II -->|Yes| JJ[Process Cancellation]
    JJ --> KK[Apply Cancellation Policy]
    KK --> LL[Issue Refund if Applicable]
    LL --> MM[Update Booking Status]

    %% Styling - High Contrast Colors
    classDef processBox fill:#ffffff,stroke:#1565c0,stroke-width:3px,color:#000000
    classDef decisionBox fill:#fff8e1,stroke:#ef6c00,stroke-width:3px,color:#000000
    classDef completedBox fill:#e8f5e8,stroke:#2e7d32,stroke-width:3px,color:#000000
    classDef errorBox fill:#ffebee,stroke:#d32f2f,stroke-width:3px,color:#000000

    class A,B,C,D,E,F,G,H,I,J,L,O,P,Q,R,S,T,U,W,X,Y,Z,AA,BB,CC,DD,EE processBox
    class K,V,II decisionBox
    class FF,GG,MM completedBox
    class M,HH errorBox
```

## Workflow Phases

### 1. Lead Generation & Client Setup

- **Lead Inquiry**: Initial customer contact
- **Client Profile**: Create customer record
- **Traveler Information**: Add participant details
- **Requirements Assessment**: Understand customer needs

### 2. Quote Creation & Management

- **Quote Creation**: Build pricing proposal
- **Service Items**: Add tour components
- **Pricing Calculation**: Compute costs and taxes
- **Terms & Conditions**: Set business terms
- **Public Link**: Generate secure quote access
- **Client Communication**: Send quote to customer

### 3. Quote Review Process

- **Client Review**: Customer evaluates proposal
- **Modification Loop**: Handle change requests
- **Approval/Rejection**: Final decision point
- **Status Updates**: Track quote lifecycle

### 4. Booking & Scheduling

- **Booking Creation**: Convert approved quote
- **Event Scheduling**: Set tour dates/times
- **Participant Management**: Add travelers to booking
- **Confirmation**: Finalize booking details

### 5. Financial Processing

- **Invoice Generation**: Create billing document
- **Payment Links**: Generate payment URLs
- **Payment Tracking**: Monitor payment status
- **Reminders**: Handle overdue payments

### 6. Tour Execution

- **Event Day**: Actual tour date
- **Check-in Process**: Participant registration
- **Tour Conduct**: Service delivery
- **Completion**: Mark tour as finished

### 7. Post-Tour Activities

- **Receipt Generation**: Create completion documents
- **Confirmation**: Send completion notice
- **Feedback Collection**: Gather customer reviews
- **Archival**: Store completed records

### 8. Exception Handling

- **Quote Rejection**: Handle declined proposals
- **Expiration**: Manage expired quotes
- **Cancellations**: Process booking cancellations
- **Refunds**: Handle payment reversals

## Key Decision Points

1. **Quote Review**: Client approval/rejection/modification
2. **Payment Status**: Received/pending/failed
3. **Cancellation Requests**: Process or deny
4. **Quote Expiration**: Automatic status updates

## Business Rules

- Quotes expire after validity period
- Invoices generated only from approved quotes
- Payments tracked through webhooks
- Cancellation policies applied automatically
- All actions logged for audit trail
