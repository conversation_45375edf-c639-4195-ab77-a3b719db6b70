{% load static %}

<c-layouts.dashboard>
  <c-slot name="title">
    Manage Participants - {{ booking.booking_reference }}
  </c-slot>

  <c-slot name="main">
    <div class="container mx-auto px-4 py-8">
      <div class="flex justify-between items-center mb-8">
        <div>
          <h1 class="text-3xl font-bold text-gray-900">Manage Participants</h1>
          <p class="text-gray-600 mt-2">Booking {{ booking.booking_reference }} -
            {{ booking.client.display_name }}</p>
        </div>
        <a href="{% url 'bookings:booking_detail' booking.pk %}" class="text-gray-600 hover:text-gray-800">
          ← Back to Booking
        </a>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Current Participants -->
        <div class="bg-white rounded-lg shadow">
          <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-semibold text-gray-900">Current Participants</h2>
            <p class="text-sm text-gray-600 mt-1">{{ booking.participants.count }} of
              {{ booking.number_of_participants }} participants</p>
          </div>
          <div class="p-6">
            {% if booking.participants.exists %}
            <div class="space-y-4">
              {% for participant in booking.participants.all %}
              <div class="border border-gray-200 rounded-lg p-4">
                <div class="flex justify-between items-start">
                  <div class="flex-1">
                    <h3 class="font-medium text-gray-900">{{ participant.traveler.full_name }}</h3>
                    <p class="text-sm text-gray-600">
                      {{ participant.traveler.email|default:"No email" }}
                    </p>
                    {% if participant.traveler.phone %}
                    <p class="text-sm text-gray-600">{{ participant.traveler.phone }}</p>
                    {% endif %}
                    {% if participant.is_primary_contact %}
                    <span
                      class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 mt-2">
                      Primary Contact
                    </span>
                    {% endif %}
                    {% if participant.special_requirements %}
                    <p class="text-sm text-gray-600 mt-2">
                      <strong>Special Requirements:</strong>
                      {{ participant.special_requirements }}
                    </p>
                    {% endif %}
                  </div>
                  <div class="ml-4 text-right">
                    {% if participant.checked_in %}
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                      Checked In
                    </span>
                    <p class="text-xs text-gray-500 mt-1">
                      {{ participant.check_in_time|date:"M d, H:i" }}</p>
                    {% elif participant.no_show %}
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                      No Show
                    </span>
                    {% else %}
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                      Not Checked In
                    </span>
                    {% endif %}
                  </div>
                </div>

                <!-- Participant Actions -->
                <div class="mt-4 flex flex-wrap gap-2">
                  {% if not participant.checked_in and not participant.no_show %}
                  <form method="post" class="inline">
                    {% csrf_token %}
                    <input type="hidden" name="action" value="check_in">
                    <input type="hidden" name="participant_id" value="{{ participant.pk }}">
                    <button type="submit"
                      class="text-xs bg-green-600 text-white px-3 py-1 rounded hover:bg-green-700 transition-colors">
                      Check In
                    </button>
                  </form>
                  <form method="post" class="inline">
                    {% csrf_token %}
                    <input type="hidden" name="action" value="mark_no_show">
                    <input type="hidden" name="participant_id" value="{{ participant.pk }}">
                    <button type="submit"
                      class="text-xs bg-red-600 text-white px-3 py-1 rounded hover:bg-red-700 transition-colors">
                      Mark No Show
                    </button>
                  </form>
                  {% endif %}
                  <form method="post" class="inline">
                    {% csrf_token %}
                    <input type="hidden" name="action" value="remove_participant">
                    <input type="hidden" name="participant_id" value="{{ participant.pk }}">
                    <button type="submit"
                      class="text-xs bg-gray-600 text-white px-3 py-1 rounded hover:bg-gray-700 transition-colors"
                      onclick="return confirm('Are you sure you want to remove this participant?')">
                      Remove
                    </button>
                  </form>
                </div>
              </div>
              {% endfor %}
            </div>
            {% else %}
            <div class="text-center py-8">
              <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z">
                </path>
              </svg>
              <h3 class="mt-2 text-sm font-medium text-gray-900">No participants assigned</h3>
              <p class="mt-1 text-sm text-gray-500">Add participants from the available travelers list.
              </p>
            </div>
            {% endif %}
          </div>
        </div>

        <!-- Available Travelers -->
        <div class="bg-white rounded-lg shadow">
          <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-semibold text-gray-900">Available Travelers</h2>
            <p class="text-sm text-gray-600 mt-1">Travelers from {{ booking.client.display_name }}</p>
          </div>
          <div class="p-6">
            {% if available_travelers %}
            <div class="space-y-4">
              {% for traveler in available_travelers %}
              <div class="border border-gray-200 rounded-lg p-4">
                <div class="flex justify-between items-start">
                  <div class="flex-1">
                    <h3 class="font-medium text-gray-900">{{ traveler.full_name }}</h3>
                    <p class="text-sm text-gray-600">{{ traveler.email|default:"No email" }}</p>
                    {% if traveler.phone %}
                    <p class="text-sm text-gray-600">{{ traveler.phone }}</p>
                    {% endif %}
                    {% if traveler.date_of_birth %}
                    <p class="text-sm text-gray-600">Age: {{ traveler.age }}</p>
                    {% endif %}
                    {% if traveler.dietary_restrictions %}
                    <p class="text-sm text-gray-600">
                      <strong>Dietary:</strong> {{ traveler.dietary_restrictions }}
                    </p>
                    {% endif %}
                    {% if traveler.medical_conditions %}
                    <p class="text-sm text-gray-600">
                      <strong>Medical:</strong> {{ traveler.medical_conditions }}
                    </p>
                    {% endif %}
                  </div>
                  <div class="ml-4">
                    <form method="post" class="inline">
                      {% csrf_token %}
                      <input type="hidden" name="action" value="add_participant">
                      <input type="hidden" name="traveler_id" value="{{ traveler.pk }}">
                      <button type="submit"
                        class="bg-blue-600 text-white px-3 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm">
                        Add to Booking
                      </button>
                    </form>
                  </div>
                </div>
              </div>
              {% endfor %}
            </div>
            {% else %}
            <div class="text-center py-8">
              <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z">
                </path>
              </svg>
              <h3 class="mt-2 text-sm font-medium text-gray-900">No available travelers</h3>
              <p class="mt-1 text-sm text-gray-500">All travelers for this client are already
                participants, or
                no travelers exist.</p>
              <div class="mt-6">
                <a href="{% url 'clients:traveler_create' %}?client={{ booking.client.pk }}"
                  class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                  Add New Traveler
                </a>
              </div>
            </div>
            {% endif %}
          </div>
        </div>
      </div>

      <!-- Booking Summary -->
      <div class="mt-6 bg-white rounded-lg shadow p-6">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">Booking Summary</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div class="bg-gray-50 rounded-lg p-4">
            <div class="flex items-center">
              <div class="p-2 bg-blue-100 rounded-lg">
                <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z">
                  </path>
                </svg>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Total Participants</p>
                <p class="text-2xl font-semibold text-gray-900">
                  {{ booking.participants.count }}/{{ booking.number_of_participants }}</p>
              </div>
            </div>
          </div>

          <div class="bg-gray-50 rounded-lg p-4">
            <div class="flex items-center">
              <div class="p-2 bg-green-100 rounded-lg">
                <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Checked In</p>
                <p class="text-2xl font-semibold text-gray-900">
                  {{ booking.participants.filter.checked_in.count }}</p>
              </div>
            </div>
          </div>

          <div class="bg-gray-50 rounded-lg p-4">
            <div class="flex items-center">
              <div class="p-2 bg-red-100 rounded-lg">
                <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">No Shows</p>
                <p class="text-2xl font-semibold text-gray-900">
                  {{ booking.participants.filter.no_show.count }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Event Information -->
      <div class="mt-6 bg-white rounded-lg shadow p-6">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">Event Information</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700">Event</label>
            <p class="mt-1 text-sm text-gray-900">{{ booking.tour_event.event.title }}</p>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700">Type</label>
            <p class="mt-1 text-sm text-gray-900">{{ booking.tour_event.get_event_type_display }}</p>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700">Start Time</label>
            <p class="mt-1 text-sm text-gray-900">
              {{ booking.tour_event.event.start_time|date:"M d, Y H:i" }}
            </p>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700">End Time</label>
            <p class="mt-1 text-sm text-gray-900">{{ booking.tour_event.event.end_time|date:"M d, Y H:i" }}
            </p>
          </div>
          {% if booking.tour_event.meeting_point %}
          <div class="md:col-span-2">
            <label class="block text-sm font-medium text-gray-700">Meeting Point</label>
            <p class="mt-1 text-sm text-gray-900">{{ booking.tour_event.meeting_point }}</p>
          </div>
          {% endif %}
        </div>
      </div>
    </div>
  </c-slot>
</c-layouts.dashboard>
