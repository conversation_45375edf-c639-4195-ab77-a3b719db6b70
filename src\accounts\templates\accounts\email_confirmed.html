{% load i18n %}

<c-layouts.base title="{% trans 'Email Confirmed' %} - Tour Business Management">
  <c-slot name="content">
    <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div class="max-w-md w-full space-y-8">
        <div class="text-center">
          <div class="mx-auto h-16 w-16 flex items-center justify-center bg-green-100 rounded-full">
            <svg class="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
            {% trans "Email Confirmed!" %}
          </h2>
          <p class="mt-2 text-center text-sm text-gray-600">
            {% trans "Your account has been successfully activated" %}
          </p>
        </div>

        <div class="bg-white shadow rounded-lg p-6">
          <div class="space-y-4">
            <div class="flex items-center space-x-3">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                  <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                </div>
              </div>
              <div>
                <p class="text-sm font-medium text-gray-900">{% trans "Email Verified" %}</p>
                <p class="text-sm text-gray-500">{% trans "Your email address has been confirmed" %}</p>
              </div>
            </div>

            <div class="flex items-center space-x-3">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z">
                    </path>
                  </svg>
                </div>
              </div>
              <div>
                <p class="text-sm font-medium text-gray-900">{% trans "Administrator Role Assigned" %}
                </p>
                <p class="text-sm text-gray-500">{% trans "You can now manage your business" %}</p>
              </div>
            </div>

            <div class="flex items-center space-x-3">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                  <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h4M9 7h6m-6 4h6m-6 4h6">
                    </path>
                  </svg>
                </div>
              </div>
              <div>
                <p class="text-sm font-medium text-gray-900">{% trans "Business Profile Created" %}</p>
                <p class="text-sm text-gray-500">{% trans "Ready for setup and customization" %}</p>
              </div>
            </div>
          </div>

          <div class="mt-6 pt-6 border-t border-gray-200">
            <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
              <div class="flex">
                <div class="flex-shrink-0">
                  <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd"
                      d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                      clip-rule="evenodd"></path>
                  </svg>
                </div>
                <div class="ml-3">
                  <h3 class="text-sm font-medium text-blue-800">
                    {% trans "Next Steps" %}
                  </h3>
                  <div class="mt-2 text-sm text-blue-700">
                    <p>{% trans "Please log in to complete your profile setup and start using the platform." %}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="mt-6">
            <a href="{% url 'accounts:login' %}"
              class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
              {% trans "Continue to Login" %}
            </a>
          </div>
        </div>
      </div>
    </div>
  </c-slot>
</c-layouts.base>
