# Business Access Control Mixins

This document describes the business access control mixins that limit user access to resources owned by their business.

## Overview

The business access control mixins provide a standardized way to ensure that users can only access resources that belong to businesses they have access to. This is essential for multi-tenant applications where data isolation between businesses is critical.

## Available Mixins

### 1. BusinessAccessMixin

The base mixin that provides core business access control functionality.

**Features:**
- Get businesses accessible to the current user
- Filter querysets by accessible businesses
- Check if a user has access to a specific business
- Get the user's primary business

**Usage:**
```python
from core.business_mixins import BusinessAccessMixin

class MyListView(BusinessAccessMixin, ListView):
    model = MyModel
    
    def get_queryset(self):
        queryset = super().get_queryset()
        return self.filter_by_accessible_businesses(queryset)
```

**Methods:**
- `get_accessible_businesses()` - Returns QuerySet of accessible businesses
- `filter_by_accessible_businesses(queryset, business_field='business')` - Filters queryset by accessible businesses
- `check_business_access(business)` - Checks if user has access to a specific business
- `get_user_business()` - Gets the user's primary business

### 2. BusinessOwnerRequiredMixin

Extends BusinessAccessMixin to require business owner privileges.

**Usage:**
```python
from core.business_mixins import BusinessOwnerRequiredMixin

class AdminOnlyView(BusinessOwnerRequiredMixin, TemplateView):
    template_name = 'admin_only.html'
```

### 3. BusinessObjectMixin

For views that work with objects belonging to a business. Automatically checks business access when getting objects.

**Usage:**
```python
from core.business_mixins import BusinessObjectMixin

class MyDetailView(BusinessObjectMixin, DetailView):
    model = MyModel
    business_field = 'business'  # Override if different field name
```

**Features:**
- Automatically filters querysets by accessible businesses
- Checks business access when getting individual objects
- Raises Http404 if user doesn't have access

### 4. BusinessCreateMixin

For views that create objects belonging to a business. Automatically sets the business field.

**Usage:**
```python
from core.business_mixins import BusinessCreateMixin

class MyCreateView(BusinessCreateMixin, CreateView):
    model = MyModel
    form_class = MyForm
    business_field = 'business'  # Override if different field name
```

**Features:**
- Automatically sets the business field to the user's primary business
- Ensures created objects belong to the correct business

### 5. MultiBusinessMixin

For views that need to work across multiple businesses with business switching capability.

**Usage:**
```python
from core.business_mixins import MultiBusinessMixin

class MultiBusinessView(MultiBusinessMixin, TemplateView):
    template_name = 'multi_business.html'
```

**Features:**
- Get currently selected business from URL or session
- Switch between accessible businesses
- Store business selection in session
- Add business context to templates

## Access Control Rules

### Superuser Access
- Superusers have access to all businesses
- No restrictions apply to superusers

### Business Owner Access
- Can access businesses they created
- Can access businesses where they are listed as users

### Agent Access
- Can access businesses where they are listed as users
- Cannot access businesses they don't belong to

### Customer Access
- Typically have no business access
- May have limited access based on specific business rules

## Implementation Examples

### Example 1: Simple List View with Business Filtering

```python
from core.business_mixins import BusinessAccessMixin
from django.views.generic import ListView
from .models import Invoice

class InvoiceListView(BusinessAccessMixin, ListView):
    model = Invoice
    template_name = 'invoices/list.html'
    
    def get_queryset(self):
        queryset = super().get_queryset()
        return self.filter_by_accessible_businesses(queryset)
```

### Example 2: Detail View with Access Control

```python
from core.business_mixins import BusinessObjectMixin
from django.views.generic import DetailView
from .models import Quote

class QuoteDetailView(BusinessObjectMixin, DetailView):
    model = Quote
    template_name = 'quotes/detail.html'
    business_field = 'business'  # Quote.business field
```

### Example 3: Create View with Auto-Assignment

```python
from core.business_mixins import BusinessCreateMixin
from django.views.generic import CreateView
from .models import Client
from .forms import ClientForm

class ClientCreateView(BusinessCreateMixin, CreateView):
    model = Client
    form_class = ClientForm
    template_name = 'clients/create.html'
    business_field = 'business'  # Client.business field
```

### Example 4: Business Owner Only View

```python
from core.business_mixins import BusinessOwnerRequiredMixin
from django.views.generic import TemplateView

class UserManagementView(BusinessOwnerRequiredMixin, TemplateView):
    template_name = 'accounts/user_management.html'
```

## Error Handling

### PermissionDenied
Raised when:
- User has no business access (`get_user_business()`)
- User is not a business owner (BusinessOwnerRequiredMixin)

### Http404
Raised when:
- Object doesn't exist or user doesn't have access (BusinessObjectMixin)

## Best Practices

1. **Always use the appropriate mixin** for your view type
2. **Specify the business_field** if it's not 'business'
3. **Combine mixins** when you need multiple features
4. **Test access control** thoroughly with different user types
5. **Use BusinessOwnerRequiredMixin** for administrative functions

## Testing

Always test your views with different user types:

```python
def test_business_access_control(self):
    # Test with business owner
    self.client.login(username='owner', password='pass')
    response = self.client.get('/my-view/')
    self.assertEqual(response.status_code, 200)
    
    # Test with agent from same business
    self.client.login(username='agent', password='pass')
    response = self.client.get('/my-view/')
    self.assertEqual(response.status_code, 200)
    
    # Test with user from different business
    self.client.login(username='other', password='pass')
    response = self.client.get('/my-view/')
    self.assertEqual(response.status_code, 403)  # or 404
```

## Migration Guide

To migrate existing views to use these mixins:

1. **Identify the view type** (list, detail, create, etc.)
2. **Choose the appropriate mixin**
3. **Replace manual business filtering** with mixin methods
4. **Update imports**
5. **Test thoroughly**

Example migration:

```python
# Before
class MyListView(LoginRequiredMixin, ListView):
    def get_queryset(self):
        queryset = super().get_queryset()
        if not self.request.user.is_superuser:
            accessible_businesses = Business.objects.filter(
                Q(created_by=self.request.user) | Q(users=self.request.user)
            ).distinct()
            queryset = queryset.filter(business__in=accessible_businesses)
        return queryset

# After
class MyListView(BusinessAccessMixin, ListView):
    def get_queryset(self):
        queryset = super().get_queryset()
        return self.filter_by_accessible_businesses(queryset)
```
