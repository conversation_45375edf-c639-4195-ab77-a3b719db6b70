"""Mixins for models, forms and views in the system."""

import uuid

from crispy_forms.helper import FormHelper
from crispy_forms.layout import Submit
from django.db import models
from django.utils.translation import gettext_lazy as _
from simple_history.models import HistoricalRecords


class CrispyFormsMixin:
    @property
    def helper(self):
        helper = FormHelper()
        helper.add_input(
            Submit(
                name='submit',
                value='Submit',
                css_class='px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 cursor-pointer',
            )
        )
        return helper


class TimeAuditModel(models.Model):
    """Mixin for models that need time-based audit fields."""

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        abstract = True
        indexes = [
            models.Index(fields=['created_at']),
        ]


class BaseModel(TimeAuditModel):
    """Base model for all models in the system."""

    id = models.UUIDField(
        primary_key=True,
        unique=True,
        default=uuid.uuid4,
        editable=False,
        help_text=_('Unique identifier for this record'),
    )
    # History tracking
    history = HistoricalRecords()

    class Meta:
        abstract = True
        indexes = [
            models.Index(fields=['created_at']),
        ]


class UserAuditModel(BaseModel):
    """Mixin for models that need user-based audit fields."""

    created_by = models.ForeignKey(
        'accounts.User',
        on_delete=models.PROTECT,
        related_name='%(class)ss_created',
        help_text=_('User who created this record'),
    )

    class Meta:
        abstract = True
        indexes = [
            models.Index(fields=['created_by']),
        ]
