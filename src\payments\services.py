"""
Lemon Squeezy API integration service.
Based on official Lemon Squeezy API documentation: https://docs.lemonsqueezy.com/api
"""

import hashlib
import hmac
import logging
from datetime import timedelta
from importlib import import_module
from typing import Dict, Optional

import requests
from django.conf import settings
from django.utils import timezone

from .models import Payment, PaymentLink, Subscription

logger = logging.getLogger(__name__)


class LemonSqueezyService:
    """
    Service for interacting with Lemon Squeezy API.

    Implements the official Lemon Squeezy API v1 specification.
    Rate limit: 300 API calls per minute.
    """

    def __init__(self):
        self.api_key = settings.LEMON_SQUEEZY_API_KEY
        self.store_id = settings.LEMON_SQUEEZY_STORE_ID
        self.webhook_secret = getattr(settings, 'LEMON_SQUEEZY_WEBHOOK_SECRET', '')
        self.base_url = settings.LEMON_SQUEEZY_API_URL
        self.headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/vnd.api+json',
            'Accept': 'application/vnd.api+json',
        }

        if not self.api_key:
            logger.warning('Lemon Squeezy API key not configured')
        if not self.store_id:
            logger.warning('Lemon Squeezy store ID not configured')

    def _make_request(
        self, method: str, endpoint: str, data: Optional[Dict] = None
    ) -> Optional[Dict]:
        """Make a request to the Lemon Squeezy API."""
        url = f'{self.base_url}/{endpoint}'

        try:
            response = requests.request(
                method=method, url=url, headers=self.headers, json=data, timeout=30
            )
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f'Lemon Squeezy API error: {e}')
            return None

    def validate_webhook_signature(self, payload: str, signature: str) -> bool:
        """
        Validate webhook signature according to Lemon Squeezy documentation.

        The signature is sent in the X-Signature header and is a hash made up of
        the signing secret plus the request body.

        Args:
            payload: Raw request body as string
            signature: Signature from X-Signature header

        Returns:
            bool: True if signature is valid, False otherwise
        """
        if not self.webhook_secret:
            logger.warning('Webhook secret not configured - cannot validate signature')
            return False

        if not signature:
            logger.warning('No signature provided in webhook request')
            return False

        # Remove 'sha256=' prefix if present
        if signature.startswith('sha256='):
            signature = signature[7:]

        # Create expected signature
        expected_signature = hmac.new(
            self.webhook_secret.encode('utf-8'), payload.encode('utf-8'), hashlib.sha256
        ).hexdigest()

        # Compare signatures using constant-time comparison
        return hmac.compare_digest(expected_signature, signature)

    def process_webhook_event(self, event_data: Dict) -> bool:
        """
        Process webhook event data according to event type.

        Args:
            event_data: Webhook event data

        Returns:
            bool: True if processed successfully, False otherwise
        """
        try:
            meta = event_data.get('meta', {})
            event_name = meta.get('event_name')

            if not event_name:
                logger.error('No event_name in webhook data')
                return False

            logger.info(f'Processing webhook event: {event_name}')

            # Route to appropriate handler based on event type
            if event_name.startswith('subscription_'):
                return self._handle_subscription_event(event_data, event_name)
            elif event_name.startswith('order_'):
                return self._handle_order_event(event_data, event_name)
            elif event_name.startswith('license_key_'):
                return self._handle_license_event(event_data, event_name)
            else:
                logger.info(f'Unhandled event type: {event_name}')
                return True  # Return True for unhandled but valid events

        except Exception as e:
            logger.error(f'Error processing webhook event: {e}')
            return False

    def create_checkout(self, invoice) -> Optional[Dict]:
        """Create a checkout session for an invoice."""
        checkout_data = {
            'data': {
                'type': 'checkouts',
                'attributes': {
                    'product_options': {
                        'name': f'Invoice {invoice.invoice_number}',
                        'description': f'Payment for invoice {invoice.invoice_number}',
                        'media': [],
                        'redirect_url': self._get_success_url(invoice),
                        'receipt_button_text': 'Go to Dashboard',
                        'receipt_link_url': self._get_success_url(invoice),
                    },
                    'checkout_options': {
                        'embed': False,
                        'media': True,
                        'logo': True,
                    },
                    'checkout_data': {
                        'email': invoice.client.email,
                        'name': invoice.client.full_name,
                        'billing_address': {
                            'country': invoice.client.country or 'US',
                            'zip': invoice.client.postal_code or '',
                        },
                        'tax_number': '',
                        'discount_code': '',
                        'custom': {
                            'invoice_id': str(invoice.id),
                            'business_id': str(invoice.business.id),
                            'client_id': str(invoice.client.id),
                        },
                    },
                    'expires_at': None,
                    'preview': True,
                    'test_mode': settings.DEBUG,
                },
                'relationships': {
                    'store': {'data': {'type': 'stores', 'id': self.store_id}},
                    'variant': {
                        'data': {
                            'type': 'variants',
                            'id': self._get_or_create_variant_for_invoice(invoice),
                        }
                    },
                },
            }
        }

        return self._make_request('POST', 'checkouts', checkout_data)

    def _get_or_create_variant_for_invoice(self, invoice) -> str:
        """Get or create a product variant for the invoice amount."""
        # For simplicity, we'll use a single product variant
        # In a real implementation, you might want to create dynamic variants
        # or use a single "Invoice Payment" product
        return '1'  # Replace with actual variant ID from your Lemon Squeezy store

    def _get_success_url(self, invoice) -> str:
        """Get the success URL after payment."""
        from django.contrib.sites.models import Site
        from django.urls import reverse

        try:
            current_site = Site.objects.get_current()
            domain = f'https://{current_site.domain}'
        except:
            domain = 'http://localhost:8000'  # Fallback for development

        return (
            f'{domain}{reverse("invoices:detail", kwargs={"invoice_id": invoice.id})}'
        )

    def get_checkout(self, checkout_id: str) -> Optional[Dict]:
        """Get checkout details by ID."""
        return self._make_request('GET', f'checkouts/{checkout_id}')

    def create_subscription(
        self, business, plan: str, customer_email: str
    ) -> Optional[Dict]:
        """Create a subscription for a business."""
        subscription_data = {
            'data': {
                'type': 'subscriptions',
                'attributes': {
                    'product_name': f'Tour Business Management - {plan.title()} Plan',
                    'variant_name': f'{plan.title()} Plan',
                    'user_name': business.name,
                    'user_email': customer_email,
                    'status': 'active',
                    'card_brand': '',
                    'card_last_four': '',
                    'pause': None,
                    'cancelled': False,
                    'trial_ends_at': None,
                    'billing_anchor': 1,
                    'urls': {
                        'update_payment_method': '',
                        'customer_portal': '',
                    },
                    'renews_at': (timezone.now() + timedelta(days=30)).isoformat(),
                    'ends_at': None,
                    'created_at': timezone.now().isoformat(),
                    'updated_at': timezone.now().isoformat(),
                    'test_mode': settings.DEBUG,
                },
                'relationships': {
                    'store': {'data': {'type': 'stores', 'id': self.store_id}},
                    'customer': {
                        'data': {
                            'type': 'customers',
                            'id': self._get_or_create_customer(
                                business, customer_email
                            ),
                        }
                    },
                },
            }
        }

        return self._make_request('POST', 'subscriptions', subscription_data)

    def _get_or_create_customer(self, business, email: str) -> str:
        """Get or create a customer in Lemon Squeezy."""
        # This is a simplified implementation
        # In practice, you'd want to store and retrieve customer IDs
        customer_data = {
            'data': {
                'type': 'customers',
                'attributes': {
                    'name': business.name,
                    'email': email,
                    'status': 'active',
                    'city': business.city,
                    'region': business.state_province,
                    'country': business.country,
                },
                'relationships': {
                    'store': {'data': {'type': 'stores', 'id': self.store_id}}
                },
            }
        }

        response = self._make_request('POST', 'customers', customer_data)
        if response and 'data' in response:
            return response['data']['id']
        return '1'  # Fallback customer ID

    def get_subscription(self, subscription_id: str) -> Optional[Dict]:
        """Get subscription details by ID."""
        return self._make_request('GET', f'subscriptions/{subscription_id}')

    def cancel_subscription(self, subscription_id: str) -> Optional[Dict]:
        """Cancel a subscription."""
        cancel_data = {
            'data': {
                'type': 'subscriptions',
                'id': subscription_id,
                'attributes': {'cancelled': True},
            }
        }

        return self._make_request(
            'PATCH', f'subscriptions/{subscription_id}', cancel_data
        )

    def _handle_subscription_created(self, attributes: Dict) -> bool:
        """Handle subscription_created event."""
        try:
            from businesses.models import Business

            # Extract custom data to find the business
            custom_data = attributes.get('custom', {})
            business_id = custom_data.get('business_id')

            if not business_id:
                logger.warning('No business_id in subscription_created event')
                return True

            business = Business.objects.get(id=business_id)

            # Create or update subscription
            subscription, created = Subscription.objects.get_or_create(
                business=business,
                lemon_squeezy_subscription_id=attributes.get('id'),
                defaults={
                    'lemon_squeezy_customer_id': attributes.get('customer_id', ''),
                    'status': Subscription.Status.ACTIVE,
                    'plan': Subscription.Plan.BASIC,  # Default plan
                    'current_period_start': timezone.now(),
                    'current_period_end': timezone.now() + timedelta(days=30),
                },
            )

            logger.info(
                f'Subscription {"created" if created else "updated"} for business {business.name}'
            )
            return True

        except Business.DoesNotExist:
            logger.error(f'Business {business_id} not found for subscription')
            return False
        except Exception as e:
            logger.error(f'Error handling subscription_created: {e}')
            return False

    def _handle_subscription_updated(self, attributes: Dict) -> bool:
        """Handle subscription_updated event."""
        try:
            subscription_id = attributes.get('id')
            subscription = Subscription.objects.get(
                lemon_squeezy_subscription_id=subscription_id
            )

            # Update subscription status
            status_mapping = {
                'active': Subscription.Status.ACTIVE,
                'cancelled': Subscription.Status.CANCELLED,
                'expired': Subscription.Status.EXPIRED,
                'paused': Subscription.Status.PAUSED,
            }

            new_status = status_mapping.get(attributes.get('status'))
            if new_status:
                subscription.status = new_status
                subscription.save()

            return True

        except Subscription.DoesNotExist:
            logger.error(f'Subscription {subscription_id} not found')
            return False
        except Exception as e:
            logger.error(f'Error handling subscription_updated: {e}')
            return False

    def _handle_subscription_cancelled(self, attributes: Dict) -> bool:
        """Handle subscription_cancelled event."""
        try:
            subscription_id = attributes.get('id')
            subscription = Subscription.objects.get(
                lemon_squeezy_subscription_id=subscription_id
            )

            subscription.status = Subscription.Status.CANCELLED
            subscription.save()

            logger.info(
                f'Subscription cancelled for business {subscription.business.name}'
            )
            return True

        except Subscription.DoesNotExist:
            logger.error(f'Subscription {subscription_id} not found')
            return False
        except Exception as e:
            logger.error(f'Error handling subscription_cancelled: {e}')
            return False

    def _handle_subscription_expired(self, attributes: Dict) -> bool:
        """Handle subscription_expired event."""
        try:
            subscription_id = attributes.get('id')
            subscription = Subscription.objects.get(
                lemon_squeezy_subscription_id=subscription_id
            )

            subscription.status = Subscription.Status.EXPIRED
            subscription.save()

            logger.info(
                f'Subscription expired for business {subscription.business.name}'
            )
            return True

        except Subscription.DoesNotExist:
            logger.error(f'Subscription {subscription_id} not found')
            return False
        except Exception as e:
            logger.error(f'Error handling subscription_expired: {e}')
            return False

    def _handle_subscription_payment_success(self, attributes: Dict) -> bool:
        """Handle subscription_payment_success event."""
        try:
            subscription_id = attributes.get('subscription_id')
            subscription = Subscription.objects.get(
                lemon_squeezy_subscription_id=subscription_id
            )

            # Create payment record
            Payment.objects.create(
                subscription=subscription,
                amount=attributes.get('total', 0),
                payment_method=Payment.PaymentMethod.LEMON_SQUEEZY,
                status=Payment.Status.COMPLETED,
                lemon_squeezy_order_id=attributes.get('id'),
                external_payment_id=attributes.get('id'),
                payment_processor='lemon_squeezy',
            )

            # Update subscription status to active
            subscription.status = Subscription.Status.ACTIVE
            subscription.save()

            return True

        except Subscription.DoesNotExist:
            logger.error(f'Subscription {subscription_id} not found')
            return False
        except Exception as e:
            logger.error(f'Error handling subscription_payment_success: {e}')
            return False

    def _handle_subscription_payment_failed(self, attributes: Dict) -> bool:
        """Handle subscription_payment_failed event."""
        try:
            subscription_id = attributes.get('subscription_id')
            subscription = Subscription.objects.get(
                lemon_squeezy_subscription_id=subscription_id
            )

            # Create failed payment record
            Payment.objects.create(
                subscription=subscription,
                amount=attributes.get('total', 0),
                payment_method=Payment.PaymentMethod.LEMON_SQUEEZY,
                status=Payment.Status.FAILED,
                lemon_squeezy_order_id=attributes.get('id'),
                external_payment_id=attributes.get('id'),
                payment_processor='lemon_squeezy',
                failure_reason=attributes.get('failure_reason', 'Payment failed'),
            )

            return True

        except Subscription.DoesNotExist:
            logger.error(f'Subscription {subscription_id} not found')
            return False
        except Exception as e:
            logger.error(f'Error handling subscription_payment_failed: {e}')
            return False

    def _handle_order_created(self, attributes: Dict) -> bool:
        """Handle order_created event."""
        try:
            from invoices.models import Invoice

            # Extract custom data to find the invoice
            custom_data = attributes.get('custom', {})
            invoice_id = custom_data.get('invoice_id')

            if not invoice_id:
                logger.warning('No invoice_id in order_created event')
                return True

            invoice = Invoice.objects.get(id=invoice_id)

            # Create payment record
            payment = Payment.objects.create(
                invoice=invoice,
                amount=invoice.total_amount,
                payment_method=Payment.PaymentMethod.LEMON_SQUEEZY,
                status=Payment.Status.COMPLETED,
                lemon_squeezy_order_id=attributes.get('id'),
                external_payment_id=attributes.get('id'),
                payment_processor='lemon_squeezy',
                reference_number=attributes.get('order_number', ''),
            )

            payment.mark_as_completed()

            # Mark payment link as used
            if hasattr(invoice, 'payment_link'):
                invoice.payment_link.mark_as_used()

            logger.info(f'Payment processed for invoice {invoice.invoice_number}')
            return True

        except Invoice.DoesNotExist:
            logger.error(f'Invoice {invoice_id} not found for order')
            return False
        except Exception as e:
            logger.error(f'Error handling order_created: {e}')
            return False

    def _handle_order_refunded(self, attributes: Dict) -> bool:
        """Handle order_refunded event."""
        try:
            order_id = attributes.get('id')

            # Find the payment by order ID
            payment = Payment.objects.get(lemon_squeezy_order_id=order_id)
            payment.status = Payment.Status.REFUNDED
            payment.save()

            logger.info(f'Payment refunded for order {order_id}')
            return True

        except Payment.DoesNotExist:
            logger.error(f'Payment for order {order_id} not found')
            return False
        except Exception as e:
            logger.error(f'Error handling order_refunded: {e}')
            return False


def create_payment_link_for_invoice(invoice, user):
    """Create a payment link for an invoice using Lemon Squeezy."""

    # Check if payment link already exists
    if hasattr(invoice, 'payment_link'):
        return invoice.payment_link

    service = LemonSqueezyService()

    # Create checkout in Lemon Squeezy
    checkout_response = service.create_checkout(invoice)

    if not checkout_response or 'data' not in checkout_response:
        logger.error(
            f'Failed to create Lemon Squeezy checkout for invoice {invoice.id}'
        )
        return None

    checkout_data = checkout_response['data']

    # Create payment link
    payment_link = PaymentLink.objects.create(
        invoice=invoice,
        expires_at=timezone.now() + timedelta(days=30),  # 30 days expiry
        lemon_squeezy_checkout_url=checkout_data['attributes']['url'],
        lemon_squeezy_checkout_id=checkout_data['id'],
        created_by=user,
    )

    return payment_link


def process_lemon_squeezy_webhook(event_type: str, data: Dict) -> bool:
    """Process Lemon Squeezy webhook events."""
    from businesses.models import Business
    from invoices.models import Invoice

    try:
        if event_type == 'order_created':
            # Handle successful payment
            custom_data = (
                data.get('attributes', {}).get('first_order_item', {}).get('custom', {})
            )
            invoice_id = custom_data.get('invoice_id')

            if invoice_id:
                try:
                    invoice = Invoice.objects.get(id=invoice_id)

                    # Create payment record
                    payment = Payment.objects.create(
                        invoice=invoice,
                        amount=invoice.total_amount,
                        payment_method=Payment.PaymentMethod.LEMON_SQUEEZY,
                        status=Payment.Status.COMPLETED,
                        lemon_squeezy_order_id=data['id'],
                        external_payment_id=data['id'],
                        payment_processor='lemon_squeezy',
                        reference_number=data.get('attributes', {}).get(
                            'order_number', ''
                        ),
                    )

                    payment.mark_as_completed()

                    # Mark payment link as used
                    if hasattr(invoice, 'payment_link'):
                        invoice.payment_link.mark_as_used()

                    logger.info(
                        f'Payment processed for invoice {invoice.invoice_number}'
                    )

                except Invoice.DoesNotExist:
                    logger.error(
                        f'Invoice {invoice_id} not found for Lemon Squeezy order {data["id"]}'
                    )

        elif event_type == 'subscription_created':
            # Handle subscription creation
            custom_data = data.get('attributes', {}).get('custom', {})
            business_id = custom_data.get('business_id')

            if business_id:
                try:
                    business = Business.objects.get(id=business_id)

                    # Create or update subscription
                    subscription, created = Subscription.objects.get_or_create(
                        business=business,
                        defaults={
                            'lemon_squeezy_subscription_id': data['id'],
                            'lemon_squeezy_customer_id': data.get('relationships', {})
                            .get('customer', {})
                            .get('data', {})
                            .get('id', ''),
                            'status': Subscription.Status.ACTIVE,
                            'plan': Subscription.Plan.BASIC,  # Default plan
                            'current_period_start': timezone.now(),
                            'current_period_end': timezone.now() + timedelta(days=30),
                        },
                    )

                    if not created:
                        subscription.status = Subscription.Status.ACTIVE
                        subscription.lemon_squeezy_subscription_id = data['id']
                        subscription.save()

                    logger.info(
                        f'Subscription {"created" if created else "updated"} for business {business.name}'
                    )

                except Business.DoesNotExist:
                    logger.error(
                        f'Business {business_id} not found for Lemon Squeezy subscription {data["id"]}'
                    )

        elif event_type == 'subscription_cancelled':
            # Handle subscription cancellation
            subscription_id = data['id']

            try:
                subscription = Subscription.objects.get(
                    lemon_squeezy_subscription_id=subscription_id
                )
                subscription.status = Subscription.Status.CANCELLED
                subscription.save()

                logger.info(
                    f'Subscription cancelled for business {subscription.business.name}'
                )

            except Subscription.DoesNotExist:
                logger.error(f'Subscription {subscription_id} not found')

        return True

    except Exception as e:
        logger.error(f'Error processing Lemon Squeezy webhook: {e}')
        return False


class PaymentProcessor:
    """Unified interface for payment providers"""

    PROVIDERS = {
        'STRIPE': 'payments.gateways.StripeGateway',
        'LEMONSQUEEZY': 'payments.gateways.LemonSqueezyGateway',
    }

    def __init__(self, method='LEMONSQUEEZY'):
        self.gateway = self._load_gateway(method)

    def _load_gateway(self, method):
        """Dynamic gateway loading (avoiding hard dependencies)"""

        try:
            module_path, class_name = self.PROVIDERS[method].rsplit('.', 1)
            module = import_module(module_path)
            return getattr(module, class_name)(settings.PYAMENT_CREDENTIALS[method])
        except (KeyError, ImportError) as e:
            raise ValueError(f'Invalid payment method: {method}') from e

    def charge(self, amount, client, description=''):
        """Process payment with retry logic"""
        try:
            return self.gateway.charge(amount, client, description)
        except PaymentProviderError as e:
            # critical: log provider-specific error for operators
            logger.error(
                f'Payment failed: {e} | Code: {e.code} | Booking: {client.booking_id}',
                extra={'provider': self.gateway.NAME},
            )

            # fallback strategry for production
            if self.gateway.NAME == 'Stripe' and e.code in [
                'rate_limit',
                'api_connection',
            ]:
                return self._retry_with_fallback(amount, client, description)

    def _retry_with_fallback(self, amount, client, description):
        """Fallback to secondary provide on transient errors"""
        fb_gateway = self._load_gateway('PAYPAL')
        return fb_gateway.charge(amount, client.id, f'[FALLBACK] {description}')
