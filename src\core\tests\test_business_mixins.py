"""Tests for business access control mixins."""

import pytest
from django.contrib.auth import get_user_model
from django.core.exceptions import PermissionDenied
from django.http import Http404
from django.test import RequestFactory, TestCase
from django.views.generic import ListView, DetailView, CreateView

from accounts.models import Profile
from businesses.models import Business
from core.business_mixins import (
    BusinessAccessMixin,
    BusinessOwnerRequiredMixin,
    BusinessObjectMixin,
    BusinessCreateMixin,
    MultiBusinessMixin,
)

User = get_user_model()


class TestBusinessAccessMixin(TestCase):
    """Test the BusinessAccessMixin."""
    
    def setUp(self):
        """Set up test data."""
        self.factory = RequestFactory()
        
        # Create users
        self.business_owner = User.objects.create_user(
            username='owner',
            email='<EMAIL>',
            password='testpass123'
        )
        self.agent = User.objects.create_user(
            username='agent',
            email='<EMAIL>',
            password='testpass123'
        )
        self.other_user = User.objects.create_user(
            username='other',
            email='<EMAIL>',
            password='testpass123'
        )
        self.superuser = User.objects.create_superuser(
            username='admin',
            email='<EMAIL>',
            password='adminpass123'
        )
        
        # Create profiles
        Profile.objects.create(
            user=self.business_owner,
            role=Profile.Role.BUSINESS_OWNER
        )
        Profile.objects.create(
            user=self.agent,
            role=Profile.Role.AGENT
        )
        Profile.objects.create(
            user=self.other_user,
            role=Profile.Role.CUSTOMER
        )
        
        # Create businesses
        self.business1 = Business.objects.create(
            name='Test Business 1',
            email='<EMAIL>',
            phone='+1234567890',
            created_by=self.business_owner
        )
        self.business2 = Business.objects.create(
            name='Test Business 2',
            email='<EMAIL>',
            phone='+1234567891',
            created_by=self.other_user
        )
        
        # Set up business relationships
        self.business_owner.business = self.business1
        self.business_owner.save()
        
        self.agent.business = self.business1
        self.agent.save()
        
        # Add agent to business users
        self.business1.users.add(self.agent)
        
        # Create a test view
        class TestView(BusinessAccessMixin, ListView):
            model = Business
            
        self.view_class = TestView
    
    def test_get_accessible_businesses_business_owner(self):
        """Test that business owner can access their business."""
        request = self.factory.get('/')
        request.user = self.business_owner
        
        view = self.view_class()
        view.request = request
        
        accessible = view.get_accessible_businesses()
        self.assertIn(self.business1, accessible)
        self.assertNotIn(self.business2, accessible)
    
    def test_get_accessible_businesses_agent(self):
        """Test that agent can access their business."""
        request = self.factory.get('/')
        request.user = self.agent
        
        view = self.view_class()
        view.request = request
        
        accessible = view.get_accessible_businesses()
        self.assertIn(self.business1, accessible)
        self.assertNotIn(self.business2, accessible)
    
    def test_get_accessible_businesses_superuser(self):
        """Test that superuser can access all businesses."""
        request = self.factory.get('/')
        request.user = self.superuser
        
        view = self.view_class()
        view.request = request
        
        accessible = view.get_accessible_businesses()
        self.assertIn(self.business1, accessible)
        self.assertIn(self.business2, accessible)
    
    def test_get_accessible_businesses_no_access(self):
        """Test that user with no business access gets empty queryset."""
        request = self.factory.get('/')
        request.user = self.other_user
        
        view = self.view_class()
        view.request = request
        
        accessible = view.get_accessible_businesses()
        self.assertNotIn(self.business1, accessible)
        # other_user created business2 but is not added to users
        self.assertIn(self.business2, accessible)
    
    def test_check_business_access_owner(self):
        """Test business access check for owner."""
        request = self.factory.get('/')
        request.user = self.business_owner
        
        view = self.view_class()
        view.request = request
        
        self.assertTrue(view.check_business_access(self.business1))
        self.assertFalse(view.check_business_access(self.business2))
    
    def test_check_business_access_superuser(self):
        """Test business access check for superuser."""
        request = self.factory.get('/')
        request.user = self.superuser
        
        view = self.view_class()
        view.request = request
        
        self.assertTrue(view.check_business_access(self.business1))
        self.assertTrue(view.check_business_access(self.business2))
    
    def test_get_user_business(self):
        """Test getting user's primary business."""
        request = self.factory.get('/')
        request.user = self.business_owner
        
        view = self.view_class()
        view.request = request
        
        business = view.get_user_business()
        self.assertEqual(business, self.business1)
    
    def test_get_user_business_no_access(self):
        """Test getting user business when user has no access."""
        # Create a user with no business access
        no_access_user = User.objects.create_user(
            username='noaccess',
            email='<EMAIL>',
            password='testpass123'
        )
        
        request = self.factory.get('/')
        request.user = no_access_user
        
        view = self.view_class()
        view.request = request
        
        with self.assertRaises(PermissionDenied):
            view.get_user_business()


class TestBusinessOwnerRequiredMixin(TestCase):
    """Test the BusinessOwnerRequiredMixin."""
    
    def setUp(self):
        """Set up test data."""
        self.factory = RequestFactory()
        
        self.business_owner = User.objects.create_user(
            username='owner',
            email='<EMAIL>',
            password='testpass123'
        )
        self.agent = User.objects.create_user(
            username='agent',
            email='<EMAIL>',
            password='testpass123'
        )
        self.superuser = User.objects.create_superuser(
            username='admin',
            email='<EMAIL>',
            password='adminpass123'
        )
        
        # Create profiles
        Profile.objects.create(
            user=self.business_owner,
            role=Profile.Role.BUSINESS_OWNER
        )
        Profile.objects.create(
            user=self.agent,
            role=Profile.Role.AGENT
        )
        
        class TestView(BusinessOwnerRequiredMixin, ListView):
            model = Business
            
        self.view_class = TestView
    
    def test_business_owner_access(self):
        """Test that business owner passes the test."""
        request = self.factory.get('/')
        request.user = self.business_owner
        
        view = self.view_class()
        view.request = request
        
        self.assertTrue(view.test_func())
    
    def test_agent_no_access(self):
        """Test that agent fails the test."""
        request = self.factory.get('/')
        request.user = self.agent
        
        view = self.view_class()
        view.request = request
        
        self.assertFalse(view.test_func())
    
    def test_superuser_access(self):
        """Test that superuser passes the test."""
        request = self.factory.get('/')
        request.user = self.superuser
        
        view = self.view_class()
        view.request = request
        
        self.assertTrue(view.test_func())


@pytest.mark.django_db
class TestBusinessObjectMixin:
    """Test the BusinessObjectMixin."""
    
    def test_get_object_with_access(self, business_owner_user, business):
        """Test getting object when user has access."""
        from django.test import RequestFactory
        
        factory = RequestFactory()
        request = factory.get('/')
        request.user = business_owner_user
        
        class TestView(BusinessObjectMixin, DetailView):
            model = Business
            
        view = TestView()
        view.request = request
        view.kwargs = {'pk': business.pk}
        
        obj = view.get_object()
        assert obj == business
    
    def test_get_object_no_access(self, agent_user, other_business):
        """Test getting object when user has no access."""
        from django.test import RequestFactory
        
        factory = RequestFactory()
        request = factory.get('/')
        request.user = agent_user
        
        class TestView(BusinessObjectMixin, DetailView):
            model = Business
            
        view = TestView()
        view.request = request
        view.kwargs = {'pk': other_business.pk}
        
        with pytest.raises(Http404):
            view.get_object()
