from django.urls import path

from . import views

app_name = 'tours'

urlpatterns = [
    # Public tour catalog (no authentication required)
    path('catalog/', views.PublicTourCatalogView.as_view(), name='public-catalog'),
    path(
        'catalog/<uuid:pk>/', views.PublicTourDetailView.as_view(), name='public-detail'
    ),
    path('search/', views.TourSearchView.as_view(), name='public-search'),
    # tours (authenticated)
    path('', views.tour_list, name='tour-list'),
    path('<uuid:pk>/', views.tour_detail, name='tour-detail'),
    path('create/', views.tour_create, name='tour-create'),
    path('set-date/', views.set_tour_date, name='tour-set-date'),
    path('set-capacity/', views.capacity_step, name='tour-set-capacity'),
    path('confirm/', views.confirmation_step, name='tour-confirmation'),
    # Tour events
    path(
        'events/',
        views.TourEventListView.as_view(),
        name='event_list',
    ),
    path(
        'events/<int:pk>/',
        views.TourEventDetailView.as_view(),
        name='event_detail',
    ),
    path(
        'events/create/',
        views.TourEventCreateView.as_view(),
        name='event_create',
    ),
    # Tour Category
    path('categories/', views.category_list, name='category-list'),
    path('categories/create/', views.category_create, name='category-create'),
    path('categories/<uuid:pk>/', views.category_detail, name='category-detail'),
    path('categories/<uuid:pk>/edit/', views.category_update, name='category-update'),
    # tour templates
    path('tour-templates/', views.tour_template_list, name='tour-template-list'),
    path(
        'tour-templates/create/',
        views.tour_template_create,
        name='tour-template-create',
    ),
    path(
        'tour-templates/<uuid:pk>/',
        views.tour_template_detail,
        name='tour-template-detail',
    ),
    path(
        'tour-templates/<uuid:pk>/edit/',
        views.tour_template_update,
        name='tour-template-update',
    ),
]
