# Tour Management SaaS - Data Flow Diagram

This diagram illustrates how data flows through the system between external entities, core processes, and data stores.

## Diagram

```mermaid
flowchart LR
    %% External Entities
    Client[Client/Customer]
    Agent[Agent/Business Owner]
    PaymentProvider[Payment Provider]
    EmailSystem[Email System]

    %% Core Processes
    subgraph "Core Business Processes"
        P1[1.0 User Management]
        P2[2.0 Client Management]
        P3[3.0 Quote Management]
        P4[4.0 Booking Management]
        P5[5.0 Invoice Management]
        P6[6.0 Payment Processing]
        P7[7.0 Document Generation]
        P8[8.0 Calendar Management]
    end

    %% Data Stores
    subgraph "Data Stores"
        D1[(D1: User Data)]
        D2[(D2: Business Data)]
        D3[(D3: Client Data)]
        D4[(D4: Quote Data)]
        D5[(D5: Booking Data)]
        D6[(D6: Invoice Data)]
        D7[(D7: Payment Data)]
        D8[(D8: Document Files)]
        D9[(D9: Calendar Events)]
        D10[(D10: Audit Logs)]
    end

    %% Data Flows

    %% User Management Flows
    Agent -->|User credentials| P1
    P1 -->|User profile| D1
    P1 -->|Authentication status| Agent
    D1 -->|User permissions| P1

    %% Client Management Flows
    Agent -->|Client information| P2
    P2 -->|Client data| D3
    P2 -->|Client list| Agent
    D3 -->|Client details| P2

    %% Quote Management Flows
    Agent -->|Quote request| P3
    P3 -->|Quote data| D4
    P3 -->|Public quote link| Client
    Client -->|Quote approval/rejection| P3
    D4 -->|Quote details| P3
    D3 -->|Client info| P3

    %% Booking Management Flows
    P3 -->|Approved quote| P4
    P4 -->|Booking data| D5
    P4 -->|Booking confirmation| Agent
    P4 -->|Booking details| Client
    D5 -->|Booking info| P4

    %% Invoice Management Flows
    P4 -->|Booking details| P5
    P5 -->|Invoice data| D6
    P5 -->|Invoice| Client
    D6 -->|Invoice details| P5
    D4 -->|Quote amounts| P5

    %% Payment Processing Flows
    Client -->|Payment initiation| P6
    P6 -->|Payment request| PaymentProvider
    PaymentProvider -->|Payment status| P6
    P6 -->|Payment data| D7
    P6 -->|Payment confirmation| Client
    D7 -->|Payment history| P6
    D6 -->|Invoice amounts| P6

    %% Document Generation Flows
    P3 -->|Quote data| P7
    P5 -->|Invoice data| P7
    P6 -->|Payment data| P7
    P7 -->|Generated documents| D8
    P7 -->|PDF documents| Client
    P7 -->|Document links| Agent

    %% Calendar Management Flows
    Agent -->|Event scheduling| P8
    P8 -->|Calendar events| D9
    P8 -->|Schedule updates| Agent
    P4 -->|Booking dates| P8
    D9 -->|Event details| P8

    %% Email Communications
    P3 -->|Quote emails| EmailSystem
    P5 -->|Invoice emails| EmailSystem
    P6 -->|Payment confirmations| EmailSystem
    EmailSystem -->|Email delivery| Client
    EmailSystem -->|Notifications| Agent

    %% Audit and Logging
    P1 -->|User actions| D10
    P2 -->|Client actions| D10
    P3 -->|Quote actions| D10
    P4 -->|Booking actions| D10
    P5 -->|Invoice actions| D10
    P6 -->|Payment actions| D10

    %% Business Data Integration
    D2 -->|Business settings| P1
    D2 -->|Business settings| P2
    D2 -->|Business settings| P3
    D2 -->|Business settings| P4
    D2 -->|Business settings| P5
    D2 -->|Business settings| P6
    D2 -->|Business branding| P7

    %% Styling - High Contrast Colors
    classDef external fill:#fff8e1,stroke:#e65100,stroke-width:3px,color:#000000
    classDef process fill:#ffffff,stroke:#1565c0,stroke-width:3px,color:#000000
    classDef datastore fill:#e8f5e8,stroke:#1b5e20,stroke-width:3px,color:#000000

    class Client,Agent,PaymentProvider,EmailSystem external
    class P1,P2,P3,P4,P5,P6,P7,P8 process
    class D1,D2,D3,D4,D5,D6,D7,D8,D9,D10 datastore
```

## Data Flow Analysis

### External Entities

#### 1. Client/Customer

**Data Inputs:**

- Quote approval/rejection decisions
- Payment initiation requests
- Booking modification requests
- Personal information updates

**Data Outputs:**

- Public quote links and details
- Invoice documents and payment links
- Booking confirmations and receipts
- Email notifications and updates

#### 2. Agent/Business Owner

**Data Inputs:**

- User credentials and authentication
- Client information and profiles
- Quote creation and management
- Event scheduling and management
- Booking confirmations and updates

**Data Outputs:**

- Authentication status and permissions
- Client lists and details
- Booking confirmations and status
- Schedule updates and notifications
- Document links and reports

#### 3. Payment Provider (Lemon Squeezy)

**Data Inputs:**

- Payment requests and transaction data
- Refund requests and cancellations

**Data Outputs:**

- Payment status updates (webhooks)
- Transaction confirmations
- Refund confirmations

#### 4. Email System

**Data Inputs:**

- Quote notification requests
- Invoice delivery requests
- Payment confirmation data
- General notification requests

**Data Outputs:**

- Email delivery to clients
- Notification delivery to agents
- Delivery status reports

### Core Business Processes

#### 1.0 User Management

**Purpose:** Handle user authentication, authorization, and profile management

**Data Inputs:**

- User credentials from agents
- User permissions from user data store
- Business settings from business data store

**Data Outputs:**

- User profiles to user data store
- Authentication status to agents
- User actions to audit logs

#### 2.0 Client Management

**Purpose:** Manage customer relationships and profiles

**Data Inputs:**

- Client information from agents
- Client details from client data store
- Business settings from business data store

**Data Outputs:**

- Client data to client data store
- Client lists to agents
- Client actions to audit logs

#### 3.0 Quote Management

**Purpose:** Handle quote creation, approval, and lifecycle management

**Data Inputs:**

- Quote requests from agents
- Quote approval/rejection from clients
- Quote details from quote data store
- Client information from client data store
- Business settings from business data store

**Data Outputs:**

- Quote data to quote data store
- Public quote links to clients
- Quote data to document generation
- Quote actions to audit logs

#### 4.0 Booking Management

**Purpose:** Manage reservations and scheduling

**Data Inputs:**

- Approved quotes from quote management
- Booking information from booking data store
- Business settings from business data store

**Data Outputs:**

- Booking data to booking data store
- Booking confirmations to agents and clients
- Booking dates to calendar management
- Booking details to invoice management
- Booking actions to audit logs

#### 5.0 Invoice Management

**Purpose:** Handle billing and invoice generation

**Data Inputs:**

- Booking details from booking management
- Quote amounts from quote data store
- Invoice details from invoice data store
- Business settings from business data store

**Data Outputs:**

- Invoice data to invoice data store
- Invoices to clients
- Invoice data to document generation
- Invoice amounts to payment processing
- Invoice actions to audit logs

#### 6.0 Payment Processing

**Purpose:** Handle payment transactions and tracking

**Data Inputs:**

- Payment initiation from clients
- Payment status from payment provider
- Payment history from payment data store
- Invoice amounts from invoice data store

**Data Outputs:**

- Payment requests to payment provider
- Payment data to payment data store
- Payment confirmations to clients
- Payment data to document generation
- Payment actions to audit logs

#### 7.0 Document Generation

**Purpose:** Create PDF documents and reports

**Data Inputs:**

- Quote data from quote management
- Invoice data from invoice management
- Payment data from payment processing
- Business branding from business data store

**Data Outputs:**

- Generated documents to document files store
- PDF documents to clients
- Document links to agents

#### 8.0 Calendar Management

**Purpose:** Manage events and scheduling

**Data Inputs:**

- Event scheduling from agents
- Booking dates from booking management
- Event details from calendar events store

**Data Outputs:**

- Calendar events to calendar events store
- Schedule updates to agents

### Data Stores

#### D1: User Data

- User profiles and authentication information
- Role assignments and permissions
- User preferences and settings

#### D2: Business Data

- Business profiles and settings
- Branding and customization data
- Configuration and preferences

#### D3: Client Data

- Customer profiles and contact information
- Traveler details and requirements
- Lead sources and notes

#### D4: Quote Data

- Quote details and pricing
- Terms and conditions
- Status and approval history

#### D5: Booking Data

- Reservation details and status
- Participant information
- Special requirements and notes

#### D6: Invoice Data

- Billing information and amounts
- Payment terms and due dates
- Invoice status and history

#### D7: Payment Data

- Transaction records and status
- Payment method information
- Refund and cancellation data

#### D8: Document Files

- Generated PDF documents
- Templates and assets
- File metadata and storage

#### D9: Calendar Events

- Scheduled tours and meetings
- Event details and participants
- Availability and conflicts

#### D10: Audit Logs

- User action history
- Data change tracking
- Security and compliance logs

## Data Flow Characteristics

### 1. Multi-Directional Flow

- Data flows both ways between processes and stores
- External entities both provide and receive data
- Processes communicate with multiple data stores

### 2. Business Context Integration

- All processes access business settings
- Data is scoped to business context
- Multi-tenant isolation maintained

### 3. Audit Trail Maintenance

- All processes log actions to audit store
- Complete traceability of data changes
- Compliance and security monitoring

### 4. External Service Integration

- Payment provider integration for transactions
- Email system for communications
- Secure data exchange protocols

### 5. Document Generation Pipeline

- Multiple processes feed document generation
- Centralized document storage and management
- Client and agent access to generated documents
