from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from simple_history.admin import SimpleHistoryAdmin

from .models import Booking, BookingParticipant


class BookingParticipantInline(admin.TabularInline):
    """Inline admin for booking participants."""

    model = BookingParticipant
    extra = 0
    readonly_fields = ['check_in_time', 'created_at', 'updated_at']
    fields = [
        'traveler',
        'is_primary_contact',
        'special_requirements',
        'checked_in',
        'check_in_time',
        'no_show',
    ]


@admin.register(Booking)
class BookingAdmin(SimpleHistoryAdmin):
    """Admin interface for Booking model."""

    list_display = [
        'booking_reference',
        'client_name',
        'tour_event_title',
        'status',
        'number_of_participants',
        'total_amount',
        'deposit_paid',
        'booking_date',
    ]
    list_filter = [
        'status',
        'business',
        'deposit_paid',
        'full_payment_received',
        'booking_date',
    ]
    search_fields = [
        'booking_reference',
        'client__first_name',
        'client__last_name',
        'client__company_name',
        'tour_event__event__title',
    ]
    readonly_fields = [
        'booking_reference',
        'booking_date',
        'confirmation_date',
        'cancellation_date',
        'created_at',
        'updated_at',
        'outstanding_balance',
    ]
    inlines = [BookingParticipantInline]

    fieldsets = (
        (
            _('Basic Information'),
            {
                'fields': (
                    'business',
                    'client',
                    'tour_event',
                    'booking_reference',
                    'number_of_participants',
                    'status',
                )
            },
        ),
        (
            _('Pricing'),
            {
                'fields': (
                    'total_amount',
                    'deposit_amount',
                    'outstanding_balance',
                    'deposit_paid',
                    'full_payment_received',
                )
            },
        ),
        (
            _('Dates'),
            {'fields': ('booking_date', 'confirmation_date', 'cancellation_date')},
        ),
        (
            _('Additional Information'),
            {'fields': ('special_requests', 'internal_notes')},
        ),
        (
            _('Cancellation Details'),
            {
                'fields': ('cancellation_reason', 'refund_amount'),
                'classes': ('collapse',),
            },
        ),
        (
            _('Audit Information'),
            {
                'fields': ('created_by', 'created_at', 'updated_at'),
                'classes': ('collapse',),
            },
        ),
    )

    def get_queryset(self, request):
        """Optimize queryset with select_related."""
        return (
            super()
            .get_queryset(request)
            .select_related(
                'business', 'client', 'tour_event', 'tour_event__event', 'created_by'
            )
        )

    def client_name(self, obj):
        return obj.client.display_name

    client_name.short_description = _('Client')

    def tour_event_title(self, obj):
        return obj.tour_event.event.title

    tour_event_title.short_description = _('Event')

    actions = ['confirm_bookings', 'cancel_bookings']

    def confirm_bookings(self, request, queryset):
        """Confirm selected bookings."""
        updated = 0
        for booking in queryset.filter(status=Booking.Status.PENDING):
            if booking.confirm_booking(user=request.user):
                updated += 1
        self.message_user(request, f'{updated} bookings were confirmed.')

    confirm_bookings.short_description = _('Confirm selected bookings')

    def cancel_bookings(self, request, queryset):
        """Cancel selected bookings."""
        updated = 0
        for booking in queryset.filter(
            status__in=[Booking.Status.PENDING, Booking.Status.CONFIRMED]
        ):
            if booking.cancel_booking(reason='Cancelled via admin', user=request.user):
                updated += 1
        self.message_user(request, f'{updated} bookings were cancelled.')

    cancel_bookings.short_description = _('Cancel selected bookings')


@admin.register(BookingParticipant)
class BookingParticipantAdmin(admin.ModelAdmin):
    """Admin interface for BookingParticipant model."""

    list_display = [
        'traveler_name',
        'booking_reference',
        'is_primary_contact',
        'checked_in',
        'no_show',
        'created_at',
    ]
    list_filter = ['is_primary_contact', 'checked_in', 'no_show', 'created_at']
    search_fields = [
        'traveler__first_name',
        'traveler__last_name',
        'booking__booking_reference',
    ]
    readonly_fields = ['check_in_time', 'created_at', 'updated_at']

    def get_queryset(self, request):
        """Optimize queryset with select_related."""
        return super().get_queryset(request).select_related('traveler', 'booking')

    def traveler_name(self, obj):
        return obj.traveler.full_name

    traveler_name.short_description = _('Traveler')

    def booking_reference(self, obj):
        return obj.booking.booking_reference

    booking_reference.short_description = _('Booking')

    actions = ['check_in_participants', 'mark_no_show']

    def check_in_participants(self, request, queryset):
        """Check in selected participants."""
        updated = 0
        for participant in queryset.filter(checked_in=False, no_show=False):
            if participant.check_in():
                updated += 1
        self.message_user(request, f'{updated} participants were checked in.')

    check_in_participants.short_description = _('Check in selected participants')

    def mark_no_show(self, request, queryset):
        """Mark selected participants as no-show."""
        updated = 0
        for participant in queryset.filter(checked_in=False, no_show=False):
            if participant.mark_no_show():
                updated += 1
        self.message_user(request, f'{updated} participants were marked as no-show.')

    mark_no_show.short_description = _('Mark selected participants as no-show')
