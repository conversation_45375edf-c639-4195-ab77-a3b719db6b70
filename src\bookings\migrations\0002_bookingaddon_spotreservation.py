# Generated by Django 5.2.4 on 2025-08-13 07:53

import django.core.validators
import django.db.models.deletion
import djmoney.models.fields
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ('bookings', '0001_initial'),
        ('tours', '0002_touraddon_alter_tourtemplate_options_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='BookingAddon',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                (
                    'id',
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        help_text='Unique identifier for this record',
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                (
                    'quantity',
                    models.PositiveSmallIntegerField(
                        default=1,
                        help_text='Quantity of this addon',
                        validators=[django.core.validators.MinValueValidator(1)],
                    ),
                ),
                (
                    'unit_price_currency',
                    djmoney.models.fields.CurrencyField(
                        choices=[
                            ('AUD', 'Australian Dollar'),
                            ('BRL', 'Brazilian Real'),
                            ('GBP', 'British Pound'),
                            ('CAD', 'Canadian Dollar'),
                            ('CNY', 'Chinese Yuan'),
                            ('EUR', 'Euro'),
                            ('INR', 'Indian Rupee'),
                            ('JPY', 'Japanese Yen'),
                            ('MXN', 'Mexican Peso'),
                            ('CHF', 'Swiss Franc'),
                            ('TZS', 'Tanzanian Shilling'),
                            ('USD', 'US Dollar'),
                        ],
                        default='TZS',
                        editable=False,
                        max_length=3,
                    ),
                ),
                (
                    'unit_price',
                    djmoney.models.fields.MoneyField(
                        decimal_places=2,
                        help_text='Price per unit at time of booking',
                        max_digits=10,
                    ),
                ),
                (
                    'total_price_currency',
                    djmoney.models.fields.CurrencyField(
                        choices=[
                            ('AUD', 'Australian Dollar'),
                            ('BRL', 'Brazilian Real'),
                            ('GBP', 'British Pound'),
                            ('CAD', 'Canadian Dollar'),
                            ('CNY', 'Chinese Yuan'),
                            ('EUR', 'Euro'),
                            ('INR', 'Indian Rupee'),
                            ('JPY', 'Japanese Yen'),
                            ('MXN', 'Mexican Peso'),
                            ('CHF', 'Swiss Franc'),
                            ('TZS', 'Tanzanian Shilling'),
                            ('USD', 'US Dollar'),
                        ],
                        default='TZS',
                        editable=False,
                        max_length=3,
                    ),
                ),
                (
                    'total_price',
                    djmoney.models.fields.MoneyField(
                        decimal_places=2,
                        help_text='Total price for this addon (quantity × unit_price)',
                        max_digits=10,
                    ),
                ),
                (
                    'addon',
                    models.ForeignKey(
                        help_text='Tour addon selected',
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name='booking_addons',
                        to='tours.touraddon',
                    ),
                ),
                (
                    'booking',
                    models.ForeignKey(
                        help_text='Booking this addon is for',
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='addons',
                        to='bookings.booking',
                    ),
                ),
                (
                    'created_by',
                    models.ForeignKey(
                        help_text='User who created this record',
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name='%(class)ss_created',
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                'verbose_name': 'Booking Addon',
                'verbose_name_plural': 'Booking Addons',
                'indexes': [
                    models.Index(
                        fields=['booking'], name='bookings_bo_booking_22e14e_idx'
                    ),
                    models.Index(
                        fields=['addon'], name='bookings_bo_addon_i_f51a44_idx'
                    ),
                ],
                'unique_together': {('booking', 'addon')},
            },
        ),
        migrations.CreateModel(
            name='SpotReservation',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                (
                    'id',
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        help_text='Unique identifier for this record',
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                (
                    'session_key',
                    models.CharField(
                        help_text='Session key of the user making the reservation',
                        max_length=40,
                    ),
                ),
                (
                    'email',
                    models.EmailField(
                        blank=True,
                        help_text='Email of the user making the reservation',
                        max_length=254,
                    ),
                ),
                (
                    'number_of_spots',
                    models.PositiveSmallIntegerField(
                        help_text='Number of spots reserved',
                        validators=[django.core.validators.MinValueValidator(1)],
                    ),
                ),
                (
                    'status',
                    models.CharField(
                        choices=[
                            ('active', 'Active'),
                            ('expired', 'Expired'),
                            ('converted', 'Converted to Booking'),
                            ('cancelled', 'Cancelled'),
                        ],
                        default='active',
                        max_length=20,
                    ),
                ),
                (
                    'expires_at',
                    models.DateTimeField(help_text='When this reservation expires'),
                ),
                (
                    'booking',
                    models.ForeignKey(
                        blank=True,
                        help_text='Booking created from this reservation',
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='spot_reservation',
                        to='bookings.booking',
                    ),
                ),
                (
                    'tour_instance',
                    models.ForeignKey(
                        help_text='Tour instance being reserved',
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='spot_reservations',
                        to='tours.tourinstance',
                    ),
                ),
            ],
            options={
                'verbose_name': 'Spot Reservation',
                'verbose_name_plural': 'Spot Reservations',
                'indexes': [
                    models.Index(
                        fields=['tour_instance', 'status'],
                        name='bookings_sp_tour_in_a9ce26_idx',
                    ),
                    models.Index(
                        fields=['session_key'], name='bookings_sp_session_d87eae_idx'
                    ),
                    models.Index(
                        fields=['expires_at'], name='bookings_sp_expires_feede4_idx'
                    ),
                    models.Index(
                        fields=['status'], name='bookings_sp_status_af8918_idx'
                    ),
                ],
            },
        ),
    ]
