{% load i18n %}

<c-layouts.base>
  <c-slot name="title">{% trans "User Activity" %} - Tour Business Management</c-slot>

  <c-slot name="content">
    <div class="container mx-auto px-4 py-8">
      <div class="max-w-6xl mx-auto">
        <!-- Header -->
        <div class="flex justify-between items-center mb-6">
          <div>
            <h1 class="text-3xl font-bold text-gray-900">{% trans "User Activity" %}</h1>
            <p class="text-gray-600 mt-1">{% trans "Monitor team activity and engagement for" %}
              {{ business.name }}</p>
          </div>
          <div class="flex space-x-3">
            <a href="{% url 'accounts:user_management' %}"
              class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium">
              {% trans "Back to Users" %}
            </a>
          </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
              <div class="p-2 bg-blue-100 rounded-lg">
                <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z">
                  </path>
                </svg>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">{% trans "Total Users" %}</p>
                <p class="text-2xl font-semibold text-gray-900">{{ total_users }}</p>
              </div>
            </div>
          </div>

          <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
              <div class="p-2 bg-green-100 rounded-lg">
                <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">{% trans "Active Users" %}</p>
                <p class="text-2xl font-semibold text-gray-900">{{ active_users }}</p>
              </div>
            </div>
          </div>

          <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
              <div class="p-2 bg-yellow-100 rounded-lg">
                <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">{% trans "Pending Users" %}</p>
                <p class="text-2xl font-semibold text-gray-900">{{ pending_users }}</p>
              </div>
            </div>
          </div>

          <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
              <div class="p-2 bg-purple-100 rounded-lg">
                <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z">
                  </path>
                </svg>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">{% trans "Recently Active" %}</p>
                <p class="text-2xl font-semibold text-gray-900">{{ recent_active_users }}</p>
                <p class="text-xs text-gray-500">{% trans "Last 30 days" %}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Role Distribution and Recent Activity -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          <!-- Role Distribution -->
          <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">{% trans "Role Distribution" %}</h3>
            <div class="space-y-3">
              {% for role_name, count in role_stats.items %}
              <div class="flex items-center justify-between">
                <div class="flex items-center">
                  <div class="w-3 h-3 rounded-full mr-3
                                {% if 'Owner' in role_name %}bg-purple-500
                                {% elif 'Agent' in role_name %}bg-blue-500
                                {% else %}bg-gray-500{% endif %}">
                  </div>
                  <span class="text-sm text-gray-700">{{ role_name }}</span>
                </div>
                <span class="text-sm font-medium text-gray-900">{{ count }}</span>
              </div>
              {% endfor %}
            </div>
          </div>

          <!-- Quick Actions -->
          <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">{% trans "Quick Actions" %}</h3>
            <div class="space-y-3">
              <a href="{% url 'accounts:add_user' %}"
                class="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                  <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                  </svg>
                </div>
                <div>
                  <p class="text-sm font-medium text-gray-900">{% trans "Add New User" %}</p>
                  <p class="text-xs text-gray-500">{% trans "Invite a team member" %}</p>
                </div>
              </a>

              <a href="{% url 'accounts:bulk_upload' %}"
                class="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                  <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12">
                    </path>
                  </svg>
                </div>
                <div>
                  <p class="text-sm font-medium text-gray-900">{% trans "Bulk Upload" %}</p>
                  <p class="text-xs text-gray-500">{% trans "Upload multiple users" %}</p>
                </div>
              </a>

              <a href="{% url 'accounts:download_template' %}"
                class="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center mr-3">
                  <svg class="w-4 h-4 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                    </path>
                  </svg>
                </div>
                <div>
                  <p class="text-sm font-medium text-gray-900">{% trans "Download Template" %}</p>
                  <p class="text-xs text-gray-500">{% trans "Excel template for bulk upload" %}</p>
                </div>
              </a>
            </div>
          </div>
        </div>

        <!-- Recent User Activity -->
        <div class="bg-white rounded-lg shadow">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">{% trans "Recent User Activity" %}</h3>
          </div>
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {% trans "User" %}
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {% trans "Role" %}
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {% trans "Status" %}
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {% trans "Last Login" %}
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {% trans "Joined" %}
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                {% for user in users %}
                <tr class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="flex-shrink-0 h-8 w-8">
                        <div class="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center">
                          <span class="text-xs font-medium text-gray-700">
                            {{ user.profile.first_name|first|default:user.username|first|upper }}
                          </span>
                        </div>
                      </div>
                      <div class="ml-3">
                        <div class="text-sm font-medium text-gray-900">
                          {{ user.profile.get_full_name|default:user.username }}
                        </div>
                        <div class="text-sm text-gray-500">{{ user.email }}</div>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                    {% if user.profile.role == 'business_owner' %}bg-purple-100 text-purple-800
                                    {% elif user.profile.role == 'agent' %}bg-blue-100 text-blue-800
                                    {% else %}bg-gray-100 text-gray-800{% endif %}">
                      {{ user.get_role_display }}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    {% if user.is_active %}
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                      {% trans "Active" %}
                    </span>
                    {% else %}
                    <span
                      class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                      {% trans "Pending" %}
                    </span>
                    {% endif %}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {% if user.last_login %}
                    {{ user.last_login|date:"M d, Y H:i" }}
                    {% else %}
                    {% trans "Never" %}
                    {% endif %}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {{ user.date_joined|date:"M d, Y" }}
                  </td>
                </tr>
                {% empty %}
                <tr>
                  <td colspan="5" class="px-6 py-4 text-center text-gray-500">
                    {% trans "No users found." %}
                  </td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </c-slot>
</c-layouts.base>
