<c-vars 
    business="None"
    show_logo="True"
    show_contact="False"
    class=""
/>

<div class="business-header {{ class }}" 
     style="--business-primary: {{ business.primary_color }}; --business-secondary: {{ business.secondary_color }};">
    
    {% if show_logo and business.logo %}
        <div class="business-logo">
            <img src="{{ business.get_logo_url }}" 
                 alt="{{ business.name }}" 
                 class="h-12 w-auto">
        </div>
    {% endif %}
    
    <div class="business-info">
        <h1 class="business-name text-2xl font-bold" 
            style="color: var(--business-primary);">
            {{ business.name }}
        </h1>
        
        {% if business.description %}
            <p class="business-description text-gray-600 mt-1">
                {{ business.description|truncatewords:20 }}
            </p>
        {% endif %}
        
        {% if show_contact %}
            <div class="business-contact mt-2 text-sm text-gray-500 space-y-1">
                {% if business.email %}
                    <div class="flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
                            <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
                        </svg>
                        <a href="mailto:{{ business.email }}" class="hover:text-blue-600">
                            {{ business.email }}
                        </a>
                    </div>
                {% endif %}
                
                {% if business.phone %}
                    <div class="flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"></path>
                        </svg>
                        <a href="tel:{{ business.phone }}" class="hover:text-blue-600">
                            {{ business.phone }}
                        </a>
                    </div>
                {% endif %}
                
                {% if business.website %}
                    <div class="flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4.083 9h1.946c.089-1.546.383-2.97.837-4.118A6.004 6.004 0 004.083 9zM10 2a8 8 0 100 16 8 8 0 000-16zm0 2c-.076 0-.232.032-.465.262-.238.234-.497.623-.737 1.182-.389.907-.673 2.142-.766 3.556h3.936c-.093-1.414-.377-2.649-.766-3.556-.24-.559-.499-.948-.737-1.182C10.232 4.032 10.076 4 10 4zm3.971 5c-.089-1.546-.383-2.97-.837-4.118A6.004 6.004 0 0115.917 9h-1.946zm-2.003 2H8.032c.093 1.414.377 2.649.766 3.556.24.559.499.948.737 ************.389.262.465.262.076 0 .232-.032.465-.262.238-.234.497-.623.737-1.182.389-.907.673-2.142.766-3.556zm1.166 4.118c.454-1.147.748-2.572.837-4.118h1.946a6.004 6.004 0 01-2.783 4.118zm-6.268 0C6.412 13.97 6.118 12.546 6.03 11H4.083a6.004 6.004 0 002.783 4.118z" clip-rule="evenodd"></path>
                        </svg>
                        <a href="{{ business.website }}" target="_blank" class="hover:text-blue-600">
                            {{ business.website|slice:"8:" }}
                        </a>
                    </div>
                {% endif %}
            </div>
        {% endif %}
    </div>
</div>

<style>
    .business-header {
        @apply flex items-center space-x-4 p-4 bg-white border-b border-gray-200;
    }
    
    .business-header.centered {
        @apply justify-center text-center;
    }
    
    .business-header.vertical {
        @apply flex-col space-x-0 space-y-4 text-center;
    }
    
    .business-logo img {
        @apply object-contain;
    }
    
    .business-name {
        transition: color 0.2s ease;
    }
    
    .business-contact a {
        transition: color 0.2s ease;
    }
</style>
