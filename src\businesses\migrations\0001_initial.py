# Generated by Django 5.2.4 on 2025-08-12 11:35

import django.core.validators
import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Business',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, help_text='Unique identifier for this record', primary_key=True, serialize=False, unique=True)),
                ('name', models.CharField(max_length=200, verbose_name='Business name')),
                ('description', models.TextField(blank=True, help_text='Business description')),
                ('email', models.EmailField(help_text='Business contact email', max_length=254, validators=[django.core.validators.EmailValidator()])),
                ('phone', models.CharField(help_text='Business contact phone', max_length=20, validators=[django.core.validators.RegexValidator(message="Phone number must be entered in the format: '+999999999'. Up to 15 digits allowed.", regex='^\\+?1?\\d{9,15}$')])),
                ('website', models.URLField(blank=True, help_text='Business website URL')),
                ('address_line1', models.CharField(help_text='Street address', max_length=255)),
                ('address_line2', models.CharField(blank=True, help_text='Apartment, suite, etc.', max_length=255)),
                ('city', models.CharField(help_text='City', max_length=100)),
                ('state_province', models.CharField(help_text='State or Province', max_length=100)),
                ('postal_code', models.CharField(help_text='Postal or ZIP code', max_length=20)),
                ('country', models.CharField(default='Tanzania', max_length=100, verbose_name='Country')),
                ('timezone', models.CharField(default='Africa/Dar_es_Salaam', help_text='Business timezone', max_length=50)),
                ('currency', models.CharField(default='TZS', help_text='Default currency code (ISO 4217)', max_length=3)),
                ('plan', models.CharField(default='basic', help_text='Subscription plan', max_length=50)),
                ('is_active', models.BooleanField(default=True, help_text='Whether the business account is active')),
                ('logo', models.ImageField(blank=True, help_text='Business logo for branding', null=True, upload_to='business_logos/')),
                ('primary_color', models.CharField(default='#007bff', help_text='Primary brand color (hex format)', max_length=7)),
                ('secondary_color', models.CharField(default='#6c757d', help_text='Secondary brand color (hex format)', max_length=7)),
                ('accent_color', models.CharField(default='#28a745', help_text='Accent color for highlights (hex format)', max_length=7)),
                ('custom_css', models.TextField(blank=True, help_text='Custom CSS for business-specific styling')),
                ('email_signature', models.TextField(blank=True, help_text='Custom email signature for this business')),
                ('quote_template', models.CharField(default='default', help_text='Quote PDF template to use', max_length=100)),
                ('invoice_template', models.CharField(default='default', help_text='Invoice PDF template to use', max_length=100)),
                ('created_by', models.ForeignKey(help_text='User who created this record', on_delete=django.db.models.deletion.PROTECT, related_name='%(class)ss_created', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Business',
                'verbose_name_plural': 'Businesses',
                'db_table': 'businesses_business',
                'indexes': [models.Index(fields=['name'], name='businesses__name_a138ad_idx'), models.Index(fields=['is_active'], name='businesses__is_acti_e70536_idx'), models.Index(fields=['created_at'], name='businesses__created_34b472_idx')],
            },
        ),
    ]
