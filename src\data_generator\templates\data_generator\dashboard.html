{% load static %}
{% load i18n %}

<c-layouts.dashboard>

  <c-slot name="title">{% trans 'Data Generator' %}</c-slot>

  <c-slot name="main">
    <div class="container mx-auto px-4 py-2">
      <div class="max-w-6xl mx-auto">
        <!-- Header -->
        <div class="flex justify-between items-center mb-8">
          <div>
            <p class="text-gray-600 mt-2">
              Generate realistic dummy data for testing and demos
            </p>
          </div>
          <div class="flex space-x-3">
            <button onclick="refreshStats()"
              class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
              🔄 Refresh
            </button>
            <a href="{% url 'bookings:dashboard' %}"
              class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
              ← Back to Dashboard
            </a>
          </div>
        </div>

        <!-- Current Data Stats -->
        <div class="bg-white rounded-lg shadow p-6 mb-8">
          <h2 class="text-xl font-semibold text-gray-900 mb-4">Current Data</h2>
          <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4" id="current-stats">
            {% for key, count in current_counts.items %}
            <div class="text-center">
              <div class="text-2xl font-bold text-blue-600" data-stat="{{ key }}">{{ count }}</div>
              <div class="text-sm text-gray-600 capitalize">{{ key }}</div>
            </div>
            {% endfor %}
          </div>
        </div>

        <!-- Quick Actions -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          <!-- Quick Generate -->
          <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">🚀 Quick Actions</h3>
            <div class="space-y-4">
              <form method="post" action="{% url 'data_generator:quick_generate' %}">
                {% csrf_token %}
                <input type="hidden" name="action" value="demo_data">
                <button type="submit"
                  class="w-full bg-green-600 text-white px-4 py-3 rounded-lg hover:bg-green-700 transition-colors">
                  <div class="font-medium">Generate Demo Data</div>
                  <div class="text-sm opacity-90">Medium dataset perfect for demonstrations</div>
                </button>
              </form>

              <form method="post" action="{% url 'data_generator:quick_generate' %}">
                {% csrf_token %}
                <input type="hidden" name="action" value="test_data">
                <button type="submit"
                  class="w-full bg-blue-600 text-white px-4 py-3 rounded-lg hover:bg-blue-700 transition-colors">
                  <div class="font-medium">Generate Test Data</div>
                  <div class="text-sm opacity-90">Small dataset for quick testing</div>
                </button>
              </form>

              <form method="post" action="{% url 'data_generator:quick_generate' %}"
                onsubmit="return confirm('This will delete ALL data except superusers. Are you sure?')">
                {% csrf_token %}
                <input type="hidden" name="action" value="clear_all">
                <button type="submit"
                  class="w-full bg-red-600 text-white px-4 py-3 rounded-lg hover:bg-red-700 transition-colors">
                  <div class="font-medium">🗑️ Clear All Data</div>
                  <div class="text-sm opacity-90">Remove all generated data</div>
                </button>
              </form>
            </div>
          </div>

          <!-- Advanced Options -->
          <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">⚙️ Advanced Generation</h3>
            <div class="space-y-4">
              {% for scale in scale_options %}
              <div class="border border-gray-200 rounded-lg p-4">
                <div class="flex justify-between items-start mb-2">
                  <div>
                    <h4 class="font-medium text-gray-900">{{ scale.label }}</h4>
                    <p class="text-sm text-gray-600">{{ scale.description }}</p>
                  </div>
                  <button onclick="generateDataset('{{ scale.name }}')"
                    class="bg-purple-600 text-white px-3 py-1 rounded text-sm hover:bg-purple-700 transition-colors">
                    Generate
                  </button>
                </div>
                <div class="grid grid-cols-4 gap-2 text-xs text-gray-500">
                  {% for key, count in scale.counts.items %}
                  <div>{{ count }} {{ key }}</div>
                  {% endfor %}
                </div>
              </div>
              {% endfor %}
            </div>
          </div>
        </div>

        <!-- Specific Data Generation -->
        <div class="bg-white rounded-lg shadow p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">🎯 Generate Specific Data</h3>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">Users</label>
              <div class="flex">
                <input type="number" id="users-count" value="5" min="1" max="20"
                  class="flex-1 px-3 py-2 border border-gray-300 rounded-l-lg focus:ring-blue-500 focus:border-blue-500">
                <button onclick="generateSpecific('users')"
                  class="bg-blue-600 text-white px-3 py-2 rounded-r-lg hover:bg-blue-700 transition-colors">
                  +
                </button>
              </div>
            </div>

            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">Businesses</label>
              <div class="flex">
                <input type="number" id="businesses-count" value="2" min="1" max="10"
                  class="flex-1 px-3 py-2 border border-gray-300 rounded-l-lg focus:ring-blue-500 focus:border-blue-500">
                <button onclick="generateSpecific('businesses')"
                  class="bg-green-600 text-white px-3 py-2 rounded-r-lg hover:bg-green-700 transition-colors">
                  +
                </button>
              </div>
            </div>

            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">Clients</label>
              <div class="flex">
                <input type="number" id="clients-count" value="10" min="1" max="50"
                  class="flex-1 px-3 py-2 border border-gray-300 rounded-l-lg focus:ring-blue-500 focus:border-blue-500">
                <button onclick="generateSpecific('clients')"
                  class="bg-yellow-600 text-white px-3 py-2 rounded-r-lg hover:bg-yellow-700 transition-colors">
                  +
                </button>
              </div>
            </div>

            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">Events</label>
              <div class="flex">
                <input type="number" id="events-count" value="15" min="1" max="50"
                  class="flex-1 px-3 py-2 border border-gray-300 rounded-l-lg focus:ring-blue-500 focus:border-blue-500">
                <button onclick="generateSpecific('events')"
                  class="bg-purple-600 text-white px-3 py-2 rounded-r-lg hover:bg-purple-700 transition-colors">
                  +
                </button>
              </div>
            </div>

            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">Travelers</label>
              <div class="flex">
                <input type="number" id="travelers-count" value="20" min="1" max="100"
                  class="flex-1 px-3 py-2 border border-gray-300 rounded-l-lg focus:ring-blue-500 focus:border-blue-500">
                <button onclick="generateSpecific('travelers')"
                  class="bg-indigo-600 text-white px-3 py-2 rounded-r-lg hover:bg-indigo-700 transition-colors">
                  +
                </button>
              </div>
            </div>

            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">Quotes</label>
              <div class="flex">
                <input type="number" id="quotes-count" value="12" min="1" max="50"
                  class="flex-1 px-3 py-2 border border-gray-300 rounded-l-lg focus:ring-blue-500 focus:border-blue-500">
                <button onclick="generateSpecific('quotes')"
                  class="bg-pink-600 text-white px-3 py-2 rounded-r-lg hover:bg-pink-700 transition-colors">
                  +
                </button>
              </div>
            </div>

            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">Bookings</label>
              <div class="flex">
                <input type="number" id="bookings-count" value="20" min="1" max="100"
                  class="flex-1 px-3 py-2 border border-gray-300 rounded-l-lg focus:ring-blue-500 focus:border-blue-500">
                <button onclick="generateSpecific('bookings')"
                  class="bg-teal-600 text-white px-3 py-2 rounded-r-lg hover:bg-teal-700 transition-colors">
                  +
                </button>
              </div>
            </div>

            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">Invoices</label>
              <div class="flex">
                <input type="number" id="invoices-count" value="8" min="1" max="50"
                  class="flex-1 px-3 py-2 border border-gray-300 rounded-l-lg focus:ring-blue-500 focus:border-blue-500">
                <button onclick="generateSpecific('invoices')"
                  class="bg-orange-600 text-white px-3 py-2 rounded-r-lg hover:bg-orange-700 transition-colors">
                  +
                </button>
              </div>
            </div>

            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">Payments</label>
              <div class="flex">
                <input type="number" id="payments-count" value="15" min="1" max="100"
                  class="flex-1 px-3 py-2 border border-gray-300 rounded-l-lg focus:ring-blue-500 focus:border-blue-500">
                <button onclick="generateSpecific('payments')"
                  class="bg-emerald-600 text-white px-3 py-2 rounded-r-lg hover:bg-emerald-700 transition-colors">
                  +
                </button>
              </div>
            </div>

            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">Templates</label>
              <div class="flex">
                <input type="number" id="templates-count" value="10" min="1" max="30"
                  class="flex-1 px-3 py-2 border border-gray-300 rounded-l-lg focus:ring-blue-500 focus:border-blue-500">
                <button onclick="generateSpecific('templates')"
                  class="bg-violet-600 text-white px-3 py-2 rounded-r-lg hover:bg-violet-700 transition-colors">
                  +
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Loading Modal -->
    <div id="loading-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 max-w-sm mx-4">
        <div class="flex items-center space-x-3">
          <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          <span class="text-gray-900">Generating data...</span>
        </div>
      </div>
    </div>

    <script>
      function showLoading() {
        document.getElementById('loading-modal').classList.remove('hidden');
        document.getElementById('loading-modal').classList.add('flex');
      }

      function hideLoading() {
        document.getElementById('loading-modal').classList.add('hidden');
        document.getElementById('loading-modal').classList.remove('flex');
      }

      function showMessage(message, type = 'success') {
        // Create a simple toast notification
        const toast = document.createElement('div');
        toast.className = `fixed top-4 right-4 p-4 rounded-lg text-white z-50 ${type === 'success' ? 'bg-green-600' : 'bg-red-600'}`;
        toast.textContent = message;
        document.body.appendChild(toast);

        setTimeout(() => {
          document.body.removeChild(toast);
        }, 5000);
      }

      async function generateDataset(scale) {
        showLoading();

        try {
          const response = await fetch('{% url "data_generator:generate" %}', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              action: 'generate_complete',
              scale: scale,
              clear_first: true
            })
          });

          const data = await response.json();

          if (data.success) {
            showMessage(data.message);
            refreshStats();
          } else {
            showMessage(data.message, 'error');
          }
        } catch (error) {
          showMessage('Error generating data: ' + error.message, 'error');
        } finally {
          hideLoading();
        }
      }

      async function generateSpecific(dataType) {
        const count = document.getElementById(dataType + '-count').value;
        showLoading();

        try {
          const response = await fetch('{% url "data_generator:generate" %}', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              action: 'generate_specific',
              data_type: dataType,
              count: parseInt(count)
            })
          });

          const data = await response.json();

          if (data.success) {
            showMessage(data.message);
            refreshStats();
          } else {
            showMessage(data.message, 'error');
          }
        } catch (error) {
          showMessage('Error generating data: ' + error.message, 'error');
        } finally {
          hideLoading();
        }
      }

      async function refreshStats() {
        try {
          const response = await fetch('{% url "data_generator:stats" %}');
          const data = await response.json();

          // Update the stats display
          for (const [key, count] of Object.entries(data.counts)) {
            const element = document.querySelector(`[data-stat="${key}"]`);
            if (element) {
              element.textContent = count;
            }
          }
        } catch (error) {
          console.error('Error refreshing stats:', error);
        }
      }

      // Auto-refresh stats every 30 seconds
      setInterval(refreshStats, 30000);
    </script>
  </c-slot>
</c-layouts.dashboard>
