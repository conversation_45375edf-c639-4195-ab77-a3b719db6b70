# Generated by Django 5.2.4 on 2025-08-12 11:35

import django.db.models.deletion
import djmoney.models.fields
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('businesses', '0001_initial'),
        ('clients', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Quote',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, help_text='Unique identifier for this record', primary_key=True, serialize=False, unique=True)),
                ('quote_number', models.CharField(help_text='Unique quote number', max_length=50, unique=True)),
                ('title', models.CharField(help_text='Quote title or trip name', max_length=200)),
                ('description', models.TextField(blank=True, help_text='Detailed description of the tour/service')),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('sent', 'Sent'), ('approved', 'Approved'), ('rejected', 'Rejected'), ('expired', 'Expired')], default='draft', help_text='Current status of the quote', max_length=20)),
                ('valid_until', models.DateField(help_text='Quote validity expiration date')),
                ('public_hash', models.CharField(help_text='Unique hash for public quote access', max_length=64, unique=True)),
                ('subtotal_currency', djmoney.models.fields.CurrencyField(choices=[('AUD', 'Australian Dollar'), ('BRL', 'Brazilian Real'), ('GBP', 'British Pound'), ('CAD', 'Canadian Dollar'), ('CNY', 'Chinese Yuan'), ('EUR', 'Euro'), ('INR', 'Indian Rupee'), ('JPY', 'Japanese Yen'), ('MXN', 'Mexican Peso'), ('CHF', 'Swiss Franc'), ('TZS', 'Tanzanian Shilling'), ('USD', 'US Dollar')], default='USD', editable=False, max_length=3)),
                ('subtotal', djmoney.models.fields.MoneyField(decimal_places=2, default_currency='USD', help_text='Subtotal amount before taxes', max_digits=14)),
                ('tax_amount_currency', djmoney.models.fields.CurrencyField(choices=[('AUD', 'Australian Dollar'), ('BRL', 'Brazilian Real'), ('GBP', 'British Pound'), ('CAD', 'Canadian Dollar'), ('CNY', 'Chinese Yuan'), ('EUR', 'Euro'), ('INR', 'Indian Rupee'), ('JPY', 'Japanese Yen'), ('MXN', 'Mexican Peso'), ('CHF', 'Swiss Franc'), ('TZS', 'Tanzanian Shilling'), ('USD', 'US Dollar')], default='USD', editable=False, max_length=3)),
                ('tax_amount', djmoney.models.fields.MoneyField(decimal_places=2, default_currency='USD', help_text='Tax amount', max_digits=14)),
                ('total_amount_currency', djmoney.models.fields.CurrencyField(choices=[('AUD', 'Australian Dollar'), ('BRL', 'Brazilian Real'), ('GBP', 'British Pound'), ('CAD', 'Canadian Dollar'), ('CNY', 'Chinese Yuan'), ('EUR', 'Euro'), ('INR', 'Indian Rupee'), ('JPY', 'Japanese Yen'), ('MXN', 'Mexican Peso'), ('CHF', 'Swiss Franc'), ('TZS', 'Tanzanian Shilling'), ('USD', 'US Dollar')], default='USD', editable=False, max_length=3)),
                ('total_amount', djmoney.models.fields.MoneyField(decimal_places=2, default_currency='USD', help_text='Total amount including taxes', max_digits=14)),
                ('terms_conditions', models.TextField(blank=True, help_text='Terms and conditions for this quote')),
                ('payment_terms', models.TextField(blank=True, help_text='Payment terms and schedule')),
                ('cancellation_policy', models.TextField(blank=True, help_text='Cancellation policy')),
                ('internal_notes', models.TextField(blank=True, help_text='Internal notes (not visible to client)')),
                ('sent_at', models.DateTimeField(blank=True, help_text='When the quote was sent to the client', null=True)),
                ('approved_at', models.DateTimeField(blank=True, help_text='When the quote was approved by the client', null=True)),
                ('business', models.ForeignKey(help_text='Business this quote belongs to', on_delete=django.db.models.deletion.CASCADE, related_name='quotes', to='businesses.business')),
                ('client', models.ForeignKey(help_text='Client this quote is for', on_delete=django.db.models.deletion.CASCADE, related_name='quotes', to='clients.client')),
                ('created_by', models.ForeignKey(help_text='User who created this record', on_delete=django.db.models.deletion.PROTECT, related_name='%(class)ss_created', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Quote',
                'verbose_name_plural': 'Quotes',
                'db_table': 'quotes_quote',
                'indexes': [models.Index(fields=['business', 'status'], name='quotes_quot_busines_bd7bd5_idx'), models.Index(fields=['business', 'client'], name='quotes_quot_busines_c89940_idx'), models.Index(fields=['public_hash'], name='quotes_quot_public__b087f3_idx'), models.Index(fields=['valid_until'], name='quotes_quot_valid_u_e269ff_idx')],
                'unique_together': {('business', 'quote_number')},
            },
        ),
    ]
