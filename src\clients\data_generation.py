"""
Data generation services for clients app using model_bakery.
"""

import random
from model_bakery import baker
from faker import Faker

from .models import Client, Traveler

fake = Faker()


class ClientsDataGenerator:
    """Data generator for clients app models."""

    def __init__(self):
        self.created_objects = {
            'clients': [],
            'travelers': []
        }

    def generate_clients(self, count=20, businesses=None):
        """Generate clients for businesses."""
        if not businesses:
            return []

        clients = []

        # Client type distribution: 70% individual, 25% corporate, 5% travel agent
        client_types = [
            (Client.ClientType.INDIVIDUAL, 0.7),
            (Client.ClientType.CORPORATE, 0.25),
            (Client.ClientType.TRAVEL_AGENT, 0.05)
        ]

        # International client countries
        countries = [
            'United States', 'United Kingdom', 'Germany', 'France', 'Australia',
            'Canada', 'Netherlands', 'Switzerland', 'Sweden', 'Norway',
            'Denmark', 'Belgium', 'Italy', 'Spain', 'Japan', 'South Korea'
        ]

        for i in range(count):
            business = random.choice(businesses)

            # Select client type based on weights
            client_type = random.choices(
                [ct for ct, _ in client_types],
                weights=[weight for _, weight in client_types]
            )[0]

            # Generate client based on type
            if client_type == Client.ClientType.CORPORATE:
                client = baker.make(
                    Client,
                    business=business,
                    client_type=client_type,
                    company_name=fake.company(),
                    first_name=fake.first_name(),
                    last_name=fake.last_name(),
                    email=fake.company_email(),
                    phone=fake.phone_number(),

                    # Address information
                    address_line1=fake.street_address(),
                    address_line2=fake.secondary_address() if fake.boolean(chance_of_getting_true=30) else '',
                    city=fake.city(),
                    state_province=fake.state(),
                    postal_code=fake.postcode(),
                    country=random.choice(countries),

                    # Business details
                    notes=fake.text(max_nb_chars=200) if fake.boolean(chance_of_getting_true=40) else '',
                    preferences=self._generate_client_preferences(),

                    created_by=business.created_by,
                    is_active=fake.boolean(chance_of_getting_true=95),
                )

            elif client_type == Client.ClientType.TRAVEL_AGENT:
                client = baker.make(
                    Client,
                    business=business,
                    client_type=client_type,
                    company_name=f'{fake.last_name()} Travel Agency',
                    first_name=fake.first_name(),
                    last_name=fake.last_name(),
                    email=fake.email(),
                    phone=fake.phone_number(),

                    # Address information
                    address_line1=fake.street_address(),
                    city=fake.city(),
                    state_province=fake.state(),
                    postal_code=fake.postcode(),
                    country=random.choice(countries),

                    # Business details
                    notes=f'Travel agent specializing in {random.choice(["luxury", "adventure", "cultural", "eco", "family"])} tours',
                    preferences=self._generate_agent_preferences(),

                    created_by=business.created_by,
                    is_active=fake.boolean(chance_of_getting_true=98),
                )

            else:  # Individual client
                client = baker.make(
                    Client,
                    business=business,
                    client_type=client_type,
                    first_name=fake.first_name(),
                    last_name=fake.last_name(),
                    email=fake.email(),
                    phone=fake.phone_number(),

                    # Address information
                    address_line1=fake.street_address(),
                    address_line2=fake.secondary_address() if fake.boolean(chance_of_getting_true=20) else '',
                    city=fake.city(),
                    state_province=fake.state(),
                    postal_code=fake.postcode(),
                    country=random.choice(countries),

                    # Personal details
                    notes=fake.text(max_nb_chars=150) if fake.boolean(chance_of_getting_true=30) else '',
                    preferences=self._generate_individual_preferences(),

                    created_by=business.created_by,
                    is_active=fake.boolean(chance_of_getting_true=97),
                )

            clients.append(client)

        self.created_objects['clients'].extend(clients)
        return clients

    def generate_travelers(self, count=50, clients=None):
        """Generate travelers for clients."""
        if not clients:
            clients = self.created_objects['clients']

        if not clients:
            return []

        travelers = []

        # Dietary restrictions options
        dietary_options = [
            '', 'Vegetarian', 'Vegan', 'Gluten-free', 'Halal', 'Kosher',
            'Dairy-free', 'Nut allergy', 'Pescatarian', 'Low sodium'
        ]

        # Medical conditions options
        medical_options = [
            '', 'None', 'Diabetes', 'Heart condition', 'High blood pressure',
            'Asthma', 'Arthritis', 'Back problems', 'Mobility issues', 'Allergies'
        ]

        # Relationship options
        relationships = [
            'Spouse', 'Partner', 'Parent', 'Child', 'Sibling', 'Friend',
            'Colleague', 'Relative', 'Guardian', 'Other'
        ]

        for i in range(count):
            client = random.choice(clients)

            traveler = baker.make(
                Traveler,
                client=client,
                first_name=fake.first_name(),
                last_name=fake.last_name(),
                email=fake.email(),
                phone=fake.phone_number(),

                # Personal information
                date_of_birth=fake.date_of_birth(minimum_age=5, maximum_age=85),
                gender=random.choice(['male', 'female', 'other', 'prefer_not_to_say', '']),

                # Travel documents
                passport_number=fake.bothify(text='??#######'),
                passport_expiry=fake.future_date(end_date='+10y'),
                nationality=client.country,  # Usually same as client's country

                # Health and dietary information
                dietary_restrictions=random.choice(dietary_options),
                medical_conditions=random.choice(medical_options),
                mobility_requirements=fake.text(max_nb_chars=50) if fake.boolean(chance_of_getting_true=10) else '',

                # Emergency contact
                emergency_contact_name=fake.name(),
                emergency_contact_phone=fake.phone_number(),
                emergency_contact_relationship=random.choice(relationships),

                # Travel preferences
                special_requests=fake.text(max_nb_chars=100) if fake.boolean(chance_of_getting_true=25) else '',

                created_by=client.created_by,
                is_active=fake.boolean(chance_of_getting_true=98),
            )

            travelers.append(traveler)

        self.created_objects['travelers'].extend(travelers)
        return travelers

    def _generate_client_preferences(self):
        """Generate realistic client preferences."""
        preferences = []

        # Accommodation preferences
        if fake.boolean(chance_of_getting_true=60):
            preferences.append(f"Accommodation: {random.choice(['Luxury hotels', 'Boutique lodges', 'Eco-friendly accommodations', 'Budget-friendly options'])}")

        # Activity preferences
        if fake.boolean(chance_of_getting_true=70):
            preferences.append(f"Activities: {random.choice(['Wildlife viewing', 'Cultural experiences', 'Adventure activities', 'Photography tours', 'Relaxation'])}")

        # Group size preferences
        if fake.boolean(chance_of_getting_true=50):
            preferences.append(f"Group size: {random.choice(['Private tours only', 'Small groups (2-6 people)', 'Medium groups (7-12 people)', 'Any group size'])}")

        # Communication preferences
        if fake.boolean(chance_of_getting_true=40):
            preferences.append(f"Communication: {random.choice(['Email preferred', 'Phone calls preferred', 'WhatsApp preferred', 'Text messages preferred'])}")

        return '; '.join(preferences)

    def _generate_agent_preferences(self):
        """Generate travel agent specific preferences."""
        preferences = [
            f"Commission rate: {random.choice(['10%', '12%', '15%', 'Negotiable'])}",
            f"Specialization: {random.choice(['Luxury travel', 'Adventure tours', 'Cultural experiences', 'Family vacations', 'Honeymoon packages'])}",
            f"Payment terms: {random.choice(['Net 30', 'Net 15', 'Immediate', 'Upon booking confirmation'])}",
        ]

        if fake.boolean(chance_of_getting_true=60):
            preferences.append(f"Marketing support: {random.choice(['Brochures needed', 'Digital assets preferred', 'Co-marketing opportunities', 'Training sessions required'])}")

        return '; '.join(preferences)

    def _generate_individual_preferences(self):
        """Generate individual client preferences."""
        preferences = []

        # Travel style
        if fake.boolean(chance_of_getting_true=80):
            preferences.append(f"Travel style: {random.choice(['Luxury', 'Mid-range', 'Budget-conscious', 'Adventure-focused', 'Cultural immersion'])}")

        # Interests
        if fake.boolean(chance_of_getting_true=70):
            interests = random.sample([
                'Wildlife photography', 'Bird watching', 'Cultural sites', 'Local cuisine',
                'Adventure activities', 'Relaxation', 'Shopping', 'Nightlife', 'History'
            ], k=random.randint(1, 3))
            preferences.append(f"Interests: {', '.join(interests)}")

        # Special occasions
        if fake.boolean(chance_of_getting_true=30):
            preferences.append(f"Special occasion: {random.choice(['Honeymoon', 'Anniversary', 'Birthday celebration', 'Retirement trip', 'Family reunion'])}")

        return '; '.join(preferences)

    def get_generation_summary(self):
        """Get summary of generated objects."""
        return {
            key: len(objects) for key, objects in self.created_objects.items()
        }
