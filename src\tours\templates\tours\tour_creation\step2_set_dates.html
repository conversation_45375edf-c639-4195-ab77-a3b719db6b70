{% extends 'tours/tour_creation/base.html' %}

{% block content %}
<div class="space-y-6">
  <!-- Tour Header -->
  <div class="bg-white p-4 rounded-lg shadow flex items-center">
    <div class="h-12 w-12 bg-indigo-100 rounded-lg flex items-center justify-center mr-4">
      <span class="text-indigo-800 text-xl">{{ tour.emoji }}</span>
    </div>
    <div>
      <h2 class="text-lg font-medium text-gray-900">{{ tour.name }}</h2>
      <p class="text-sm text-gray-500">Max {{ tour.max_capacity }} guests • {{ tour.timezone }}</p>
    </div>
  </div>

  <!-- Timezone Banner -->
  <div class="bg-blue-50 border-l-4 border-blue-400 p-4">
    <div class="flex">
      <div class="flex-shrink-0">
        <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
        </svg>
      </div>
      <div class="ml-3 flex-1">
        <p class="text-sm text-blue-700">
          All times shown in <strong>{{ tour.timezone }} ({{ tour.tz_offset }})</strong>.
          <a href="#" class="underline">Convert to your timezone</a>
        </p>
      </div>
    </div>
  </div>

  <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
    <!-- Calendar -->
    <div class="lg:col-span-2">
      <div class="bg-white rounded-lg shadow overflow-hidden">
        <div class="border-b border-gray-200 p-4 flex justify-between items-center">
          <h3 class="text-lg font-medium">Select Date</h3>
          <div class="text-sm text-gray-500">
            Availability:
            <span class="text-green-600">Good</span> •
            <span class="text-yellow-600">Limited</span> •
            <span class="text-red-600">Full</span>
          </div>
        </div>

        <div class="p-4">
          <!-- Simplified calendar - integrate with flatpickr in real app -->
          <div class="grid grid-cols-7 gap-1 text-center">
            {% for day in calendar_days %}
              <div class="p-1 rounded cursor-pointer
                {% if day.is_past %}opacity-50 cursor-not-allowed{% endif %}
                {% if day.available_slots == 0 %}bg-red-100 text-red-800{% endif %}
                {% if day.available_slots > 0 and day.available_slots <= 5 %}bg-yellow-100 text-yellow-800{% endif %}
                {% if day.available_slots > 5 %}bg-green-100 text-green-800{% endif %}">
                <div class="text-xs font-medium">{{ day.date|date:"D" }}</div>
                <div class="text-sm font-semibold">{{ day.date.day }}</div>
                {% if day.available_slots > 0 %}
                  <div class="text-xs mt-1">{{ day.available_slots }} left</div>
                {% endif %}
              </div>
            {% endfor %}
          </div>
        </div>

        <div class="border-t border-gray-200 p-4">
          <div class="grid grid-cols-2 gap-4">
            <div>
              <label for="start-time" class="block text-sm font-medium text-gray-700">Start Time</label>
              <select id="start-time" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 rounded-md">
                {% for time in available_times %}
                <option value="{{ time.iso }}">{{ time.strftime }}{% if time.is_ending_soon %} (⚠️ Ends in 2h){% endif %}</option>
                {% endfor %}
              </select>
            </div>

            <div>
              <label for="duration" class="block text-sm font-medium text-gray-700">Duration</label>
              <select id="duration" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 rounded-md">
                <option>1 hour</option>
                <option selected>2 hours</option>
                <option>3 hours</option>
                <option>4+ hours</option>
              </select>
            </div>
          </div>

          <div class="mt-4 bg-gray-50 p-3 rounded-md">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-indigo-500" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd" />
                </svg>
              </div>
              <p class="ml-3 text-sm text-gray-700">
                End time: <strong>12:00 PM {{ tour.timezone }}</strong>
                <span class="text-gray-500">(Aug 15, 2025)</span>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Date Summary -->
    <div>
      <div class="bg-white rounded-lg shadow overflow-hidden sticky top-6">
        <div class="border-b border-gray-200 p-4">
          <h3 class="text-lg font-medium">Selected Date</h3>
        </div>

        <div class="p-4 space-y-4">
          <div class="text-center p-4 bg-gray-50 rounded-lg">
            <div class="text-4xl font-bold text-indigo-600">15</div>
            <div class="text-lg font-medium text-gray-900 mt-1">AUG</div>
            <div class="text-sm text-gray-500">Friday</div>
          </div>

          <div>
            <div class="text-sm font-medium text-gray-700">Time</div>
            <div class="mt-1 text-lg">9:00 AM - 12:00 PM</div>
          </div>

          <div>
            <div class="text-sm font-medium text-gray-700">Availability</div>
            <div class="mt-1 relative pt-2">
              <div class="flex mb-2 items-center justify-between">
                <div>
                  <span class="text-xs font-semibold inline-block py-1 px-2 uppercase rounded-full text-green-600 bg-green-200">
                    17 slots available
                  </span>
                </div>
                <div class="text-right">
                  <span class="text-xs font-semibold inline-block text-green-600">
                    85%
                  </span>
                </div>
              </div>
              <div class="overflow-hidden h-2 mb-4 text-xs flex rounded bg-green-200">
                <div style="width:85%" class="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-green-500"></div>
              </div>
            </div>
          </div>

          <div class="bg-yellow-50 border-l-4 border-yellow-400 p-3">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3 flex-1">
                <p class="text-sm text-yellow-700">
                  <strong>Olympic Opening Ceremony:</strong> Expect traffic delays
                </p>
              </div>
            </div>
          </div>
        </div>

        <div class="border-t border-gray-200 p-4">
          <button
                  class="w-full px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
            Continue to Capacity
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  document.getElementById('current-step').textContent = '2';
  // Real app would use flatpickr for calendar with hx-trigger
</script>
{% endblock %}
