{% load i18n %}
{% load crispy_forms_filters %}

<c-layouts.dashboard>
  <c-slot name="title">
    {% if object %}
    {% trans "Edit Quote" %} - {{ object.quote_number }}
    {% else %}
    {% trans "Create Quote" %}
    {% endif %}
  </c-slot>

  <c-slot name="main">
    <div class="container mx-auto">
      <div class="max-w-4xl mx-auto">
        <div class="bg-white shadow rounded-lg">

          <form method="post" class="px-6 py-6">
            {% csrf_token %}

            {{ form | crispy }}

            <div class="mt-8 flex justify-end space-x-3">
              <a href="{% if object %}{% url 'quotes:quote-detail' object.pk %}{% else %}{% url 'quotes:quote-list' %}{% endif %}"
                class="btn btn-secondary">
                {% trans "Cancel" %}
              </a>
              <button type="submit" class="cursor-pointer">
                {% if object %}
                {% trans "Update Quote" %}
                {% else %}
                {% trans "Create Quote" %}
                {% endif %}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>


    <script>
      // Auto-calculate total when subtotal or tax changes
      document.addEventListener('DOMContentLoaded', function () {
        const subtotalField = document.getElementById('{{ form.subtotal.id_for_label }}');
        const taxField = document.getElementById('{{ form.tax_amount.id_for_label }}');
        const totalField = document.getElementById('{{ form.total_amount.id_for_label }}');

        function calculateTotal() {
          const subtotal = parseFloat(subtotalField.value) || 0;
          const tax = parseFloat(taxField.value) || 0;
          const total = subtotal + tax;
          totalField.value = total.toFixed(2);
        }

        if (subtotalField && taxField && totalField) {
          subtotalField.addEventListener('input', calculateTotal);
          taxField.addEventListener('input', calculateTotal);
        }
      });
    </script>
  </c-slot>
</c-layouts.dashboard>
