"""
Forms for user profile management.
"""

from django import forms
from django.contrib.auth.forms import PasswordResetForm, UserCreationForm
from django.urls import reverse

from businesses.models import Business
from core.notifications import EmailConfirmationNotification, PasswordResetNotification

from .models import Profile, User


class CustomUserCreationForm(UserCreationForm):
    """Custom user creation form for initial registration."""

    email = forms.EmailField(required=True)

    class Meta:
        model = User
        fields = (
            'username',
            'email',
            'password1',
            'password2',
        )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Add CSS classes to form fields
        for field_name, field in self.fields.items():
            field.widget.attrs['class'] = (
                'form-input w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500'
            )

    def save(self, commit=True):
        user = super().save(commit=False)
        user.email = self.cleaned_data['email']
        # Set user as inactive until email confirmation
        user.is_active = False
        # todo: set profile role and business

        if commit:
            user.save()

        return user

    @staticmethod
    def send_confirmation_email(user, confirmation_url):
        """Send confirmation email to user."""
        EmailConfirmationNotification(
            user,
            confirmation_url,
        ).send()


class ProfileForm(forms.ModelForm):
    """Form for editing user profile information."""

    class Meta:
        model = Profile
        fields = [
            'first_name',
            'last_name',
            'phone',
            'avatar',
            'department',
            'timezone',
            'language',
        ]
        widgets = {
            'date_of_birth': forms.DateInput(attrs={'type': 'date'}),
            'bio': forms.Textarea(attrs={'rows': 4}),
            'first_name': forms.TextInput(attrs={'class': 'form-input'}),
            'last_name': forms.TextInput(attrs={'class': 'form-input'}),
            'phone': forms.TextInput(attrs={'class': 'form-input'}),
            'address_line1': forms.TextInput(attrs={'class': 'form-input'}),
            'address_line2': forms.TextInput(attrs={'class': 'form-input'}),
            'city': forms.TextInput(attrs={'class': 'form-input'}),
            'state_province': forms.TextInput(attrs={'class': 'form-input'}),
            'postal_code': forms.TextInput(attrs={'class': 'form-input'}),
            'country': forms.TextInput(attrs={'class': 'form-input'}),
            'job_title': forms.TextInput(attrs={'class': 'form-input'}),
            'department': forms.TextInput(attrs={'class': 'form-input'}),
            'emergency_contact_name': forms.TextInput(attrs={'class': 'form-input'}),
            'emergency_contact_phone': forms.TextInput(attrs={'class': 'form-input'}),
            'emergency_contact_relationship': forms.TextInput(
                attrs={'class': 'form-input'}
            ),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Add CSS classes to all form fields
        for field_name, field in self.fields.items():
            if 'class' not in field.widget.attrs:
                field.widget.attrs['class'] = (
                    'form-input w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500'
                )


class UserAccountForm(forms.ModelForm):
    """Form for editing basic user account information."""

    class Meta:
        model = User
        fields = [
            'username',
            'email',
        ]
        widgets = {
            'username': forms.TextInput(attrs={'class': 'form-input'}),
            'email': forms.EmailInput(attrs={'class': 'form-input'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Add CSS classes to all form fields
        for field_name, field in self.fields.items():
            if 'class' not in field.widget.attrs:
                if isinstance(field.widget, forms.Select):
                    field.widget.attrs['class'] = (
                        'form-select w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500'
                    )
                else:
                    field.widget.attrs['class'] = (
                        'form-input w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500'
                    )


class OnboardingBusinessForm(forms.ModelForm):
    """Simplified form for onboarding business creation."""

    class Meta:
        model = Business
        fields = [
            'name',
            'description',
            'email',
            'phone',
        ]
        widgets = {
            'name': forms.TextInput(
                attrs={
                    'class': 'form-input',
                    'required': True,
                    'placeholder': 'Your Business Name',
                }
            ),
            'description': forms.Textarea(
                attrs={
                    'class': 'form-input',
                    'rows': 3,
                    'placeholder': 'Brief description of your tour business',
                }
            ),
            'email': forms.EmailInput(
                attrs={
                    'class': 'form-input',
                    'required': True,
                    'placeholder': '<EMAIL>',
                }
            ),
            'phone': forms.TextInput(
                attrs={'class': 'form-input', 'placeholder': '******-123-4567'}
            ),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Make certain fields required for onboarding
        self.fields['name'].required = True
        self.fields['email'].required = True


class OnboardingProfileForm(forms.ModelForm):
    """Simplified form for onboarding profile completion."""

    class Meta:
        model = Profile
        fields = [
            'first_name',
            'last_name',
            'phone',
            'avatar',
            'timezone',
            'language',
        ]

    # def __init__(self, *args, **kwargs):
    #     super().__init__(*args, **kwargs)
    #     # Make required fields actually required
    #     for field_name in [
    #         'first_name',
    #         'last_name',
    #         'phone',
    #     ]:
    #         self.fields[field_name].required = True

    #     # Add CSS classes to all form fields
    #     for field_name, field in self.fields.items():
    #         if 'class' not in field.widget.attrs:
    #             field.widget.attrs['class'] = (
    #                 'form-input w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500'
    #             )


class AddUserForm(forms.ModelForm):
    """Form for business owners to add new users to their business."""

    password1 = forms.CharField(
        label='Password',
        widget=forms.PasswordInput(attrs={'class': 'form-input'}),
        help_text='Leave blank to send invitation email for user to set password',
        required=False,
    )
    password2 = forms.CharField(
        label='Confirm Password',
        widget=forms.PasswordInput(attrs={'class': 'form-input'}),
        required=False,
    )

    class Meta:
        model = User
        fields = [
            'username',
            'email',
        ]
        widgets = {
            'username': forms.TextInput(attrs={'class': 'form-input'}),
            'email': forms.EmailInput(attrs={'class': 'form-input'}),
        }

    def __init__(self, *args, **kwargs):
        self.business = kwargs.pop('business', None)
        super().__init__(*args, **kwargs)

        # # Limit role choices for business users
        # if self.business:
        #     self.fields['role'].choices = [
        #         (Profile.Role.AGENT, Profile.Role.AGENT.label),
        #         (Profile.Role.BUSINESS_OWNER, Profile.Role.BUSINESS_OWNER.label),
        #     ]

        # Add CSS classes
        for field_name, field in self.fields.items():
            if 'class' not in field.widget.attrs:
                if isinstance(field.widget, forms.Select):
                    field.widget.attrs['class'] = (
                        'form-select w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500'
                    )
                else:
                    field.widget.attrs['class'] = (
                        'form-input w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500'
                    )

    def clean(self):
        """Validate form data."""
        cleaned_data = super().clean()
        password1 = cleaned_data.get('password1')
        password2 = cleaned_data.get('password2')

        # If password is provided, confirm password is required
        if password1 and not password2:
            raise forms.ValidationError('Please confirm the password.')

        if password1 and password2 and password1 != password2:
            raise forms.ValidationError("Passwords don't match.")

        return cleaned_data

    def save(self, commit=True):
        """Save user and associate with business."""
        user = super().save(commit=False)

        # Set password if provided, otherwise mark for invitation
        password1 = self.cleaned_data.get('password1')
        if password1:
            user.set_password(password1)
            user.is_active = True
            user.email_confirmed = True
        else:
            # User will be invited via email
            user.is_active = False
            user.email_confirmed = False

        if commit:
            user.save()

            # Associate with business
            if self.business:
                user.profile.business = self.business
                user.save(update_fields=['business'])

        return user


class BulkUserUploadForm(forms.Form):
    """Form for uploading users via Excel file."""

    excel_file = forms.FileField(
        label='Excel File',
        help_text='Upload an Excel file with user information',
        widget=forms.FileInput(attrs={'class': 'form-input', 'accept': '.xlsx,.xls'}),
    )

    def clean_excel_file(self):
        """Validate uploaded Excel file."""
        file = self.cleaned_data['excel_file']

        if not file.name.endswith(('.xlsx', '.xls')):
            raise forms.ValidationError('Please upload an Excel file (.xlsx or .xls)')

        if file.size > 5 * 1024 * 1024:  # 5MB limit
            raise forms.ValidationError('File size must be less than 5MB')

        return file


class CustomPasswordResetForm(PasswordResetForm):
    """Custom password reset form that uses our Herald notification with proper URL namespace."""

    def send_mail(
        self,
        subject_template_name,
        email_template_name,
        context,
        from_email,
        to_email,
        html_email_template_name=None,
    ):
        """
        Send a django.core.mail.EmailMultiAlternatives to `to_email`.
        This method is overridden to use our custom Herald notification.
        """
        # Get the user from the context
        user = context['user']

        # Build the reset URL with proper namespace
        request = context.get('request')
        if request:
            reset_url = request.build_absolute_uri(
                reverse(
                    'accounts:password_reset_confirm',
                    kwargs={
                        'uidb64': context['uid'],
                        'token': context['token'],
                    },
                )
            )
        else:
            # Fallback if no request in context
            from django.conf import settings
            from django.contrib.sites.shortcuts import get_current_site

            current_site = get_current_site(None)
            protocol = 'https' if getattr(settings, 'USE_TLS', False) else 'http'
            reset_url = f'{protocol}://{current_site.domain}{reverse("accounts:password_reset_confirm", kwargs={"uidb64": context["uid"], "token": context["token"]})}'

        # Send using our custom Herald notification
        PasswordResetNotification(user, reset_url).send()

    def get_users(self, email):
        """Given an email, return matching user(s) who should receive a reset."""
        active_users = User.objects.filter(email__iexact=email, is_active=True)
        return (
            u
            for u in active_users
            if u.has_usable_password()
            and getattr(u, 'email_confirmed', True)  # Only send to confirmed emails
        )
