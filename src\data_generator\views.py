"""
Views for data generation interface.
"""

import json

from django.contrib import messages
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.db import transaction
from django.http import JsonResponse
from django.shortcuts import redirect
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from django.views.generic import TemplateView

from .services import DataGeneratorService


class DataGeneratorMixin(UserPassesTestMixin):
    """Mixin to ensure only business users can access data generation."""

    def test_func(self):
        # Allow access to any authenticated user who belongs to a business
        # or is a superuser (for system administration)
        if not self.request.user.is_authenticated:
            return False

        if self.request.user.is_superuser:
            return True

        # Allow access if user belongs to any business
        return (
            hasattr(self.request.user.profile, 'business')
            and self.request.user.profile.business is not None
        )


class DataGeneratorDashboardView(LoginRequired<PERSON><PERSON><PERSON>, DataGenerator<PERSON><PERSON><PERSON>, TemplateView):
    """Dashboard for data generation with options and controls."""

    template_name = 'data_generator/dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Get current data counts
        from accounts.models import Profile, User
        from bookings.models import Booking, TourEvent
        from businesses.models import Business
        from clients.models import Client, Traveler
        from invoices.models import Invoice
        from payments.models import Payment
        from quotes.models import Quote
        from templates_manager.models import PDFTemplate

        context['current_counts'] = {
            'users': User.objects.filter(is_superuser=False).count(),
            'profiles': Profile.objects.count(),
            'businesses': Business.objects.count(),
            'clients': Client.objects.count(),
            'travelers': Traveler.objects.count(),
            'events': TourEvent.objects.count(),
            'bookings': Booking.objects.count(),
            'quotes': Quote.objects.count(),
            'invoices': Invoice.objects.count(),
            'payments': Payment.objects.count(),
            'pdf_templates': PDFTemplate.objects.count(),
        }

        context['scale_options'] = [
            {
                'name': 'small',
                'label': 'Small Dataset',
                'description': 'Perfect for testing - 1 business, 5 clients, 8 bookings',
                'counts': {
                    'users': 3,
                    'profiles': 3,
                    'businesses': 1,
                    'clients': 5,
                    'travelers': 8,
                    'events': 6,
                    'quotes': 4,
                    'bookings': 8,
                    'invoices': 3,
                    'payments': 5,
                    'templates': 6,
                },
            },
            {
                'name': 'medium',
                'label': 'Medium Dataset',
                'description': 'Good for demos - 2 businesses, 10 clients, 15 bookings',
                'counts': {
                    'users': 5,
                    'profiles': 5,
                    'businesses': 2,
                    'clients': 10,
                    'travelers': 20,
                    'events': 12,
                    'quotes': 8,
                    'bookings': 15,
                    'invoices': 5,
                    'payments': 12,
                    'templates': 10,
                },
            },
            {
                'name': 'large',
                'label': 'Large Dataset',
                'description': 'Comprehensive data - 3 businesses, 20 clients, 30 bookings',
                'counts': {
                    'users': 8,
                    'profiles': 8,
                    'businesses': 3,
                    'clients': 20,
                    'travelers': 40,
                    'events': 25,
                    'quotes': 15,
                    'bookings': 30,
                    'invoices': 10,
                    'payments': 25,
                    'templates': 15,
                },
            },
        ]

        return context


@method_decorator(csrf_exempt, name='dispatch')
class GenerateDataView(LoginRequiredMixin, DataGeneratorMixin, TemplateView):
    """View to handle data generation requests."""

    def post(self, request, *args, **kwargs):
        """Handle data generation POST request."""
        try:
            data = json.loads(request.body)
            action = data.get('action')
            scale = data.get('scale', 'medium')

            service = DataGeneratorService()

            with transaction.atomic():
                if action == 'clear_all':
                    service.clear_all_data()
                    return JsonResponse(
                        {
                            'success': True,
                            'message': 'All data cleared successfully!',
                            'counts': service.get_generation_summary(),
                        }
                    )

                elif action == 'generate_complete':
                    # Clear existing data first if requested
                    if data.get('clear_first', False):
                        service.clear_all_data()

                    # Generate complete dataset
                    counts = service.generate_complete_dataset(scale)

                    return JsonResponse(
                        {
                            'success': True,
                            'message': f'Successfully generated {scale} dataset!',
                            'counts': counts,
                        }
                    )

                elif action == 'generate_specific':
                    # Generate specific type of data
                    data_type = data.get('data_type')
                    count = data.get('count', 5)

                    if data_type == 'users':
                        objects = service.generate_users(count)
                    elif data_type == 'businesses':
                        objects = service.generate_businesses(count)
                    elif data_type == 'clients':
                        objects = service.generate_clients(count)
                    elif data_type == 'travelers':
                        objects = service.generate_travelers(count)
                    elif data_type == 'events':
                        objects = service.generate_events(count)
                    elif data_type == 'quotes':
                        objects = service.generate_quotes(count)
                    elif data_type == 'bookings':
                        objects = service.generate_bookings(count)
                    elif data_type == 'invoices':
                        objects = service.generate_invoices(count)
                    elif data_type == 'payments':
                        objects = service.generate_payments(count)
                    elif data_type == 'templates':
                        objects = service.generate_pdf_templates(count)
                    else:
                        return JsonResponse(
                            {
                                'success': False,
                                'message': f'Unknown data type: {data_type}',
                            }
                        )

                    return JsonResponse(
                        {
                            'success': True,
                            'message': f'Successfully generated {len(objects)} {data_type}!',
                            'counts': {data_type: len(objects)},
                        }
                    )

                else:
                    return JsonResponse(
                        {'success': False, 'message': f'Unknown action: {action}'}
                    )

        except Exception as e:
            return JsonResponse(
                {'success': False, 'message': f'Error generating data: {str(e)}'}
            )


class QuickGenerateView(LoginRequiredMixin, DataGeneratorMixin, TemplateView):
    """Quick data generation for common scenarios."""

    def post(self, request, *args, **kwargs):
        """Handle quick generation requests."""
        action = request.POST.get('action')

        try:
            service = DataGeneratorService()

            with transaction.atomic():
                if action == 'demo_data':
                    # Generate demo data (medium scale)
                    counts = service.generate_complete_dataset('medium')
                    messages.success(
                        request,
                        f'Demo data generated successfully! Created {counts["businesses"]} businesses, '
                        f'{counts["clients"]} clients, {counts["events"]} events, and {counts["bookings"]} bookings.',
                    )

                elif action == 'test_data':
                    # Generate test data (small scale)
                    counts = service.generate_complete_dataset('small')
                    messages.success(
                        request,
                        f'Test data generated successfully! Created {counts["businesses"]} business, '
                        f'{counts["clients"]} clients, {counts["events"]} events, and {counts["bookings"]} bookings.',
                    )

                elif action == 'clear_all':
                    service.clear_all_data()
                    messages.success(
                        request,
                        'All data cleared successfully!',
                    )

                else:
                    messages.error(
                        request,
                        f'Unknown action: {action}',
                    )

        except Exception as e:
            messages.error(
                request,
                f'Error: {str(e)}',
            )

        return redirect('data_generator:dashboard')


class DataStatsView(LoginRequiredMixin, DataGeneratorMixin, TemplateView):
    """View to get current data statistics."""

    def get(self, request, *args, **kwargs):
        """Return current data counts as JSON."""
        from accounts.models import Profile, User
        from bookings.models import Booking, TourEvent
        from businesses.models import Business
        from clients.models import Client, Traveler
        from invoices.models import Invoice
        from payments.models import Payment
        from quotes.models import Quote
        from templates_manager.models import PDFTemplate

        counts = {
            'users': User.objects.filter(is_superuser=False).count(),
            'profiles': Profile.objects.count(),
            'businesses': Business.objects.count(),
            'clients': Client.objects.count(),
            'travelers': Traveler.objects.count(),
            'events': TourEvent.objects.count(),
            'bookings': Booking.objects.count(),
            'quotes': Quote.objects.count(),
            'invoices': Invoice.objects.count(),
            'payments': Payment.objects.count(),
            'pdf_templates': PDFTemplate.objects.count(),
        }

        return JsonResponse({'counts': counts})
