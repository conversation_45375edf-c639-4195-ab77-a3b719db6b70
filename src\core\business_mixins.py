"""Business access control mixins for views."""

from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.core.exceptions import PermissionDenied
from django.http import Http404


class BusinessAccessMixin(LoginRequiredMixin):
    """
    Mixin that provides business-level access control for views.

    This mixin ensures that users can only access resources that belong to
    businesses they have access to. It provides methods to:
    - Get businesses accessible to the current user
    - Filter querysets by accessible businesses
    - Check if a user has access to a specific business

    Usage:
        class MyView(BusinessAccessMixin, ListView):
            model = MyModel

            def get_queryset(self):
                return self.filter_by_accessible_businesses(
                    super().get_queryset()
                )
    """

    def get_accessible_businesses(self):
        """
        Get businesses accessible to the current user.

        Returns:
            QuerySet: Businesses the user has access to
        """
        from businesses.models import Business

        if self.request.user.is_superuser:
            return Business.objects.all()
        else:
            # Check if user has a profile and business relationship
            accessible_businesses = Business.objects.filter(
                created_by=self.request.user
            )

            if hasattr(self.request.user, 'profile') and self.request.user.profile:
                # Add businesses where user's profile is associated
                profile_business = self.request.user.profile.business
                if profile_business:
                    accessible_businesses = (
                        accessible_businesses
                        | Business.objects.filter(id=profile_business.id)
                    )

            return accessible_businesses.distinct()

    def filter_by_accessible_businesses(self, queryset, business_field='business'):
        """
        Filter a queryset to only include objects from accessible businesses.

        Args:
            queryset: The queryset to filter
            business_field: The field name that relates to the business model

        Returns:
            QuerySet: Filtered queryset
        """
        accessible_businesses = self.get_accessible_businesses()
        filter_kwargs = {f'{business_field}__in': accessible_businesses}
        return queryset.filter(**filter_kwargs)

    def check_business_access(self, business):
        """
        Check if the current user has access to a specific business.

        Args:
            business: Business instance to check access for

        Returns:
            bool: True if user has access, False otherwise
        """
        if self.request.user.is_superuser:
            return True

        # Check if user created the business
        if business.created_by == self.request.user:
            return True

        # Check if user's profile is associated with this business
        if hasattr(self.request.user, 'profile') and self.request.user.profile:
            return self.request.user.profile.business == business

        return False

    def get_user_business(self):
        """
        Get the primary business for the current user.

        Returns:
            Business: The user's primary business

        Raises:
            PermissionDenied: If user has no business access
        """
        if (
            hasattr(self.request.user, 'business')
            and self.request.user.profile.business
        ):
            return self.request.user.profile.business

        # Fallback to first accessible business
        accessible_businesses = self.get_accessible_businesses()
        if accessible_businesses.exists():
            return accessible_businesses.first()

        raise PermissionDenied("You don't have access to any business")


class BusinessOwnerRequiredMixin(BusinessAccessMixin, UserPassesTestMixin):
    """
    Mixin that requires the user to be a business owner.

    This mixin extends BusinessAccessMixin and adds a test to ensure
    the user has business owner privileges.
    """

    def test_func(self):
        """Test if user is a business owner."""
        if not self.request.user.is_authenticated:
            return False

        if self.request.user.is_superuser:
            return True

        # Check if user has business owner role
        if hasattr(self.request.user, 'profile') and self.request.user.profile:
            from accounts.models import Profile

            return self.request.user.profile.role == Profile.Role.BUSINESS_OWNER

        return False


class BusinessObjectMixin(BusinessAccessMixin):
    """
    Mixin for views that work with objects belonging to a business.

    This mixin automatically checks business access when getting objects
    and provides business-filtered querysets.
    """

    business_field = (
        'business'  # Override this if the business field has a different name
    )

    def get_object(self, queryset=None):
        """
        Get object and check business access.

        Returns:
            Model instance

        Raises:
            Http404: If object doesn't exist or user doesn't have access
        """
        if queryset is None:
            queryset = self.get_queryset()

        # Get the object
        obj = super().get_object(queryset)

        # Check business access
        business = getattr(obj, self.business_field, None)
        if business and not self.check_business_access(business):
            raise Http404('Object not found or access denied')

        return obj

    def get_queryset(self):
        """Get queryset filtered by accessible businesses."""
        queryset = super().get_queryset()
        return self.filter_by_accessible_businesses(queryset, self.business_field)


class BusinessCreateMixin(BusinessAccessMixin):
    """
    Mixin for views that create objects belonging to a business.

    This mixin automatically sets the business field when creating new objects.
    """

    business_field = (
        'business'  # Override this if the business field has a different name
    )

    def form_valid(self, form):
        """Set the business before saving."""
        if hasattr(form.instance, self.business_field):
            # Set the business to the user's primary business
            setattr(form.instance, self.business_field, self.get_user_business())

        return super().form_valid(form)


class MultiBusinessMixin(BusinessAccessMixin):
    """
    Mixin for views that need to work across multiple businesses.

    This mixin provides utilities for handling multiple business contexts
    and switching between them.
    """

    def get_current_business(self):
        """
        Get the currently selected business from session or URL.

        Returns:
            Business: Currently selected business
        """
        # Try to get business from URL parameter
        business_id = self.kwargs.get('business_id') or self.request.GET.get(
            'business_id'
        )
        if business_id:
            accessible_businesses = self.get_accessible_businesses()
            try:
                business = accessible_businesses.get(id=business_id)
                # Store in session for future requests
                self.request.session['current_business_id'] = str(business.id)
                return business
            except Exception:
                pass

        # Try to get from session
        current_business_id = self.request.session.get('current_business_id')
        if current_business_id:
            accessible_businesses = self.get_accessible_businesses()
            try:
                return accessible_businesses.get(id=current_business_id)
            except Exception:
                pass

        # Fallback to user's primary business
        return self.get_user_business()

    def get_context_data(self, **kwargs):
        """Add business context to template."""
        context = super().get_context_data(**kwargs)
        context.update(
            {
                'current_business': self.get_current_business(),
                'accessible_businesses': self.get_accessible_businesses(),
            }
        )
        return context
