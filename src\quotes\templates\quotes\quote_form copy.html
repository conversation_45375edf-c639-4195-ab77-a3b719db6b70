{% load i18n %}

<c-layouts.dashboard>
  <c-slot name="title">
    {% if object %}
    {% trans "Edit Quote" %} - {{ object.quote_number }}
    {% else %}
    {% trans "Create Quote" %}
    {% endif %}
  </c-slot>

  <c-slot name="main">
    <div class="container mx-auto px-4 py-8">
      <div class="max-w-4xl mx-auto">
        <div class="bg-white shadow rounded-lg">
          <div class="px-6 py-4 border-b border-gray-200">
            <h1 class="text-2xl font-bold text-gray-900">
              {% if object %}
              {% trans "Edit Quote" %}
              {% else %}
              {% trans "Create Quote" %}
              {% endif %}
            </h1>
          </div>

          <form method="post" class="px-6 py-6">
            {% csrf_token %}

            {% if form.errors %}
            <div class="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
              <div class="flex">
                <div class="flex-shrink-0">
                  <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                      clip-rule="evenodd" />
                  </svg>
                </div>
                <div class="ml-3">
                  <h3 class="text-sm font-medium text-red-800">
                    {% trans "There were errors with your submission" %}
                  </h3>
                  <div class="mt-2 text-sm text-red-700">
                    <ul class="list-disc pl-5 space-y-1">
                      {% for field, errors in form.errors.items %}
                      {% for error in errors %}
                      <li>{{ field|capfirst }}: {{ error }}</li>
                      {% endfor %}
                      {% endfor %}
                    </ul>
                  </div>
                </div>
              </div>
            </div>
            {% endif %}

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- Basic Information -->
              <div class="md:col-span-2">
                <h3 class="text-lg font-medium text-gray-900 mb-4">{% trans "Quote Information" %}</h3>
              </div>

              <div>
                <label for="{{ form.business.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                  {{ form.business.label }}
                </label>
                {{ form.business }}
              </div>

              <div>
                <label for="{{ form.client.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                  {{ form.client.label }}
                </label>
                {{ form.client }}
              </div>

              <div class="md:col-span-2">
                <label for="{{ form.title.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                  {{ form.title.label }}
                </label>
                {{ form.title }}
              </div>

              <div class="md:col-span-2">
                <label for="{{ form.description.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                  {{ form.description.label }}
                </label>
                {{ form.description }}
              </div>

              <!-- Pricing -->
              <div class="md:col-span-2 mt-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">{% trans "Pricing" %}</h3>
              </div>

              <div>
                <label for="{{ form.subtotal.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                  {{ form.subtotal.label }}
                </label>
                {{ form.subtotal }}
              </div>

              <div>
                <label for="{{ form.tax_amount.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                  {{ form.tax_amount.label }}
                </label>
                {{ form.tax_amount }}
              </div>

              <div>
                <label for="{{ form.total_amount.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                  {{ form.total_amount.label }}
                </label>
                {{ form.total_amount }}
              </div>

              <div>
                <label for="{{ form.currency.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                  {{ form.currency.label }}
                </label>
                {{ form.currency }}
              </div>

              <!-- Terms and Validity -->
              <div class="md:col-span-2 mt-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">{% trans "Terms and Validity" %}</h3>
              </div>

              <div>
                <label for="{{ form.valid_until.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                  {{ form.valid_until.label }}
                </label>
                {{ form.valid_until }}
              </div>

              {% if object %}
              <div>
                <label for="{{ form.status.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                  {{ form.status.label }}
                </label>
                {{ form.status }}
              </div>
              {% endif %}

              <div class="md:col-span-2">
                <label for="{{ form.terms_conditions.id_for_label }}"
                  class="block text-sm font-medium text-gray-700 mb-2">
                  {{ form.terms_conditions.label }}
                </label>
                {{ form.terms_conditions }}
              </div>
            </div>

            <div class="mt-8 flex justify-end space-x-3">
              <a href="{% if object %}{% url 'quotes:quote-detail' object.pk %}{% else %}{% url 'quotes:quote-list' %}{% endif %}"
                class="btn btn-secondary">
                {% trans "Cancel" %}
              </a>
              <button type="submit" class="btn btn-primary">
                {% if object %}
                {% trans "Update Quote" %}
                {% else %}
                {% trans "Create Quote" %}
                {% endif %}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <style>
      /* Style form inputs */
      input[type="text"],
      input[type="email"],
      input[type="number"],
      input[type="date"],
      textarea,
      select {
        @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500;
      }

      textarea {
        @apply h-32 resize-vertical;
      }
    </style>

    <script>
      // Auto-calculate total when subtotal or tax changes
      document.addEventListener('DOMContentLoaded', function () {
        const subtotalField = document.getElementById('{{ form.subtotal.id_for_label }}');
        const taxField = document.getElementById('{{ form.tax_amount.id_for_label }}');
        const totalField = document.getElementById('{{ form.total_amount.id_for_label }}');

        function calculateTotal() {
          const subtotal = parseFloat(subtotalField.value) || 0;
          const tax = parseFloat(taxField.value) || 0;
          const total = subtotal + tax;
          totalField.value = total.toFixed(2);
        }

        if (subtotalField && taxField && totalField) {
          subtotalField.addEventListener('input', calculateTotal);
          taxField.addEventListener('input', calculateTotal);
        }
      });
    </script>
  </c-slot>
</c-layouts.dashboard>
