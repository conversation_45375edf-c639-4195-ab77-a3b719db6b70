from django.db import transaction
from django.utils import timezone

from bookings.models import Booking
from payments.models import BookingPayment
from tours.models import TourInstance


class BookingService:
    """Orchestrate booking workflow with state enforcement"""

    @staticmethod
    @transaction.atomic
    def create_pending_booking(
        tour_instance_id, client_id, num_of_participants, special_requests=''
    ):
        """
        Step 1: Create a booking that's PENDING payment

        :raises: CapacityError, ValidationError
        """
        tour_instance = TourInstance.objects.select_for_update().get(
            id=tour_instance_id
        )

        # CRITICAL: Real-time capacity check
        if tour_instance.available_spots < num_of_participants:
            raise CapacityError(
                f'Not enought spots! Only {tour_instance.available_spots} available.',
                available_spots=tour_instance.available_spots,
                required=num_of_participants,
            )
            # create draft booking
            booking = Booking.objects.create(
                tour_instance=tour_instance,
                client_id=client_id,
                number_of_participants=num_of_participants,
                special_request=special_requests,
                status=Booking.Status.PENDING,
                payment_status=Booking.PaymentStatus.UNPAID,
            )
            # Schedule auto-cancellation
            return booking

    @staticmethod
    @transaction.atomic
    def confirm_booking(booking_id, payment_method='', is_deposit=False):
        """
        Step 2: Process payment and confirm booking (Called via webhook or payment success callback)

        :raises: PaymentError, StateError
        """
        booking = Booking.objects.select_related('tour_instance').get(id=booking_id)

        if booking.status != Booking.Status.PENDING:
            raise StateError(
                'Booking must be PENDING to confirm', current_status=booking.status
            )

        # calculate tamount (handles deposits/total)
        amount = _calculate_booking_amount(booking, is_deposit)
        if amount <= 0:
            raise PaymentError('Amount must be positive')

        # process payment (external service)
        try:
            processor = PaymentProcessor(method=payment_method)
            transaction_id = processor.charge(
                amount=amount,
                client=booking.client,
                description=f'Booking {booking.pk}',
            )
        except PaymentProviderError as e:
            raise PaymentError(f'Payment failed: {str(e)}', provider_error=e.code)

        # update system state (atomic)
        BookingPayment.objects.create(
            booking=booking,
            amount=amount,
            transaction_id=transaction_id,
            is_deposit=is_deposit,
        )

        # update booking status
        booking.payment_status = (
            Booking.PaymentStatus.DEPOSIT_PAID
            if is_deposit
            else Booking.PaymentStatus.FULLY_PAID
        )
        booking.status = Booking.Status.CONFIRMED
        booking.save()
        # send booking confirmation
        return booking

    @staticmethod
    @transaction.atomic
    def cancel_booking(booking_id, reason, refund_strategy='auto'):
        """
        Step 3: Cancel with policy enforcement

        :raises: CancellationPolicyError
        """
        booking = Booking.objects.select_for_update().get(id=booking_id)

        # check cancellation policy
        cancellation_window = booking.tour_instance.date - timezone.now().date()

        if cancellation_window.days < 3:  # 72 hour policy
            raise CancellationPolicyError(
                'Must cancel at least 72 hours before tour',
                remaining_hours=cancellation_window.total_seconds() // 3600,
            )

        # process refund (if applicable)
        if booking.payment_status in [
            Booking.PaymentStatus.DEPOSIT_PAID,
            Booking.PaymentStatus.FULLY_PAID,
        ]:
            refund_service = RefundService(booking)
            refund_result = refund_service.process_refund(refund_strategy)

        # update state
        booking.status = Booking.Status.CANCELLED
        booking.cancellation_reason = reason

        booking.cancellation_date = timezone.now()
        booking.save()

        # todo: notify staff

        return booking


def _calculate_booking_amount(booking, is_deposit):
    """
    Handles dynamic pricing rules from TourTemplate.pricing rules
    """
    template = booking.tour_instance.tour_template
    rules = template.pricing_rules
    base_price = rules.get('base', 0)
    total = base_price * booking.num_of_participants

    # apply group discount
    if booking.num_of_participants >= rules.get('group_min', 999):
        total = rules['group_rate'] * booking.num_of_participants

    # apply other discounts like child discount

    return total


class BookingError(Exception):
    """Base exception for booking operations"""


class CapacityError(BookingError):
    def __init__(self, message, available_spots, required):
        super().__init__(message)
        self.available_spots = available_spots
        self.required = required


class StateError(BookingError):
    pass


class PaymentError(BookingError):
    pass


class CancellationPolicyError(BookingError):
    pass
