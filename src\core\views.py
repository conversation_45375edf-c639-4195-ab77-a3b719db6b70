"""
Core views for the application.
"""

import logging
from datetime import date, datetime

from django.conf import settings
from django.contrib.admin.views.decorators import staff_member_required
from django.http import JsonResponse
from django.shortcuts import render
from django.utils.decorators import method_decorator
from django.views import View
from django.views.generic import TemplateView

from bookings.models import Booking
from clients.models import Client
from core.logging_utils import AuditLogger
from invoices.models import Invoice
from quotes.models import Quote
from tours.models import TourEvent

logger = logging.getLogger('tour_business')


def landing_page(request):
    """Landing page view for non-authenticated users and marketing."""
    return render(request, 'landing.html')


def dashboard(request):
    """Dashboard view for authenticated users."""
    context = {}

    if (
        request.user.is_authenticated
        and hasattr(request.user, 'business')
        and request.user.profile.business
    ):
        business = request.user.profile.business

        # Calculate dashboard statistics
        context['stats'] = {
            'active_quotes': Quote.objects.for_business(business).active().count(),
            'pending_invoices': Invoice.objects.for_business(business)
            .filter(status__in=[Invoice.Status.SENT, Invoice.Status.OVERDUE])
            .count(),
            'total_clients': Client.objects.for_business(business).active().count(),
            'upcoming_tours': TourEvent.objects.filter(
                business=business,
                is_confirmed=True,
                is_cancelled=False,
                event__occurrence__start_time__date__gte=date.today(),
            ).count(),
        }

        # Add recent activity data
        context['recent_quotes'] = Quote.objects.for_business(business).order_by(
            '-created_at'
        )[:5]
        context['recent_bookings'] = (
            Booking.objects.filter(business=business)
            .select_related('client', 'tour_event__event')
            .order_by('-booking_date')[:5]
        )

        # Log dashboard access
        AuditLogger.log_data_access(
            user=request.user,
            resource='dashboard',
            action='view',
            count=sum(context['stats'].values()),
        )

    return render(request, 'home.html', context)


def home(request):
    """Home page view - shows landing page for non-authenticated users, dashboard for authenticated users."""
    if request.user.is_authenticated:
        # Check if this is a demo user
        if hasattr(request.user, 'is_demo_user') and request.user.is_demo_user:
            from django.shortcuts import redirect

            return redirect('demo_dashboard')
        else:
            return dashboard(request)
    else:
        return landing_page(request)


@method_decorator(staff_member_required, name='dispatch')
class LogViewerView(TemplateView):
    """View for displaying application logs to staff members."""

    template_name = 'admin/log_viewer.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        log_dir = settings.PROJECT_ROOT / 'logs'
        log_files = []

        if log_dir.exists():
            for log_file in log_dir.glob('*.log'):
                try:
                    stat = log_file.stat()
                    log_files.append(
                        {
                            'name': log_file.name,
                            'path': str(log_file),
                            'size': stat.st_size,
                            'modified': datetime.fromtimestamp(stat.st_mtime),
                        }
                    )
                except Exception:
                    continue

        context['log_files'] = sorted(
            log_files, key=lambda x: x['modified'], reverse=True
        )
        context['title'] = 'Log Viewer'

        return context


@method_decorator(staff_member_required, name='dispatch')
class LogContentView(View):
    """AJAX view to get log file content."""

    def get(self, request):
        log_file = request.GET.get('file')
        lines = int(request.GET.get('lines', 100))

        if not log_file:
            return JsonResponse({'error': 'No log file specified'}, status=400)

        log_path = settings.PROJECT_ROOT / 'logs' / log_file

        if not log_path.exists() or not log_path.is_file():
            return JsonResponse({'error': 'Log file not found'}, status=404)

        try:
            with open(log_path, 'r') as f:
                all_lines = f.readlines()
                # Get last N lines
                content_lines = (
                    all_lines[-lines:] if len(all_lines) > lines else all_lines
                )

            return JsonResponse(
                {
                    'content': ''.join(content_lines),
                    'total_lines': len(all_lines),
                    'showing_lines': len(content_lines),
                }
            )
        except Exception as e:
            return JsonResponse({'error': f'Error reading file: {str(e)}'}, status=500)
