# Tour Business Management SaaS

A comprehensive SaaS web application designed to organize and simplify daily tour management activities for tour businesses.

## Features

### Core Business Management
- **Multi-Tenant Business Management**: Business account creation with role-based access control
- **Client & Traveler Management**: Complete client profile and traveler information management
- **Quote & Booking System**: Trip requests, quote generation with multiple options, and booking workflows
- **Invoice & Payment Processing**: Automated invoicing with payment integration
- **Document Generation**: PDF generation for quotes, invoices, and receipts

### User Management & Authentication ✨ NEW
- **Email Confirmation Registration**: Secure registration with email verification
- **Multi-step Onboarding**: Guided setup for new business owners
- **Business User Management**: Add team members with role-based access
- **User Invitation System**: Invite users via email with secure token-based setup
- **Bulk User Upload**: Excel-based bulk user import functionality

### Technical Features ✨ NEW
- **Django Cotton Components**: Modern component-based templating
- **Comprehensive Testing**: Pytest with full integration test coverage
- **Management Commands**: Demo data and setup automation
- **Professional UI**: Enhanced Tailwind CSS design

## Tech Stack

- **Backend**: Django 5.2+
- **Frontend**: Django Templates + Tailwind CSS + DatastarJS
- **Database**: SQLite for both dev and production
- **Authentication**: Django Auth + django-guardian (RBAC)

## Quick Start

1. **Clone and Setup**:
   ```bash
   git clone <repository-url>
   cd tour
   ```

2. **Install Dependencies**:
   ```bash
   uv sync
   ```

3. **Environment Setup**:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Database Setup**:
   ```bash
   cd src
   uv run python manage.py migrate
   uv run python manage.py createsuperuser
   ```

5. **Run Development Server**:
   ```bash
   uv run python manage.py runserver
   ```

## 🎯 Demo Setup (NEW)

For a quick demo with sample data:

```bash
cd src
uv run python manage.py setup_demo
uv run python manage.py runserver
```

### Demo Access

**Admin Interface**: http://localhost:8000/admin/
- Username: `admin` | Password: `admin123`

**Business Owner Accounts**:
- Username: `demo_owner_1` | Password: `demo123` | Business: Demo Safari Adventures
- Username: `demo_owner_2` | Password: `demo123` | Business: Demo Mountain Expeditions
- Username: `demo_owner_3` | Password: `demo123` | Business: Demo Cultural Tours

### Demo Features
- Complete user registration and email confirmation flow
- Business user management with team member invitations
- Sample clients, quotes, and invoices
- Multi-step onboarding process
- Role-based access control demonstration

## Project Structure

```
tour/
├── src/                    # Django project source
│   ├── config/            # Django settings and configuration
│   ├── accounts/          # User authentication and management
│   ├── businesses/        # Business account management
│   ├── clients/           # Client and traveler management
│   ├── bookings/          # Booking management
│   ├── quotes/            # Quote generation and management
│   ├── invoices/          # Invoice generation
│   ├── payments/          # Payment processing
│   ├── documents/         # PDF document generation
│   ├── templates/         # Django templates
│   └── static/            # Static files (CSS, JS, images)
├── logs/                  # Application logs
├── .env                   # Environment variables
└── pyproject.toml         # Python dependencies
```

## 🧪 Testing (NEW)

Run the comprehensive test suite:

```bash
cd src

# Run all tests
pytest

# Run with coverage
pytest --cov=src

# Run specific test files
pytest accounts/test_registration.py
pytest tests/test_integration.py

# Run integration tests
pytest tests/test_integration.py::TestCompleteUserJourney
```

### Test Coverage
- User registration and email confirmation flow
- User invitation and acceptance process
- Multi-step onboarding workflow
- Business user management
- Profile management
- Complete integration tests for user journeys

## 🔧 Management Commands (NEW)

### Demo Data Management
```bash
# Setup complete demo environment
uv run python manage.py setup_demo

# Create demo data only
uv run python manage.py create_demo_data --businesses 3 --clients 10

# Create realistic demo data with factories
uv run python manage.py create_factory_demo_data --businesses 3 --clients-per-business 8

# Create demo superuser
uv run python manage.py create_demo_superuser

# Clear demo data
uv run python manage.py clear_demo_data --confirm
```

### Development Helpers
```bash
# Run development server
uv run python manage.py runserver

# Create and apply migrations
uv run python manage.py makemigrations
uv run python manage.py migrate

# Create superuser
uv run python manage.py createsuperuser
```

## 📚 Documentation (NEW)

- [Registration System Documentation](src/docs/REGISTRATION_SYSTEM.md)
- [Demo Setup Guide](src/docs/DEMO_SETUP.md)

## 🏗 Architecture Highlights (NEW)

- **Custom User Model**: Role-based access with email confirmation workflow
- **Multi-tenant Design**: Business-scoped data access and team management
- **Component-based UI**: Django Cotton for reusable, maintainable components
- **Comprehensive Testing**: Pytest with full integration test coverage
- **Organized Factories**: Factory classes organized by app for realistic test data
- **Demo-ready**: Complete sample data and setup automation for demonstrations

## Development

- **Create migrations**: `uv run python manage.py makemigrations`
- **Apply migrations**: `uv run python manage.py migrate`
- **Run tests**: `uv run python manage.py test`
- **Create superuser**: `uv run python manage.py createsuperuser`

## License

This project is proprietary software.
