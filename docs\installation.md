# Installation Guide

This guide will walk you through setting up the Tour Business Management System on your local machine or server.

## Prerequisites

### System Requirements

- **Python**: 3.11 or higher
- **Database**: PostgreSQL 13+ (recommended) or SQLite for development
- **Node.js**: 18+ (for frontend asset compilation)
- **Memory**: Minimum 2GB RAM
- **Storage**: At least 5GB free space

### Required Tools

- [uv](https://github.com/astral-sh/uv) - Python package manager
- Git
- PostgreSQL (if using PostgreSQL)

## Installation Steps

### 1. Clone the Repository

```bash
git clone <repository-url>
cd tour-business-management
```

### 2. Install Python Dependencies

Using uv (recommended):
```bash
uv sync
```

Or using pip:
```bash
pip install -r requirements.txt
```

### 3. Database Setup

#### PostgreSQL (Recommended for Production)

1. Install PostgreSQL:
```bash
# Ubuntu/Debian
sudo apt-get install postgresql postgresql-contrib

# macOS with Homebrew
brew install postgresql

# Windows
# Download from https://www.postgresql.org/download/windows/
```

2. Create database and user:
```sql
sudo -u postgres psql
CREATE DATABASE tour_business_db;
CREATE USER tour_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE tour_business_db TO tour_user;
\q
```

#### SQLite (Development Only)

SQLite is included with Python and requires no additional setup.

### 4. Environment Configuration

1. Copy the example environment file:
```bash
cp .env.example .env
```

2. Edit `.env` with your configuration:
```env
# Database Configuration
DATABASE_URL=postgresql://tour_user:your_password@localhost:5432/tour_business_db
# For SQLite: DATABASE_URL=sqlite:///db.sqlite3

# Django Settings
SECRET_KEY=your-secret-key-here
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# Email Configuration
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password

# Lemon Squeezy Configuration
LEMON_SQUEEZY_API_KEY=your-lemon-squeezy-api-key
LEMON_SQUEEZY_STORE_ID=your-store-id
LEMON_SQUEEZY_WEBHOOK_SECRET=your-webhook-secret

# Media and Static Files
MEDIA_ROOT=media/
STATIC_ROOT=staticfiles/

# Security (Production)
SECURE_SSL_REDIRECT=False
SECURE_HSTS_SECONDS=0
```

### 5. Database Migration

Run the database migrations:
```bash
uv run python src/manage.py migrate
```

### 6. Create Superuser

Create an admin user:
```bash
uv run python src/manage.py createsuperuser
```

### 7. Collect Static Files

For production deployment:
```bash
uv run python src/manage.py collectstatic
```

### 8. Load Initial Data (Optional)

Load sample data for development:
```bash
uv run python src/manage.py loaddata fixtures/sample_data.json
```

## Development Setup

### 1. Install Development Dependencies

```bash
uv sync --dev
```

### 2. Install Pre-commit Hooks

```bash
pre-commit install
```

### 3. Run Tests

```bash
pytest
```

### 4. Start Development Server

```bash
uv run python src/manage.py runserver
```

The application will be available at `http://localhost:8000`.

## Docker Setup (Alternative)

### 1. Using Docker Compose

```bash
# Build and start services
docker-compose up -d

# Run migrations
docker-compose exec web python manage.py migrate

# Create superuser
docker-compose exec web python manage.py createsuperuser
```

### 2. Docker Environment Variables

Create a `.env` file for Docker:
```env
POSTGRES_DB=tour_business_db
POSTGRES_USER=tour_user
POSTGRES_PASSWORD=your_password
DATABASE_URL=********************************************/tour_business_db
```

## Verification

### 1. Check Installation

Visit the following URLs to verify installation:

- **Admin Interface**: `http://localhost:8000/admin/`
- **Main Application**: `http://localhost:8000/`
- **API Documentation**: `http://localhost:8000/api/docs/`

### 2. Run System Checks

```bash
uv run python src/manage.py check
```

### 3. Test Email Configuration

```bash
uv run python src/manage.py shell
>>> from django.core.mail import send_mail
>>> send_mail('Test', 'Test message', '<EMAIL>', ['<EMAIL>'])
```

## Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Verify PostgreSQL is running
   - Check database credentials in `.env`
   - Ensure database exists

2. **Permission Denied Errors**
   - Check file permissions
   - Ensure proper ownership of project files

3. **Static Files Not Loading**
   - Run `collectstatic` command
   - Check `STATIC_URL` and `STATIC_ROOT` settings

4. **Email Not Sending**
   - Verify SMTP settings
   - Check firewall settings
   - Test with a different email provider

### Getting Help

- Check the [Troubleshooting Guide](troubleshooting.md)
- Review Django logs: `tail -f logs/django.log`
- Enable debug mode: Set `DEBUG=True` in `.env`

## Next Steps

After successful installation:

1. Read the [Configuration Guide](configuration.md)
2. Set up your first business in the admin interface
3. Configure email templates and notifications
4. Review the [API Documentation](api.md)
5. Customize the application using the [Customization Guide](customization.md)
