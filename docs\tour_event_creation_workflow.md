FUNCTION create_tour_event(agent):
// STEP 1: SELECT TOUR TEMPLATE
templates = Tour.objects.filter(is_active=True).order_by('name')
show_tour_selection_page(templates)

IF agent.selects_new_template():
validate_template_input(agent.input) // Name, base_price, max_capacity
tour = create_new_tour_template(agent.input)
ELSE:
tour = get_existing_tour(agent.selected_id)

// STEP 2: SET DATE & TIME (with timezone handling)
show_date_picker(tour.timezone) // Critical for global tours

date_data = agent.submits_date_form()
validate_date_time(date_data) // End time > start time, not in past

// STEP 3: CAPACITY CHECK (Real-time guardrails)
IF date_data.start_datetime in tour.dates.values_list('start_datetime', flat=True):
show_error("Date already exists for this tour")
RETURN to STEP 2

available_slots = calculate_available_slots(tour, date_data) // tour.max_capacity - current_bookings

IF available_slots < 1:
show_warning(f"No slots available for {date_data.start_datetime.date()}")
RETURN to STEP 2 // Let agent pick different date

// STEP 4: CONFIRMATION SCREEN (Prevents accidental dupes)
show_preview(
tour=tour,
date=date_data,
available_slots=available_slots,
warnings=check_for_risks(date_data) // e.g., holiday, low demand
)

IF agent.confirms():
// ATOMIC SAVE - critical for concurrency
try:
WITH transaction.atomic():
// Re-validate under lock (slots may have changed!)
if slots_filled_during_confirmation(tour, date_data):
raise ConcurrencyError("Slots no longer available")

        TourDate.create(
          tour=tour,
          start_datetime=date_data.start,
          end_datetime=date_data.end,
          current_bookings=0  // Always start at 0 for new dates
        )

        log_tour_creation(agent, tour)  // Audit trail
        send_confirmation_email(agent, tour)
        show_success("Tour event created!")
    EXCEPT ConcurrencyError:
      show_error("Sorry! Slots filled while you were confirming. Please try again.")
      RETURN to STEP 3

ELSE:
RETURN to STEP 2 // Agent changed mind

<!-- capacity guardrails -->

FUNCTION calculate_available_slots(tour, date):
existing_bookings = Booking.objects.filter(
tour_date**start_datetime=date.start
).aggregate(Sum('num_guests'))['num_guests**sum'] or 0

return max(0, tour.max_capacity - existing_bookings)
