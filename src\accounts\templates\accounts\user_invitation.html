{% load i18n %}

<c-layouts.base>
  <c-slot name="title">
    {% trans "Accept Invitation" %} - Tour Business user_management
  </c-slot>

  <c-slot name="content">
    <div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div class="sm:mx-auto sm:w-full sm:max-w-md">
        <div class="text-center">
          <div class="mx-auto h-16 w-16 flex items-center justify-center bg-blue-100 rounded-full">
            <svg class="h-8 w-8 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
          </div>
          <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
            {% trans "You're Invited!" %}
          </h2>
          <p class="mt-2 text-center text-sm text-gray-600">
            {% trans "Join" %} {{ business.name }} {% trans "on Tour Business Management" %}
          </p>
        </div>
      </div>

      <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <!-- Invitation Details -->
          <div class="bg-blue-50 border border-blue-200 rounded-md p-4 mb-6">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd"
                    d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                    clip-rule="evenodd"></path>
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-blue-800">
                  {% trans "Invitation Details" %}
                </h3>
                <div class="mt-2 text-sm text-blue-700">
                  <p><strong>{% trans "Business:" %}</strong> {{ business.name }}</p>
                  <p><strong>{% trans "Email:" %}</strong> {{ user.email }}</p>
                  <p><strong>{% trans "Role:" %}</strong> {{ user.get_role_display }}</p>
                </div>
              </div>
            </div>
          </div>

          <form method="post" class="space-y-6">
            {% csrf_token %}

            <div>
              <h3 class="text-lg font-medium text-gray-900 mb-4">{% trans "Set Your Password" %}</h3>
              <p class="text-sm text-gray-600 mb-4">
                {% trans "Create a secure password to complete your account setup." %}
              </p>
            </div>

            <div>
              <label for="password1" class="block text-sm font-medium text-gray-700">
                {% trans "Password" %}
              </label>
              <div class="mt-1">
                <input id="password1" name="password1" type="password" required
                  class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
              </div>
            </div>

            <div>
              <label for="password2" class="block text-sm font-medium text-gray-700">
                {% trans "Confirm Password" %}
              </label>
              <div class="mt-1">
                <input id="password2" name="password2" type="password" required
                  class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
              </div>
            </div>

            <div class="bg-gray-50 border border-gray-200 rounded-md p-4">
              <h4 class="text-sm font-medium text-gray-900 mb-2">{% trans "Password Requirements" %}</h4>
              <ul class="text-sm text-gray-600 space-y-1">
                <li class="flex items-center">
                  <svg class="w-4 h-4 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  {% trans "At least 8 characters long" %}
                </li>
                <li class="flex items-center">
                  <svg class="w-4 h-4 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  {% trans "Mix of letters, numbers, and symbols recommended" %}
                </li>
              </ul>
            </div>

            <div>
              <button type="submit"
                class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                  <svg class="h-5 w-5 text-blue-500 group-hover:text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd"
                      d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z"
                      clip-rule="evenodd" />
                  </svg>
                </span>
                {% trans "Set Password & Join Team" %}
              </button>
            </div>
          </form>

          <!-- What Happens Next -->
          <div class="mt-8 pt-6 border-t border-gray-200">
            <h4 class="text-sm font-medium text-gray-900 mb-3">{% trans "What happens next?" %}</h4>
            <div class="space-y-2">
              <div class="flex items-start">
                <div class="flex-shrink-0">
                  <div class="flex items-center justify-center w-6 h-6 bg-blue-100 rounded-full">
                    <span class="text-xs font-medium text-blue-600">1</span>
                  </div>
                </div>
                <div class="ml-3">
                  <p class="text-sm text-gray-600">{% trans "Your account will be activated" %}</p>
                </div>
              </div>

              <div class="flex items-start">
                <div class="flex-shrink-0">
                  <div class="flex items-center justify-center w-6 h-6 bg-blue-100 rounded-full">
                    <span class="text-xs font-medium text-blue-600">2</span>
                  </div>
                </div>
                <div class="ml-3">
                  <p class="text-sm text-gray-600">
                    {% trans "You'll be guided through profile setup" %}
                  </p>
                </div>
              </div>

              <div class="flex items-start">
                <div class="flex-shrink-0">
                  <div class="flex items-center justify-center w-6 h-6 bg-blue-100 rounded-full">
                    <span class="text-xs font-medium text-blue-600">3</span>
                  </div>
                </div>
                <div class="ml-3">
                  <p class="text-sm text-gray-600">
                    {% trans "You'll gain access to the business dashboard" %}</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Business Features -->
          <div class="mt-6 bg-gray-50 border border-gray-200 rounded-md p-4">
            <h4 class="text-sm font-medium text-gray-900 mb-3">{% trans "What you'll have access to:" %}
            </h4>
            <div class="grid grid-cols-2 gap-2">
              <div class="flex items-center text-sm text-gray-600">
                <svg class="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                {% trans "Tour Management" %}
              </div>
              <div class="flex items-center text-sm text-gray-600">
                <svg class="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                {% trans "Client Database" %}
              </div>
              <div class="flex items-center text-sm text-gray-600">
                <svg class="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                {% trans "Quote Creation" %}
              </div>
              <div class="flex items-center text-sm text-gray-600">
                <svg class="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                {% trans "Booking System" %}
              </div>
              <div class="flex items-center text-sm text-gray-600">
                <svg class="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                {% trans "Invoice Generation" %}
              </div>
              <div class="flex items-center text-sm text-gray-600">
                <svg class="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                {% trans "Reporting Tools" %}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </c-slot>

</c-layouts.base>
