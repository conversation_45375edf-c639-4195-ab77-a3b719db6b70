{% load i18n %}
{% load crispy_forms_tags %}

<c-layouts.dashboard>
  <c-slot name="title">
    {% if object %}
    {% trans "Edit Client" %} - {{ object.display_name }}
    {% else %}
    {% trans "Create Client" %}
    {% endif %}
  </c-slot>

  <c-slot name="main">
    <div class="max-w-4xl mx-auto">
      <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
          <h1 class="text-2xl font-bold text-gray-900">
            {% if object %}
            {% trans "Edit Client" %}
            {% else %}
            {% trans "Create Client" %}
            {% endif %}
          </h1>
        </div>

        {% crispy form %}

        <form method="post" class="px-6 py-6">
          {% csrf_token %}

          {% if form.errors %}
          <div class="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                    clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">
                  {% trans "There were errors with your submission" %}
                </h3>
                <div class="mt-2 text-sm text-red-700">
                  <ul class="list-disc pl-5 space-y-1">
                    {% for field, errors in form.errors.items %}
                    {% for error in errors %}
                    <li>{{ field|capfirst }}: {{ error }}</li>
                    {% endfor %}
                    {% endfor %}
                  </ul>
                </div>
              </div>
            </div>
          </div>
          {% endif %}

          <!-- Business Information -->
          <div class="mb-8">
            <h3 class="text-lg font-medium text-gray-900 mb-4">
              {% trans "Business Information" %}
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label for="{{ form.business.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                  {{ form.business.label }}
                  {% if form.business.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ form.business }}
                {% if form.business.help_text %}
                <p class="mt-1 text-sm text-gray-500">
                  {{ form.business.help_text }}
                </p>
                {% endif %}
                {% if form.business.errors %}
                <p class="mt-1 text-sm text-red-600">
                  {{ form.business.errors.0 }}
                </p>
                {% endif %}
              </div>

              {% if form.client_type %}
              <div>
                <label for="{{ form.client_type.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                  {{ form.client_type.label }}
                  {% if form.client_type.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ form.client_type }}
                {% if form.client_type.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ form.client_type.help_text }}</p>
                {% endif %}
                {% if form.client_type.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.client_type.errors.0 }}</p>
                {% endif %}
              </div>
              {% endif %}
            </div>
          </div>

          <!-- Personal Information -->
          <div class="mb-8">
            <h3 class="text-lg font-medium text-gray-900 mb-4">{% trans "Personal Information" %}</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label for="{{ form.first_name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                  {{ form.first_name.label }}
                  {% if form.first_name.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ form.first_name }}
                {% if form.first_name.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ form.first_name.help_text }}</p>
                {% endif %}
                {% if form.first_name.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.first_name.errors.0 }}</p>
                {% endif %}
              </div>

              <div>
                <label for="{{ form.last_name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                  {{ form.last_name.label }}
                  {% if form.last_name.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ form.last_name }}
                {% if form.last_name.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ form.last_name.help_text }}</p>
                {% endif %}
                {% if form.last_name.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.last_name.errors.0 }}</p>
                {% endif %}
              </div>
            </div>

            <!-- Company Name (for corporate clients) -->
            {% if form.company_name %}
            <div class="mt-6">
              <div>
                <label for="{{ form.company_name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                  {{ form.company_name.label }}
                  {% if form.company_name.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ form.company_name }}
                {% if form.company_name.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ form.company_name.help_text }}</p>
                {% endif %}
                {% if form.company_name.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.company_name.errors.0 }}</p>
                {% endif %}
              </div>
            </div>
            {% endif %}
          </div>

          <!-- Contact Information -->
          <div class="mb-8">
            <h3 class="text-lg font-medium text-gray-900 mb-4">{% trans "Contact Information" %}</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label for="{{ form.email.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                  {{ form.email.label }}
                  {% if form.email.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ form.email }}
                {% if form.email.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ form.email.help_text }}</p>
                {% endif %}
                {% if form.email.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.email.errors.0 }}</p>
                {% endif %}
              </div>

              <div>
                <label for="{{ form.phone.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                  {{ form.phone.label }}
                  {% if form.phone.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ form.phone }}
                {% if form.phone.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ form.phone.help_text }}</p>
                {% endif %}
                {% if form.phone.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.phone.errors.0 }}</p>
                {% endif %}
              </div>
            </div>
          </div>

          <!-- Address Information -->
          {% if form.address_line1 or form.address_line2 or form.city or form.state_province or form.postal_code or form.country %}
          <div class="mb-8">
            <h3 class="text-lg font-medium text-gray-900 mb-4">{% trans "Address Information" %}</h3>
            <div class="grid grid-cols-1 gap-6">
              {% if form.address_line1 %}
              <div>
                <label for="{{ form.address_line1.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                  {{ form.address_line1.label }}
                  {% if form.address_line1.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ form.address_line1 }}
                {% if form.address_line1.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ form.address_line1.help_text }}</p>
                {% endif %}
                {% if form.address_line1.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.address_line1.errors.0 }}</p>
                {% endif %}
              </div>
              {% endif %}

              {% if form.address_line2 %}
              <div>
                <label for="{{ form.address_line2.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                  {{ form.address_line2.label }}
                  {% if form.address_line2.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ form.address_line2 }}
                {% if form.address_line2.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ form.address_line2.help_text }}</p>
                {% endif %}
                {% if form.address_line2.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.address_line2.errors.0 }}</p>
                {% endif %}
              </div>
              {% endif %}

              <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                {% if form.city %}
                <div>
                  <label for="{{ form.city.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                    {{ form.city.label }}
                    {% if form.city.field.required %}<span class="text-red-500">*</span>{% endif %}
                  </label>
                  {{ form.city }}
                  {% if form.city.help_text %}
                  <p class="mt-1 text-sm text-gray-500">{{ form.city.help_text }}</p>
                  {% endif %}
                  {% if form.city.errors %}
                  <p class="mt-1 text-sm text-red-600">{{ form.city.errors.0 }}</p>
                  {% endif %}
                </div>
                {% endif %}

                {% if form.state_province %}
                <div>
                  <label for="{{ form.state_province.id_for_label }}"
                    class="block text-sm font-medium text-gray-700 mb-2">
                    {{ form.state_province.label }}
                    {% if form.state_province.field.required %}<span class="text-red-500">*</span>{% endif %}
                  </label>
                  {{ form.state_province }}
                  {% if form.state_province.help_text %}
                  <p class="mt-1 text-sm text-gray-500">{{ form.state_province.help_text }}</p>
                  {% endif %}
                  {% if form.state_province.errors %}
                  <p class="mt-1 text-sm text-red-600">{{ form.state_province.errors.0 }}</p>
                  {% endif %}
                </div>
                {% endif %}

                {% if form.postal_code %}
                <div>
                  <label for="{{ form.postal_code.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                    {{ form.postal_code.label }}
                    {% if form.postal_code.field.required %}<span class="text-red-500">*</span>{% endif %}
                  </label>
                  {{ form.postal_code }}
                  {% if form.postal_code.help_text %}
                  <p class="mt-1 text-sm text-gray-500">{{ form.postal_code.help_text }}</p>
                  {% endif %}
                  {% if form.postal_code.errors %}
                  <p class="mt-1 text-sm text-red-600">{{ form.postal_code.errors.0 }}</p>
                  {% endif %}
                </div>
                {% endif %}
              </div>

              {% if form.country %}
              <div>
                <label for="{{ form.country.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                  {{ form.country.label }}
                  {% if form.country.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ form.country }}
                {% if form.country.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ form.country.help_text }}</p>
                {% endif %}
                {% if form.country.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.country.errors.0 }}</p>
                {% endif %}
              </div>
              {% endif %}
            </div>
          </div>
          {% endif %}

          <!-- Travel Information -->
          {% if form.date_of_birth or form.nationality or form.passport_number %}
          <div class="mb-8">
            <h3 class="text-lg font-medium text-gray-900 mb-4">{% trans "Travel Information" %}</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
              {% if form.date_of_birth %}
              <div>
                <label for="{{ form.date_of_birth.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                  {{ form.date_of_birth.label }}
                  {% if form.date_of_birth.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ form.date_of_birth }}
                {% if form.date_of_birth.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ form.date_of_birth.help_text }}</p>
                {% endif %}
                {% if form.date_of_birth.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.date_of_birth.errors.0 }}</p>
                {% endif %}
              </div>
              {% endif %}

              {% if form.nationality %}
              <div>
                <label for="{{ form.nationality.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                  {{ form.nationality.label }}
                  {% if form.nationality.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ form.nationality }}
                {% if form.nationality.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ form.nationality.help_text }}</p>
                {% endif %}
                {% if form.nationality.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.nationality.errors.0 }}</p>
                {% endif %}
              </div>
              {% endif %}

              {% if form.passport_number %}
              <div>
                <label for="{{ form.passport_number.id_for_label }}"
                  class="block text-sm font-medium text-gray-700 mb-2">
                  {{ form.passport_number.label }}
                  {% if form.passport_number.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ form.passport_number }}
                {% if form.passport_number.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ form.passport_number.help_text }}</p>
                {% endif %}
                {% if form.passport_number.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.passport_number.errors.0 }}</p>
                {% endif %}
              </div>
              {% endif %}
            </div>
          </div>
          {% endif %}

          <!-- Emergency Contact Information -->
          {% if form.emergency_contact_name or form.emergency_contact_phone %}
          <div class="mb-8">
            <h3 class="text-lg font-medium text-gray-900 mb-4">{% trans "Emergency Contact" %}</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              {% if form.emergency_contact_name %}
              <div>
                <label for="{{ form.emergency_contact_name.id_for_label }}"
                  class="block text-sm font-medium text-gray-700 mb-2">
                  {{ form.emergency_contact_name.label }}
                  {% if form.emergency_contact_name.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ form.emergency_contact_name }}
                {% if form.emergency_contact_name.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ form.emergency_contact_name.help_text }}</p>
                {% endif %}
                {% if form.emergency_contact_name.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.emergency_contact_name.errors.0 }}</p>
                {% endif %}
              </div>
              {% endif %}

              {% if form.emergency_contact_phone %}
              <div>
                <label for="{{ form.emergency_contact_phone.id_for_label }}"
                  class="block text-sm font-medium text-gray-700 mb-2">
                  {{ form.emergency_contact_phone.label }}
                  {% if form.emergency_contact_phone.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ form.emergency_contact_phone }}
                {% if form.emergency_contact_phone.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ form.emergency_contact_phone.help_text }}</p>
                {% endif %}
                {% if form.emergency_contact_phone.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.emergency_contact_phone.errors.0 }}</p>
                {% endif %}
              </div>
              {% endif %}
            </div>
          </div>
          {% endif %}

          <!-- Special Requirements -->
          {% if form.dietary_restrictions or form.medical_conditions or form.travel_preferences %}
          <div class="mb-8">
            <h3 class="text-lg font-medium text-gray-900 mb-4">{% trans "Special Requirements" %}</h3>
            <div class="grid grid-cols-1 gap-6">
              {% if form.dietary_restrictions %}
              <div>
                <label for="{{ form.dietary_restrictions.id_for_label }}"
                  class="block text-sm font-medium text-gray-700 mb-2">
                  {{ form.dietary_restrictions.label }}
                  {% if form.dietary_restrictions.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ form.dietary_restrictions }}
                {% if form.dietary_restrictions.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ form.dietary_restrictions.help_text }}</p>
                {% endif %}
                {% if form.dietary_restrictions.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.dietary_restrictions.errors.0 }}</p>
                {% endif %}
              </div>
              {% endif %}

              {% if form.medical_conditions %}
              <div>
                <label for="{{ form.medical_conditions.id_for_label }}"
                  class="block text-sm font-medium text-gray-700 mb-2">
                  {{ form.medical_conditions.label }}
                  {% if form.medical_conditions.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ form.medical_conditions }}
                {% if form.medical_conditions.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ form.medical_conditions.help_text }}</p>
                {% endif %}
                {% if form.medical_conditions.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.medical_conditions.errors.0 }}</p>
                {% endif %}
              </div>
              {% endif %}

              {% if form.travel_preferences %}
              <div>
                <label for="{{ form.travel_preferences.id_for_label }}"
                  class="block text-sm font-medium text-gray-700 mb-2">
                  {{ form.travel_preferences.label }}
                  {% if form.travel_preferences.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ form.travel_preferences }}
                {% if form.travel_preferences.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ form.travel_preferences.help_text }}</p>
                {% endif %}
                {% if form.travel_preferences.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.travel_preferences.errors.0 }}</p>
                {% endif %}
              </div>
              {% endif %}
            </div>
          </div>
          {% endif %}

          <!-- Additional Information -->
          {% if form.notes or form.preferences %}
          <div class="mb-8">
            <h3 class="text-lg font-medium text-gray-900 mb-4">{% trans "Additional Information" %}</h3>
            <div class="grid grid-cols-1 gap-6">
              {% if form.notes %}
              <div>
                <label for="{{ form.notes.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                  {{ form.notes.label }}
                  {% if form.notes.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ form.notes }}
                {% if form.notes.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ form.notes.help_text }}</p>
                {% endif %}
                {% if form.notes.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.notes.errors.0 }}</p>
                {% endif %}
              </div>
              {% endif %}

              {% if form.preferences %}
              <div>
                <label for="{{ form.preferences.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                  {{ form.preferences.label }}
                  {% if form.preferences.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ form.preferences }}
                {% if form.preferences.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ form.preferences.help_text }}</p>
                {% endif %}
                {% if form.preferences.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.preferences.errors.0 }}</p>
                {% endif %}
              </div>
              {% endif %}
            </div>
          </div>
          {% endif %}

          <!-- Status (for update form only) -->
          {% if form.is_active %}
          <div class="mb-8">
            <h3 class="text-lg font-medium text-gray-900 mb-4">{% trans "Status" %}</h3>
            <div class="grid grid-cols-1 gap-6">
              <div class="flex items-center">
                {{ form.is_active }}
                <label for="{{ form.is_active.id_for_label }}" class="ml-2 text-sm font-medium text-gray-700">
                  {{ form.is_active.label }}
                </label>
                {% if form.is_active.help_text %}
                <p class="ml-2 text-sm text-gray-500">{{ form.is_active.help_text }}</p>
                {% endif %}
                {% if form.is_active.errors %}
                <p class="ml-2 text-sm text-red-600">{{ form.is_active.errors.0 }}</p>
                {% endif %}
              </div>
            </div>
          </div>
          {% endif %}

          <div class="mt-8 flex justify-end space-x-3">
            <a href="{% if object %}{% url 'clients:client-detail' object.pk %}{% else %}{% url 'clients:client-list' %}{% endif %}"
              class="btn btn-secondary">
              {% trans "Cancel" %}
            </a>
            <button type="submit" class="btn btn-primary">
              {% if object %}
              {% trans "Update Client" %}
              {% else %}
              {% trans "Create Client" %}
              {% endif %}
            </button>
          </div>
        </form>
      </div>
    </div>

    <style>
      /* Style form inputs */
      input[type="text"],
      input[type="email"],
      input[type="tel"],
      input[type="date"],
      textarea,
      select {
        @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500;
      }

      textarea {
        @apply h-24 resize-vertical;
      }

      /* Button styles */
      .btn {
        @apply px-4 py-2 rounded-md font-medium focus:outline-none focus:ring-2 focus:ring-offset-2;
      }

      .btn-primary {
        @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
      }

      .btn-secondary {
        @apply bg-gray-300 text-gray-700 hover:bg-gray-400 focus:ring-gray-500;
      }
    </style>
  </c-slot>
</c-layouts.dashboard>
