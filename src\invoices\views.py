from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.shortcuts import get_object_or_404, render
from django.utils import timezone
from django.views.decorators.http import require_http_methods

from .models import Invoice


@login_required
def invoice_detail(request, invoice_id):
    """Detail view for invoices."""
    invoice = get_object_or_404(Invoice, id=invoice_id)

    # Check if user has access to this invoice's business
    if not request.user.has_business_access(invoice.business):
        return render(request, '403.html', status=403)

    context = {
        'invoice': invoice,
        'is_overdue': invoice.is_overdue,
        'can_mark_paid': invoice.status
        in [Invoice.Status.SENT, Invoice.Status.OVERDUE],
    }

    return render(request, 'invoices/invoice_detail.html', context)


@login_required
@require_http_methods(['POST'])
def mark_invoice_paid(request, invoice_id):
    """Mark an invoice as paid."""
    try:
        invoice = get_object_or_404(Invoice, id=invoice_id)

        # Check if user has access to this invoice's business
        if not request.user.has_business_access(invoice.business):
            return JsonResponse(
                {
                    'success': False,
                    'error': 'You do not have permission to access this invoice.',
                },
                status=403,
            )

        # Check if invoice can be marked as paid
        if invoice.status not in [Invoice.Status.SENT, Invoice.Status.OVERDUE]:
            return JsonResponse(
                {
                    'success': False,
                    'error': 'Only sent or overdue invoices can be marked as paid.',
                },
                status=400,
            )

        # Mark as paid
        invoice.mark_as_paid()

        return JsonResponse(
            {
                'success': True,
                'message': 'Invoice marked as paid successfully!',
                'invoice_number': invoice.invoice_number,
                'status': invoice.get_status_display(),
                'paid_at': invoice.paid_at.isoformat() if invoice.paid_at else None,
            }
        )

    except Invoice.DoesNotExist:
        return JsonResponse(
            {'success': False, 'error': 'Invoice not found.'}, status=404
        )
    except Exception as e:
        return JsonResponse(
            {
                'success': False,
                'error': f'An error occurred while marking the invoice as paid: {str(e)}',
            },
            status=500,
        )


@login_required
@require_http_methods(['POST'])
def send_invoice(request, invoice_id):
    """Send an invoice to the client."""
    try:
        invoice = get_object_or_404(Invoice, id=invoice_id)

        # Check if user has access to this invoice's business
        if not request.user.has_business_access(invoice.business):
            return JsonResponse(
                {
                    'success': False,
                    'error': 'You do not have permission to access this invoice.',
                },
                status=403,
            )

        # Check if invoice can be sent
        if invoice.status != Invoice.Status.DRAFT:
            return JsonResponse(
                {'success': False, 'error': 'Only draft invoices can be sent.'},
                status=400,
            )

        # Update invoice status
        invoice.status = Invoice.Status.SENT
        invoice.sent_at = timezone.now()
        invoice.save()

        # TODO: Implement actual email sending logic here
        # send_invoice_email(invoice)

        return JsonResponse(
            {
                'success': True,
                'message': 'Invoice sent successfully!',
                'invoice_number': invoice.invoice_number,
                'status': invoice.get_status_display(),
                'sent_at': invoice.sent_at.isoformat() if invoice.sent_at else None,
            }
        )

    except Invoice.DoesNotExist:
        return JsonResponse(
            {'success': False, 'error': 'Invoice not found.'}, status=404
        )
    except Exception as e:
        return JsonResponse(
            {
                'success': False,
                'error': f'An error occurred while sending the invoice: {str(e)}',
            },
            status=500,
        )


# Additional imports for new views
from django.contrib import messages
from django.contrib.auth.mixins import LoginRequiredMixin
from django.db.models import Q
from django.urls import reverse_lazy
from django.views.generic import CreateView, DetailView, ListView, UpdateView

from businesses.models import Business
from clients.models import Client
from quotes.models import Quote


class InvoiceListView(LoginRequiredMixin, ListView):
    """List view for invoices."""

    model = Invoice
    template_name = 'invoices/invoice_list.html'
    context_object_name = 'invoices'
    paginate_by = 20

    def get_queryset(self):
        """Filter invoices based on user's business access."""
        queryset = Invoice.objects.all()

        # Filter by user's accessible businesses
        # if not self.request.user.is_superuser:
        #     accessible_businesses = Business.objects.filter(
        #         Q(created_by=self.request.user) | Q(users=self.request.user)
        #     ).distinct()
        #     queryset = queryset.filter(business__in=accessible_businesses)

        # Search functionality
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(invoice_number__icontains=search)
                | Q(client__first_name__icontains=search)
                | Q(client__last_name__icontains=search)
            )

        # Filter by status
        status = self.request.GET.get('status')
        if status:
            queryset = queryset.filter(status=status)

        # Filter by business
        business_id = self.request.GET.get('business')
        if business_id:
            queryset = queryset.filter(business_id=business_id)

        return queryset.order_by('-created_at')


class InvoiceCreateView(LoginRequiredMixin, CreateView):
    """Create view for invoices."""

    model = Invoice
    template_name = 'invoices/invoice_form.html'
    fields = [
        'business',
        'client',
        'quote',
        'subtotal',
        'tax_amount',
        'total_amount',
        'currency',
        'issue_date',
        'due_date',
        'payment_terms',
        'terms_conditions',
    ]
    success_url = reverse_lazy('invoices:list')

    def get_form(self, form_class=None):
        """Limit choices to user's accessible businesses and related objects."""
        form = super().get_form(form_class)

        if not self.request.user.is_superuser:
            accessible_businesses = Business.objects.filter(
                Q(created_by=self.request.user) | Q(users=self.request.user)
            ).distinct()
            form.fields['business'].queryset = accessible_businesses
            form.fields['client'].queryset = Client.objects.filter(
                business__in=accessible_businesses
            )
            form.fields['quote'].queryset = Quote.objects.filter(
                business__in=accessible_businesses, status=Quote.Status.APPROVED
            )

        return form

    def form_valid(self, form):
        """Handle successful form submission."""
        messages.success(
            self.request,
            f'Invoice "{form.instance.invoice_number}" created successfully!',
        )
        return super().form_valid(form)


class InvoiceUpdateView(LoginRequiredMixin, UpdateView):
    """Update view for invoices."""

    model = Invoice
    template_name = 'invoices/invoice_form.html'
    fields = [
        'business',
        'client',
        'quote',
        'subtotal',
        'tax_amount',
        'total_amount',
        'currency',
        'issue_date',
        'due_date',
        'payment_terms',
        'terms_conditions',
        'status',
    ]

    def get_object(self):
        """Get invoice and check permissions."""
        invoice = get_object_or_404(Invoice, pk=self.kwargs['pk'])

        # Check if user has access to this invoice's business
        if not self.request.user.is_superuser:
            if (
                invoice.business.created_by != self.request.user
                and self.request.user not in invoice.business.users.all()
            ):
                raise PermissionError("You don't have access to this invoice")

        return invoice

    def get_form(self, form_class=None):
        """Limit choices to user's accessible businesses and related objects."""
        form = super().get_form(form_class)

        if not self.request.user.is_superuser:
            accessible_businesses = Business.objects.filter(
                Q(created_by=self.request.user) | Q(users=self.request.user)
            ).distinct()
            form.fields['business'].queryset = accessible_businesses
            form.fields['client'].queryset = Client.objects.filter(
                business__in=accessible_businesses
            )
            form.fields['quote'].queryset = Quote.objects.filter(
                business__in=accessible_businesses
            )

        return form

    def form_valid(self, form):
        """Handle successful form submission."""
        messages.success(
            self.request,
            f'Invoice "{form.instance.invoice_number}" updated successfully!',
        )
        return super().form_valid(form)

    def get_success_url(self):
        """Return to invoice detail page."""
        return reverse_lazy('invoices:detail', kwargs={'invoice_id': self.object.pk})


class InvoiceDetailView(LoginRequiredMixin, DetailView):
    """Class-based detail view for invoices."""

    model = Invoice
    template_name = 'invoices/invoice_detail.html'
    context_object_name = 'invoice'

    def get_object(self):
        """Get invoice and check permissions."""
        invoice = get_object_or_404(Invoice, pk=self.kwargs['pk'])

        # Check if user has access to this invoice's business
        if not self.request.user.is_superuser:
            if (
                invoice.business.created_by != self.request.user
                and self.request.user not in invoice.business.users.all()
            ):
                raise PermissionError("You don't have access to this invoice")

        return invoice

    def get_context_data(self, **kwargs):
        """Add additional context."""
        context = super().get_context_data(**kwargs)
        invoice = self.get_object()

        context.update(
            {
                'is_overdue': invoice.is_overdue,
                'can_mark_paid': invoice.status
                in [Invoice.Status.SENT, Invoice.Status.OVERDUE],
                'payments': invoice.payments.all().order_by('-created_at'),
            }
        )

        return context
