"""
Management command to create demo data using factory_boy and faker.
This command creates realistic sample data for demonstration purposes.
"""

from django.contrib.auth import get_user_model
from django.core.management.base import BaseCommand

from accounts.factories import AgentFactory, ProfileFactory
from bookings.factories import (
    BookingFactory,
    BookingParticipantFactory,
    CompletedBookingFactory,
    ConfirmedBookingFactory,
    PendingBookingFactory,
    TourEventFactory,
)
from businesses.factories import (
    AdventureBusinessFactory,
    CulturalBusinessFactory,
    SafariBusinessFactory,
)
from clients.factories import ClientFactory, CorporateClientFactory, TravelerFactory
from invoices.factories import InvoiceFactory
from quotes.factories import (
    AdventureQuoteFactory,
    CulturalQuoteFactory,
    SafariQuoteFactory,
)

User = get_user_model()


class Command(BaseCommand):
    help = 'Create realistic demo data using factory_boy and faker'

    def add_arguments(self, parser):
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear existing demo data before creating new data',
        )
        parser.add_argument(
            '--businesses',
            type=int,
            default=3,
            help='Number of demo businesses to create (default: 3)',
        )
        parser.add_argument(
            '--clients-per-business',
            type=int,
            default=8,
            help='Number of clients per business (default: 8)',
        )
        parser.add_argument(
            '--agents-per-business',
            type=int,
            default=2,
            help='Number of agents per business (default: 2)',
        )
        parser.add_argument(
            '--quotes-per-business',
            type=int,
            default=5,
            help='Number of quotes per business (default: 5)',
        )
        parser.add_argument(
            '--events-per-business',
            type=int,
            default=5,
            help='Number of tour events per business (default: 5)',
        )
        parser.add_argument(
            '--bookings-per-business',
            type=int,
            default=8,
            help='Number of bookings per business (default: 8)',
        )

    def handle(self, *args, **options):
        if options['clear']:
            self.stdout.write('Clearing existing demo data...')
            self.clear_demo_data()

        self.stdout.write('Creating realistic demo data with factories...')

        businesses = self.create_businesses(options['businesses'])

        for business in businesses:
            self.create_team_members(business, options['agents_per_business'])
            self.create_clients(business, options['clients_per_business'])
            self.create_quotes_and_invoices(business, options['quotes_per_business'])
            self.create_events(business, options['events_per_business'])
            self.create_bookings(business, options['bookings_per_business'])

        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully created realistic demo data:\n'
                f'- {len(businesses)} businesses with realistic profiles\n'
                f'- {options["agents_per_business"] * len(businesses)} team members\n'
                f'- {options["clients_per_business"] * len(businesses)} clients with travelers\n'
                f'- {options["quotes_per_business"] * len(businesses)} quotes and invoices\n'
                f'- {options["events_per_business"] * len(businesses)} tour events\n'
                f'- {options["bookings_per_business"] * len(businesses)} bookings with participants\n'
                f'- All data generated with realistic faker data'
            )
        )

    def clear_demo_data(self):
        """Clear existing demo data."""
        # Delete demo users (those with demo_ prefix or created by factories)
        demo_users = User.objects.filter(username__startswith='demo_')
        factory_users = User.objects.filter(username__startswith='user')

        self.stdout.write(f'Deleting {demo_users.count()} demo users...')
        demo_users.delete()

        self.stdout.write(f'Deleting {factory_users.count()} factory users...')
        factory_users.delete()

    def create_businesses(self, count):
        """Create demo businesses using factories."""
        businesses = []

        # Create different types of businesses
        business_factories = [
            SafariBusinessFactory,
            AdventureBusinessFactory,
            CulturalBusinessFactory,
        ]

        for i in range(count):
            factory_class = business_factories[i % len(business_factories)]

            # Create business with owner
            business = factory_class()

            # Create profile for the owner
            ProfileFactory(user=business.created_by)

            businesses.append(business)

            self.stdout.write(
                f'Created business: {business.name} (Owner: {business.created_by.username})'
            )

        return businesses

    def create_team_members(self, business, count):
        """Create team members for a business."""
        for i in range(count):
            # Create agent user
            agent = AgentFactory(business=business)

            # Create profile for agent
            ProfileFactory(user=agent)

            self.stdout.write(f'  Added agent: {agent.username} to {business.name}')

    def create_clients(self, business, count):
        """Create clients and travelers for a business."""
        individual_count = int(count * 0.8)  # 80% individual clients
        corporate_count = count - individual_count  # 20% corporate clients

        # Create individual clients
        for i in range(individual_count):
            client = ClientFactory(business=business)

            # Create 1-4 travelers per client
            traveler_count = min(4, max(1, i % 4 + 1))
            for j in range(traveler_count):
                TravelerFactory(client=client)

            self.stdout.write(
                f'  Created client: {client.full_name} with {traveler_count} travelers'
            )

        # Create corporate clients
        for i in range(corporate_count):
            client = CorporateClientFactory(business=business)

            # Corporate clients typically have more travelers
            traveler_count = min(8, max(2, (i + 1) * 2))
            for j in range(traveler_count):
                TravelerFactory(client=client)

            self.stdout.write(
                f'  Created corporate client: {client.company_name} with {traveler_count} travelers'
            )

    def create_quotes_and_invoices(self, business, count):
        """Create quotes and invoices for a business."""
        clients = list(business.clients.all())

        if not clients:
            return

        # Determine quote factory based on business type
        if 'Safari' in business.name or 'Wildlife' in business.name:
            quote_factory = SafariQuoteFactory
        elif 'Mountain' in business.name or 'Adventure' in business.name:
            quote_factory = AdventureQuoteFactory
        else:
            quote_factory = CulturalQuoteFactory

        for i in range(count):
            client = clients[i % len(clients)]

            # Create quote
            quote = quote_factory(business=business, client=client)

            # Create invoice for some approved quotes (30% chance)
            if quote.status == quote.Status.APPROVED and i % 3 == 0:
                invoice = InvoiceFactory(business=business, client=client, quote=quote)
                self.stdout.write(
                    f'  Created quote and invoice: {quote.title} (${quote.total_amount})'
                )
            else:
                self.stdout.write(
                    f'  Created quote: {quote.title} (${quote.total_amount}) - {quote.status}'
                )

    def create_superuser_if_needed(self):
        """Create a superuser if none exists."""
        if not User.objects.filter(is_superuser=True).exists():
            admin_user = User.objects.create_superuser(
                username='admin', email='<EMAIL>', password='admin123'
            )
            admin_user.role = Profile.Role.ADMIN
            admin_user.save()

            self.stdout.write(
                self.style.SUCCESS('Created admin superuser (admin/admin123)')
            )
        else:
            self.stdout.write('Admin superuser already exists')

    def create_events(self, business, count):
        """Create tour events for a business."""
        self.stdout.write(f'Creating {count} tour events for {business.name}...')

        # Get clients for this business to assign to events
        clients = list(business.clients.all())

        for i in range(count):
            # Choose appropriate factory based on business type
            if hasattr(business, 'safari_business'):
                event_type = TourEventFactory._meta.model.EventType.TOUR
            elif hasattr(business, 'cultural_business'):
                event_type = TourEventFactory._meta.model.EventType.CONSULTATION
            else:
                event_type = TourEventFactory._meta.model.EventType.TOUR

            # Create event with or without client
            if clients and i % 3 != 0:  # 2/3 of events have clients
                client = self.fake.random_element(clients)
                event = TourEventFactory(
                    business=business, client=client, event_type=event_type
                )
            else:
                event = TourEventFactory(
                    business=business, client=None, event_type=event_type
                )

            self.stdout.write(f'  Created event: {event.event.title}')

    def create_bookings(self, business, count):
        """Create bookings for a business."""
        self.stdout.write(f'Creating {count} bookings for {business.name}...')

        # Get clients and events for this business
        clients = list(business.clients.all())
        events = list(business.tour_events.all())

        if not clients:
            self.stdout.write('  No clients found, skipping booking creation')
            return

        if not events:
            self.stdout.write('  No events found, skipping booking creation')
            return

        booking_count = 0
        for i in range(count):
            client = self.fake.random_element(clients)
            event = self.fake.random_element(events)

            # Check if event has capacity
            if event.available_spots <= 0:
                continue

            # Create different types of bookings
            if i % 4 == 0:
                booking = PendingBookingFactory(
                    business=business, client=client, tour_event=event
                )
            elif i % 4 == 1:
                booking = ConfirmedBookingFactory(
                    business=business, client=client, tour_event=event
                )
            elif i % 4 == 2:
                booking = CompletedBookingFactory(
                    business=business, client=client, tour_event=event
                )
            else:
                booking = BookingFactory(
                    business=business, client=client, tour_event=event
                )

            # Update event participant count
            event.current_participants += booking.number_of_participants
            event.save()

            # Create participants for the booking
            self.create_booking_participants(booking)

            booking_count += 1
            self.stdout.write(f'  Created booking: {booking.booking_reference}')

        self.stdout.write(f'  Created {booking_count} bookings total')

    def create_booking_participants(self, booking):
        """Create participants for a booking."""
        # Get travelers for this client
        travelers = list(booking.client.travelers.all())

        if not travelers:
            return

        # Create participants up to the booking's participant count
        participants_needed = min(booking.number_of_participants, len(travelers))
        selected_travelers = self.fake.random_elements(
            travelers, length=participants_needed, unique=True
        )

        for i, traveler in enumerate(selected_travelers):
            participant = BookingParticipantFactory(
                booking=booking,
                traveler=traveler,
                is_primary_contact=(i == 0),  # First participant is primary contact
            )

            # For completed bookings, check in participants
            if booking.status == booking.Status.COMPLETED:
                participant.check_in()
