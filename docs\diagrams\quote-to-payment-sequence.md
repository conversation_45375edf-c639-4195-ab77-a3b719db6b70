# Tour Management SaaS - Quote to Payment Sequence Diagram

This sequence diagram shows the detailed interaction flow from quote creation through payment completion, including all system components and external services.

## Diagram

```mermaid
sequenceDiagram
    participant C as Client
    participant A as Agent/Business Owner
    participant S as System
    participant P as Payment Gateway
    participant E as Email Service

    %% Quote Creation Phase
    Note over A,S: Quote Creation Phase
    A->>S: Create new quote
    A->>S: Add quote items/services
    A->>S: Set pricing and terms
    S->>S: Generate quote number
    S->>S: Generate public hash
    A->>S: Send quote to client
    S->>E: Send quote email with public link
    E->>C: Email with quote link

    %% Quote Review Phase
    Note over C,S: Quote Review Phase
    C->>S: Access public quote via hash
    S->>S: Validate quote (not expired)
    S->>C: Display quote details

    alt Quote Approved
        C->>S: Approve quote
        S->>S: Update quote status to "approved"
        S->>A: Notify quote approval

        %% Booking Creation
        Note over A,S: Booking Creation Phase
        A->>S: Create booking from quote
        S->>S: Generate booking reference
        A->>S: Schedule tour event
        A->>S: Add booking participants
        S->>S: Update booking status to "confirmed"

        %% Invoice Generation
        Note over A,S: Invoice Generation Phase
        S->>S: Auto-generate invoice from quote
        S->>S: Generate invoice number
        A->>S: Send invoice to client
        S->>P: Create payment link
        P->>S: Return payment URL
        S->>E: Send invoice email with payment link
        E->>C: Email with invoice and payment link

        %% Payment Processing
        Note over C,P: Payment Processing Phase
        C->>P: Access payment page
        C->>P: Submit payment details
        P->>P: Process payment

        alt Payment Successful
            P->>S: Send payment webhook (success)
            S->>S: Mark payment as completed
            S->>S: Update invoice status to "paid"
            S->>S: Generate receipt document
            S->>E: Send payment confirmation
            E->>C: Payment confirmation email
            E->>A: Payment received notification

            %% Tour Execution
            Note over A,S: Tour Execution Phase
            A->>S: Check-in participants on tour day
            S->>S: Update participant check-in status
            A->>S: Mark tour as completed
            S->>S: Update booking status to "completed"
            S->>E: Send completion confirmation
            E->>C: Tour completion email

        else Payment Failed
            P->>S: Send payment webhook (failed)
            S->>S: Mark payment as failed
            S->>A: Notify payment failure
            S->>E: Send payment failure notification
            E->>C: Payment failed email
        end

    else Quote Rejected
        C->>S: Reject quote
        S->>S: Update quote status to "rejected"
        S->>A: Notify quote rejection

    else Quote Expired
        S->>S: Auto-expire quote after validity date
        S->>S: Update quote status to "expired"
        S->>A: Notify quote expiration
    end

    %% Cancellation Flow (Optional)
    Note over C,A: Cancellation Flow (if applicable)
    C->>A: Request cancellation
    A->>S: Process cancellation request
    S->>S: Apply cancellation policy
    alt Refund Applicable
        S->>P: Process refund
        P->>S: Confirm refund
        S->>S: Update booking status to "cancelled"
        S->>E: Send cancellation confirmation
        E->>C: Cancellation and refund email
    else No Refund
        S->>S: Update booking status to "cancelled"
        S->>E: Send cancellation confirmation
        E->>C: Cancellation email (no refund)
    end
```

## Sequence Flow Breakdown

### Phase 1: Quote Creation

1. **Agent Actions**: Create quote, add services, set pricing
2. **System Processing**: Generate quote number and public hash
3. **Communication**: Send quote email with secure public link
4. **Client Notification**: Receive quote via email

### Phase 2: Quote Review

1. **Client Access**: Use public hash to view quote
2. **System Validation**: Check quote validity and expiration
3. **Quote Display**: Present quote details to client
4. **Decision Point**: Client approves, rejects, or lets expire

### Phase 3: Booking Creation (If Approved)

1. **Booking Generation**: Create booking from approved quote
2. **Reference Creation**: Generate unique booking reference
3. **Event Scheduling**: Set tour dates and times
4. **Participant Management**: Add travelers to booking
5. **Status Update**: Mark booking as confirmed

### Phase 4: Invoice Generation

1. **Auto-Generation**: Create invoice from quote data
2. **Invoice Numbering**: Generate unique invoice number
3. **Payment Integration**: Create payment link via gateway
4. **Client Communication**: Send invoice with payment link

### Phase 5: Payment Processing

1. **Payment Access**: Client accesses payment page
2. **Payment Submission**: Client submits payment details
3. **Gateway Processing**: Payment gateway processes transaction
4. **Webhook Response**: Gateway sends success/failure webhook

### Phase 6: Payment Success Flow

1. **Status Updates**: Mark payment and invoice as completed
2. **Document Generation**: Create receipt documents
3. **Notifications**: Send confirmations to client and agent
4. **Tour Preparation**: System ready for tour execution

### Phase 7: Tour Execution

1. **Check-in Process**: Agent checks in participants
2. **Status Tracking**: Update participant check-in status
3. **Tour Completion**: Mark tour as completed
4. **Final Communication**: Send completion confirmation

## Alternative Flows

### Quote Rejection

- Client rejects quote
- System updates status
- Agent receives rejection notification
- Quote archived

### Quote Expiration

- System automatically expires quotes
- Status updated to "expired"
- Agent notified of expiration
- Quote archived

### Payment Failure

- Payment gateway reports failure
- System marks payment as failed
- Notifications sent to client and agent
- Payment retry options available

### Cancellation Process

- Client requests cancellation
- Agent processes request
- Cancellation policy applied
- Refund processed if applicable
- Status updated and notifications sent

## Key Integration Points

### Payment Gateway (Lemon Squeezy)

- **Payment Link Creation**: Generate secure payment URLs
- **Webhook Processing**: Handle payment status updates
- **Refund Processing**: Handle cancellation refunds
- **Transaction Tracking**: Monitor payment status

### Email Service

- **Quote Notifications**: Send quote links to clients
- **Invoice Delivery**: Send invoices with payment links
- **Status Updates**: Payment confirmations and failures
- **Tour Communications**: Completion and follow-up emails

### System Components

- **Quote Management**: Handle quote lifecycle
- **Booking System**: Manage reservations and scheduling
- **Invoice Generation**: Create billing documents
- **Payment Tracking**: Monitor financial transactions
- **Document Generation**: Create PDFs and receipts

## Security Considerations

### Public Quote Access

- Secure hash-based URLs
- Expiration date validation
- No authentication required for viewing
- Approval requires validation

### Payment Security

- External payment gateway integration
- No sensitive payment data stored
- Webhook signature verification
- PCI compliance through gateway

### Data Protection

- Business-level data isolation
- Audit logging for all actions
- Secure communication channels
- Role-based access control
