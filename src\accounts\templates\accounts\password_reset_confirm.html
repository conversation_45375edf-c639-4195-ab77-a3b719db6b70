{% load i18n %}

<c-layouts.base>
  <c-slot name="title">
    {% trans "Set New Password" %}
  </c-slot>

  <c-slot name="content">
    <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div class="max-w-md w-full space-y-8">
        <div>
          <div class="mx-auto h-12 w-auto flex justify-center">
            <svg class="h-12 w-12 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
            </svg>
          </div>
          <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
            {% trans "Set your new password" %}
          </h2>
          <p class="mt-2 text-center text-sm text-gray-600">
            {% trans "Please enter your new password twice so we can verify you typed it in correctly." %}
          </p>
        </div>

        {% if validlink %}
        <form class="mt-8 space-y-6" method="post">
          {% csrf_token %}

          {% if form.errors %}
          <div class="bg-red-50 border border-red-200 rounded-md p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                    clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">
                  {% trans "There were errors with your password" %}
                </h3>
                <div class="mt-2 text-sm text-red-700">
                  <ul class="list-disc pl-5 space-y-1">
                    {% for field, errors in form.errors.items %}
                    {% for error in errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                    {% endfor %}
                  </ul>
                </div>
              </div>
            </div>
          </div>
          {% endif %}

          <div class="space-y-4">
            <div>
              <label for="{{ form.new_password1.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                {% trans "New Password" %}
              </label>
              <input id="{{ form.new_password1.id_for_label }}" name="{{ form.new_password1.name }}" type="password"
                required
                class="appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                placeholder="{% trans 'Enter your new password' %}">
            </div>

            <div>
              <label for="{{ form.new_password2.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                {% trans "Confirm New Password" %}
              </label>
              <input id="{{ form.new_password2.id_for_label }}" name="{{ form.new_password2.name }}" type="password"
                required
                class="appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                placeholder="{% trans 'Confirm your new password' %}">
            </div>
          </div>

          <div class="text-sm text-gray-600">
            <ul class="list-disc pl-5 space-y-1">
              <li>{% trans "Your password must contain at least 8 characters." %}</li>
              <li>{% trans "Your password can't be too similar to your other personal information." %}</li>
              <li>{% trans "Your password can't be a commonly used password." %}</li>
              <li>{% trans "Your password can't be entirely numeric." %}</li>
            </ul>
          </div>

          <div>
            <button type="submit"
              class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
              <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                <svg class="h-5 w-5 text-blue-500 group-hover:text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd"
                    d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z"
                    clip-rule="evenodd" />
                </svg>
              </span>
              {% trans "Change Password" %}
            </button>
          </div>
        </form>
        {% else %}
        <div class="bg-red-50 border border-red-200 rounded-md p-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                  clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-red-800">
                {% trans "Password reset link invalid" %}
              </h3>
              <div class="mt-2 text-sm text-red-700">
                <p>
                  {% trans "The password reset link was invalid, possibly because it has already been used. Please request a new password reset." %}
                </p>
              </div>
            </div>
          </div>
        </div>

        <div class="text-center">
          <a href="{% url 'accounts:password_reset' %}"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
            {% trans "Request New Password Reset" %}
          </a>
        </div>
        {% endif %}
      </div>
    </div>
  </c-slot name="content">
</c-layouts.base>
