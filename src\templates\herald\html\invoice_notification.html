{% extends "herald/html/base_email.html" %}

{% block title %}Invoice {{ action|title }} - {{ invoice.invoice_number }}{% endblock %}

{% block header %}
    <h2>Invoice {{ action|title }}</h2>
{% endblock %}

{% block content %}
    <p>Dear {{ client.display_name }},</p>

    {% if action == 'created' %}
        <p>An invoice has been generated for your recent booking.</p>
    {% elif action == 'sent' %}
        <p>Your invoice is ready and payment is now due.</p>
    {% elif action == 'paid' %}
        <div class="success-box">
            <p><strong>Thank you!</strong> Your payment has been received and processed successfully.</p>
        </div>
    {% elif action == 'overdue' %}
        <div class="warning-box">
            <p><strong>Payment Reminder:</strong> This invoice is now overdue. Please arrange payment as soon as possible.</p>
        </div>
    {% endif %}

    <div class="info-box">
        <h3>Invoice Details</h3>
        <table class="details-table">
            <tr>
                <th>Invoice Number:</th>
                <td>{{ invoice.invoice_number }}</td>
            </tr>
            <tr>
                <th>Issue Date:</th>
                <td>{{ invoice.issue_date|date:"F d, Y" }}</td>
            </tr>
            <tr>
                <th>Due Date:</th>
                <td>{{ invoice.due_date|date:"F d, Y" }}</td>
            </tr>
            <tr>
                <th>Subtotal:</th>
                <td>{{ invoice.subtotal }}</td>
            </tr>
            {% if invoice.tax_amount %}
            <tr>
                <th>Tax:</th>
                <td>{{ invoice.tax_amount }}</td>
            </tr>
            {% endif %}
            <tr>
                <th><strong>Total Amount:</strong></th>
                <td><strong>{{ invoice.total_amount }}</strong></td>
            </tr>
            <tr>
                <th>Status:</th>
                <td>{{ invoice.get_status_display }}</td>
            </tr>
        </table>
    </div>

    {% if invoice.quote %}
        <p><strong>Related Quote:</strong> {{ invoice.quote.quote_number }}</p>
    {% endif %}

    {% if action == 'sent' or action == 'overdue' %}
        {% if invoice.payment_link %}
            <p>You can pay this invoice securely online by clicking the button below:</p>
            <p style="text-align: center;">
                <a href="{{ request.build_absolute_uri }}{{ invoice.payment_link.get_payment_url }}" class="button">
                    Pay Invoice Online
                </a>
            </p>
        {% endif %}

        {% if invoice.payment_terms %}
            <h3>Payment Terms</h3>
            <p>{{ invoice.payment_terms|linebreaks }}</p>
        {% endif %}

        {% if invoice.terms_conditions %}
            <h3>Terms & Conditions</h3>
            <p>{{ invoice.terms_conditions|linebreaks }}</p>
        {% endif %}
    {% endif %}

    {% if action == 'paid' %}
        <p>Your booking is now confirmed and we look forward to providing you with an excellent tour experience.</p>
    {% elif action == 'overdue' %}
        <p>To avoid any service interruptions, please arrange payment immediately. If you have already made payment, please disregard this notice.</p>
    {% else %}
        <p>If you have any questions about this invoice, please don't hesitate to contact us.</p>
    {% endif %}
{% endblock %}
