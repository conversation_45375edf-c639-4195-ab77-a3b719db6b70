{% extends "admin/base_site.html" %}
{% load static %}

{% block title %}Data Generator - Django Admin{% endblock %}

{% block content %}
<div class="module">
    <h1>Data Generator</h1>
    <p>Generate realistic dummy data for testing and development using model_bakery.</p>
    
    <!-- Current Data Stats -->
    <div class="module">
        <h2>Current Data</h2>
        <table>
            <thead>
                <tr>
                    <th>Data Type</th>
                    <th>Count</th>
                </tr>
            </thead>
            <tbody>
                {% for key, count in current_counts.items %}
                    <tr>
                        <td>{{ key|capfirst }}</td>
                        <td>{{ count }}</td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    
    <!-- Generation Options -->
    <div class="module">
        <h2>Generate Data</h2>
        
        <form method="post" style="margin-bottom: 20px;">
            {% csrf_token %}
            <input type="hidden" name="action" value="generate_complete">
            
            <fieldset class="module aligned">
                <div class="form-row">
                    <div>
                        <label for="id_scale">Dataset Scale:</label>
                        <select name="scale" id="id_scale">
                            {% for value, label in scale_options %}
                                <option value="{{ value }}">{{ label }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                
                <div class="form-row">
                    <div>
                        <label for="id_clear_first">
                            <input type="checkbox" name="clear_first" id="id_clear_first">
                            Clear existing data first
                        </label>
                    </div>
                </div>
            </fieldset>
            
            <div class="submit-row">
                <input type="submit" value="Generate Complete Dataset" class="default">
            </div>
        </form>
        
        <!-- Clear All Data -->
        <form method="post" onsubmit="return confirm('This will delete ALL data except superusers. Are you sure?');">
            {% csrf_token %}
            <input type="hidden" name="action" value="clear_all">
            <div class="submit-row">
                <input type="submit" value="Clear All Data" class="deletelink">
            </div>
        </form>
    </div>
    
    <!-- CLI Instructions -->
    <div class="module">
        <h2>Command Line Usage</h2>
        <p>You can also generate data using the management command:</p>
        <pre style="background: #f8f8f8; padding: 10px; border: 1px solid #ddd;">
# Generate medium dataset
python manage.py generate_dummy_data --scale medium

# Generate specific data types
python manage.py generate_dummy_data --users 5 --businesses 2 --clients 10

# Clear data and generate new
python manage.py generate_dummy_data --clear --scale large

# Generate specific counts
python manage.py generate_dummy_data --events 20 --bookings 30
        </pre>
    </div>
</div>
{% endblock %}
