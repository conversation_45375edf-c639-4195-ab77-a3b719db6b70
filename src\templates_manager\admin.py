"""
Admin interface for template management.
"""

from django.contrib import admin
from django.urls import reverse
from django.utils.html import format_html
from simple_history.admin import SimpleHistoryAdmin

from .models import PDFTemplate


@admin.register(PDFTemplate)
class PDFTemplateAdmin(SimpleHistoryAdmin):
    """Admin interface for PDF templates."""

    list_display = [
        'name',
        'business_link',
        'template_type',
        'is_active',
        'is_default',
        'preview_thumbnail',
        'created_at',
    ]
    list_filter = [
        'template_type',
        'is_active',
        'is_default',
        'page_size',
        'orientation',
        'business',
        'created_at',
    ]
    search_fields = ['name', 'description', 'business__name']
    readonly_fields = ['created_at', 'updated_at', 'preview_thumbnail']

    fieldsets = (
        (
            'Basic Information',
            {'fields': ('name', 'business', 'template_type', 'description')},
        ),
        (
            'Template Settings',
            {'fields': ('is_active', 'is_default', 'page_size', 'orientation')},
        ),
        (
            'Template Content',
            {
                'fields': ('html_content', 'css_content'),
                'classes': ('wide',),
                'description': 'Use Django template syntax. Available context variables depend on template type.',
            },
        ),
        (
            'Preview',
            {
                'fields': ('preview_image', 'preview_thumbnail'),
                'classes': ('collapse',),
            },
        ),
        (
            'Audit Information',
            {
                'fields': ('created_by', 'created_at', 'updated_at'),
                'classes': ('collapse',),
            },
        ),
    )

    def business_link(self, obj):
        """Display clickable link to business."""
        if obj.business:
            url = reverse('admin:businesses_business_change', args=[obj.business.pk])
            return format_html('<a href="{}">{}</a>', url, obj.business.name)
        return format_html('<em>System Template</em>')

    business_link.short_description = 'Business'

    def preview_thumbnail(self, obj):
        """Display preview image thumbnail."""
        if obj.preview_image:
            return format_html(
                '<img src="{}" style="max-height: 100px; max-width: 150px;" />',
                obj.preview_image.url,
            )
        return format_html('<em>No preview available</em>')

    preview_thumbnail.short_description = 'Preview'

    def save_model(self, request, obj, form, change):
        """Save model and update template files."""
        if not change:  # New object
            obj.created_by = request.user

        super().save_model(request, obj, form, change)

        # Save template files to filesystem
        try:
            obj.save_template_files()
            self.message_user(
                request, f'Template "{obj.name}" saved successfully and files updated.'
            )
        except Exception as e:
            self.message_user(
                request,
                f'Template saved but file update failed: {str(e)}',
                level='WARNING',
            )

    def get_queryset(self, request):
        """Optimize queryset with select_related."""
        return super().get_queryset(request).select_related('business', 'created_by')

    class Media:
        css = {'all': ('admin/css/template_admin.css',)}
        js = ('admin/js/template_admin.js',)
