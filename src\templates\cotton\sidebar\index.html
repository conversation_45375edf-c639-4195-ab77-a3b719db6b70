{% load static %}

<!-- Enhanced Sidebar with Collapsible Navigation -->
<div id="sidebar" class="bg-white shadow-lg h-full transition-all duration-300 ease-in-out" style="width: 256px;">
  <div class="flex flex-col h-full">

    <!-- Top Navigation Header -->
    <div class="flex items-center justify-between h-16 px-4 bg-primary-600 text-white">
      <a href="{% url 'core:home' %}" class="flex items-center text-xl font-bold text-gray-900">
        <i class="fas fa-route text-blue-500 mr-2"></i>
        <span class="sidebar-text">TourFlow</span>
      </a>
    </div>

    <!-- Navigation -->
    <nav class="flex-1 px-2 py-4 space-y-1 overflow-y-auto">

      <!-- Dashboard -->
      <a href="{% url 'core:dashboard' %}"
        class="sidebar-item flex items-center px-3 py-2 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors {% if request.resolver_match.url_name == 'home' %}bg-blue-50 text-blue-700 border-r-2 border-blue-500{% endif %}"
        data-tooltip="Dashboard">
        <svg class="sidebar-icon w-5 h-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
            d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 22V12h6v10"></path>
        </svg>
        <span class="sidebar-text ml-3">Dashboard</span>
      </a>

      <!-- Business Operations Section -->
      <div class="sidebar-section">
        <div
          class="sidebar-section-header flex items-center justify-between px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider cursor-pointer"
          onclick="toggleSection('business-ops')">
          <span class="sidebar-text">Business Operations</span>
          <svg class="sidebar-text w-4 h-4 transition-transform" id="business-ops-icon" fill="none"
            stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
          </svg>
        </div>

        <div id="business-ops" class="sidebar-subsection space-y-1">
          <!-- Tour Templates with Sub-items -->
          <div class="sidebar-menu-item">
            <div
              class="flex items-center justify-between px-3 py-2 ml-4 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer"
              onclick="toggleMenuItem('tour-templates-submenu')" data-tooltip="Tour Templates">
              <div class="flex items-center">
                <svg class="sidebar-icon w-5 h-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                  </path>
                </svg>
                <span class="sidebar-text ml-3">Tour Templates</span>
              </div>
              <svg class="sidebar-text w-4 h-4 transition-transform" id="tour-templates-submenu-icon" fill="none"
                stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </div>
            <div id="tour-templates-submenu" class="sidebar-submenu ml-8 space-y-1 hidden">
              <a href="{% url 'tours:tour-template-list' %}"
                class="sidebar-subitem flex items-center px-3 py-1 text-sm text-gray-600 rounded hover:bg-gray-100 transition-colors"
                data-tooltip="All Tour Templates">
                <i class="fas fa-list w-4 h-4 mr-2"></i>
                <span class="sidebar-text">All Tour Templates</span>
              </a>
              <a href="{% url 'tours:tour-template-create' %}"
                class="sidebar-subitem flex items-center px-3 py-1 text-sm text-gray-600 rounded hover:bg-gray-100 transition-colors"
                data-tooltip="Create Tour Template">
                <i class="fas fa-plus w-4 h-4 mr-2"></i>
                <span class="sidebar-text">Create Tour Template</span>
              </a>
            </div>
          </div>

          <!-- Tours with Sub-items -->
          <div class="sidebar-menu-item">
            <div
              class="flex items-center justify-between px-3 py-2 ml-4 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer"
              onclick="toggleMenuItem('tours-submenu')" data-tooltip="Tours">
              <div class="flex items-center">
                <svg class="sidebar-icon w-5 h-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z">
                  </path>
                </svg>
                <span class="sidebar-text ml-3">Tours</span>
              </div>
              <svg class="sidebar-text w-4 h-4 transition-transform" id="tours-submenu-icon" fill="none"
                stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </div>
            <div id="tours-submenu" class="sidebar-submenu ml-8 space-y-1 hidden">
              <a href="{% url 'tours:tour-list' %}"
                class="sidebar-subitem flex items-center px-3 py-1 text-sm text-gray-600 rounded hover:bg-gray-100 transition-colors"
                data-tooltip="All tours">
                <i class="fas fa-list w-4 h-4 mr-2"></i>
                <span class="sidebar-text">All tours</span>
              </a>
              <a href="{% url 'tours:tour-create' %}"
                class="sidebar-subitem flex items-center px-3 py-1 text-sm text-gray-600 rounded hover:bg-gray-100 transition-colors"
                data-tooltip="Add Tour">
                <i class="fas fa-plus w-4 h-4 mr-2"></i>
                <span class="sidebar-text">Add Tour</span>
              </a>
            </div>
          </div>

          <!-- Clients with Sub-items -->
          <div class="sidebar-menu-item">
            <div
              class="flex items-center justify-between px-3 py-2 ml-4 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer"
              onclick="toggleMenuItem('clients-submenu')" data-tooltip="Clients">
              <div class="flex items-center">
                <svg class="sidebar-icon w-5 h-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z">
                  </path>
                </svg>
                <span class="sidebar-text ml-3">Clients</span>
              </div>
              <svg class="sidebar-text w-4 h-4 transition-transform" id="clients-submenu-icon" fill="none"
                stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </div>
            <div id="clients-submenu" class="sidebar-submenu ml-8 space-y-1 hidden">
              <a href="{% url 'clients:client-list' %}"
                class="sidebar-subitem flex items-center px-3 py-1 text-sm text-gray-600 rounded hover:bg-gray-100 transition-colors"
                data-tooltip="All Clients">
                <i class="fas fa-list w-4 h-4 mr-2"></i>
                <span class="sidebar-text">All Clients</span>
              </a>
              <a href="{% url 'clients:client-create' %}"
                class="sidebar-subitem flex items-center px-3 py-1 text-sm text-gray-600 rounded hover:bg-gray-100 transition-colors"
                data-tooltip="Add Client">
                <i class="fas fa-plus w-4 h-4 mr-2"></i>
                <span class="sidebar-text">Add Client</span>
              </a>
              <a href="#"
                class="sidebar-subitem flex items-center px-3 py-1 text-sm text-gray-600 rounded hover:bg-gray-100 transition-colors"
                data-tooltip="Client Groups">
                <i class="fas fa-users w-4 h-4 mr-2"></i>
                <span class="sidebar-text">Client Groups</span>
              </a>
            </div>
          </div>

          <!-- Quotes with Sub-items -->
          <div class="sidebar-menu-item">
            <div
              class="flex items-center justify-between px-3 py-2 ml-4 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer"
              onclick="toggleMenuItem('quotes-submenu')" data-tooltip="Quotes">
              <div class="flex items-center">
                <svg class="sidebar-icon w-5 h-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                  </path>
                </svg>
                <span class="sidebar-text ml-3">Quotes</span>
              </div>
              <svg class="sidebar-text w-4 h-4 transition-transform" id="quotes-submenu-icon" fill="none"
                stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </div>
            <div id="quotes-submenu" class="sidebar-submenu ml-8 space-y-1 hidden">
              <a href="{% url 'quotes:quote-list' %}"
                class="sidebar-subitem flex items-center px-3 py-1 text-sm text-gray-600 rounded hover:bg-gray-100 transition-colors"
                data-tooltip="All Quotes">
                <i class="fas fa-list w-4 h-4 mr-2"></i>
                <span class="sidebar-text">All Quotes</span>
              </a>
              <a href="{% url 'quotes:quote-create' %}"
                class="sidebar-subitem flex items-center px-3 py-1 text-sm text-gray-600 rounded hover:bg-gray-100 transition-colors"
                data-tooltip="Create Quote">
                <i class="fas fa-plus w-4 h-4 mr-2"></i>
                <span class="sidebar-text">Create Quote</span>
              </a>
              <a href="#"
                class="sidebar-subitem flex items-center px-3 py-1 text-sm text-gray-600 rounded hover:bg-gray-100 transition-colors"
                data-tooltip="Draft Quotes">
                <i class="fas fa-edit w-4 h-4 mr-2"></i>
                <span class="sidebar-text">Draft Quotes</span>
              </a>
              <a href="#"
                class="sidebar-subitem flex items-center px-3 py-1 text-sm text-gray-600 rounded hover:bg-gray-100 transition-colors"
                data-tooltip="Approved Quotes">
                <i class="fas fa-check-circle w-4 h-4 mr-2"></i>
                <span class="sidebar-text">Approved</span>
              </a>
            </div>
          </div>

          <!-- Invoices with Sub-items -->
          <div class="sidebar-menu-item">
            <div
              class="flex items-center justify-between px-3 py-2 ml-4 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer"
              onclick="toggleMenuItem('invoices-submenu')" data-tooltip="Invoices">
              <div class="flex items-center">
                <svg class="sidebar-icon w-5 h-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2">
                  </path>
                </svg>
                <span class="sidebar-text ml-3">Invoices</span>
              </div>
              <svg class="sidebar-text w-4 h-4 transition-transform" id="invoices-submenu-icon" fill="none"
                stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </div>
            <div id="invoices-submenu" class="sidebar-submenu ml-8 space-y-1 hidden">
              <a href="{% url 'invoices:list' %}"
                class="sidebar-subitem flex items-center px-3 py-1 text-sm text-gray-600 rounded hover:bg-gray-100 transition-colors"
                data-tooltip="All Invoices">
                <i class="fas fa-list w-4 h-4 mr-2"></i>
                <span class="sidebar-text">All Invoices</span>
              </a>
              <a href="{% url 'invoices:create' %}"
                class="sidebar-subitem flex items-center px-3 py-1 text-sm text-gray-600 rounded hover:bg-gray-100 transition-colors"
                data-tooltip="Create Invoice">
                <i class="fas fa-plus w-4 h-4 mr-2"></i>
                <span class="sidebar-text">Create Invoice</span>
              </a>
              <a href="#"
                class="sidebar-subitem flex items-center px-3 py-1 text-sm text-gray-600 rounded hover:bg-gray-100 transition-colors"
                data-tooltip="Pending Invoices">
                <i class="fas fa-clock w-4 h-4 mr-2"></i>
                <span class="sidebar-text">Pending</span>
              </a>
              <a href="#"
                class="sidebar-subitem flex items-center px-3 py-1 text-sm text-gray-600 rounded hover:bg-gray-100 transition-colors"
                data-tooltip="Paid Invoices">
                <i class="fas fa-check w-4 h-4 mr-2"></i>
                <span class="sidebar-text">Paid</span>
              </a>
            </div>
          </div>

          <!-- Bookings with Sub-items -->
          <div class="sidebar-menu-item">
            <div
              class="flex items-center justify-between px-3 py-2 ml-4 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer"
              onclick="toggleMenuItem('bookings-submenu')">
              <div class="flex items-center">
                <svg class="sidebar-icon w-5 h-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
                <span class="sidebar-text ml-3">Bookings</span>
              </div>
              <svg class="sidebar-text w-4 h-4 transition-transform" id="bookings-submenu-icon" fill="none"
                stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </div>
            <div id="bookings-submenu" class="sidebar-submenu ml-8 space-y-1 hidden">
              <a href="{% url 'bookings:dashboard' %}"
                class="sidebar-subitem flex items-center px-3 py-1 text-sm text-gray-600 rounded hover:bg-gray-100 transition-colors"
                data-tooltip="Bookings Dashboard">
                <i class="fas fa-tachometer-alt w-4 h-4 mr-2"></i>
                <span class="sidebar-text">Dashboard</span>
              </a>
              <a href="{% url 'bookings:booking_list' %}"
                class="sidebar-subitem flex items-center px-3 py-1 text-sm text-gray-600 rounded hover:bg-gray-100 transition-colors"
                data-tooltip="All Bookings">
                <i class="fas fa-list w-4 h-4 mr-2"></i>
                <span class="sidebar-text">All Bookings</span>
              </a>
              <a href="{% url 'bookings:calendar' %}"
                class="sidebar-subitem flex items-center px-3 py-1 text-sm text-gray-600 rounded hover:bg-gray-100 transition-colors"
                data-tooltip="Calendar View">
                <i class="fas fa-calendar w-4 h-4 mr-2"></i>
                <span class="sidebar-text">Calendar</span>
              </a>
              <a href="{% url 'tours:event_create' %}"
                class="sidebar-subitem flex items-center px-3 py-1 text-sm text-gray-600 rounded hover:bg-gray-100 transition-colors"
                data-tooltip="Create Event">
                <i class="fas fa-plus w-4 h-4 mr-2"></i>
                <span class="sidebar-text">Create Event</span>
              </a>
            </div>
          </div>

          <!-- Payments with Sub-items -->
          <div class="sidebar-menu-item">
            <div
              class="flex items-center justify-between px-3 py-2 ml-4 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer"
              onclick="toggleMenuItem('payments-submenu')">
              <div class="flex items-center">
                <svg class="sidebar-icon w-5 h-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1">
                  </path>
                </svg>
                <span class="sidebar-text ml-3">Payments</span>
              </div>
              <svg class="sidebar-text w-4 h-4 transition-transform" id="payments-submenu-icon" fill="none"
                stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </div>
            <div id="payments-submenu" class="sidebar-submenu ml-8 space-y-1 hidden">
              <a href="{% url 'payments:subscription_dashboard' %}"
                class="sidebar-subitem flex items-center px-3 py-1 text-sm text-gray-600 rounded hover:bg-gray-100 transition-colors"
                data-tooltip="Payment Dashboard">
                <i class="fas fa-tachometer-alt w-4 h-4 mr-2"></i>
                <span class="sidebar-text">Dashboard</span>
              </a>
              <a href="#"
                class="sidebar-subitem flex items-center px-3 py-1 text-sm text-gray-600 rounded hover:bg-gray-100 transition-colors"
                data-tooltip="Payment History">
                <i class="fas fa-history w-4 h-4 mr-2"></i>
                <span class="sidebar-text">History</span>
              </a>
              <a href="#"
                class="sidebar-subitem flex items-center px-3 py-1 text-sm text-gray-600 rounded hover:bg-gray-100 transition-colors"
                data-tooltip="Payment Methods">
                <i class="fas fa-credit-card w-4 h-4 mr-2"></i>
                <span class="sidebar-text">Methods</span>
              </a>
              <a href="#"
                class="sidebar-subitem flex items-center px-3 py-1 text-sm text-gray-600 rounded hover:bg-gray-100 transition-colors"
                data-tooltip="Subscription">
                <i class="fas fa-sync w-4 h-4 mr-2"></i>
                <span class="sidebar-text">Subscription</span>
              </a>
            </div>
          </div>
        </div>

        <!-- Divider -->
        <div class="border-t border-gray-200 my-2"></div>

        <!-- Management Section -->
        <div class="sidebar-section">
          <div
            class="sidebar-section-header flex items-center justify-between px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider cursor-pointer"
            onclick="toggleSection('management')">
            <span class="sidebar-text">Management</span>
            <svg class="sidebar-text w-4 h-4 transition-transform" id="management-icon" fill="none"
              stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </div>

          <div id="management" class="sidebar-subsection space-y-1">
            <!-- Team Management -->
            <a href="{% url 'accounts:user_management' %}"
              class="sidebar-item flex items-center px-3 py-2 ml-4 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors"
              data-tooltip="Team Management">
              <svg class="sidebar-icon w-5 h-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z">
                </path>
              </svg>
              <span class="sidebar-text ml-3">Team Management</span>
            </a>

            <!-- Business Settings -->
            <a href=""
              class="sidebar-item flex items-center px-3 py-2 ml-4 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors"
              data-tooltip="Business Settings">
              <svg class="sidebar-icon w-5 h-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4">
                </path>
              </svg>
              <span class="sidebar-text ml-3">Business Settings</span>
            </a>
          </div>
        </div>

        <!-- Tools Section -->
        <div class="sidebar-section">
          <div
            class="sidebar-section-header flex items-center justify-between px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider cursor-pointer"
            onclick="toggleSection('tools')">
            <span class="sidebar-text">Tools</span>
            <svg class="sidebar-text w-4 h-4 transition-transform" id="tools-icon" fill="none" stroke="currentColor"
              viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </div>

          <div id="tools" class="sidebar-subsection space-y-1">
            <!-- Data Generator -->
            <a href="{% url 'data_generator:dashboard' %}"
              class="sidebar-item flex items-center px-3 py-2 ml-4 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors"
              data-tooltip="Data Generator">
              <svg class="sidebar-icon w-5 h-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4">
                </path>
              </svg>
              <span class="sidebar-text ml-3">Data Generator</span>
            </a>
          </div>
        </div>

    </nav>
  </div>
</div>

<!-- Tooltip for collapsed sidebar -->
<div id="sidebar-tooltip"
  class="fixed z-50 px-2 py-1 text-sm text-white bg-gray-900 rounded shadow-lg pointer-events-none opacity-0 transition-opacity duration-200">
</div>

<!-- JavaScript for Sidebar Functionality -->

<div data-signals="{sidebarCollapsed: false, businessOps: true, management: true, tools: true, clientsSubmenu: true}">

</div>

<script src="{% static "js/sidebar.js" %}"></script>
