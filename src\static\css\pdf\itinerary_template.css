/* PDF Itinerary Template Styles */

/* Page Setup */
@page {
    size: A4;
    margin: 1.5cm;
    @top-center {
        content: "{{ business.name }} - {{ tour.title }}";
        font-size: 10px;
        color: #666;
    }
    @bottom-center {
        content: "Page " counter(page) " of " counter(pages);
        font-size: 10px;
        color: #666;
    }
}

/* Print Optimizations */
@media print {
    body {
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
        font-size: 12px;
        line-height: 1.4;
    }
    
    .page-break {
        page-break-before: always;
    }
    
    .no-break {
        page-break-inside: avoid;
        break-inside: avoid;
    }
    
    .header-gradient,
    .day-number,
    .highlight-box {
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
    }
}

/* Typography */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: #2d3748;
    line-height: 1.6;
}

h1, h2, h3 {
    font-weight: 700;
    margin-bottom: 0.5rem;
}

h1 {
    font-size: 2.25rem;
    line-height: 1.2;
}

h2 {
    font-size: 1.5rem;
    line-height: 1.3;
}

h3 {
    font-size: 1.25rem;
    line-height: 1.4;
}

/* Header Styling */
.header-gradient {
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 50%, #60a5fa 100%);
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(30, 64, 175, 0.2);
}

.header-gradient h1 {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Day Number Styling */
.day-number {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 50%, #b45309 100%);
    min-width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    font-size: 14px;
    font-weight: 700;
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Highlight Boxes */
.highlight-box {
    background: linear-gradient(135deg, #10b981 0%, #059669 50%, #047857 100%);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.2);
    transition: transform 0.2s ease;
}

.highlight-box:hover {
    transform: translateY(-2px);
}

.highlight-box h3 {
    font-size: 16px;
    margin-bottom: 8px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.highlight-box p {
    font-size: 13px;
    opacity: 0.95;
}

/* Content Sections */
.content-section {
    margin-bottom: 2rem;
}

.section-title {
    border-bottom: 3px solid;
    padding-bottom: 8px;
    margin-bottom: 1.5rem;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -3px;
    left: 0;
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, currentColor 0%, transparent 100%);
}

/* Day Itinerary Styling */
.day-container {
    margin-bottom: 1.5rem;
    border-left: 4px solid #e5e7eb;
    padding-left: 1rem;
    position: relative;
}

.day-container::before {
    content: '';
    position: absolute;
    left: -6px;
    top: 20px;
    width: 8px;
    height: 8px;
    background: #f59e0b;
    border-radius: 50%;
}

.day-content {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.day-content p {
    margin-bottom: 8px;
    font-size: 14px;
}

.day-content strong {
    color: #374151;
    font-weight: 600;
}

/* Inclusions/Exclusions Lists */
.inclusion-list,
.exclusion-list {
    background: #f9fafb;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
    padding: 1.5rem;
}

.inclusion-list h2 {
    color: #059669;
    border-bottom: 2px solid #d1fae5;
    padding-bottom: 8px;
    margin-bottom: 1rem;
}

.exclusion-list h2 {
    color: #dc2626;
    border-bottom: 2px solid #fecaca;
    padding-bottom: 8px;
    margin-bottom: 1rem;
}

.inclusion-list ul li,
.exclusion-list ul li {
    padding: 4px 0;
    font-size: 13px;
    line-height: 1.5;
}

.inclusion-list ul li::marker {
    color: #059669;
}

.exclusion-list ul li::marker {
    color: #dc2626;
}

/* Footer Styling */
.footer {
    border-top: 2px solid #e5e7eb;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 8px;
    margin-top: 2rem;
}

.footer p {
    margin-bottom: 4px;
}

/* Responsive Grid */
.grid-2 {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

.grid-4 {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

@media (max-width: 768px) {
    .grid-2,
    .grid-4 {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
}

/* Logo Styling */
.logo {
    max-height: 64px;
    width: auto;
    filter: brightness(0) invert(1);
}

/* Business Info */
.business-info {
    font-size: 14px;
    opacity: 0.9;
}

.business-info p {
    margin-bottom: 4px;
}

/* Special Elements */
.tour-overview {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-left: 4px solid #3b82f6;
    border-radius: 0 8px 8px 0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

/* Utility Classes */
.text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.box-shadow {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.border-gradient {
    border-image: linear-gradient(90deg, #3b82f6, #10b981) 1;
}

/* Animation for Interactive Elements */
@media screen {
    .highlight-box,
    .day-content {
        transition: all 0.3s ease;
    }
    
    .highlight-box:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(16, 185, 129, 0.3);
    }
    
    .day-content:hover {
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    }
}

/* Color Variables for Customization */
:root {
    --primary-color: #3b82f6;
    --secondary-color: #f59e0b;
    --accent-color: #10b981;
    --danger-color: #dc2626;
    --text-color: #2d3748;
    --light-bg: #f8fafc;
    --border-color: #e5e7eb;
}

/* Dark mode support (for screen viewing) */
@media (prefers-color-scheme: dark) and screen {
    :root {
        --text-color: #f7fafc;
        --light-bg: #2d3748;
        --border-color: #4a5568;
    }
    
    body {
        background-color: #1a202c;
        color: var(--text-color);
    }
    
    .day-content,
    .inclusion-list,
    .exclusion-list {
        background: var(--light-bg);
        border-color: var(--border-color);
    }
}
