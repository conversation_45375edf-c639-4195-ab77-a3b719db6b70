# Tour Management SaaS - User Roles and Permissions Workflow

This diagram shows the user role system, onboarding process, and permission management for the multi-tenant SaaS application.

## Diagram

```mermaid
flowchart TD
    %% User Registration and Onboarding
    A[User Registration] --> B{User Role?}

    %% Business Owner Path
    B -->|Business Owner| C[Create Business Account]
    C --> D[Complete Business Profile]
    D --> E[Setup Business Settings]
    E --> F[Configure Branding]
    F --> G[Invite Team Members]

    %% Agent Path
    B -->|Agent| H[Receive Invitation]
    H --> I[Accept Invitation]
    I --> J[Complete Profile]
    J --> K[Access Training Materials]
    K --> L[Start Working]

    %% Customer Path
    B -->|Customer| M[Basic Registration]
    M --> N[Email Verification]
    N --> O[Access Public Quotes]

    %% Business Owner Capabilities
    G --> P[Business Owner Dashboard]
    P --> Q[Manage All Business Data]
    P --> R[User Management]
    P --> S[Financial Reports]
    P --> T[System Configuration]

    %% Agent Capabilities
    L --> U[Agent Dashboard]
    U --> V[Client Management]
    U --> W[Quote Creation]
    U --> X[Booking Management]
    U --> Y[Limited Reports]

    %% Customer Capabilities
    O --> Z[Customer Portal]
    Z --> AA[View Own Quotes]
    Z --> BB[Make Payments]
    Z --> CC[View Booking History]

    %% Permission Checks
    Q --> DD{Permission Check}
    DD -->|Authorized| EE[Access Granted]
    DD -->|Unauthorized| FF[Access Denied]

    V --> GG{Business Scope Check}
    GG -->|Same Business| HH[Access Granted]
    GG -->|Different Business| II[Access Denied]

    AA --> JJ{Owner Check}
    JJ -->|Own Data| KK[Access Granted]
    JJ -->|Other's Data| LL[Access Denied]

    %% Multi-Tenant Security
    EE --> MM[Business Data Isolation]
    HH --> MM
    KK --> MM
    MM --> NN[Secure Data Access]

    %% Styling - High Contrast Colors
    classDef userRole fill:#ffffff,stroke:#1565c0,stroke-width:3px,color:#000000
    classDef businessOwner fill:#e8f5e8,stroke:#1b5e20,stroke-width:3px,color:#000000
    classDef agent fill:#fff8e1,stroke:#e65100,stroke-width:3px,color:#000000
    classDef customer fill:#f3e5f5,stroke:#6a1b9a,stroke-width:3px,color:#000000
    classDef security fill:#ffebee,stroke:#b71c1c,stroke-width:3px,color:#000000
    classDef success fill:#e8f5e8,stroke:#2e7d32,stroke-width:3px,color:#000000
    classDef denied fill:#ffcdd2,stroke:#c62828,stroke-width:3px,color:#000000

    class A,B userRole
    class C,D,E,F,G,P,Q,R,S,T businessOwner
    class H,I,J,K,L,U,V,W,X,Y agent
    class M,N,O,Z,AA,BB,CC customer
    class DD,GG,JJ,MM,NN security
    class EE,HH,KK success
    class FF,II,LL denied
```

## User Roles

### 1. Business Owner

**Highest privilege level with full business control**

#### Onboarding Process:

- Create business account
- Complete business profile
- Setup business settings (timezone, currency, branding)
- Configure company branding
- Invite team members (agents)

#### Capabilities:

- **Full Data Access**: All business data and operations
- **User Management**: Invite, manage, and remove team members
- **Financial Reports**: Complete financial analytics and reporting
- **System Configuration**: Business settings, integrations, customization
- **All Agent Capabilities**: Can perform any agent function

#### Permissions:

- Create, read, update, delete all business data
- Manage user accounts within business
- Access financial and analytical reports
- Configure business settings and integrations

### 2. Agent

**Operational role for day-to-day tour management**

#### Onboarding Process:

- Receive invitation from business owner
- Accept invitation and join business
- Complete user profile
- Access training materials
- Begin operational work

#### Capabilities:

- **Client Management**: Create and manage client profiles
- **Quote Creation**: Build and send quotes to clients
- **Booking Management**: Handle reservations and scheduling
- **Limited Reports**: Operational reports within scope
- **Tour Operations**: Check-in, tour management, completion

#### Permissions:

- Access only data within assigned business
- Cannot access other businesses' data
- Cannot manage users or business settings
- Limited to operational functions

### 3. Customer

**External users who receive and interact with quotes**

#### Onboarding Process:

- Basic registration (optional)
- Email verification
- Access public quote links

#### Capabilities:

- **Quote Access**: View quotes via public links
- **Payment Processing**: Make payments on invoices
- **Booking History**: View own booking records
- **Profile Management**: Update personal information

#### Permissions:

- Access only own quotes and bookings
- Cannot access other customers' data
- Cannot access business operational data
- Limited to customer-facing functions

## Security Framework

### Multi-Tenant Data Isolation

- **Business Scope**: All data scoped to business entity
- **Query Filtering**: Automatic business-level filtering
- **Permission Checks**: Role-based access control
- **Data Separation**: Complete isolation between businesses

### Permission Hierarchy

1. **Business Owner**: Full access within business
2. **Agent**: Operational access within business
3. **Customer**: Personal data access only

### Access Control Mechanisms

- **Django Guardian**: Object-level permissions
- **Business Managers**: Automatic data scoping
- **Role Decorators**: View-level access control
- **Template Guards**: UI-level permission checks

## Key Security Features

### Authentication

- Django's built-in authentication system
- Email verification required
- Secure password requirements
- Session management

### Authorization

- Role-based access control (RBAC)
- Object-level permissions
- Business-scoped data access
- Automatic permission inheritance

### Data Protection

- Business-level data isolation
- Encrypted sensitive data
- Audit logging for all actions
- Secure public quote access via hash

### Multi-Tenancy

- Complete data separation between businesses
- Shared application, isolated data
- Business-specific configurations
- Scalable architecture for multiple tenants
