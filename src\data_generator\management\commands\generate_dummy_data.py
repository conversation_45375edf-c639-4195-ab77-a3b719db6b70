"""
Management command to generate dummy data using model_bakery.
"""

from django.core.management.base import BaseCommand, CommandError
from django.db import transaction

from data_generator.services import DataGeneratorService


class Command(BaseCommand):
    help = 'Generate dummy data for testing and development using model_bakery'

    def add_arguments(self, parser):
        parser.add_argument(
            '--scale',
            type=str,
            choices=['small', 'medium', 'large'],
            default='medium',
            help='Scale of data to generate (default: medium)'
        )

        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear existing data before generating new data'
        )

        parser.add_argument(
            '--users',
            type=int,
            help='Number of users to generate'
        )

        parser.add_argument(
            '--businesses',
            type=int,
            help='Number of businesses to generate'
        )

        parser.add_argument(
            '--clients',
            type=int,
            help='Number of clients to generate'
        )

        parser.add_argument(
            '--travelers',
            type=int,
            help='Number of travelers to generate'
        )

        parser.add_argument(
            '--events',
            type=int,
            help='Number of events to generate'
        )

        parser.add_argument(
            '--quotes',
            type=int,
            help='Number of quotes to generate'
        )

        parser.add_argument(
            '--bookings',
            type=int,
            help='Number of bookings to generate'
        )

        parser.add_argument(
            '--invoices',
            type=int,
            help='Number of invoices to generate'
        )

        parser.add_argument(
            '--payments',
            type=int,
            help='Number of payments to generate'
        )

        parser.add_argument(
            '--templates',
            type=int,
            help='Number of PDF templates to generate'
        )

    def handle(self, *args, **options):
        service = DataGeneratorService()

        try:
            with transaction.atomic():
                # Clear data if requested
                if options['clear']:
                    self.stdout.write(
                        self.style.WARNING('Clearing existing data...')
                    )
                    service.clear_all_data()
                    self.stdout.write(
                        self.style.SUCCESS('Data cleared successfully!')
                    )

                # Check if specific counts are provided
                specific_counts = {
                    key: options[key] for key in [
                        'users', 'businesses', 'clients', 'travelers',
                        'events', 'quotes', 'bookings', 'invoices', 'payments', 'templates'
                    ] if options[key] is not None
                }

                if specific_counts:
                    # Generate specific data types
                    self.stdout.write(
                        self.style.SUCCESS('Generating specific data types...')
                    )

                    total_generated = 0

                    if 'users' in specific_counts:
                        users = service.generate_users(specific_counts['users'])
                        total_generated += len(users)
                        self.stdout.write(f'Generated {len(users)} users')

                    if 'businesses' in specific_counts:
                        businesses = service.generate_businesses(specific_counts['businesses'])
                        total_generated += len(businesses)
                        self.stdout.write(f'Generated {len(businesses)} businesses')

                    if 'clients' in specific_counts:
                        clients = service.generate_clients(specific_counts['clients'])
                        total_generated += len(clients)
                        self.stdout.write(f'Generated {len(clients)} clients')

                    if 'travelers' in specific_counts:
                        travelers = service.generate_travelers(specific_counts['travelers'])
                        total_generated += len(travelers)
                        self.stdout.write(f'Generated {len(travelers)} travelers')

                    if 'events' in specific_counts:
                        events = service.generate_events(specific_counts['events'])
                        total_generated += len(events)
                        self.stdout.write(f'Generated {len(events)} events')

                    if 'quotes' in specific_counts:
                        quotes = service.generate_quotes(specific_counts['quotes'])
                        total_generated += len(quotes)
                        self.stdout.write(f'Generated {len(quotes)} quotes')

                    if 'bookings' in specific_counts:
                        bookings = service.generate_bookings(specific_counts['bookings'])
                        total_generated += len(bookings)
                        self.stdout.write(f'Generated {len(bookings)} bookings')

                    if 'invoices' in specific_counts:
                        invoices = service.generate_invoices(specific_counts['invoices'])
                        total_generated += len(invoices)
                        self.stdout.write(f'Generated {len(invoices)} invoices')

                    if 'payments' in specific_counts:
                        payments = service.generate_payments(specific_counts['payments'])
                        total_generated += len(payments)
                        self.stdout.write(f'Generated {len(payments)} payments')

                    if 'templates' in specific_counts:
                        templates = service.generate_pdf_templates(specific_counts['templates'])
                        total_generated += len(templates)
                        self.stdout.write(f'Generated {len(templates)} PDF templates')

                    self.stdout.write(
                        self.style.SUCCESS(
                            f'Successfully generated {total_generated} objects!'
                        )
                    )

                else:
                    # Generate complete dataset
                    scale = options['scale']
                    self.stdout.write(
                        self.style.SUCCESS(f'Generating {scale} dataset...')
                    )

                    counts = service.generate_complete_dataset(scale)

                    self.stdout.write(
                        self.style.SUCCESS(f'Successfully generated {scale} dataset!')
                    )

                    # Display summary
                    self.stdout.write('\nGeneration Summary:')
                    for data_type, count in counts.items():
                        self.stdout.write(f'  {data_type.capitalize()}: {count}')

                    total = sum(counts.values())
                    self.stdout.write(f'\nTotal objects created: {total}')

        except Exception as e:
            raise CommandError(f'Error generating data: {str(e)}')
