<c-layouts.dashboard title="Dashboard" subtitle="Welcome to your tour business management center">
  <div class="space-y-8">
    {% if stats %}
    <!-- Dashboard Statistics -->
    <c-dashboard-stats stats="{{ stats_data }}" />
    {% endif %}

    {% if user.is_authenticated %}
    <!-- Dashboard Statistics -->
    {% if stats %}
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <!-- Active Quotes -->
      <div class="bg-white rounded-lg shadow p-6 hover:shadow-md transition-shadow">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
              <i class="fas fa-file-invoice-dollar text-white"></i>
            </div>
          </div>
          <div class="ml-4 flex-1">
            <p class="text-sm font-medium text-gray-500">Active Quotes</p>
            <p class="text-2xl font-semibold text-gray-900">{{ stats.active_quotes|default:0 }}</p>
            <div class="mt-2">
              <a href="{% url 'quotes:quote-list' %}" class="text-blue-600 hover:text-blue-700 text-sm font-medium">
                View all quotes →
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- Pending Invoices -->
      <div class="bg-white rounded-lg shadow p-6 hover:shadow-md transition-shadow">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center">
              <i class="fas fa-receipt text-white"></i>
            </div>
          </div>
          <div class="ml-4 flex-1">
            <p class="text-sm font-medium text-gray-500">Pending Invoices</p>
            <p class="text-2xl font-semibold text-gray-900">{{ stats.pending_invoices|default:0 }}</p>
            <div class="mt-2">
              <a href="{% url 'invoices:list' %}" class="text-green-600 hover:text-green-700 text-sm font-medium">
                View all invoices →
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- Total Clients -->
      <div class="bg-white rounded-lg shadow p-6 hover:shadow-md transition-shadow">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center">
              <i class="fas fa-users text-white"></i>
            </div>
          </div>
          <div class="ml-4 flex-1">
            <p class="text-sm font-medium text-gray-500">Total Clients</p>
            <p class="text-2xl font-semibold text-gray-900">{{ stats.total_clients|default:0 }}</p>
            <div class="mt-2">
              <a href="{% url 'clients:client-list' %}"
                class="text-purple-600 hover:text-purple-700 text-sm font-medium">
                View all clients →
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- Upcoming Tours -->
      <div class="bg-white rounded-lg shadow p-6 hover:shadow-md transition-shadow">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-10 h-10 bg-yellow-500 rounded-lg flex items-center justify-center">
              <i class="fas fa-calendar-check text-white"></i>
            </div>
          </div>
          <div class="ml-4 flex-1">
            <p class="text-sm font-medium text-gray-500">Upcoming Tours</p>
            <p class="text-2xl font-semibold text-gray-900">{{ stats.upcoming_tours|default:0 }}</p>
            <div class="mt-2">
              <a href="{% url 'bookings:booking_list' %}"
                class="text-yellow-600 hover:text-yellow-700 text-sm font-medium">
                View all bookings →
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
    {% endif %}

    <!-- Quick Actions -->
    {% if user.profile.business %}
    <div class="bg-white rounded-lg shadow p-6">
      <h2 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h2>
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <a href="{% url 'quotes:quote-create' %}"
          class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
          <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6">
              </path>
            </svg>
          </div>
          <div>
            <h3 class="font-medium text-gray-900">Create Quote</h3>
            <p class="text-sm text-gray-500">Generate a new quote for a client</p>
          </div>
        </a>

        <a href="{% url 'clients:client-create' %}"
          class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
          <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-4">
            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
            </svg>
          </div>
          <div>
            <h3 class="font-medium text-gray-900">Add Client</h3>
            <p class="text-sm text-gray-500">Register a new client</p>
          </div>
        </a>

        <a href="{% url 'tours:event_create' %}"
          class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
          <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
            <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z">
              </path>
            </svg>
          </div>
          <div>
            <h3 class="font-medium text-gray-900">Schedule Tour</h3>
            <p class="text-sm text-gray-500">Create a new tour event</p>
          </div>
        </a>

        <a href="{% url 'invoices:create' %}"
          class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
          <div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center mr-4">
            <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2">
              </path>
            </svg>
          </div>
          <div>
            <h3 class="font-medium text-gray-900">Create Invoice</h3>
            <p class="text-sm text-gray-500">Generate a new invoice</p>
          </div>
        </a>
      </div>
    </div>

    <!-- Recent Activity -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <div class="bg-white rounded-lg shadow p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Recent Quotes</h3>
        {% if recent_quotes %}
        <div class="space-y-3">
          {% for quote in recent_quotes %}
          <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
            <div>
              <p class="font-medium text-gray-900">{{ quote.title }}</p>
              <p class="text-sm text-gray-500">{{ quote.client.full_name }}</p>
            </div>
            <div class="text-right">
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                    {% if quote.status == 'approved' %}bg-green-100 text-green-800
                                    {% elif quote.status == 'sent' %}bg-blue-100 text-blue-800
                                    {% elif quote.status == 'draft' %}bg-gray-100 text-gray-800
                                    {% else %}bg-red-100 text-red-800{% endif %}">
                {{ quote.get_status_display }}
              </span>
              <p class="text-sm text-gray-500 mt-1">${{ quote.total_amount }}</p>
            </div>
          </div>
          {% endfor %}
        </div>
        <div class="mt-4">
          <a href="{% url 'quotes:quote-list' %}"
            class="text-primary-600 hover:text-primary-700 text-sm font-medium">View all
            quotes →</a>
        </div>
        {% else %}
        <p class="text-gray-500">No quotes yet. <a href="{% url 'quotes:quote-create' %}"
            class="text-primary-600 hover:text-primary-700">Create your first quote</a></p>
        {% endif %}
      </div>

      <div class="bg-white rounded-lg shadow p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Recent Bookings</h3>
        {% if recent_bookings %}
        <div class="space-y-3">
          {% for booking in recent_bookings %}
          <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
            <div>
              <p class="font-medium text-gray-900">{{ booking.tour_event.event.title }}</p>
              <p class="text-sm text-gray-500">{{ booking.client.full_name }} •
                {{ booking.number_of_participants }} participants</p>
            </div>
            <div class="text-right">
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                    {% if booking.status == 'confirmed' %}bg-green-100 text-green-800
                                    {% elif booking.status == 'pending' %}bg-yellow-100 text-yellow-800
                                    {% else %}bg-red-100 text-red-800{% endif %}">
                {{ booking.get_status_display }}
              </span>
              <p class="text-sm text-gray-500 mt-1">${{ booking.total_amount }}</p>
            </div>
          </div>
          {% endfor %}
        </div>
        <div class="mt-4">
          <a href="{% url 'tours:event_list' %}"
            class="text-primary-600 hover:text-primary-700 text-sm font-medium">View
            all bookings →</a>
        </div>
        {% else %}
        <p class="text-gray-500">No bookings yet. <a href="{% url 'tours:event_create' %}"
            class="text-primary-600 hover:text-primary-700">Schedule your first tour</a></p>
        {% endif %}
      </div>
    </div>
    {% endif %}
    {% else %}
    <!-- Landing page for non-authenticated users -->
    <div class="bg-white rounded-lg shadow p-8">
      <div class="text-center">
        <h2 class="text-2xl font-bold text-gray-900 mb-4">
          Get Started Today
        </h2>
        <p class="text-gray-600 mb-6">
          Sign in to access your tour business dashboard and manage your operations efficiently.
        </p>
        <a href="/admin/"
          class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors">
          Access Admin Panel
        </a>
      </div>
    </div>

    <!-- Features Section -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
      <div class="text-center">
        <div class="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
          <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
            </path>
          </svg>
        </div>
        <h3 class="text-lg font-semibold text-gray-900 mb-2">Quote Management</h3>
        <p class="text-gray-600">Create professional quotes with multiple options and track approval status.</p>
      </div>

      <div class="text-center">
        <div class="w-16 h-16 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
          <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z">
            </path>
          </svg>
        </div>
        <h3 class="text-lg font-semibold text-gray-900 mb-2">Client Management</h3>
        <p class="text-gray-600">Manage client information, travel preferences, and booking history.</p>
      </div>

      <div class="text-center">
        <div class="w-16 h-16 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
          <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z">
            </path>
          </svg>
        </div>
        <h3 class="text-lg font-semibold text-gray-900 mb-2">Tour Scheduling</h3>
        <p class="text-gray-600">Schedule tours, manage capacity, and track participant information.</p>
      </div>
    </div>
    {% endif %}
  </div>
</c-layouts.dashboard>
