"""
URL configuration for invoices app.
"""

from django.urls import path

from . import views

app_name = "invoices"

urlpatterns = [
    # Invoice list and CRUD operations
    path("", views.InvoiceListView.as_view(), name="list"),
    path("create/", views.InvoiceCreateView.as_view(), name="create"),
    path("<int:pk>/edit/", views.InvoiceUpdateView.as_view(), name="edit"),
    path("<int:pk>/detail/", views.InvoiceDetailView.as_view(), name="detail_cbv"),
    # Legacy function-based views
    path("<int:invoice_id>/", views.invoice_detail, name="detail"),
    path("<int:invoice_id>/mark-paid/", views.mark_invoice_paid, name="mark_paid"),
    path("<int:invoice_id>/send/", views.send_invoice, name="send"),
]
