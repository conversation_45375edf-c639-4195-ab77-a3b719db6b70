from django.core.validators import EmailValidator, RegexValidator
from django.db import models
from django.urls import reverse_lazy
from django.utils.translation import gettext_lazy as _

from core.mixins import UserAuditModel


class ClientQuerySet(models.QuerySet):
    """Custom QuerySet for Client model with chainable methods."""

    def for_business(self, business):
        """Filter queryset by business."""
        return self.filter(business=business)

    def active(self):
        """Return only active records."""
        return self.filter(is_active=True)

    def corporate(self):
        """Return only corporate clients."""
        return self.filter(client_type='corporate')

    def individual(self):
        """Return only individual clients."""
        return self.filter(client_type='individual')

    def with_travelers(self):
        """Return clients with prefetched travelers."""
        return self.prefetch_related('travelers')


class BusinessScopedManager(models.Manager):
    """Custom manager for business-scoped models."""

    def get_queryset(self):
        """Return optimized queryset."""
        return ClientQuerySet(self.model, using=self._db).select_related(
            'business',
            'created_by',
        )

    def for_business(self, business):
        """Filter queryset by business."""
        return self.get_queryset().for_business(business)

    def active(self):
        """Return only active records."""
        return self.get_queryset().active()


class ClientManager(BusinessScopedManager):
    """Optimized manager for Client model."""

    def with_travelers(self):
        """Return clients with prefetched travelers."""
        return self.get_queryset().with_travelers()

    def corporate(self):
        """Return only corporate clients."""
        return self.get_queryset().corporate()

    def individual(self):
        """Return only individual clients."""
        return self.get_queryset().individual()


class Client(UserAuditModel):
    """
    Client model representing customers of the tour business.
    Each client belongs to a specific business for data isolation.
    """

    class ClientType(models.TextChoices):
        INDIVIDUAL = 'individual', _('Individual')
        CORPORATE = 'corporate', _('Corporate')
        TRAVEL_AGENT = 'travel_agent', _('Travel Agent')

    # Business relationship (for multi-tenancy)
    business = models.ForeignKey(
        'businesses.Business',
        on_delete=models.CASCADE,
        related_name='clients',
        help_text=_('Business this client belongs to'),
    )

    # Basic Information
    client_type = models.CharField(
        max_length=20,
        choices=ClientType.choices,
        default=ClientType.INDIVIDUAL,
        help_text=_('Type of client'),
    )

    # Personal Information (for individuals)
    first_name = models.CharField(
        max_length=100,
        help_text=_('First name'),
    )
    last_name = models.CharField(
        max_length=100,
        help_text=_('Last name'),
    )

    # Company Information (for corporate clients)
    company_name = models.CharField(
        max_length=200,
        blank=True,
        help_text=_('Company name (for corporate clients)'),
    )

    # Contact Information
    email = models.EmailField(
        validators=[EmailValidator()],
        help_text=_('Primary email address'),
    )
    phone = models.CharField(
        max_length=20,
        validators=[
            RegexValidator(
                regex=r'^\+?1?\d{9,15}$',
                message=_(
                    "Phone number must be entered in the format: '+*********'. Up to 15 digits allowed."
                ),
            )
        ],
        help_text=_('Primary phone number'),
    )
    secondary_phone = models.CharField(
        max_length=20,
        blank=True,
        validators=[
            RegexValidator(
                regex=r'^\+?1?\d{9,15}$',
                message=_(
                    "Phone number must be entered in the format: '+*********'. Up to 15 digits allowed."
                ),
            )
        ],
        help_text=_('Secondary phone number'),
    )

    # Address Information
    address_line1 = models.CharField(
        max_length=255,
        blank=True,
        help_text=_('Street address'),
    )
    address_line2 = models.CharField(
        max_length=255,
        blank=True,
        help_text=_('Apartment, suite, etc.'),
    )
    city = models.CharField(
        max_length=100,
        blank=True,
        help_text=_('City'),
    )
    state_province = models.CharField(
        max_length=100,
        blank=True,
        help_text=_('State or Province'),
    )
    postal_code = models.CharField(
        max_length=20,
        blank=True,
        help_text=_('Postal or ZIP code'),
    )
    country = models.CharField(
        max_length=100,
        blank=True,
        help_text=_('Country'),
    )

    # Travel Information
    passport_number = models.CharField(
        max_length=20,
        blank=True,
        help_text=_('Passport number for international travel'),
    )
    passport_expiry_date = models.DateField(
        null=True,
        blank=True,
        help_text=_('Passport expiry date'),
    )
    nationality = models.CharField(
        max_length=100,
        blank=True,
        help_text=_('Nationality/citizenship'),
    )
    date_of_birth = models.DateField(
        null=True,
        blank=True,
        help_text=_('Date of birth'),
    )

    # Emergency Contact Information
    emergency_contact_name = models.CharField(
        max_length=200,
        blank=True,
        help_text=_('Emergency contact full name'),
    )
    emergency_contact_relationship = models.CharField(
        max_length=100,
        blank=True,
        help_text=_('Relationship to emergency contact (e.g., "Father", "Spouse")'),
    )
    emergency_contact_phone = models.CharField(
        max_length=20,
        blank=True,
        validators=[
            RegexValidator(
                regex=r'^\+?1?\d{9,15}$',
                message=_(
                    "Phone number must be entered in the format: '+*********'. Up to 15 digits allowed."
                ),
            )
        ],
        help_text=_('Emergency contact phone number'),
    )
    emergency_contact_email = models.EmailField(
        blank=True,
        help_text=_('Emergency contact email address'),
    )

    # Dietary and Medical Information
    dietary_requirements = models.TextField(
        blank=True,
        help_text=_(
            'Dietary requirements and restrictions (e.g., vegetarian, allergies)'
        ),
    )
    medical_conditions = models.TextField(
        blank=True,
        help_text=_('Medical conditions and allergies relevant to travel'),
    )

    # Business Details
    notes = models.TextField(
        blank=True,
        help_text=_('Internal notes about the client'),
    )
    preferences = models.TextField(
        blank=True,
        help_text=_('Client preferences and special requirements'),
    )

    is_active = models.BooleanField(
        default=True, help_text=_('Whether the client is active')
    )

    # Custom manager
    objects = ClientManager()

    class Meta:
        db_table = 'clients_client'
        verbose_name = _('Client')
        verbose_name_plural = _('Clients')
        unique_together = [['business', 'email']]  # Unique email per business
        indexes = [
            models.Index(fields=['business', 'email']),
            models.Index(fields=['business', 'last_name', 'first_name']),
            models.Index(fields=['business', 'company_name']),
            models.Index(fields=['business', 'is_active']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        if self.client_type == self.ClientType.CORPORATE and self.company_name:
            return f'{self.company_name} ({self.full_name})'
        return self.full_name

    @property
    def full_name(self):
        """Return the client's full name."""
        return f'{self.first_name} {self.last_name}'.strip()

    @property
    def display_name(self):
        """Return the appropriate display name based on client type."""
        if self.client_type == self.ClientType.CORPORATE and self.company_name:
            return self.company_name
        return self.full_name

    @property
    def full_address(self):
        """Return the complete formatted address."""
        if not self.address_line1:
            return ''

        address_parts = [self.address_line1]
        if self.address_line2:
            address_parts.append(self.address_line2)

        city_state_zip = []
        if self.city:
            city_state_zip.append(self.city)
        if self.state_province:
            city_state_zip.append(self.state_province)
        if self.postal_code:
            city_state_zip.append(self.postal_code)

        if city_state_zip:
            address_parts.append(', '.join(city_state_zip))

        if self.country:
            address_parts.append(self.country)

        return '\n'.join(address_parts)

    def get_absolute_url(self):
        return reverse_lazy(
            'clients:client-detail',
            kwargs={'pk': self.pk},
        )


class TravelerManager(models.Manager):
    """Optimized manager for Traveler model."""

    def get_queryset(self):
        """Return optimized queryset."""
        return (
            super()
            .get_queryset()
            .select_related(
                'client',
                'client__business',
                'created_by',
            )
        )

    def for_business(self, business):
        """Filter travelers by business through client."""
        return self.filter(client__business=business)

    def active(self):
        """Return only active travelers."""
        return self.filter(is_active=True)

    def with_valid_passport(self):
        """Return travelers with valid passports."""
        from datetime import date

        return self.filter(
            passport_expiry__isnull=False, passport_expiry__gt=date.today()
        )


class Traveler(UserAuditModel):
    """
    Traveler model representing individuals who will be traveling.
    Travelers are associated with clients and contain personal travel information.
    """

    # Client relationship
    client = models.ForeignKey(
        Client,
        on_delete=models.CASCADE,
        related_name='travelers',
        help_text=_('Client this traveler belongs to'),
    )

    # Personal Information
    first_name = models.CharField(
        max_length=100,
        verbose_name=_('First name'),
    )
    last_name = models.CharField(
        max_length=100,
        verbose_name=_('Last name'),
    )
    date_of_birth = models.DateField(
        null=True,
        blank=True,
        verbose_name=_('Date of birth'),
    )
    gender = models.CharField(
        max_length=1,
        choices=[
            ('M', _('Male')),
            ('F', _('Female')),
        ],
        verbose_name=_('Gender'),
    )

    # Contact Information
    email = models.EmailField(
        blank=True, validators=[EmailValidator()], help_text=_('Email address')
    )
    phone = models.CharField(
        max_length=20,
        blank=True,
        validators=[
            RegexValidator(
                regex=r'^\+?1?\d{9,15}$',
                message=_(
                    "Phone number must be entered in the format: '+*********'. Up to 15 digits allowed."
                ),
            )
        ],
        help_text=_('Phone number'),
    )

    # Travel Documents
    # ENCRYPT THIS FIELD (use django-encrypted-model-fields)
    passport_number = models.CharField(
        max_length=50, blank=True, help_text=_('Passport number')
    )
    passport_expiry = models.DateField(
        null=True, blank=True, help_text=_('Passport expiry date')
    )
    nationality = models.CharField(
        max_length=100, blank=True, help_text=_('Nationality')
    )

    # Emergency Contact
    emergency_contact_name = models.CharField(
        max_length=200, blank=True, help_text=_('Emergency contact name')
    )
    emergency_contact_phone = models.CharField(
        max_length=20,
        blank=True,
        validators=[
            RegexValidator(
                regex=r'^\+?1?\d{9,15}$',
                message=_(
                    "Phone number must be entered in the format: '+*********'. Up to 15 digits allowed."
                ),
            )
        ],
        help_text=_('Emergency contact phone'),
    )
    emergency_contact_relationship = models.CharField(
        max_length=100, blank=True, help_text=_('Relationship to emergency contact')
    )

    # Special Requirements
    dietary_restrictions = models.TextField(
        blank=True, help_text=_('Dietary restrictions and preferences')
    )
    # ENCRYPT THIS FIELD (use django-encrypted-model-fields)
    medical_conditions = models.TextField(
        blank=True, help_text=_('Medical conditions and requirements')
    )
    mobility_requirements = models.TextField(
        blank=True, help_text=_('Mobility assistance requirements')
    )
    special_requests = models.TextField(
        blank=True, help_text=_('Other special requests or notes')
    )

    is_active = models.BooleanField(
        default=True, help_text=_('Whether the traveler is active')
    )

    class Meta:
        verbose_name = _('Traveler')
        verbose_name_plural = _('Travelers')
        indexes = [
            models.Index(fields=['client', 'last_name', 'first_name']),
            models.Index(fields=['client', 'is_active']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return self.full_name

    @property
    def full_name(self):
        """Return the traveler's full name."""
        return f'{self.first_name} {self.last_name}'.strip()

    @property
    def age(self):
        """Calculate and return the traveler's age."""
        if not self.date_of_birth:
            return None

        from datetime import date

        today = date.today()
        return (
            today.year
            - self.date_of_birth.year
            - (
                (today.month, today.day)
                < (self.date_of_birth.month, self.date_of_birth.day)
            )
        )

    # Custom manager
    objects = TravelerManager()

    @property
    def business(self):
        """Get the business this traveler belongs to through the client."""
        return self.client.business
