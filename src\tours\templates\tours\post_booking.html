{% comment %} url: /bookings/ALP-ADV-2025-98765/ {% endcomment %}

<div class="booking-dashboard" data-booking-id="{{ booking.id }}">
  <div class="status-badge {{ booking.state|state_class }}">
    {{ booking.get_state_display }}
  </div>

  <!-- Smart modification menu -->
  <div class="actions">
    {% if booking.state == "CONFIRMED" and booking.balance_due_date > today %}
      <button data-action="pay-early"
              data-discount="{{ booking.early_payment_discount }}">
        Pay Balance Early (Save ${{ booking.early_payment_discount }})
      </button>
    {% endif %}

    {% if booking.state != "CANCELLED" %}
      <button data-action="change-date"
              data-min-days="{{ booking.tour.min_change_days }}">
        Change Date (Fee: ${{ booking.tour.change_fee }})
      </button>
    {% endif %}
  </div>

  <!-- Document upload section -->
  <div class="documents">
    {% for doc in booking.document_set.all %}
      <a href="{{ doc.file.url }}" class="doc-link">
        {{ doc.get_doc_type_display }} (Uploaded {{ doc.uploaded_at|date:"M j" }})
      </a>
    {% endfor %}
    <button class="upload-btn"
            data-doc-type="PASSPORT"
            data-booking-id="{{ booking.id }}">
      + Upload Passport
    </button>
  </div>
</div>
