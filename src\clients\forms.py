from crispy_forms.helper import FormHelper
from crispy_forms.layout import Submit
from django import forms

from clients.models import Client


class ClientForm(forms.ModelForm):
    class Meta:
        model = Client
        exclude = [
            'created_by',
            'business',
        ]

    @property
    def helper(self):
        helper = FormHelper()
        helper.form_class = 'p-6'
        helper.add_input(
            Submit(
                name='save',
                value='Save',
                css_class='px-4 py-2 rounded-md font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500',
            ),
        )
        return helper
