# Tour Business Management System

A comprehensive Django-based SaaS platform for managing tour businesses, including client management, quotes, bookings, invoices, and payments.

## Features

- **Client Management**: Comprehensive client profiles with travel preferences and history
- **Quote System**: Create, send, and track quotes with multiple options
- **Booking Management**: Schedule tours and manage capacity
- **Invoice & Payment Processing**: Automated invoicing with Lemon Squeezy integration
- **Email Notifications**: Automated email notifications using django-herald
- **Dynamic Settings**: Configurable settings using django-constance
- **Multi-tenant**: Support for multiple businesses
- **Responsive Design**: Modern UI with Tailwind CSS and DatastarJS
- **PDF Generation**: Customizable PDF templates for quotes and invoices
- **Subscription Management**: Business subscription handling via Lemon Squeezy

## Quick Start

### Prerequisites

- Python 3.11+
- PostgreSQL (recommended) or SQLite for development
- Node.js (for frontend assets)

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd tour-business-management
```

2. Install dependencies using uv:
```bash
uv sync
```

3. Set up environment variables:
```bash
cp .env.example .env
# Edit .env with your configuration
```

4. Run migrations:
```bash
uv run python src/manage.py migrate
```

5. Create a superuser:
```bash
uv run python src/manage.py createsuperuser
```

6. Run the development server:
```bash
uv run python src/manage.py runserver
```

Visit `http://localhost:8000` to access the application.

## Documentation Structure

- [Installation Guide](installation.md) - Detailed installation instructions
- [Configuration](configuration.md) - Environment and settings configuration
- [API Documentation](api.md) - REST API endpoints and usage
- [Customization Guide](customization.md) - Theming and template customization
- [Deployment](deployment.md) - Production deployment guide
- [Development](development.md) - Development setup and guidelines
- [Testing](testing.md) - Testing framework and best practices
- [Troubleshooting](troubleshooting.md) - Common issues and solutions

## Architecture

The system is built using:

- **Backend**: Django 5.1 with PostgreSQL
- **Frontend**: Tailwind CSS + DatastarJS for reactive components
- **Email**: django-herald for notification management
- **Payments**: Lemon Squeezy integration for subscriptions and payments
- **PDF Generation**: WeasyPrint for document generation
- **Testing**: pytest with comprehensive test coverage
- **Deployment**: Docker-ready with production configurations

## Core Apps

- `accounts` - User authentication and management
- `businesses` - Multi-tenant business management
- `clients` - Client profiles and preferences
- `quotes` - Quote creation and management
- `bookings` - Tour scheduling and capacity management
- `invoices` - Invoice generation and tracking
- `payments` - Payment processing and subscription management
- `documents` - PDF generation and template management

## Contributing

Please read our [Development Guide](development.md) for information on our development process and coding standards.

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Documentation: Check the `/docs` folder
- Issues: Create an issue on the repository
- Email: Contact the development team

## Changelog

See [CHANGELOG.md](CHANGELOG.md) for version history and updates.
