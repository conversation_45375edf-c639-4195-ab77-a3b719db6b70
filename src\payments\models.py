"""
Payment models for handling invoice payments with Lemon Squeezy integration.
"""

import uuid

from django.conf import settings
from django.core.validators import MinValueValidator
from django.db import models
from django.forms import ValidationError
from django.utils.translation import gettext_lazy as _
from djmoney.models.fields import MoneyField

from bookings.models import Booking
from core.mixins import BaseModel, UserAuditModel


class PaymentMethod(models.TextChoices):
    LEMON_SQUEEZY = 'lemon_squeezy', _('Lemon Squeezy')
    CREDIT_CARD = 'credit_card', _('Credit Card')
    BANK_TRANSFER = 'bank_transfer', _('Bank Transfer')
    CASH = 'cash', _('Cash')
    CHECK = 'check', _('Check')
    OTHER = 'other', _('Other')


class PaymentManager(models.Manager):
    """Optimized manager for Payment model."""

    def get_queryset(self):
        """Return optimized queryset."""
        return (
            super()
            .get_queryset()
            .select_related(
                'invoice', 'invoice__business', 'invoice__client', 'created_by'
            )
        )

    def for_business(self, business):
        """Filter payments by business."""
        return self.filter(invoice__business=business)

    def successful(self):
        """Return successful payments."""
        return self.filter(status=self.model.Status.COMPLETED)

    def pending(self):
        """Return pending payments."""
        return self.filter(status=self.model.Status.PENDING)


class Payment(UserAuditModel):
    """
    Payment model for tracking invoice payments via Lemon Squeezy.
    """

    class Status(models.TextChoices):
        PENDING = 'pending', _('Pending')
        PROCESSING = 'processing', _('Processing')
        COMPLETED = 'completed', _('Completed')
        FAILED = 'failed', _('Failed')
        CANCELLED = 'cancelled', _('Cancelled')
        REFUNDED = 'refunded', _('Refunded')

    # Relationships
    invoice = models.ForeignKey(
        'invoices.Invoice',
        on_delete=models.CASCADE,
        related_name='payments',
        help_text=_('Invoice this payment is for'),
    )

    # Payment Information
    payment_id = models.UUIDField(
        default=uuid.uuid4, unique=True, help_text=_('Unique payment identifier')
    )

    amount = MoneyField(
        max_digits=14,
        decimal_places=2,
        default_currency='USD',
        help_text=_('Payment amount'),
    )

    payment_method = models.CharField(
        max_length=20,
        choices=PaymentMethod.choices,
        default=PaymentMethod.LEMON_SQUEEZY,
        help_text=_('Payment method used'),
    )

    status = models.CharField(
        max_length=20,
        choices=Status.choices,
        default=Status.PENDING,
        help_text=_('Current payment status'),
    )

    # Lemon Squeezy Integration
    lemon_squeezy_order_id = models.CharField(
        max_length=255,
        blank=True,
        verbose_name=_('Lemon Squeezy order ID'),
    )

    lemon_squeezy_checkout_id = models.CharField(
        max_length=255,
        blank=True,
        verbose_name=_('Lemon Squeezy checkout ID'),
    )

    # External Payment Information
    external_payment_id = models.CharField(
        max_length=255,
        blank=True,
        help_text=_('External payment processor ID'),
    )

    payment_processor = models.CharField(
        max_length=50,
        default='lemon_squeezy',
        help_text=_('Payment processor used'),
    )

    # Payment Details
    payment_date = models.DateTimeField(
        null=True,
        blank=True,
        help_text=_('When the payment was completed'),
    )

    reference_number = models.CharField(
        max_length=100,
        blank=True,
        help_text=_('Payment reference number'),
    )

    notes = models.TextField(
        blank=True,
        help_text=_('Additional payment notes'),
    )

    # Custom manager
    objects = PaymentManager()

    class Meta:
        db_table = 'payments_payment'
        verbose_name = _('Payment')
        verbose_name_plural = _('Payments')
        indexes = [
            models.Index(fields=['invoice', 'status']),
            models.Index(fields=['payment_method']),
            models.Index(fields=['status']),
            models.Index(fields=['lemon_squeezy_order_id']),
            models.Index(fields=['payment_date']),
        ]

    def __str__(self):
        return f'Payment {self.payment_id} - {self.invoice.invoice_number}'

    @property
    def business(self):
        """Get the business this payment belongs to."""
        return self.invoice.business

    @property
    def client(self):
        """Get the client this payment is from."""
        return self.invoice.client

    def mark_as_completed(self):
        """Mark payment as completed and update invoice."""
        from django.utils import timezone

        self.status = self.Status.COMPLETED
        self.payment_date = timezone.now()
        self.save()

        # Check if invoice is fully paid
        total_paid = (
            self.invoice.payments.successful().aggregate(total=models.Sum('amount'))[
                'total'
            ]
            or 0
        )

        if total_paid >= self.invoice.total_amount.amount:
            self.invoice.mark_as_paid()

    def mark_as_failed(self, reason=None):
        """Mark payment as failed."""
        self.status = self.Status.FAILED
        if reason:
            self.notes = f'{self.notes}\nFailure reason: {reason}'.strip()
        self.save()


class PaymentLink(models.Model):
    """
    Payment link model for generating secure Lemon Squeezy checkout URLs.
    """

    class Status(models.TextChoices):
        ACTIVE = 'active', _('Active')
        EXPIRED = 'expired', _('Expired')
        USED = 'used', _('Used')
        CANCELLED = 'cancelled', _('Cancelled')

    # Relationships
    invoice = models.OneToOneField(
        'invoices.Invoice',
        on_delete=models.CASCADE,
        related_name='payment_link',
        help_text=_('Invoice this payment link is for'),
    )

    # Link Information
    link_id = models.UUIDField(
        default=uuid.uuid4, unique=True, help_text=_('Unique payment link identifier')
    )

    status = models.CharField(
        max_length=20,
        choices=Status.choices,
        default=Status.ACTIVE,
        help_text=_('Current link status'),
    )

    expires_at = models.DateTimeField(help_text=_('When this payment link expires'))

    # Lemon Squeezy Integration
    lemon_squeezy_checkout_url = models.URLField(
        blank=True, help_text=_('Lemon Squeezy checkout URL')
    )

    lemon_squeezy_checkout_id = models.CharField(
        max_length=255, blank=True, help_text=_('Lemon Squeezy checkout ID')
    )

    # Usage tracking
    access_count = models.PositiveIntegerField(
        default=0, help_text=_('Number of times this link has been accessed')
    )

    last_accessed_at = models.DateTimeField(
        null=True, blank=True, help_text=_('When this link was last accessed')
    )

    # Audit fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name='created_payment_links',
        help_text=_('User who created this payment link'),
    )

    class Meta:
        db_table = 'payments_payment_link'
        verbose_name = _('Payment Link')
        verbose_name_plural = _('Payment Links')
        indexes = [
            models.Index(fields=['status']),
            models.Index(fields=['expires_at']),
            models.Index(fields=['lemon_squeezy_checkout_id']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f'Payment Link for {self.invoice.invoice_number}'

    @property
    def is_expired(self):
        """Check if the payment link has expired."""
        from django.utils import timezone

        return timezone.now() > self.expires_at

    @property
    def is_active(self):
        """Check if the payment link is active and usable."""
        return (
            self.status == self.Status.ACTIVE
            and not self.is_expired
            and self.invoice.status in ['sent', 'overdue']
        )

    def get_payment_url(self):
        """Get the payment URL (Lemon Squeezy checkout or internal)."""
        if self.lemon_squeezy_checkout_url:
            return self.lemon_squeezy_checkout_url

        from django.urls import reverse

        return reverse('payments:pay', kwargs={'link_id': self.link_id})

    def mark_as_used(self):
        """Mark the payment link as used."""
        self.status = self.Status.USED
        self.save()

    def track_access(self):
        """Track access to this payment link."""
        from django.utils import timezone

        self.access_count += 1
        self.last_accessed_at = timezone.now()
        self.save()


class Subscription(BaseModel):
    """
    Subscription model for tracking business subscriptions via Lemon Squeezy.
    """

    class Status(models.TextChoices):
        ACTIVE = 'active', _('Active')
        CANCELLED = 'cancelled', _('Cancelled')
        EXPIRED = 'expired', _('Expired')
        PAST_DUE = 'past_due', _('Past Due')
        PAUSED = 'paused', _('Paused')
        UNPAID = 'unpaid', _('Unpaid')

    class Plan(models.TextChoices):
        BASIC = 'basic', _('Basic Plan')
        PROFESSIONAL = 'professional', _('Professional Plan')
        ENTERPRISE = 'enterprise', _('Enterprise Plan')

    # Relationships
    business = models.OneToOneField(
        'businesses.Business',
        on_delete=models.CASCADE,
        related_name='subscription',
        help_text=_('Business this subscription belongs to'),
    )

    # Subscription Information
    subscription_id = models.UUIDField(
        default=uuid.uuid4,
        unique=True,
        help_text=_('Unique subscription identifier'),
    )

    plan = models.CharField(
        max_length=20,
        choices=Plan.choices,
        default=Plan.BASIC,
        help_text=_('Subscription plan'),
    )

    status = models.CharField(
        max_length=20,
        choices=Status.choices,
        default=Status.ACTIVE,
        help_text=_('Current subscription status'),
    )

    # Lemon Squeezy Integration
    lemon_squeezy_subscription_id = models.CharField(
        max_length=255,
        blank=True,
        help_text=_('Lemon Squeezy subscription ID'),
    )

    lemon_squeezy_customer_id = models.CharField(
        max_length=255,
        blank=True,
        help_text=_('Lemon Squeezy customer ID'),
    )

    # Subscription Details
    current_period_start = models.DateTimeField(
        null=True,
        blank=True,
        help_text=_('Start of current billing period'),
    )

    current_period_end = models.DateTimeField(
        null=True,
        blank=True,
        help_text=_('End of current billing period'),
    )

    trial_end = models.DateTimeField(
        null=True,
        blank=True,
        help_text=_('End of trial period'),
    )

    # Pricing
    monthly_price = MoneyField(
        max_digits=10,
        decimal_places=2,
        default_currency='USD',
        null=True,
        blank=True,
        help_text=_('Monthly subscription price'),
    )

    class Meta:
        db_table = 'payments_subscription'
        verbose_name = _('Subscription')
        verbose_name_plural = _('Subscriptions')
        indexes = [
            models.Index(fields=['status']),
            models.Index(fields=['plan']),
            models.Index(fields=['lemon_squeezy_subscription_id']),
            models.Index(fields=['current_period_end']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f'{self.business.name} - {self.get_plan_display()} ({self.get_status_display()})'

    @property
    def is_active(self):
        """Check if subscription is active."""
        return self.status == self.Status.ACTIVE

    @property
    def is_trial(self):
        """Check if subscription is in trial period."""
        from django.utils import timezone

        return (
            self.trial_end
            and timezone.now() < self.trial_end
            and self.status == self.Status.ACTIVE
        )

    @property
    def days_until_renewal(self):
        """Get days until next renewal."""
        if not self.current_period_end:
            return None

        from django.utils import timezone

        delta = self.current_period_end - timezone.now()
        return delta.days if delta.days > 0 else 0


class Refund(models.Model):
    payment = models.OneToOneField(Payment, on_delete=models.CASCADE)
    amount = MoneyField(max_digits=10, decimal_places=2)


class BookingPayment(models.Model):
    """Links payments to bookings (for partial payments)"""

    booking = models.ForeignKey(
        Booking, related_name='payments', on_delete=models.CASCADE
    )
    amount = MoneyField(
        max_digits=10, decimal_places=2, validators=[MinValueValidator(0.01)]
    )
    payment_date = models.DateTimeField(auto_now_add=True)
    payment_method = models.CharField(max_length=50, choices=PaymentMethod)
    transaction_id = models.CharField(max_length=255, unique=True)
    is_deposit = models.BooleanField(default=False)

    def clean(self):
        # prevent overpayment
        total_paid = (
            self.booking.payments.aggregrate(total=models.Sum('amount'))['total'] or 0
        )
        if (
            total_paid + self.amount
            > self.booking.tour_instance.tour_template.pricing_rules.get('base', 0)
            * self.booking.number_of_participants
        ):
            raise ValidationError(_('Payment exceeds total booking amount'))
