{% load i18n %}

<c-layouts.base>
  <c-slot name="title">{% trans "Login" %}</c-slot>

  <c-slot name="content">
    <div class="flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div class="max-w-md w-full space-y-8">
        <div>
          <div class="mx-auto h-12 w-auto flex justify-center">
            <svg class="h-12 w-12 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
            </svg>
          </div>
          <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
            {% trans "Sign in to your account" %}
          </h2>
          <p class="mt-2 text-center text-sm text-gray-600">
            {% trans "Or" %}
            <a href="{% url 'accounts:register' %}" class="font-medium text-blue-600 hover:text-blue-500">
              {% trans "create a new account" %}
            </a>
          </p>
        </div>

        <form class="mt-8 space-y-6" method="post">
          {% csrf_token %}

          {% if form.errors %}
          <div class="bg-red-50 border border-red-200 rounded-md p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                    clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">
                  {% trans "There were errors with your login" %}
                </h3>
                <div class="mt-2 text-sm text-red-700">
                  <ul class="list-disc pl-5 space-y-1">
                    {% for field, errors in form.errors.items %}
                    {% for error in errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                    {% endfor %}
                  </ul>
                </div>
              </div>
            </div>
          </div>
          {% endif %}

          <div class="rounded-md shadow-sm -space-y-px">
            <div>
              <label for="{{ form.username.id_for_label }}" class="sr-only">{% trans "Username" %}</label>
              <input id="{{ form.username.id_for_label }}" name="{{ form.username.name }}" type="text" required
                class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                placeholder="{% trans 'Username' %}" value="{{ form.username.value|default:'' }}">
            </div>
            <div>
              <label for="{{ form.password.id_for_label }}" class="sr-only">{% trans "Password" %}</label>
              <input id="{{ form.password.id_for_label }}" name="{{ form.password.name }}" type="password" required
                class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                placeholder="{% trans 'Password' %}">
            </div>
          </div>

          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <input id="remember-me" name="remember-me" type="checkbox"
                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
              <label for="remember-me" class="ml-2 block text-sm text-gray-900">
                {% trans "Remember me" %}
              </label>
            </div>

            <div class="text-sm">
              <a href="{% url 'accounts:password_reset' %}" class="font-medium text-blue-600 hover:text-blue-500">
                {% trans "Forgot your password?" %}
              </a>
            </div>
          </div>

          <div>
            <button type="submit"
              class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
              <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                <svg class="h-5 w-5 text-white group-hover:text-white" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd"
                    d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z"
                    clip-rule="evenodd" />
                </svg>
              </span>
              {% trans "Sign in" %}
            </button>
          </div>

          <div class="text-center">
            <p class="text-sm text-gray-600">
              {% trans "Don't have an account?" %}
              <a href="{% url 'accounts:register' %}" class="font-medium text-blue-600 hover:text-blue-500">
                {% trans "Sign up here" %}
              </a>
            </p>
          </div>
        </form>
      </div>
    </div>
  </c-slot name="content">
</c-layouts.base>
