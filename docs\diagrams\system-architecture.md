# Tour Management SaaS - System Architecture Overview

This diagram shows the complete system architecture including all layers, services, and external integrations.

## Diagram

```mermaid
graph TB
    %% Frontend Layer
    subgraph "Frontend Layer"
        UI[Web Interface]
        Mobile[Mobile Responsive]
        PDF[PDF Generation]
    end

    %% Application Layer
    subgraph "Application Layer"
        Auth[Authentication & Authorization]
        Business[Business Logic]
        API[REST API]
        Views[Django Views]
    end

    %% Core Services
    subgraph "Core Services"
        UserMgmt[User Management]
        ClientMgmt[Client Management]
        QuoteMgmt[Quote Management]
        BookingMgmt[Booking Management]
        InvoiceMgmt[Invoice Management]
        PaymentMgmt[Payment Management]
        CalendarMgmt[Calendar Management]
        DocumentMgmt[Document Management]
    end

    %% Data Layer
    subgraph "Data Layer"
        Models[Django Models]
        DB[(SQLite Database)]
        Files[File Storage]
    end

    %% External Services
    subgraph "External Services"
        EmailSvc[Email Service]
        PaymentGW[Payment Gateway<br/>Lemon Squeezy]
        Storage[Media Storage]
    end

    %% Security & Infrastructure
    subgraph "Security & Infrastructure"
        RBAC[Role-Based Access Control]
        MultiTenant[Multi-Tenant Isolation]
        Audit[Audit Logging]
        Backup[Data Backup]
    end

    %% Connections
    UI --> Views
    Mobile --> Views
    PDF --> DocumentMgmt

    Views --> Auth
    Views --> API
    API --> Business
    Auth --> RBAC

    Business --> UserMgmt
    Business --> ClientMgmt
    Business --> QuoteMgmt
    Business --> BookingMgmt
    Business --> InvoiceMgmt
    Business --> PaymentMgmt
    Business --> CalendarMgmt
    Business --> DocumentMgmt

    UserMgmt --> Models
    ClientMgmt --> Models
    QuoteMgmt --> Models
    BookingMgmt --> Models
    InvoiceMgmt --> Models
    PaymentMgmt --> Models
    CalendarMgmt --> Models
    DocumentMgmt --> Models

    Models --> DB
    DocumentMgmt --> Files

    PaymentMgmt --> PaymentGW
    UserMgmt --> EmailSvc
    QuoteMgmt --> EmailSvc
    InvoiceMgmt --> EmailSvc

    Files --> Storage

    RBAC --> MultiTenant
    Models --> Audit
    DB --> Backup

    %% Styling - High Contrast Colors
    classDef frontend fill:#ffffff,stroke:#1565c0,stroke-width:3px,color:#000000
    classDef application fill:#e8f5e8,stroke:#1b5e20,stroke-width:3px,color:#000000
    classDef services fill:#fff8e1,stroke:#e65100,stroke-width:3px,color:#000000
    classDef data fill:#f3e5f5,stroke:#6a1b9a,stroke-width:3px,color:#000000
    classDef external fill:#e1f5fe,stroke:#01579b,stroke-width:3px,color:#000000
    classDef security fill:#ffcdd2,stroke:#b71c1c,stroke-width:3px,color:#000000

    class UI,Mobile,PDF frontend
    class Auth,Business,API,Views application
    class UserMgmt,ClientMgmt,QuoteMgmt,BookingMgmt,InvoiceMgmt,PaymentMgmt,CalendarMgmt,DocumentMgmt services
    class Models,DB,Files data
    class EmailSvc,PaymentGW,Storage external
    class RBAC,MultiTenant,Audit,Backup security
```

## Architecture Layers

### 1. Frontend Layer

**User Interface and Presentation**

#### Components:

- **Web Interface**: Django templates with Tailwind CSS
- **Mobile Responsive**: Responsive design for all devices
- **PDF Generation**: ReportLab for document creation

#### Technologies:

- Django Templates
- Tailwind CSS
- DatastarJS for interactivity
- ReportLab for PDF generation
- Responsive design patterns

### 2. Application Layer

**Request Processing and Business Logic Coordination**

#### Components:

- **Authentication & Authorization**: User login and permissions
- **Business Logic**: Core application logic
- **REST API**: API endpoints for external access
- **Django Views**: Request/response handling

#### Technologies:

- Django Auth system
- Django Guardian for RBAC
- Django REST Framework
- Custom business logic modules

### 3. Core Services

**Business Domain Services**

#### Services:

- **User Management**: Account and profile management
- **Client Management**: Customer relationship management
- **Quote Management**: Pricing and proposal system
- **Booking Management**: Reservation and scheduling
- **Invoice Management**: Billing and financial documents
- **Payment Management**: Payment processing and tracking
- **Calendar Management**: Event scheduling and management
- **Document Management**: File and document handling

#### Features:

- Service-oriented architecture
- Business logic encapsulation
- Cross-service communication
- Data validation and processing

### 4. Data Layer

**Data Persistence and Management**

#### Components:

- **Django Models**: ORM and data modeling
- **SQLite Database**: Primary data storage
- **File Storage**: Media and document storage

#### Features:

- Multi-tenant data isolation
- Optimized queries and indexing
- Data integrity constraints
- Audit trail support

### 5. External Services

**Third-Party Integrations**

#### Services:

- **Email Service**: Transactional email delivery
- **Payment Gateway**: Lemon Squeezy integration
- **Media Storage**: File and image storage

#### Integration Points:

- RESTful API communication
- Webhook handling
- Secure credential management
- Error handling and retry logic

### 6. Security & Infrastructure

**Security, Monitoring, and Operations**

#### Components:

- **Role-Based Access Control**: Permission management
- **Multi-Tenant Isolation**: Data separation
- **Audit Logging**: Action tracking
- **Data Backup**: Data protection

#### Features:

- Comprehensive security model
- Complete audit trails
- Data protection and privacy
- Disaster recovery capabilities

## Technology Stack

### Backend Framework

- **Django 5.2+**: Web framework
- **Python 3.12+**: Programming language
- **SQLite**: Database (dev and prod)
- **Django ORM**: Object-relational mapping

### Frontend Technologies

- **Django Templates**: Server-side rendering
- **Tailwind CSS**: Utility-first CSS framework
- **DatastarJS**: Progressive enhancement
- **Cotton**: Component library

### Key Dependencies

- **django-guardian**: Object-level permissions
- **django-money**: Multi-currency support
- **Custom Event Models**: Calendar management
- **reportlab**: PDF generation
- **python-decouple**: Environment management

### External Integrations

- **Lemon Squeezy**: Payment processing
- **Email Service**: Transactional emails
- **File Storage**: Media management

## Architectural Principles

### 1. Multi-Tenancy

- Business-level data isolation
- Shared application, separated data
- Scalable tenant management
- Secure cross-tenant protection

### 2. Service-Oriented Design

- Modular service architecture
- Clear service boundaries
- Loose coupling between services
- High cohesion within services

### 3. Security-First Approach

- Role-based access control
- Data encryption and protection
- Audit logging for compliance
- Secure external integrations

### 4. Scalability Considerations

- Efficient database queries
- Optimized data models
- Caching strategies
- Performance monitoring

### 5. Maintainability

- Clean code architecture
- Comprehensive testing
- Documentation standards
- Version control practices

## Deployment Architecture

### Development Environment

- Local SQLite database
- Django development server
- Local file storage
- Debug mode enabled

### Production Environment

- SQLite database (optimized)
- WSGI server deployment
- Cloud file storage
- Production security settings

### Security Measures

- Environment-based configuration
- Secure secret management
- HTTPS enforcement
- CSRF protection
- SQL injection prevention

## Monitoring and Logging

### Application Logging

- Django logging framework
- Structured log formats
- Error tracking and alerting
- Performance monitoring

### Audit Logging

- User action tracking
- Data change history
- Security event logging
- Compliance reporting

### Health Monitoring

- Application health checks
- Database performance monitoring
- External service monitoring
- Uptime tracking
