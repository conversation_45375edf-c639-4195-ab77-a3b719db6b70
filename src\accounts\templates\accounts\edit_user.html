{% load i18n %}

<c-layouts.base>
  <c-slot name="title">{% trans "Edit User" %} - Tour Business Management</c-slot>

  <c-slot name="content">
    <div class="container mx-auto px-4 py-8">
      <div class="max-w-2xl mx-auto">
        <!-- Header -->
        <div class="flex justify-between items-center mb-6">
          <div>
            <h1 class="text-3xl font-bold text-gray-900">{% trans "Edit User" %}</h1>
            <p class="text-gray-600 mt-1">{% trans "Update user information and role" %}</p>
          </div>
          <a href="{% url 'accounts:user_management' %}"
            class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium">
            {% trans "Back to Users" %}
          </a>
        </div>

        <div class="bg-white shadow rounded-lg p-6">
          <form method="post" class="space-y-6">
            {% csrf_token %}

            <!-- Display form errors -->
            {% if form.non_field_errors %}
            <div class="bg-red-50 border border-red-200 rounded-md p-4">
              <div class="text-sm text-red-600">
                {{ form.non_field_errors }}
              </div>
            </div>
            {% endif %}

            <!-- Username -->
            <div>
              <label for="{{ form.username.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                {{ form.username.label }}
              </label>
              {{ form.username }}
              {% if form.username.errors %}
              <p class="mt-1 text-sm text-red-600">{{ form.username.errors.0 }}</p>
              {% endif %}
              {% if form.username.help_text %}
              <p class="mt-1 text-sm text-gray-500">{{ form.username.help_text }}</p>
              {% endif %}
            </div>

            <!-- Email -->
            <div>
              <label for="{{ form.email.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                {{ form.email.label }}
              </label>
              {{ form.email }}
              {% if form.email.errors %}
              <p class="mt-1 text-sm text-red-600">{{ form.email.errors.0 }}</p>
              {% endif %}
              {% if form.email.help_text %}
              <p class="mt-1 text-sm text-gray-500">{{ form.email.help_text }}</p>
              {% endif %}
            </div>

            <!-- Role -->
            <div>
              <label for="{{ form.role.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                {{ form.role.label }}
              </label>
              {{ form.role }}
              {% if form.role.errors %}
              <p class="mt-1 text-sm text-red-600">{{ form.role.errors.0 }}</p>
              {% endif %}
              {% if form.role.help_text %}
              <p class="mt-1 text-sm text-gray-500">{{ form.role.help_text }}</p>
              {% endif %}
            </div>

            <!-- User Status Info -->
            <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
              <h3 class="text-sm font-medium text-gray-900 mb-2">{% trans "User Status" %}</h3>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <span class="text-gray-500">{% trans "Status:" %}</span>
                  {% if object.is_active %}
                  <span
                    class="ml-2 inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                    {% trans "Active" %}
                  </span>
                  {% else %}
                  <span
                    class="ml-2 inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                    {% trans "Pending" %}
                  </span>
                  {% endif %}
                </div>
                <div>
                  <span class="text-gray-500">{% trans "Joined:" %}</span>
                  <span class="ml-2 text-gray-900">{{ object.date_joined|date:"M d, Y" }}</span>
                </div>
                <div>
                  <span class="text-gray-500">{% trans "Email Confirmed:" %}</span>
                  {% if object.email_confirmed %}
                  <span class="ml-2 text-green-600">{% trans "Yes" %}</span>
                  {% else %}
                  <span class="ml-2 text-red-600">{% trans "No" %}</span>
                  {% endif %}
                </div>
                <div>
                  <span class="text-gray-500">{% trans "Last Login:" %}</span>
                  <span class="ml-2 text-gray-900">
                    {% if object.last_login %}
                    {{ object.last_login|date:"M d, Y H:i" }}
                    {% else %}
                    {% trans "Never" %}
                    {% endif %}
                  </span>
                </div>
              </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex justify-between pt-6">
              <div class="flex space-x-3">
                {% if not object.is_active %}
                <form method="post" action="{% url 'accounts:resend_invite' object.pk %}" class="inline">
                  {% csrf_token %}
                  <button type="submit"
                    class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                    {% trans "Resend Invitation" %}
                  </button>
                </form>
                {% endif %}

                {% if object != request.user %}
                <button type="button" onclick="confirmRemoveUser()"
                  class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                  {% trans "Remove User" %}
                </button>
                {% endif %}
              </div>

              <div class="flex space-x-3">
                <a href="{% url 'accounts:user_management' %}"
                  class="bg-gray-300 hover:bg-gray-400 text-gray-800 px-6 py-2 rounded-md text-sm font-medium">
                  {% trans "Cancel" %}
                </a>
                <button type="submit"
                  class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md text-sm font-medium">
                  {% trans "Save Changes" %}
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- Remove User Confirmation Modal -->
    <div id="removeUserModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
      <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
          <div class="p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <svg class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-lg font-medium text-gray-900">{% trans "Remove User" %}</h3>
                <div class="mt-2">
                  <p class="text-sm text-gray-500">
                    {% trans "Are you sure you want to remove this user? This action cannot be undone." %}
                  </p>
                </div>
              </div>
            </div>
          </div>
          <div class="bg-gray-50 px-6 py-3 flex justify-end space-x-3">
            <button type="button" onclick="closeRemoveModal()"
              class="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-50">
              {% trans "Cancel" %}
            </button>
            <form method="post" action="{% url 'accounts:remove_user' object.pk %}" class="inline">
              {% csrf_token %}
              <button type="submit"
                class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                {% trans "Remove" %}
              </button>
            </form>
          </div>
        </div>
      </div>
    </div>

    <script>
      function confirmRemoveUser() {
        document.getElementById('removeUserModal').classList.remove('hidden');
      }

      function closeRemoveModal() {
        document.getElementById('removeUserModal').classList.add('hidden');
      }

      // Close modal when clicking outside
      document.getElementById('removeUserModal').addEventListener('click', function (e) {
        if (e.target === this) {
          closeRemoveModal();
        }
      });
    </script>
  </c-slot>
</c-layouts.base>
