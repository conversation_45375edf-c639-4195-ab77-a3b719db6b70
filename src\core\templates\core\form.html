{% load crispy_forms_tags %}

<c-layouts.base>
  <c-slot name="title">
    Test Phone Number
  </c-slot>

  <c-slot name="extra_head">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/intl-tel-input@25.3.2/build/css/intlTelInput.css">
  </c-slot>

  <c-slot name="content">
    <form class="container p-6" method="post">
      {% csrf_token %}
      {% crispy form %}

      <h2>Look at checkbox</h2>

      <button type="submit">
        Submit
      </button>
    </form>

    <c-slot name="extra_scripts">
      <script src="https://cdn.jsdelivr.net/npm/intl-tel-input@25.3.2/build/js/intlTelInput.min.js"></script>
      <script>
        const input = document.querySelector("#id_phone_number");
        window.intlTelInput(input, {
          loadUtils: () => import("https://cdn.jsdelivr.net/npm/intl-tel-input@25.3.2/build/js/utils.js"),
        });
      </script>
    </c-slot>
  </c-slot>
</c-layouts.base>
