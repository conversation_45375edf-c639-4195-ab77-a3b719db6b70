"""
Demo system for anonymous users to explore the tour business management platform.
Provides temporary access with sample data that auto-expires.
"""

import logging
import uuid
from datetime import datetime, timedelta

from django.conf import settings
from django.contrib.auth import get_user_model
from django.contrib.sessions.models import Session
from django.db import transaction
from django.utils import timezone

from businesses.models import Business
from data_generator.services import DataGeneratorService

User = get_user_model()
logger = logging.getLogger(__name__)


class DemoSession:
    """Manages demo sessions for anonymous users."""

    # Demo session duration (in hours)
    DEMO_DURATION_HOURS = getattr(settings, 'DEMO_DURATION_HOURS', 2)

    # Session keys
    DEMO_SESSION_KEY = 'demo_session_id'
    DEMO_USER_KEY = 'demo_user_id'
    DEMO_BUSINESS_KEY = 'demo_business_id'
    DEMO_CREATED_KEY = 'demo_created_at'
    DEMO_EXPIRES_KEY = 'demo_expires_at'

    def __init__(self, request):
        self.request = request
        self.session = request.session

    @property
    def is_demo_session(self):
        """Check if current session is a demo session."""
        return self.DEMO_SESSION_KEY in self.session

    @property
    def is_expired(self):
        """Check if demo session has expired."""
        if not self.is_demo_session:
            return False

        expires_at = self.session.get(self.DEMO_EXPIRES_KEY)
        if not expires_at:
            return True

        # Parse the stored datetime string
        try:
            expires_datetime = datetime.fromisoformat(expires_at)
            return timezone.now() > expires_datetime
        except (ValueError, TypeError):
            return True

    @property
    def demo_user(self):
        """Get the demo user for this session."""
        if not self.is_demo_session:
            return None

        user_id = self.session.get(self.DEMO_USER_KEY)
        if not user_id:
            return None

        try:
            return User.objects.get(id=user_id)
        except User.DoesNotExist:
            return None

    @property
    def demo_business(self):
        """Get the demo business for this session."""
        if not self.is_demo_session:
            return None

        business_id = self.session.get(self.DEMO_BUSINESS_KEY)
        if not business_id:
            return None

        try:
            return Business.objects.get(id=business_id)
        except Business.DoesNotExist:
            return None

    @property
    def time_remaining(self):
        """Get remaining time for demo session."""
        if not self.is_demo_session:
            return timedelta(0)

        expires_at = self.session.get(self.DEMO_EXPIRES_KEY)
        if not expires_at:
            return timedelta(0)

        try:
            expires_datetime = datetime.fromisoformat(expires_at)
            remaining = expires_datetime - timezone.now()
            return remaining if remaining.total_seconds() > 0 else timedelta(0)
        except (ValueError, TypeError):
            return timedelta(0)

    def create_demo_session(self):
        """Create a new demo session with sample data."""
        if self.is_demo_session and not self.is_expired:
            logger.info('Demo session already exists and is valid')
            return self.demo_user, self.demo_business

        # Clean up any existing expired demo session
        if self.is_demo_session and self.is_expired:
            self.cleanup_demo_session()

        try:
            with transaction.atomic():
                # Generate unique demo session ID
                demo_session_id = str(uuid.uuid4())

                # Create demo user
                demo_user = self._create_demo_user(demo_session_id)

                # Create demo business and assign to user
                demo_business = self._create_demo_business(demo_user, demo_session_id)
                demo_user.business = demo_business
                demo_user.save()

                # Generate sample data
                self._generate_demo_data(demo_business, demo_user)

                # Set up session
                expires_at = timezone.now() + timedelta(hours=self.DEMO_DURATION_HOURS)

                self.session[self.DEMO_SESSION_KEY] = demo_session_id
                self.session[self.DEMO_USER_KEY] = str(demo_user.id)
                self.session[self.DEMO_BUSINESS_KEY] = str(demo_business.id)
                self.session[self.DEMO_CREATED_KEY] = timezone.now().isoformat()
                self.session[self.DEMO_EXPIRES_KEY] = expires_at.isoformat()
                self.session.save()

                logger.info(
                    f'Created demo session {demo_session_id} for user {demo_user.username}'
                )
                return demo_user, demo_business

        except Exception as e:
            logger.error(f'Failed to create demo session: {str(e)}')
            raise

    def cleanup_demo_session(self):
        """Clean up current demo session and associated data."""
        if not self.is_demo_session:
            return

        demo_session_id = self.session.get(self.DEMO_SESSION_KEY)
        demo_user = self.demo_user
        demo_business = self.demo_business

        try:
            with transaction.atomic():
                # First, remove the business reference from the user to avoid protected foreign key
                if demo_user and demo_business:
                    demo_user.business = None
                    demo_user.save()
                    logger.info(
                        f'Removed business reference from demo user {demo_user.username}'
                    )

                # Delete PDFTemplates created by the demo user to avoid protected foreign key
                if demo_user:
                    from templates_manager.models import PDFTemplate

                    pdf_templates = PDFTemplate.objects.filter(created_by=demo_user)
                    template_count = pdf_templates.count()
                    pdf_templates.delete()
                    logger.info(
                        f'Deleted {template_count} PDF templates created by demo user'
                    )

                # Delete demo business (cascades to related data)
                if demo_business:
                    demo_business.delete()
                    logger.info(f'Deleted demo business {demo_business.name}')

                # Delete demo user
                if demo_user:
                    demo_user.delete()
                    logger.info(f'Deleted demo user {demo_user.username}')

                # Clear session data
                for key in [
                    self.DEMO_SESSION_KEY,
                    self.DEMO_USER_KEY,
                    self.DEMO_BUSINESS_KEY,
                    self.DEMO_CREATED_KEY,
                    self.DEMO_EXPIRES_KEY,
                ]:
                    self.session.pop(key, None)

                self.session.save()
                logger.info(f'Cleaned up demo session {demo_session_id}')

        except Exception as e:
            logger.error(f'Failed to cleanup demo session: {str(e)}')

    def _create_demo_user(self, demo_session_id):
        """Create a demo user."""
        username = f'demo_{demo_session_id[:8]}'
        email = f'demo_{demo_session_id[:8]}@demo.local'

        # Create user with a random password for authentication
        user = User.objects.create_user(
            username=username,
            email=email,
            password=f'demo_password_{demo_session_id}',  # Random password for demo users
            first_name='Demo',
            last_name='User',
            role=Profile.Role.BUSINESS_OWNER,
            is_active=True,
            email_confirmed=True,
        )

        # Mark as demo user for easy identification
        user.is_demo_user = True
        user.save()

        logger.info(f'Created demo user: {username} with ID: {user.id}')
        return user

    def _create_demo_business(self, demo_user, demo_session_id):
        """Create a demo business."""
        business_name = f'Demo Tours {demo_session_id[:8]}'

        business = Business.objects.create(
            name=business_name,
            description='A sample tour business for demonstration purposes',
            email=f'info@demo{demo_session_id[:8]}.local',
            phone='******-DEMO-123',
            website='https://demo.tourflow.com',
            address_line1='123 Demo Street',
            city='Demo City',
            state_province='Demo State',
            postal_code='12345',
            country='Demo Country',
            created_by=demo_user,
        )

        logger.info(f'Created demo business: {business_name} with ID: {business.id}')
        return business

    def _generate_demo_data(self, business, user):
        """Generate sample data for the demo business."""
        logger.info(f'Starting demo data generation for business {business.id}')

        try:
            # Use the existing data generator with a small scale
            service = DataGeneratorService()

            # Generate demo data specifically for this business
            logger.info('Generating clients...')
            clients = service.clients_generator.generate_clients(5, [business])
            logger.info(f'Generated {len(clients)} clients')

            logger.info('Generating travelers...')
            travelers = service.clients_generator.generate_travelers(10, clients)
            logger.info(f'Generated {len(travelers)} travelers')

            logger.info('Generating quotes...')
            quotes = service.quotes_generator.generate_quotes(3, [business], clients)
            logger.info(f'Generated {len(quotes)} quotes')

            # Generate some events and bookings

            logger.info('Generating events...')
            events = service.generate_events(4, [business], clients)
            logger.info(f'Generated {len(events)} events')

            logger.info('Generating bookings...')
            bookings = service.generate_bookings(6, events, clients, travelers)
            logger.info(f'Generated {len(bookings)} bookings')

            # Generate invoices and payments
            logger.info('Generating invoices...')
            invoices = service.invoices_generator.generate_invoices(2, quotes)
            logger.info(f'Generated {len(invoices)} invoices')

            logger.info('Generating payments...')
            payments = service.payments_generator.generate_payments(3, invoices)
            logger.info(f'Generated {len(payments)} payments')

            # Generate PDF templates
            logger.info('Generating PDF templates...')
            pdf_templates = service.templates_generator.generate_pdf_templates(
                3, [business], [user]
            )
            logger.info(f'Generated {len(pdf_templates)} PDF templates')

            logger.info(
                f'Demo data generation completed: {len(clients)} clients, {len(quotes)} quotes, '
                f'{len(bookings)} bookings, {len(invoices)} invoices'
            )

        except Exception as e:
            logger.error(f'Error generating demo data: {str(e)}')
            raise


class DemoCleanupService:
    """Service for cleaning up expired demo sessions."""

    @classmethod
    def cleanup_expired_sessions(cls):
        """Clean up all expired demo sessions."""
        logger.info('Starting cleanup of expired demo sessions')

        # Find all sessions with demo data
        current_time = timezone.now()
        cleaned_count = 0

        # Get all active sessions
        for session in Session.objects.all():
            try:
                session_data = session.get_decoded()

                # Check if this is a demo session
                if DemoSession.DEMO_SESSION_KEY not in session_data:
                    continue

                # Check if expired
                expires_at_str = session_data.get(DemoSession.DEMO_EXPIRES_KEY)
                if not expires_at_str:
                    continue

                expires_at = datetime.fromisoformat(expires_at_str)
                if current_time <= expires_at:
                    continue  # Not expired yet

                # Clean up expired demo session
                demo_user_id = session_data.get(DemoSession.DEMO_USER_KEY)
                demo_business_id = session_data.get(DemoSession.DEMO_BUSINESS_KEY)

                with transaction.atomic():
                    # Delete business (cascades to related data)
                    if demo_business_id:
                        try:
                            business = Business.objects.get(id=demo_business_id)
                            business.delete()
                        except Business.DoesNotExist:
                            pass

                    # Delete user
                    if demo_user_id:
                        try:
                            user = User.objects.get(id=demo_user_id)
                            user.delete()
                        except User.DoesNotExist:
                            pass

                    # Delete session
                    session.delete()
                    cleaned_count += 1

                    logger.info(
                        f'Cleaned up expired demo session {session_data.get(DemoSession.DEMO_SESSION_KEY)}'
                    )

            except Exception as e:
                logger.error(
                    f'Error cleaning up session {session.session_key}: {str(e)}'
                )
                continue

        logger.info(f'Cleanup completed. Removed {cleaned_count} expired demo sessions')
        return cleaned_count
