[tool:pytest]
DJANGO_SETTINGS_MODULE = config.settings
python_files = tests.py test_*.py *_tests.py
python_classes = Test*
python_functions = test_*
addopts = 
    --verbose
    --tb=short
    --strict-markers
    --strict-config
    --cov=src
    --cov-report=html
    --cov-report=term-missing
    --cov-fail-under=80
    --reuse-db
    --nomigrations
markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    integration: marks tests as integration tests
    unit: marks tests as unit tests
    api: marks tests as API tests
    webhook: marks tests as webhook tests
    payment: marks tests as payment-related tests
    subscription: marks tests as subscription-related tests
testpaths = src
filterwarnings =
    ignore::django.utils.deprecation.RemovedInDjango50Warning
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
