{% extends "base.html" %}

{% block title %}Payment Error{% endblock %}

{% block content %}
<div class="max-w-2xl mx-auto">
    <div class="bg-white rounded-lg shadow p-6">
        <div class="text-center">
            <!-- Error Icon -->
            <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 mb-6">
                <svg class="h-8 w-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </div>
            
            <h1 class="text-3xl font-bold text-gray-900 mb-4">Payment Error</h1>
            
            {% if error %}
                <p class="text-lg text-red-600 mb-6">{{ error }}</p>
            {% else %}
                <p class="text-lg text-gray-600 mb-6">
                    There was an issue processing your payment. Please try again or contact support.
                </p>
            {% endif %}
            
            {% if payment_link.invoice %}
                <div class="bg-gray-50 rounded-lg p-4 mb-6">
                    <p class="text-sm text-gray-500">Invoice Number</p>
                    <p class="text-xl font-semibold text-gray-900">{{ payment_link.invoice.invoice_number }}</p>
                </div>
            {% endif %}
            
            <div class="space-y-3">
                <div class="pt-4 space-x-4">
                    <a href="/" class="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                        Return to Home
                    </a>
                    
                    {% if payment_link.invoice %}
                        <a href="mailto:<EMAIL>?subject=Payment Issue - Invoice {{ payment_link.invoice.invoice_number }}" 
                           class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors">
                            Contact Support
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
