"""
Pytest tests for template manager services.
"""
import pytest
from unittest.mock import patch, Mock
from django.template import Template, Context
from django.utils import timezone

from templates_manager.models import PDFTemplate
from templates_manager.services import PDFGenerationService, generate_quote_pdf


@pytest.mark.django_db
class TestPDFGenerationService:
    """Test cases for PDF generation service."""
    
    @pytest.fixture
    def pdf_template(self, business):
        """Create a test PDF template."""
        return PDFTemplate.objects.create(
            business=business,
            name='Test Quote Template',
            template_type=PDFTemplate.TemplateType.QUOTE,
            html_content='''
            <html>
            <body>
                <h1>Quote #{{ quote.quote_number }}</h1>
                <p>Client: {{ quote.client.display_name }}</p>
                <p>Amount: {{ quote.total_amount }}</p>
                <p>Business: {{ business.name }}</p>
            </body>
            </html>
            ''',
            css_content='''
            body { font-family: Arial, sans-serif; }
            h1 { color: {{ business.primary_color }}; }
            ''',
            is_active=True,
            is_default=True,
            created_by_id=1
        )
    
    @pytest.fixture
    def service(self, business):
        """Create a PDF generation service."""
        return PDFGenerationService(business)
    
    def test_service_initialization(self, business):
        """Test service initialization."""
        service = PDFGenerationService(business)
        assert service.business == business
        
        service_no_business = PDFGenerationService()
        assert service_no_business.business is None
    
    def test_get_template_business_specific(self, service, pdf_template):
        """Test getting business-specific template."""
        template = service._get_template(PDFTemplate.TemplateType.QUOTE)
        assert template == pdf_template
        assert template.business == service.business
    
    def test_get_template_system_default(self, service):
        """Test getting system default template when no business template exists."""
        # Create system template
        system_template = PDFTemplate.objects.create(
            business=None,  # System template
            name='System Quote Template',
            template_type=PDFTemplate.TemplateType.QUOTE,
            html_content='<html><body>System Template</body></html>',
            is_active=True,
            is_default=True,
            created_by_id=1
        )
        
        template = service._get_template(PDFTemplate.TemplateType.QUOTE)
        assert template == system_template
        assert template.business is None
    
    def test_get_template_not_found(self, service):
        """Test getting template when none exists."""
        template = service._get_template(PDFTemplate.TemplateType.INVOICE)
        assert template is None
    
    def test_prepare_context(self, service):
        """Test context preparation."""
        context_data = {'test_key': 'test_value'}
        context = service._prepare_context(context_data)
        
        assert context['test_key'] == 'test_value'
        assert context['business'] == service.business
        assert 'current_date' in context
        assert 'current_datetime' in context
        assert 'site_name' in context
    
    def test_render_template_with_html_content(self, service, pdf_template, quote):
        """Test rendering template with HTML content."""
        context = {
            'quote': quote,
            'business': service.business
        }
        
        html = service._render_template(pdf_template, context)
        
        assert quote.quote_number in html
        assert quote.client.display_name in html
        assert str(quote.total_amount) in html
        assert service.business.name in html
    
    @patch('templates_manager.services.WEASYPRINT_AVAILABLE', True)
    @patch('templates_manager.services.HTML')
    @patch('templates_manager.services.CSS')
    def test_generate_pdf_from_html(self, mock_css, mock_html, service, pdf_template):
        """Test PDF generation from HTML."""
        mock_html_instance = Mock()
        mock_html.return_value = mock_html_instance
        mock_html_instance.write_pdf.return_value = b'fake_pdf_data'
        
        mock_css_instance = Mock()
        mock_css.return_value = mock_css_instance
        
        html_content = '<html><body>Test</body></html>'
        pdf_data = service._generate_pdf_from_html(html_content, pdf_template)
        
        assert pdf_data == b'fake_pdf_data'
        mock_html.assert_called_once_with(string=html_content)
        mock_html_instance.write_pdf.assert_called_once()
    
    @patch('templates_manager.services.WEASYPRINT_AVAILABLE', False)
    def test_generate_pdf_weasyprint_not_available(self, service, quote):
        """Test PDF generation when WeasyPrint is not available."""
        context_data = {'quote': quote}
        
        with pytest.raises(RuntimeError, match="WeasyPrint is not available"):
            service.generate_pdf('quote', context_data)
    
    @patch('templates_manager.services.WEASYPRINT_AVAILABLE', True)
    @patch('templates_manager.services.HTML')
    def test_generate_pdf_success(self, mock_html, service, pdf_template, quote):
        """Test successful PDF generation."""
        mock_html_instance = Mock()
        mock_html.return_value = mock_html_instance
        mock_html_instance.write_pdf.return_value = b'fake_pdf_data'
        
        context_data = {'quote': quote}
        pdf_file = service.generate_pdf('quote', context_data)
        
        assert pdf_file.read() == b'fake_pdf_data'
        assert pdf_file.name.startswith('quote_')
        assert pdf_file.name.endswith('.pdf')
    
    def test_preview_template(self, service, pdf_template):
        """Test template preview generation."""
        html = service.preview_template('quote')
        
        assert 'Quote #' in html
        assert 'Sample Tour Company' in html
        assert 'John Doe' in html
    
    def test_preview_template_not_found(self, service):
        """Test template preview when template not found."""
        html = service.preview_template('nonexistent')
        assert 'No template found' in html
    
    def test_get_sample_context_quote(self, service):
        """Test sample context generation for quote."""
        context = service._get_sample_context('quote')
        
        assert 'quote' in context
        assert 'business' in context
        assert 'client' in context
        assert context['quote'].quote_number == 'QUO-2024-001'
        assert context['client'].display_name == 'John Doe'
    
    def test_get_sample_context_invoice(self, service):
        """Test sample context generation for invoice."""
        context = service._get_sample_context('invoice')
        
        assert 'invoice' in context
        assert 'business' in context
        assert 'client' in context
        assert context['invoice'].invoice_number == 'INV-2024-001'
    
    def test_get_sample_context_booking_confirmation(self, service):
        """Test sample context generation for booking confirmation."""
        context = service._get_sample_context('booking_confirmation')
        
        assert 'booking' in context
        assert 'business' in context
        assert 'client' in context
        assert context['booking'].confirmation_number == 'BOOK-2024-001'


@pytest.mark.django_db
class TestPDFTemplateModel:
    """Test cases for PDFTemplate model."""
    
    def test_create_pdf_template(self, business, user):
        """Test creating a PDF template."""
        template = PDFTemplate.objects.create(
            business=business,
            name='Test Template',
            template_type=PDFTemplate.TemplateType.QUOTE,
            html_content='<html><body>Test</body></html>',
            css_content='body { color: red; }',
            is_active=True,
            created_by=user
        )
        
        assert template.name == 'Test Template'
        assert template.business == business
        assert template.template_type == PDFTemplate.TemplateType.QUOTE
        assert template.is_active is True
    
    def test_template_str_representation(self, business, user):
        """Test template string representation."""
        template = PDFTemplate.objects.create(
            business=business,
            name='Test Template',
            template_type=PDFTemplate.TemplateType.QUOTE,
            html_content='<html><body>Test</body></html>',
            created_by=user
        )
        
        expected = f"Test Template ({business.name} - Quote)"
        assert str(template) == expected
    
    def test_system_template_str_representation(self, user):
        """Test system template string representation."""
        template = PDFTemplate.objects.create(
            business=None,  # System template
            name='System Template',
            template_type=PDFTemplate.TemplateType.QUOTE,
            html_content='<html><body>Test</body></html>',
            created_by=user
        )
        
        expected = "System Template (System - Quote)"
        assert str(template) == expected
    
    def test_get_template_path_business(self, business, user):
        """Test template path for business template."""
        template = PDFTemplate.objects.create(
            business=business,
            name='Test Template',
            template_type=PDFTemplate.TemplateType.QUOTE,
            html_content='<html><body>Test</body></html>',
            created_by=user
        )
        
        expected_path = f'pdf/business_{business.id}/quote_template.html'
        assert template.get_template_path() == expected_path
    
    def test_get_template_path_system(self, user):
        """Test template path for system template."""
        template = PDFTemplate.objects.create(
            business=None,
            name='System Template',
            template_type=PDFTemplate.TemplateType.QUOTE,
            html_content='<html><body>Test</body></html>',
            created_by=user
        )
        
        expected_path = 'pdf/system/quote_template.html'
        assert template.get_template_path() == expected_path
    
    def test_get_css_path_with_css(self, business, user):
        """Test CSS path when template has CSS content."""
        template = PDFTemplate.objects.create(
            business=business,
            name='Test Template',
            template_type=PDFTemplate.TemplateType.QUOTE,
            html_content='<html><body>Test</body></html>',
            css_content='body { color: red; }',
            created_by=user
        )
        
        expected_path = f'css/pdf/business_{business.id}_quote.css'
        assert template.get_css_path() == expected_path
    
    def test_get_css_path_without_css(self, business, user):
        """Test CSS path when template has no CSS content."""
        template = PDFTemplate.objects.create(
            business=business,
            name='Test Template',
            template_type=PDFTemplate.TemplateType.QUOTE,
            html_content='<html><body>Test</body></html>',
            created_by=user
        )
        
        assert template.get_css_path() is None


@pytest.mark.django_db
class TestConvenienceFunctions:
    """Test convenience functions for PDF generation."""
    
    @patch('templates_manager.services.PDFGenerationService.generate_pdf')
    def test_generate_quote_pdf(self, mock_generate_pdf, quote):
        """Test generate_quote_pdf convenience function."""
        mock_generate_pdf.return_value = Mock()
        
        result = generate_quote_pdf(quote)
        
        mock_generate_pdf.assert_called_once_with(
            'quote',
            {'quote': quote, 'client': quote.client},
            f'quote_{quote.quote_number}.pdf'
        )
        assert result is not None
