{% extends 'tours/tour_creation/base.html' %}

{% block content %}
<div class="max-w-3xl mx-auto">
  <div class="text-center mb-8">
    <div class="h-12 w-12 mx-auto bg-green-100 rounded-full flex items-center justify-center">
      <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
      </svg>
    </div>
    <h1 class="mt-4 text-2xl font-bold text-gray-900">Almost Done!</h1>
    <p class="mt-2 text-gray-600">
      Create this tour event to reserve your slots
    </p>
  </div>

  <div class="bg-white rounded-lg shadow overflow-hidden">
    <!-- Tour Summary -->
    <div class="border-b border-gray-200 bg-gray-50 px-6 py-4">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
        <div>
          <div class="text-sm text-gray-500">TOUR</div>
          <div class="font-medium text-gray-900">{{ tour.name }}</div>
        </div>
        <div>
          <div class="text-sm text-gray-500">DATE</div>
          <div class="font-medium text-gray-900">{{ selected_date|date:"M j, Y" }}</div>
        </div>
        <div>
          <div class="text-sm text-gray-500">TIME</div>
          <div class="font-medium text-gray-900">{{ selected_date|time:"g:i A" }} ({{ tour.timezone }})</div>
        </div>
      </div>
    </div>

    <!-- Slot Reservation -->
    <div class="px-6 py-5 border-b border-gray-200">
      <h3 class="font-medium text-gray-900 mb-3">Reservation Details</h3>

      <div class="grid grid-cols-2 gap-4 text-sm">
        <div>
          <p class="text-gray-500">Group Size</p>
          <p class="font-medium">{{ group_size }} guests</p>
        </div>

        <div>
          <p class="text-gray-500">Total Price</p>
          <p class="font-medium">${{ total_price|floatformat:2 }}</p>
        </div>
      </div>

      <div class="mt-4 p-3 bg-blue-50 border-l-4 border-blue-400 rounded-r">
        <div class="flex">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
          <p class="ml-3 text-sm text-blue-700">
            <strong>2-minute reservation hold</strong> applied. Your slots are protected during creation.
          </p>
        </div>
      </div>
    </div>

    <!-- Concurrency Alert -->
    <div id="concurrency-alert" class="hidden px-6 py-4 bg-yellow-50 border-b border-yellow-200">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <h4 class="text-sm font-medium text-yellow-800">Capacity Changed!</h4>
          <p class="text-sm text-yellow-700">
            Slots reduced to <span id="updated-slots">15</span>. Proceed with
            <span id="adjusted-size">{{ group_size }}</span> guests?
          </p>

          <div class="mt-3 flex space-x-3">
            <button id="proceed-button"
                    class="px-3 py-1.5 border border-transparent text-sm font-medium rounded text-yellow-700 bg-yellow-100 hover:bg-yellow-200 focus:outline-none">
              Yes, proceed
            </button>
            <button id="adjust-button"
                    class="px-3 py-1.5 border border-yellow-300 text-sm font-medium rounded text-yellow-700 bg-white hover:bg-yellow-50 focus:outline-none">
              Adjust group size
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="px-6 py-4 flex flex-col-reverse sm:flex-row sm:justify-between sm:items-center space-y-3 sm:space-y-0">
      <a href=".." class="text-sm text-gray-600 hover:text-gray-900">
        ← Back to adjust
      </a>

      <button id="final-create-button"
              hx-post="{% url 'tours:tour-set-date' %}"
              hx-include="[name='tour_date_id']"
              class="w-full sm:w-auto px-6 py-2.5 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
        Create Tour Event Now
      </button>
    </div>
  </div>

  <!-- Audit Trail -->
  <div class="mt-6 text-center text-sm text-gray-500">
    <p>
      Last availability check: {{ now|date:"M j, Y" }} at {{ now|time:"g:i A" }} UTC
      • Transaction ID: {{ transaction_id }}
    </p>
  </div>
</div>

<script>
  document.getElementById('current-step').textContent = '4';

  // Concurrency monitoring setup
  const capacityCheck = setInterval(() => {
    fetch('/api/check-capacity/?tour_date_id={{ tour_date.id }}')
      .then(response => response.json())
      .then(data => {
        if(data.available_slots < {{ group_size }}) {
          document.getElementById('concurrency-alert').classList.remove('hidden');
          document.getElementById('updated-slots').textContent = data.available_slots;

          if(data.available_slots >= {{ min_group_size }}) {
            document.getElementById('adjusted-size').textContent = data.available_slots;
          } else {
            document.getElementById('adjusted-size').textContent = "0";
            document.getElementById('proceed-button').disabled = true;
          }
        }
      });
  }, 15000); // Check every 15 seconds

  // Proceed button handler
  document.getElementById('proceed-button').addEventListener('click', function() {
    document.getElementById('concurrency-alert').classList.add('hidden');
    document.getElementById('final-create-button').click();
  });

  // Auto-submit after 90s if no concurrency issues
  setTimeout(() => {
    if(!document.getElementById('concurrency-alert').classList.contains('hidden')) return;
    document.getElementById('final-create-button').click();
  }, 90000);
</script>
{% endblock %}
