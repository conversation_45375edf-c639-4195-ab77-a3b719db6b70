from datetime import datetime

from django import forms
from django.utils import timezone
from djmoney.forms import MoneyWidget

from core.mixins import CrispyFormsMixin
from tours.models import TourCategory, TourEvent, TourTemplate


class TourCategoryForm(CrispyFormsMixin, forms.ModelForm):
    class Meta:
        model = TourCategory
        fields = '__all__'


class TourEventForm(forms.ModelForm):
    """Form for creating and editing tour events."""

    # Event details
    title = forms.CharField(
        max_length=200,
        widget=forms.TextInput(
            attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500',
                'placeholder': 'Event title',
            }
        ),
    )
    description = forms.CharField(
        required=False,
        widget=forms.Textarea(
            attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500',
                'rows': 3,
                'placeholder': 'Event description',
            }
        ),
    )

    # Date and time fields
    start_date = forms.DateField(
        widget=forms.DateInput(
            attrs={
                'type': 'date',
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500',
            }
        )
    )
    start_time = forms.TimeField(
        widget=forms.TimeInput(
            attrs={
                'type': 'time',
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500',
            }
        )
    )
    end_date = forms.DateField(
        required=False,
        widget=forms.DateInput(
            attrs={
                'type': 'date',
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500',
            }
        ),
    )
    end_time = forms.TimeField(
        widget=forms.TimeInput(
            attrs={
                'type': 'time',
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500',
            }
        )
    )

    class Meta:
        model = TourEvent
        fields = [
            'business',
            'client',
            'max_participants',
            'meeting_point',
            'special_instructions',
            'price_per_person',
            'is_confirmed',
        ]
        widgets = {
            'business': forms.Select(
                attrs={
                    'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500'
                }
            ),
            'client': forms.Select(
                attrs={
                    'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500'
                }
            ),
            'max_participants': forms.NumberInput(
                attrs={
                    'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500',
                    'min': 1,
                }
            ),
            'meeting_point': forms.TextInput(
                attrs={
                    'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500',
                    'placeholder': 'Meeting point address',
                }
            ),
            'special_instructions': forms.Textarea(
                attrs={
                    'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500',
                    'rows': 3,
                    'placeholder': 'Special instructions for this event',
                }
            ),
            'price_per_person': MoneyWidget(
                attrs={
                    'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500',
                }
            ),
            'is_confirmed': forms.CheckboxInput(
                attrs={
                    'class': 'h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded'
                }
            ),
        }

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)

        # Set default values for new events
        if not self.instance.pk:
            # Default to tomorrow at 10 AM
            tomorrow = timezone.now().date() + timezone.timedelta(days=1)
            self.fields['start_date'].initial = tomorrow
            self.fields['start_time'].initial = datetime.strptime(
                '10:00', '%H:%M'
            ).time()
            self.fields['end_date'].initial = tomorrow
            self.fields['end_time'].initial = datetime.strptime('12:00', '%H:%M').time()
        else:
            # Populate fields from existing event
            if self.instance.event:
                self.fields['title'].initial = self.instance.event.title
                self.fields['description'].initial = self.instance.event.description

                # Get timing from occurrence
                if self.instance.start_time:
                    self.fields['start_date'].initial = self.instance.start_time.date()
                    self.fields['start_time'].initial = self.instance.start_time.time()
                if self.instance.end_time:
                    self.fields['end_date'].initial = self.instance.end_time.date()
                    self.fields['end_time'].initial = self.instance.end_time.time()

        # Filter business and client choices based on user permissions
        if self.user and not self.user.is_superuser:
            from businesses.models import Business
            from clients.models import Client

            # Get businesses the user has access to
            accessible_businesses = Business.objects.filter(created_by=self.user)

            # Add businesses where user's profile is associated
            if (
                hasattr(self.user, 'profile')
                and self.user.profile
                and self.user.profile.business
            ):
                profile_business = self.user.profile.business
                accessible_businesses = accessible_businesses | Business.objects.filter(
                    id=profile_business.id
                )

            accessible_businesses = accessible_businesses.distinct()

            self.fields['business'].queryset = accessible_businesses
            self.fields['client'].queryset = Client.objects.filter(
                business__in=accessible_businesses
            )

    def clean(self):
        """Validate the form data."""
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        start_time = cleaned_data.get('start_time')
        end_date = cleaned_data.get('end_date')
        end_time = cleaned_data.get('end_time')

        if start_date and start_time and end_time:
            # If no end_date specified, use start_date
            if not end_date:
                end_date = start_date
                cleaned_data['end_date'] = end_date

            # Create datetime objects
            start_datetime = timezone.make_aware(
                datetime.combine(start_date, start_time)
            )
            end_datetime = timezone.make_aware(datetime.combine(end_date, end_time))

            # Validate that end time is after start time
            if end_datetime <= start_datetime:
                raise forms.ValidationError('End time must be after start time.')

            # Validate that event is not in the past
            if start_datetime < timezone.now():
                raise forms.ValidationError('Event cannot be scheduled in the past.')

            # Store the datetime objects for use in save()
            cleaned_data['start_datetime'] = start_datetime
            cleaned_data['end_datetime'] = end_datetime

        return cleaned_data

    def save(self, commit=True):
        """Save the tour event and create/update the associated custom event."""
        instance = super().save(commit=False)

        # Get the datetime objects from cleaned_data
        start_datetime = self.cleaned_data['start_datetime']
        end_datetime = self.cleaned_data['end_datetime']
        title = self.cleaned_data['title']
        description = self.cleaned_data.get('description', '')

        # Get the user for audit fields
        user = getattr(self, 'user', None) or instance.created_by

        # Create or update the Custom Event
        if instance.event:
            # Update existing event
            instance.event.title = title
            instance.event.description = description
            if commit:
                instance.event.save()
                # Update occurrence
                instance.get_or_create_occurrence(start_datetime, end_datetime)
        # else:
        # Create new event
        # event = Event.objects.create(
        #     title=title, description=description, created_by=user
        # )
        # instance.event = event
        # if commit:
        #     # Create occurrence
        #     Occurrence.objects.create(
        #         event=event,
        #         start_time=start_datetime,
        #         end_time=end_datetime,
        #         created_by=user,
        #     )

        # if commit:
        #     instance.save()

        return instance


class TourTemplateForm(CrispyFormsMixin, forms.ModelForm):
    class Meta:
        model = TourTemplate
        fields = '__all__'


class TourSearchForm(forms.Form):
    """Form for searching and filtering tours."""

    # Search query
    search = forms.CharField(
        max_length=255,
        required=False,
        widget=forms.TextInput(
            attrs={
                'placeholder': 'Search tours by name, destination...',
                'class': 'form-control',
                'data-on-input': 'sse:/tours/search/',
            }
        ),
    )

    # Destination filter
    destination = forms.CharField(
        max_length=255,
        required=False,
        widget=forms.TextInput(
            attrs={
                'placeholder': 'Destination (e.g., Swiss Alps)',
                'class': 'form-control',
                'data-on-input': 'sse:/tours/search/',
            }
        ),
    )

    # Activity type filter
    activity_type = forms.ChoiceField(
        choices=[('', 'All Activities')] + list(TourTemplate.ActivityType.choices),
        required=False,
        widget=forms.Select(
            attrs={
                'class': 'form-control',
                'data-on-change': 'sse:/tours/search/',
            }
        ),
    )

    # Difficulty level filter
    difficulty_level = forms.ChoiceField(
        choices=[('', 'All Difficulties')] + list(TourTemplate.DifficultyLevel.choices),
        required=False,
        widget=forms.Select(
            attrs={
                'class': 'form-control',
                'data-on-change': 'sse:/tours/search/',
            }
        ),
    )

    # Duration filter
    min_duration = forms.IntegerField(
        required=False,
        min_value=1,
        widget=forms.NumberInput(
            attrs={
                'placeholder': 'Min days',
                'class': 'form-control',
                'data-on-input': 'sse:/tours/search/',
            }
        ),
    )

    max_duration = forms.IntegerField(
        required=False,
        min_value=1,
        widget=forms.NumberInput(
            attrs={
                'placeholder': 'Max days',
                'class': 'form-control',
                'data-on-input': 'sse:/tours/search/',
            }
        ),
    )

    # Price range filter
    min_price = forms.DecimalField(
        required=False,
        min_value=0,
        decimal_places=2,
        widget=forms.NumberInput(
            attrs={
                'placeholder': 'Min price',
                'class': 'form-control',
                'data-on-input': 'sse:/tours/search/',
            }
        ),
    )

    max_price = forms.DecimalField(
        required=False,
        min_value=0,
        decimal_places=2,
        widget=forms.NumberInput(
            attrs={
                'placeholder': 'Max price',
                'class': 'form-control',
                'data-on-input': 'sse:/tours/search/',
            }
        ),
    )

    # Tour features
    is_guided = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(
            attrs={
                'class': 'form-check-input',
                'data-on-change': 'sse:/tours/search/',
            }
        ),
    )

    meals_included = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(
            attrs={
                'class': 'form-check-input',
                'data-on-change': 'sse:/tours/search/',
            }
        ),
    )

    accommodation_included = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(
            attrs={
                'class': 'form-check-input',
                'data-on-change': 'sse:/tours/search/',
            }
        ),
    )

    equipment_provided = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(
            attrs={
                'class': 'form-check-input',
                'data-on-change': 'sse:/tours/search/',
            }
        ),
    )
