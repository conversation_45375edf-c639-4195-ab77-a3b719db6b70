{% load static %}
<c-layouts.dashboard>
  <c-slot name="title">
    {% if object %}Edit Event{% else %}New Event{% endif %}
  </c-slot>

  <c-slot name="main">
    <div class="container mx-auto px-4 py-8">
      <div class="max-w-4xl mx-auto">
        <div class="flex justify-between items-center mb-8">
          <h1 class="text-3xl font-bold text-gray-900">
            {% if object %}Edit Event{% else %}Create New Event{% endif %}
          </h1>
          <a href="{% url 'bookings:calendar' %}" class="text-gray-600 hover:text-gray-800">
            ← Back to Calendar
          </a>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
          <form method="post" class="space-y-6">
            {% csrf_token %}

            {% if form.non_field_errors %}
            <div class="bg-red-50 border border-red-200 rounded-lg p-4">
              <div class="flex">
                <div class="flex-shrink-0">
                  <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                      clip-rule="evenodd" />
                  </svg>
                </div>
                <div class="ml-3">
                  <h3 class="text-sm font-medium text-red-800">Please correct the following errors:
                  </h3>
                  <div class="mt-2 text-sm text-red-700">
                    {{ form.non_field_errors }}
                  </div>
                </div>
              </div>
            </div>
            {% endif %}

            <!-- Event Basic Information -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div class="lg:col-span-2">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Event Information</h3>
              </div>

              <div>
                <label for="{{ form.title.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                  Event Title *
                </label>
                {{ form.title }}
                {% if form.title.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.title.errors.0 }}</p>
                {% endif %}
              </div>

              <div>
                <label for="{{ form.event_type.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                  Event Type *
                </label>
                {{ form.event_type }}
                {% if form.event_type.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.event_type.errors.0 }}</p>
                {% endif %}
              </div>

              <div class="lg:col-span-2">
                <label for="{{ form.description.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                  Description
                </label>
                {{ form.description }}
                {% if form.description.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.description.errors.0 }}</p>
                {% endif %}
              </div>
            </div>

            <!-- Date and Time -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div class="lg:col-span-2">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Schedule</h3>
              </div>

              <div>
                <label for="{{ form.start_date.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                  Start Date *
                </label>
                {{ form.start_date }}
                {% if form.start_date.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.start_date.errors.0 }}</p>
                {% endif %}
              </div>

              <div>
                <label for="{{ form.start_time.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                  Start Time *
                </label>
                {{ form.start_time }}
                {% if form.start_time.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.start_time.errors.0 }}</p>
                {% endif %}
              </div>

              <div>
                <label for="{{ form.end_date.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                  End Date
                </label>
                {{ form.end_date }}
                {% if form.end_date.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.end_date.errors.0 }}</p>
                {% endif %}
                <p class="mt-1 text-sm text-gray-500">Leave blank to use start date</p>
              </div>

              <div>
                <label for="{{ form.end_time.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                  End Time *
                </label>
                {{ form.end_time }}
                {% if form.end_time.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.end_time.errors.0 }}</p>
                {% endif %}
              </div>
            </div>

            <!-- Business and Client -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div class="lg:col-span-2">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Assignment</h3>
              </div>

              <div>
                <label for="{{ form.business.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                  Business *
                </label>
                {{ form.business }}
                {% if form.business.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.business.errors.0 }}</p>
                {% endif %}
              </div>

              <div>
                <label for="{{ form.client.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                  Client
                </label>
                {{ form.client }}
                {% if form.client.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.client.errors.0 }}</p>
                {% endif %}
                <p class="mt-1 text-sm text-gray-500">Leave blank for internal events</p>
              </div>
            </div>

            <!-- Event Details -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div class="lg:col-span-2">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Event Details</h3>
              </div>

              <div>
                <label for="{{ form.max_participants.id_for_label }}"
                  class="block text-sm font-medium text-gray-700 mb-2">
                  Maximum Participants *
                </label>
                {{ form.max_participants }}
                {% if form.max_participants.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.max_participants.errors.0 }}</p>
                {% endif %}
              </div>

              <div>
                <label for="{{ form.price_per_person.id_for_label }}"
                  class="block text-sm font-medium text-gray-700 mb-2">
                  Price per Person
                </label>
                {{ form.price_per_person }}
                {% if form.price_per_person.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.price_per_person.errors.0 }}</p>
                {% endif %}
              </div>

              <div class="lg:col-span-2">
                <label for="{{ form.meeting_point.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                  Meeting Point
                </label>
                {{ form.meeting_point }}
                {% if form.meeting_point.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.meeting_point.errors.0 }}</p>
                {% endif %}
              </div>

              <div class="lg:col-span-2">
                <label for="{{ form.special_instructions.id_for_label }}"
                  class="block text-sm font-medium text-gray-700 mb-2">
                  Special Instructions
                </label>
                {{ form.special_instructions }}
                {% if form.special_instructions.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.special_instructions.errors.0 }}</p>
                {% endif %}
              </div>

              <div class="lg:col-span-2">
                <div class="flex items-center">
                  {{ form.is_confirmed }}
                  <label for="{{ form.is_confirmed.id_for_label }}" class="ml-2 text-sm text-gray-700">
                    Mark as confirmed
                  </label>
                </div>
                {% if form.is_confirmed.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.is_confirmed.errors.0 }}</p>
                {% endif %}
              </div>
            </div>

            <!-- Form Actions -->
            <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
              <a href="{% url 'bookings:calendar' %}"
                class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300 transition-colors">
                Cancel
              </a>
              <button type="submit"
                class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 transition-colors">
                {% if object %}Update Event{% else %}Create Event{% endif %}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <script>
      // Auto-populate end date when start date changes
      document.getElementById('{{ form.start_date.id_for_label }}').addEventListener('change', function () {
        const endDateField = document.getElementById('{{ form.end_date.id_for_label }}');
        if (!endDateField.value) {
          endDateField.value = this.value;
        }
      });

      // Auto-calculate end time (2 hours after start time)
      document.getElementById('{{ form.start_time.id_for_label }}').addEventListener('change', function () {
        const endTimeField = document.getElementById('{{ form.end_time.id_for_label }}');
        if (!endTimeField.value) {
          const startTime = this.value;
          if (startTime) {
            const [hours, minutes] = startTime.split(':');
            const endHours = (parseInt(hours) + 2) % 24;
            endTimeField.value = `${endHours.toString().padStart(2, '0')}:${minutes}`;
          }
        }
      });
    </script>
  </c-slot>
</c-layouts.dashboard>
