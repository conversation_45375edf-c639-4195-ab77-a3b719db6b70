{% load i18n %}
{% load crispy_forms_tags %}

<c-layouts.dashboard>
  <c-slot name="title">
    {% if object %}
    {% trans "Edit Client" %} - {{ object.display_name }}
    {% else %}
    {% trans "Create Client" %}
    {% endif %}
  </c-slot>

  <c-slot name="main">
    <div class="max-w-4xl mx-auto">
      <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
          <h1 class="text-2xl font-bold text-gray-900">
            {% if object %}
            {% trans "Edit Client" %}
            {% else %}
            {% trans "Create Client" %}
            {% endif %}
          </h1>
        </div>

        {% crispy form %}

      </div>
    </div>

    <style>
      /* Style form inputs */
      input[type="text"],
      input[type="email"],
      input[type="tel"],
      input[type="date"],
      textarea,
      select {
        @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500;
      }

      textarea {
        @apply h-24 resize-vertical;
      }

      /* Button styles */
      .btn {
        @apply px-4 py-2 rounded-md font-medium focus:outline-none focus:ring-2 focus:ring-offset-2;
      }

      .btn-primary {
        @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
      }

      .btn-secondary {
        @apply bg-gray-300 text-gray-700 hover:bg-gray-400 focus:ring-gray-500;
      }
    </style>
  </c-slot>
</c-layouts.dashboard>
