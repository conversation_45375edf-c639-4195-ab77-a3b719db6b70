{% load i18n %}

<c-layouts.base>
  <c-slot name="title">
    {% trans "My Profile" %} - Tour Business Management
  </c-slot>

  <c-slot name="content">
    <div class="container mx-auto px-4 py-8">
      <div class="max-w-4xl mx-auto">
        <!-- Header -->
        <div class="flex justify-between items-center mb-6">
          <h1 class="text-3xl font-bold text-gray-900">
            {% trans "My Profile" %}
          </h1>
          <a href="{% url 'accounts:profile_edit' %}"
            class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">
            {% trans "Edit Profile" %}
          </a>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <!-- Profile Summary Card -->
          <div class="lg:col-span-1">
            <div class="bg-white shadow rounded-lg p-6">
              <div class="text-center">
                {% if profile.avatar %}
                <img src="{{ profile.avatar.url }}" alt="Profile Picture"
                  class="w-24 h-24 rounded-full mx-auto mb-4 object-cover">
                {% else %}
                <div class="w-24 h-24 rounded-full mx-auto mb-4 bg-gray-300 flex items-center justify-center">
                  <svg class="w-12 h-12 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                  </svg>
                </div>
                {% endif %}

                <h3 class="text-lg font-medium text-gray-900">
                  {{ profile.get_full_name|default:user.username|title }}
                </h3>

                <p class="text-sm text-gray-500">{{ user.get_role_display }}</p>

                {% if user.profile.business %}
                <p class="text-sm text-gray-600 mt-1">{{ user.profile.business.name }}</p>
                {% endif %}
              </div>

              <div class="mt-6 pt-6 border-t border-gray-200">
                <div class="text-sm">
                  <div class="flex justify-between py-2">
                    <span class="text-gray-500">{% trans "Email" %}</span>
                    <span class="text-gray-900">{{ user.email }}</span>
                  </div>
                  {% if profile.phone %}
                  <div class="flex justify-between py-2">
                    <span class="text-gray-500">{% trans "Phone" %}</span>
                    <span class="text-gray-900">{{ profile.phone }}</span>
                  </div>
                  {% endif %}
                  <div class="flex justify-between py-2">
                    <span class="text-gray-500">{% trans "Member Since" %}</span>
                    <span class="text-gray-900">{{ user.date_joined|date:"M Y" }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Profile Details -->
          <div class="lg:col-span-2 space-y-6">
            <!-- Personal Information -->
            <div class="bg-white shadow rounded-lg p-6">
              <h3 class="text-lg font-medium text-gray-900 mb-4">{% trans "Personal Information" %}</h3>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-500">{% trans "First Name" %}</label>
                  <p class="mt-1 text-sm text-gray-900">{{ profile.first_name|default:"—" }}</p>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-500">{% trans "Last Name" %}</label>
                  <p class="mt-1 text-sm text-gray-900">{{ profile.last_name|default:"—" }}</p>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-500">{% trans "Phone" %}</label>
                  <p class="mt-1 text-sm text-gray-900">{{ profile.phone|default:"—" }}</p>
                </div>
              </div>

              {% if profile.bio %}
              <div class="mt-4">
                <label class="block text-sm font-medium text-gray-500">{% trans "Bio" %}</label>
                <p class="mt-1 text-sm text-gray-900">{{ profile.bio }}</p>
              </div>
              {% endif %}
            </div>

            <!-- Address Information -->
            <div class="bg-white shadow rounded-lg p-6">
              <h3 class="text-lg font-medium text-gray-900 mb-4">{% trans "Address" %}</h3>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="md:col-span-2">
                  <label class="block text-sm font-medium text-gray-500">{% trans "Address Line 1" %}</label>
                  <p class="mt-1 text-sm text-gray-900">{{ profile.address_line1|default:"—" }}</p>
                </div>
                {% if profile.address_line2 %}
                <div class="md:col-span-2">
                  <label class="block text-sm font-medium text-gray-500">{% trans "Address Line 2" %}</label>
                  <p class="mt-1 text-sm text-gray-900">{{ profile.address_line2 }}</p>
                </div>
                {% endif %}
                <div>
                  <label class="block text-sm font-medium text-gray-500">{% trans "City" %}</label>
                  <p class="mt-1 text-sm text-gray-900">{{ profile.city|default:"—" }}</p>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-500">{% trans "State/Province" %}</label>
                  <p class="mt-1 text-sm text-gray-900">{{ profile.state_province|default:"—" }}</p>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-500">{% trans "Postal Code" %}</label>
                  <p class="mt-1 text-sm text-gray-900">{{ profile.postal_code|default:"—" }}</p>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-500">{% trans "Country" %}</label>
                  <p class="mt-1 text-sm text-gray-900">{{ profile.country|default:"—" }}</p>
                </div>
              </div>
            </div>

            <!-- Professional Information -->
            {% if profile.job_title or profile.department %}
            <div class="bg-white shadow rounded-lg p-6">
              <h3 class="text-lg font-medium text-gray-900 mb-4">{% trans "Professional Information" %}</h3>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-500">{% trans "Job Title" %}</label>
                  <p class="mt-1 text-sm text-gray-900">{{ profile.job_title|default:"—" }}</p>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-500">{% trans "Department" %}</label>
                  <p class="mt-1 text-sm text-gray-900">{{ profile.department|default:"—" }}</p>
                </div>
              </div>
            </div>
            {% endif %}

          </div>
        </div>
      </div>
    </div>
  </c-slot name="content">
</c-layouts.base>
