"""
Management command to create a demo superuser for the tour business management system.
This creates a superuser with predefined credentials for demo purposes.
"""

from django.contrib.auth import get_user_model
from django.core.management.base import BaseCommand

User = get_user_model()


class Command(BaseCommand):
    help = 'Create a demo superuser with predefined credentials'

    def add_arguments(self, parser):
        parser.add_argument(
            '--username',
            type=str,
            default='admin',
            help='Username for the superuser (default: admin)',
        )
        parser.add_argument(
            '--email',
            type=str,
            default='<EMAIL>',
            help='Email for the superuser (default: <EMAIL>)',
        )
        parser.add_argument(
            '--password',
            type=str,
            default='admin123',
            help='Password for the superuser (default: admin123)',
        )

    def handle(self, *args, **options):
        username = options['username']
        email = options['email']
        password = options['password']

        # Check if user already exists
        if User.objects.filter(username=username).exists():
            self.stdout.write(self.style.WARNING(f'User "{username}" already exists.'))
            return

        # Create superuser
        user = User.objects.create_superuser(
            username=username, email=email, password=password
        )

        # Set additional fields for our custom user model
        user.profile.role = Profile.Role.ADMIN
        user.is_active = True
        user.email_confirmed = True
        user.profile_completed = True
        user.onboarding_completed = True
        user.save()

        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully created superuser:\n'
                f'Username: {username}\n'
                f'Email: {email}\n'
                f'Password: {password}\n'
                f'Admin URL: /admin/'
            )
        )
