# User Registration and Management System

## Overview

The Tour Business Management system implements a comprehensive user registration and management system with email confirmation, onboarding, and business user management capabilities.

## Features

### 1. Email Confirmation Registration

- Users register with username, email, and password
- Account is created but inactive until email confirmation
- Confirmation email sent with unique token
- Upon confirmation, user becomes business owner with business profile created

### 2. Onboarding Process

- Multi-step onboarding for first-time users
- Profile completion (personal information)
- Business setup and introduction
- Guided tour of platform features

### 3. Business User Management

- Business owners can add team members
- Individual user creation with role assignment
- Bulk user upload via Excel file
- User invitation system with email confirmation

### 4. User Invitation System

- Invited users receive email with setup link
- Users set their own password during invitation acceptance
- Automatic profile creation and onboarding redirection

## User Flow

### New Business Owner Registration

1. User visits registration page
2. Fills out username, email, password
3. Account created as inactive
4. Confirmation email sent
5. User clicks confirmation link
6. Account activated, role set to business owner
7. Business profile created automatically
8. User redirected to login
9. First login triggers onboarding process
10. Profile completion step
11. Business setup completion
12. Access to full platform

### Team Member Invitation

1. Business owner accesses user management
2. Adds user individually or via bulk upload
3. User account created as inactive
4. Invitation email sent to user
5. User clicks invitation link
6. User sets password and activates account
7. User redirected to profile completion
8. Access to platform based on role

## Models

### User Model Extensions

- `email_confirmed`: Boolean flag for email confirmation status
- `email_confirmation_token`: UUID token for email confirmation
- `email_confirmation_sent_at`: Timestamp of confirmation email
- `profile_completed`: Boolean flag for profile completion
- `onboarding_completed`: Boolean flag for onboarding completion

### Profile Model

- Extended user information (name, address, contact details)
- Professional information (job title, department)
- Emergency contact information
- User preferences (timezone, language)

## Views and URLs

### Registration Views

- `RegisterView`: User registration form
- `EmailConfirmationView`: Email confirmation handling
- `CustomLoginView`: Login with onboarding redirection

### Onboarding Views

- `OnboardingProfileView`: Profile completion form
- `OnboardingBusinessView`: Business setup completion
- `check_onboarding_status`: Onboarding flow controller

### User Management Views

- `UserManagementView`: Business user overview
- `AddUserView`: Individual user creation
- `BulkUserUploadView`: Excel bulk upload
- `UserInvitationView`: Invitation acceptance

### Profile Management Views

- `ProfileDetailView`: Profile display
- `ProfileUpdateView`: Profile editing

## Email Templates

### Registration Confirmation

- Welcome message with confirmation button
- Security information and expiration notice
- Next steps explanation

### User Invitation

- Business information and role details
- Invitation acceptance button
- Feature overview and access information

### Welcome Business Owner

- Account activation confirmation
- Onboarding process overview
- Platform features introduction

## Forms

### Registration Forms

- `CustomUserCreationForm`: Simplified registration (username, email, password)
- `OnboardingProfileForm`: Required profile fields for onboarding

### User Management Forms

- `AddUserForm`: Individual user creation with optional password
- `BulkUserUploadForm`: Excel file upload validation

### Profile Forms

- `ProfileForm`: Complete profile editing
- `UserAccountForm`: Basic account information

## Security Features

### Email Confirmation

- UUID tokens for secure confirmation links
- Token expiration for security
- Invalid token handling

### User Invitation

- Secure invitation tokens
- Password strength requirements
- Account activation only after password setup

### Access Control

- Role-based access to user management
- Business-scoped user access
- Permission checks for user operations

## Testing

### Test Coverage

- Registration flow testing
- Email confirmation testing
- Onboarding process testing
- User management testing
- Profile management testing
- User invitation testing

### Test Types

- Unit tests for models and forms
- Integration tests for views and workflows
- End-to-end tests for complete user journeys

## Configuration

### Email Settings

- File-based email backend for development
- SMTP configuration for production
- Email template customization

### Business Settings

- Automatic business creation for new owners
- Business-scoped user management
- Role-based feature access

## Usage Examples

### Running Tests

```bash
# Run all registration tests
pytest accounts/test_registration.py

# Run specific test class
pytest accounts/test_registration.py::TestRegistrationFlow

# Run with coverage
pytest --cov=accounts accounts/test_registration.py
```

### Creating Test Data

```python
# Create business owner
user = User.objects.create_user(
    username='owner',
    email='<EMAIL>',
    password='password123',
    role=Profile.Role.BUSINESS_OWNER,
    is_active=True,
    email_confirmed=True
)

# Create business
business = Business.objects.create(
    name="Test Business",
    email=user.email,
    created_by=user
)
user.profile.business = business
user.save()
```

## API Endpoints

### Registration Endpoints

- `POST /accounts/register/` - User registration
- `GET /accounts/confirm-email/<token>/` - Email confirmation

### Onboarding Endpoints

- `GET /accounts/onboarding/` - Onboarding status check
- `POST /accounts/onboarding/profile/` - Profile completion
- `POST /accounts/onboarding/business/` - Business setup

### User Management Endpoints

- `GET /accounts/users/` - User management dashboard
- `POST /accounts/users/add/` - Add individual user
- `POST /accounts/users/bulk-upload/` - Bulk user upload
- `GET /accounts/users/template/` - Download Excel template

### Profile Endpoints

- `GET /accounts/profile/` - Profile view
- `POST /accounts/profile/edit/` - Profile update

## Troubleshooting

### Common Issues

1. **Email not sending**: Check email backend configuration
2. **Confirmation link expired**: Generate new confirmation token
3. **Onboarding not triggering**: Check user flags and login view
4. **Bulk upload failing**: Verify Excel file format and required columns

### Debug Tips

- Check user flags: `is_active`, `email_confirmed`, `profile_completed`
- Verify business association for user management access
- Test email templates in development environment
- Use Django admin to inspect user and profile data
