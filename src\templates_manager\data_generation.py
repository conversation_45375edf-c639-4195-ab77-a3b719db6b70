"""
Data generation services for templates_manager app using model_bakery.
"""

import random
from model_bakery import baker
from faker import Faker

from .models import PDFTemplate

fake = Faker()


class TemplatesDataGenerator:
    """Data generator for templates_manager app models."""

    def __init__(self):
        self.created_objects = {
            'pdf_templates': []
        }

    def generate_pdf_templates(self, count=12, businesses=None, users=None):
        """Generate PDF templates for businesses."""
        templates = []

        # Template configurations by type
        template_configs = {
            PDFTemplate.TemplateType.QUOTE: {
                'names': [
                    'Modern Quote Template', 'Professional Quote Layout', 'Elegant Quote Design',
                    'Safari Quote Template', 'Adventure Quote Style', 'Luxury Quote Format'
                ],
                'descriptions': [
                    'Clean and professional quote template with company branding',
                    'Modern design perfect for tour and travel quotes',
                    'Elegant layout with emphasis on pricing and terms'
                ]
            },
            PDFTemplate.TemplateType.INVOICE: {
                'names': [
                    'Standard Invoice Template', 'Professional Invoice Layout', 'Corporate Invoice Design',
                    'Travel Invoice Template', 'Service Invoice Style', 'Detailed Invoice Format'
                ],
                'descriptions': [
                    'Professional invoice template with payment terms',
                    'Clean invoice design with detailed line items',
                    'Corporate-style invoice with tax calculations'
                ]
            },
            PDFTemplate.TemplateType.BOOKING_CONFIRMATION: {
                'names': [
                    'Booking Confirmation Template', 'Trip Confirmation Layout', 'Reservation Confirmation',
                    'Safari Booking Confirmation', 'Adventure Booking Style', 'Travel Confirmation Format'
                ],
                'descriptions': [
                    'Comprehensive booking confirmation with itinerary details',
                    'Professional confirmation template with trip information',
                    'Detailed booking confirmation with terms and conditions'
                ]
            },
            PDFTemplate.TemplateType.RECEIPT: {
                'names': [
                    'Payment Receipt Template', 'Transaction Receipt Layout', 'Service Receipt Design',
                    'Travel Receipt Template', 'Booking Receipt Style', 'Payment Confirmation Format'
                ],
                'descriptions': [
                    'Simple and clear payment receipt template',
                    'Professional receipt design with payment details',
                    'Clean receipt layout for service payments'
                ]
            },
            PDFTemplate.TemplateType.ITINERARY: {
                'names': [
                    'Travel Itinerary Template', 'Trip Schedule Layout', 'Adventure Itinerary Design',
                    'Safari Itinerary Template', 'Cultural Tour Itinerary', 'Detailed Trip Plan Format'
                ],
                'descriptions': [
                    'Comprehensive travel itinerary with daily schedules',
                    'Detailed trip plan with activities and accommodations',
                    'Professional itinerary template with maps and contacts'
                ]
            },
            PDFTemplate.TemplateType.VOUCHER: {
                'names': [
                    'Service Voucher Template', 'Travel Voucher Layout', 'Activity Voucher Design',
                    'Tour Voucher Template', 'Experience Voucher Style', 'Gift Voucher Format'
                ],
                'descriptions': [
                    'Elegant voucher template for travel services',
                    'Professional voucher design with terms and conditions',
                    'Gift voucher template for tour experiences'
                ]
            }
        }

        # Generate system templates (no business assigned) - only if we have users
        if users:
            system_user = random.choice(users)  # Use a random user for system templates
            for template_type in PDFTemplate.TemplateType.choices:
                type_value, type_label = template_type
                config = template_configs[type_value]

                template = baker.make(
                    PDFTemplate,
                    business=None,  # System template
                    name=f'Default {type_label} Template',
                    template_type=type_value,
                    description=f'Default system template for {type_label.lower()} documents',
                    html_content=self._generate_html_content(type_value),
                    css_content=self._generate_css_content(),
                    is_active=True,
                    is_default=True,
                    page_size=random.choice(['A4', 'Letter']),
                    created_by=system_user,
                )

                templates.append(template)

        # Generate business-specific templates
        if businesses:
            remaining_count = count - len(templates)
            business_templates_count = remaining_count // len(businesses) if businesses else 0

            for business in businesses:
                for i in range(business_templates_count):
                    # Select random template type
                    template_type = random.choice([t[0] for t in PDFTemplate.TemplateType.choices])
                    config = template_configs[template_type]

                    template = baker.make(
                        PDFTemplate,
                        business=business,
                        name=random.choice(config['names']),
                        template_type=template_type,
                        description=random.choice(config['descriptions']),
                        html_content=self._generate_business_html_content(template_type, business),
                        css_content=self._generate_business_css_content(business),
                        is_active=fake.boolean(chance_of_getting_true=90),
                        is_default=fake.boolean(chance_of_getting_true=20),
                        page_size=random.choice(['A4', 'Letter', 'Legal']),
                    )

                    templates.append(template)

        self.created_objects['pdf_templates'].extend(templates)
        return templates

    def _generate_html_content(self, template_type):
        """Generate basic HTML content for template type."""
        html_templates = {
            PDFTemplate.TemplateType.QUOTE: '''
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Quote</title>
</head>
<body>
    <div class="header">
        <h1>QUOTE</h1>
        <div class="company-info">
            <h2>{{ business.name }}</h2>
            <p>{{ business.address_line1 }}, {{ business.city }}</p>
            <p>{{ business.email }} | {{ business.phone }}</p>
        </div>
    </div>

    <div class="quote-details">
        <h3>Quote Details</h3>
        <p><strong>Quote Number:</strong> {{ quote.quote_number }}</p>
        <p><strong>Date:</strong> {{ quote.created_at|date:"F d, Y" }}</p>
        <p><strong>Valid Until:</strong> {{ quote.valid_until|date:"F d, Y" }}</p>
    </div>

    <div class="client-info">
        <h3>Client Information</h3>
        <p>{{ quote.client.full_name }}</p>
        <p>{{ quote.client.email }}</p>
    </div>

    <div class="pricing">
        <table>
            <tr><td>Subtotal:</td><td>${{ quote.subtotal }}</td></tr>
            <tr><td>Tax:</td><td>${{ quote.tax_amount }}</td></tr>
            <tr><td><strong>Total:</strong></td><td><strong>${{ quote.total_amount }}</strong></td></tr>
        </table>
    </div>
</body>
</html>''',

            PDFTemplate.TemplateType.INVOICE: '''
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Invoice</title>
</head>
<body>
    <div class="header">
        <h1>INVOICE</h1>
        <div class="company-info">
            <h2>{{ business.name }}</h2>
            <p>{{ business.address_line1 }}, {{ business.city }}</p>
            <p>{{ business.email }} | {{ business.phone }}</p>
        </div>
    </div>

    <div class="invoice-details">
        <p><strong>Invoice Number:</strong> {{ invoice.invoice_number }}</p>
        <p><strong>Date:</strong> {{ invoice.issue_date|date:"F d, Y" }}</p>
        <p><strong>Due Date:</strong> {{ invoice.due_date|date:"F d, Y" }}</p>
    </div>

    <div class="client-info">
        <h3>Bill To:</h3>
        <p>{{ invoice.client.full_name }}</p>
        <p>{{ invoice.client.email }}</p>
    </div>

    <div class="pricing">
        <table>
            <tr><td>Subtotal:</td><td>${{ invoice.subtotal }}</td></tr>
            <tr><td>Tax:</td><td>${{ invoice.tax_amount }}</td></tr>
            <tr><td><strong>Total:</strong></td><td><strong>${{ invoice.total_amount }}</strong></td></tr>
        </table>
    </div>
</body>
</html>''',

            PDFTemplate.TemplateType.BOOKING_CONFIRMATION: '''
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Booking Confirmation</title>
</head>
<body>
    <div class="header">
        <h1>BOOKING CONFIRMATION</h1>
        <div class="company-info">
            <h2>{{ business.name }}</h2>
            <p>{{ business.address_line1 }}, {{ business.city }}</p>
            <p>{{ business.email }} | {{ business.phone }}</p>
        </div>
    </div>

    <div class="booking-details">
        <h3>Booking Information</h3>
        <p><strong>Booking Reference:</strong> {{ booking.id }}</p>
        <p><strong>Tour:</strong> {{ booking.tour_event.event.title }}</p>
        <p><strong>Date:</strong> {{ booking.tour_event.start_time|date:"F d, Y" }}</p>
        <p><strong>Participants:</strong> {{ booking.number_of_participants }}</p>
    </div>

    <div class="client-info">
        <h3>Client Information</h3>
        <p>{{ booking.client.full_name }}</p>
        <p>{{ booking.client.email }}</p>
    </div>
</body>
</html>'''
        }

        return html_templates.get(template_type, '<html><body><h1>Template</h1></body></html>')

    def _generate_css_content(self):
        """Generate basic CSS content."""
        return '''
body {
    font-family: Arial, sans-serif;
    margin: 40px;
    color: #333;
}

.header {
    border-bottom: 2px solid #007bff;
    padding-bottom: 20px;
    margin-bottom: 30px;
}

.header h1 {
    color: #007bff;
    margin: 0;
    font-size: 28px;
}

.company-info h2 {
    margin: 10px 0 5px 0;
    color: #333;
}

.company-info p {
    margin: 2px 0;
    color: #666;
}

h3 {
    color: #007bff;
    border-bottom: 1px solid #ddd;
    padding-bottom: 5px;
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

table td {
    padding: 8px;
    border-bottom: 1px solid #ddd;
}

table tr:last-child td {
    border-bottom: 2px solid #007bff;
    font-weight: bold;
}

.pricing {
    margin-top: 30px;
}
'''

    def _generate_business_html_content(self, template_type, business):
        """Generate business-specific HTML content."""
        base_html = self._generate_html_content(template_type)

        # Customize based on business theme
        business_name_lower = business.name.lower()

        if 'safari' in business_name_lower or 'wildlife' in business_name_lower:
            # Add safari-themed elements
            base_html = base_html.replace(
                '<h1>QUOTE</h1>',
                '<h1>🦁 SAFARI QUOTE 🦁</h1>'
            ).replace(
                '<h1>INVOICE</h1>',
                '<h1>🦁 SAFARI INVOICE 🦁</h1>'
            )

        elif 'adventure' in business_name_lower or 'mountain' in business_name_lower:
            # Add adventure-themed elements
            base_html = base_html.replace(
                '<h1>QUOTE</h1>',
                '<h1>🏔️ ADVENTURE QUOTE 🏔️</h1>'
            ).replace(
                '<h1>INVOICE</h1>',
                '<h1>🏔️ ADVENTURE INVOICE 🏔️</h1>'
            )

        elif 'cultural' in business_name_lower or 'heritage' in business_name_lower:
            # Add cultural-themed elements
            base_html = base_html.replace(
                '<h1>QUOTE</h1>',
                '<h1>🏛️ CULTURAL QUOTE 🏛️</h1>'
            ).replace(
                '<h1>INVOICE</h1>',
                '<h1>🏛️ CULTURAL INVOICE 🏛️</h1>'
            )

        return base_html

    def _generate_business_css_content(self, business):
        """Generate business-specific CSS content."""
        base_css = self._generate_css_content()

        # Use business primary color if available
        if hasattr(business, 'primary_color') and business.primary_color:
            primary_color = business.primary_color
        else:
            primary_color = '#007bff'

        # Replace default blue with business color
        business_css = base_css.replace('#007bff', primary_color)

        return business_css

    def get_generation_summary(self):
        """Get summary of generated objects."""
        return {
            key: len(objects) for key, objects in self.created_objects.items()
        }
