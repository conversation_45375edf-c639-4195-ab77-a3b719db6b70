#!/usr/bin/env python
"""
Test script for the email confirmation registration flow.
"""

import os

import django
from django.test import Client
from django.urls import reverse

from accounts.models import Profile, User

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()


def test_registration_flow():
    """Test the complete registration and email confirmation flow."""
    client = Client()

    print('Testing registration flow...')

    # Test data
    test_data = {
        'username': 'testuser',
        'email': '<EMAIL>',
        'first_name': 'Test',
        'last_name': 'User',
        'password1': 'testpassword123',
        'password2': 'testpassword123',
    }

    # 1. Test registration
    print('1. Testing user registration...')
    response = client.post(reverse('accounts:register'), test_data)

    if response.status_code == 302:  # Redirect after successful registration
        print('✓ Registration successful - redirected')
    else:
        print(f'✗ Registration failed - status code: {response.status_code}')
        if (
            hasattr(response, 'context')
            and response.context
            and 'form' in response.context
        ):
            print(f'Form errors: {response.context["form"].errors}')
        return False

    # 2. Check user was created but inactive
    print('2. Checking user creation...')
    try:
        user = User.objects.get(username='testuser')
        print(f'✓ User created: {user.username}')
        print(f'  - Email: {user.email}')
        print(f'  - Active: {user.is_active}')
        print(f'  - Email confirmed: {user.email_confirmed}')
        print(f'  - Role: {user.profile.role}')
        print(f'  - Confirmation token: {user.email_confirmation_token}')

        if not user.is_active:
            print('✓ User is inactive (correct)')
        else:
            print('✗ User should be inactive before email confirmation')

        if not user.email_confirmed:
            print('✓ Email not confirmed yet (correct)')
        else:
            print('✗ Email should not be confirmed yet')

    except User.DoesNotExist:
        print('✗ User was not created')
        return False

    # 3. Test email confirmation
    print('3. Testing email confirmation...')
    token = user.email_confirmation_token
    confirmation_url = reverse('accounts:confirm_email', kwargs={'token': str(token)})

    response = client.get(confirmation_url)

    if response.status_code == 302:  # Redirect after confirmation
        print('✓ Email confirmation successful - redirected')
    else:
        print(f'✗ Email confirmation failed - status code: {response.status_code}')
        return False

    # 4. Check user status after confirmation
    print('4. Checking user status after confirmation...')
    user.refresh_from_db()

    print(f'  - Active: {user.is_active}')
    print(f'  - Email confirmed: {user.email_confirmed}')
    print(f'  - Role: {user.profile.role}')
    print(
        f'  - Has business: {hasattr(user, "business") and user.profile.business is not None}'
    )

    if user.is_active:
        print('✓ User is now active')
    else:
        print('✗ User should be active after confirmation')

    if user.email_confirmed:
        print('✓ Email is now confirmed')
    else:
        print('✗ Email should be confirmed')

    if user.profile.role == Profile.Role.BUSINESS_OWNER:
        print('✓ User role is BUSINESS_OWNER')
    else:
        print(f'✗ User role should be BUSINESS_OWNER, got: {user.profile.role}')

    # 5. Check profile creation
    print('5. Checking profile creation...')
    try:
        profile = Profile.objects.get(user=user)
        print(f'✓ Profile created for user: {profile}')
    except Profile.DoesNotExist:
        print('✗ Profile was not created')

    # 6. Check business creation
    print('6. Checking business creation...')
    if hasattr(user, 'business') and user.profile.business:
        business = user.profile.business
        print(f'✓ Business created: {business.name}')
        print(f'  - Email: {business.email}')
        print(f'  - Created by: {business.created_by}')
    else:
        print('✗ Business was not created or not associated with user')

    print('\nRegistration flow test completed!')
    return True


def cleanup():
    """Clean up test data."""
    print('\nCleaning up test data...')
    try:
        user = User.objects.get(username='testuser')
        if hasattr(user, 'business') and user.profile.business:
            user.profile.business.delete()
        user.delete()
        print('✓ Test data cleaned up')
    except User.DoesNotExist:
        print('No test data to clean up')


if __name__ == '__main__':
    try:
        test_registration_flow()
    finally:
        cleanup()
