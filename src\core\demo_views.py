"""
Demo views for anonymous users to explore the tour business management platform.
"""

import json
import logging

from django.contrib import messages
from django.contrib.auth import login
from django.http import JsonResponse
from django.shortcuts import redirect
from django.urls import reverse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.views.generic import TemplateView

from .demo import DemoSession

logger = logging.getLogger(__name__)


@require_http_methods(['POST'])
@csrf_exempt
def start_demo(request):
    """Start a new demo session for anonymous users."""
    if request.user.is_authenticated:
        return JsonResponse(
            {
                'success': False,
                'error': 'Demo mode is only available for anonymous users. Please log out first.',
            },
            status=400,
        )

    try:
        demo_session = DemoSession(request)

        # Check if demo session already exists and is valid
        if demo_session.is_demo_session and not demo_session.is_expired:
            return JsonResponse(
                {
                    'success': True,
                    'message': 'Demo session already active',
                    'redirect_url': reverse('demo_dashboard'),
                    'time_remaining': str(demo_session.time_remaining),
                }
            )

        # Create new demo session
        demo_user, demo_business = demo_session.create_demo_session()

        # Log in the demo user
        login(request, demo_user, backend='django.contrib.auth.backends.ModelBackend')

        logger.info(f'Started demo session for user {demo_user.username}')
        logger.info(f'Demo business created: {demo_business.name}')
        logger.info(f'Demo user business assignment: {demo_user.business}')

        return JsonResponse(
            {
                'success': True,
                'message': 'Demo session created successfully!',
                'redirect_url': reverse('demo_dashboard'),
                'business_name': demo_business.name,
                'time_remaining': str(demo_session.time_remaining),
            }
        )

    except Exception as e:
        logger.error(f'Failed to start demo session: {str(e)}')
        return JsonResponse(
            {
                'success': False,
                'error': 'Failed to create demo session. Please try again.',
            },
            status=500,
        )


@require_http_methods(['POST'])
def end_demo(request):
    """End the current demo session."""
    if not request.user.is_authenticated:
        return JsonResponse(
            {'success': False, 'error': 'No active demo session found.'}, status=400
        )

    # Check if this is a demo user
    if not hasattr(request.user, 'is_demo_user') or not request.user.is_demo_user:
        return JsonResponse(
            {'success': False, 'error': 'This is not a demo session.'}, status=400
        )

    try:
        demo_session = DemoSession(request)
        demo_session.cleanup_demo_session()

        # Log out the user
        from django.contrib.auth import logout

        logout(request)

        logger.info('Demo session ended by user')

        return JsonResponse(
            {
                'success': True,
                'message': 'Demo session ended successfully.',
                'redirect_url': reverse('core:home'),
            }
        )

    except Exception as e:
        logger.error(f'Failed to end demo session: {str(e)}')
        return JsonResponse(
            {'success': False, 'error': 'Failed to end demo session.'}, status=500
        )


def demo_status(request):
    """Get the current demo session status."""
    if not request.user.is_authenticated:
        return JsonResponse({'is_demo': False, 'is_active': False})

    # Check if this is a demo user
    if not hasattr(request.user, 'is_demo_user') or not request.user.is_demo_user:
        return JsonResponse({'is_demo': False, 'is_active': False})

    demo_session = DemoSession(request)

    return JsonResponse(
        {
            'is_demo': True,
            'is_active': demo_session.is_demo_session and not demo_session.is_expired,
            'time_remaining': str(demo_session.time_remaining),
            'business_name': demo_session.demo_business.name
            if demo_session.demo_business
            else None,
            'expires_at': request.session.get(DemoSession.DEMO_EXPIRES_KEY),
        }
    )


class DemoDashboardView(TemplateView):
    """Dashboard view specifically for demo users."""

    template_name = 'demo/dashboard.html'

    def dispatch(self, request, *args, **kwargs):
        """Ensure only demo users can access this view."""
        if not request.user.is_authenticated:
            messages.error(request, 'Please start a demo session first.')
            return redirect('core:home')

        # Check if this is a demo user
        if not hasattr(request.user, 'is_demo_user') or not request.user.is_demo_user:
            messages.error(request, 'This page is only available in demo mode.')
            return redirect('dashboard')

        # Check if demo session is expired
        demo_session = DemoSession(request)
        if demo_session.is_expired:
            messages.error(request, 'Your demo session has expired.')
            demo_session.cleanup_demo_session()
            return redirect('core:home')

        return super().dispatch(request, *args, **kwargs)

    def get_context_data(self, **kwargs):
        """Add demo-specific context data."""
        context = super().get_context_data(**kwargs)

        demo_session = DemoSession(request=self.request)
        business = demo_session.demo_business

        # Debug logging
        logger.info(f'Demo dashboard accessed by user: {self.request.user}')
        logger.info(f'Demo session exists: {demo_session.is_demo_session}')
        logger.info(f'Demo business: {business}')
        if business:
            logger.info(f'Business ID: {business.id}, Name: {business.name}')

        # Always add demo session info
        context.update(
            {
                'demo_mode': True,
                'time_remaining': demo_session.time_remaining,
                'expires_at': self.request.session.get(DemoSession.DEMO_EXPIRES_KEY),
            }
        )

        if business:
            # Get demo statistics
            from bookings.models import Booking
            from clients.models import Client
            from invoices.models import Invoice
            from quotes.models import Quote

            # Debug: Check actual data counts
            client_count = Client.objects.filter(business=business).count()
            quote_count = Quote.objects.filter(business=business).count()
            booking_count = Booking.objects.filter(business=business).count()

            logger.info(
                f'Demo data counts - Clients: {client_count}, Quotes: {quote_count}, Bookings: {booking_count}'
            )

            context.update(
                {
                    'business': business,
                    'stats': {
                        'total_clients': Client.objects.filter(
                            business=business
                        ).count(),
                        'active_quotes': Quote.objects.filter(
                            business=business, status='draft'
                        ).count(),
                        'total_bookings': Booking.objects.filter(
                            business=business
                        ).count(),
                        'pending_invoices': Invoice.objects.filter(
                            business=business, status__in=['sent', 'overdue']
                        ).count(),
                    },
                    'recent_clients': Client.objects.filter(business=business).order_by(
                        '-created_at'
                    )[:5],
                    'recent_quotes': Quote.objects.filter(business=business).order_by(
                        '-created_at'
                    )[:5],
                    'recent_bookings': Booking.objects.filter(
                        business=business
                    ).order_by('-booking_date')[:5],
                }
            )
        else:
            # If no business found, provide empty data
            context.update(
                {
                    'business': None,
                    'stats': {
                        'total_clients': 0,
                        'active_quotes': 0,
                        'total_bookings': 0,
                        'pending_invoices': 0,
                    },
                    'recent_clients': [],
                    'recent_quotes': [],
                    'recent_bookings': [],
                }
            )

        return context


class DemoInfoView(TemplateView):
    """Information page about demo mode."""

    template_name = 'demo/info.html'

    def get_context_data(self, **kwargs):
        """Add demo information to context."""
        context = super().get_context_data(**kwargs)

        context.update(
            {
                'demo_duration_hours': DemoSession.DEMO_DURATION_HOURS,
                'features': [
                    'Client Management - Add and manage customer profiles',
                    'Quote Creation - Generate professional quotes with multiple options',
                    'Booking System - Schedule tours and manage capacity',
                    'Invoice Generation - Create and send invoices automatically',
                    'Payment Tracking - Monitor payment status and history',
                    'PDF Templates - Customize documents with your branding',
                    'Calendar Integration - View and manage tour schedules',
                    'Analytics Dashboard - Track business performance metrics',
                ],
            }
        )

        return context


def demo_feature_tour(request):
    """API endpoint for guided feature tour."""
    if not request.user.is_authenticated or not hasattr(request.user, 'is_demo_user'):
        return JsonResponse(
            {'success': False, 'error': 'Demo mode required for feature tour.'},
            status=400,
        )

    demo_session = DemoSession(request)
    business = demo_session.demo_business

    if not business:
        return JsonResponse(
            {'success': False, 'error': 'No demo business found.'}, status=400
        )

    # Handle POST request for tour completion tracking
    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            action = data.get('action')

            if action == 'tour_completed':
                logger.info(f'Demo user {request.user.username} completed feature tour')
                return JsonResponse(
                    {'success': True, 'message': 'Tour completion tracked'}
                )

        except json.JSONDecodeError:
            pass

        return JsonResponse({'success': False, 'error': 'Invalid request'}, status=400)

    # Return tour data with actual demo data
    from bookings.models import Booking
    from clients.models import Client
    from quotes.models import Quote

    tour_data = {
        'steps': [
            {
                'title': 'Welcome to Your Demo Business',
                'description': f"You're now managing {business.name}. This is your dashboard with real sample data.",
                'target': '#dashboard-overview',
                'data': {
                    'business_name': business.name,
                    'client_count': Client.objects.filter(business=business).count(),
                },
            },
            {
                'title': 'Client Management',
                'description': 'View and manage your customer database with detailed profiles.',
                'target': '#clients-section',
                'url': reverse('clients:client-list'),
                'data': {
                    'sample_clients': list(
                        Client.objects.filter(business=business).values(
                            'first_name', 'last_name', 'email'
                        )[:3]
                    )
                },
            },
            {
                'title': 'Quote System',
                'description': 'Create professional quotes with multiple options and track approval status.',
                'target': '#quotes-section',
                'url': reverse('quotes:quote-list'),
                'data': {
                    'sample_quotes': list(
                        Quote.objects.filter(business=business).values(
                            'title', 'total_amount', 'status'
                        )[:3]
                    )
                },
            },
            {
                'title': 'Booking Management',
                'description': 'Schedule tours and manage your calendar with integrated booking system.',
                'target': '#bookings-section',
                'url': reverse('bookings:booking_list'),
                'data': {
                    'sample_bookings': list(
                        Booking.objects.filter(business=business).values(
                            'booking_reference', 'status'
                        )[:3]
                    )
                },
            },
        ],
        'time_remaining': str(demo_session.time_remaining),
    }

    return JsonResponse({'success': True, 'tour_data': tour_data})
