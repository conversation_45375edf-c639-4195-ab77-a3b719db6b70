# Customization Guide

This guide covers how to customize the Tour Business Management System to match your brand and business requirements.

## Theme Customization

### Business-Specific Theming

Each business can have its own custom theme through the admin interface:

1. Navigate to **Admin > Businesses > Business Settings**
2. Upload custom logo and brand colors
3. Configure PDF templates and email signatures

### CSS Customization

#### Custom CSS per Business

Create custom CSS files in `static/css/themes/`:

```css
/* static/css/themes/business-123.css */
:root {
  --primary-color: #your-brand-color;
  --secondary-color: #your-secondary-color;
  --accent-color: #your-accent-color;
}

.business-header {
  background-color: var(--primary-color);
}

.btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}
```

#### Global Theme Override

Modify `static/css/custom.css` for system-wide changes:

```css
/* Override default Tailwind colors */
.bg-primary {
  background-color: #your-primary-color !important;
}

.text-primary {
  color: #your-primary-color !important;
}

/* Custom components */
.quote-card {
  border: 2px solid var(--primary-color);
  border-radius: 12px;
}
```

### Logo and Branding

#### Business Logo Upload

```python
# In your business model
class Business(models.Model):
    logo = models.ImageField(upload_to='logos/', blank=True)
    primary_color = models.CharField(max_length=7, default='#007bff')
    secondary_color = models.CharField(max_length=7, default='#6c757d')

    def get_logo_url(self):
        if self.logo:
            return self.logo.url
        return '/static/images/default-logo.png'
```

#### Template Usage

```html
<!-- In your templates -->
<img
  src="{{ business.get_logo_url }}"
  alt="{{ business.name }}"
  class="business-logo" />

<style>
  :root {
      --business-primary: {{ business.primary_color }};
      --business-secondary: {{ business.secondary_color }};
  }
</style>
```

## PDF Template Customization

### Creating Custom PDF Templates

#### 1. Template Structure

Create templates in `templates/pdf/`:

```html
<!-- templates/pdf/quote_template.html -->
<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <style>
      @page {
          size: A4;
          margin: 2cm;
      }

      .header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          border-bottom: 2px solid {{ business.primary_color }};
          padding-bottom: 20px;
          margin-bottom: 30px;
      }

      .logo {
          max-height: 80px;
      }

      .company-info {
          text-align: right;
          color: #666;
      }

      .quote-details {
          background-color: #f8f9fa;
          padding: 20px;
          border-radius: 8px;
          margin: 20px 0;
      }

      .total-amount {
          font-size: 24px;
          font-weight: bold;
          color: {{ business.primary_color }};
      }
    </style>
  </head>
  <body>
    <div class="header">
      <img
        src="{{ business.get_logo_url }}"
        alt="{{ business.name }}"
        class="logo" />
      <div class="company-info">
        <h2>{{ business.name }}</h2>
        <p>{{ business.address }}</p>
        <p>{{ business.phone }} | {{ business.email }}</p>
      </div>
    </div>

    <h1>Quote #{{ quote.quote_number }}</h1>

    <div class="quote-details">
      <h3>Quote Details</h3>
      <p><strong>Client:</strong> {{ quote.client.display_name }}</p>
      <p><strong>Date:</strong> {{ quote.created_at|date:"F d, Y" }}</p>
      <p><strong>Valid Until:</strong> {{ quote.valid_until|date:"F d, Y" }}</p>
    </div>

    <div class="description">
      <h3>Description</h3>
      <p>{{ quote.description|linebreaks }}</p>
    </div>

    <div class="pricing">
      <table style="width: 100%; border-collapse: collapse;">
        <tr>
          <td style="text-align: right; padding: 10px;">
            <strong>Subtotal:</strong>
          </td>
          <td style="text-align: right; padding: 10px;">
            {{ quote.subtotal }}
          </td>
        </tr>
        <tr>
          <td style="text-align: right; padding: 10px;">
            <strong>Tax:</strong>
          </td>
          <td style="text-align: right; padding: 10px;">
            {{ quote.tax_amount }}
          </td>
        </tr>
        <tr style="border-top: 2px solid #333;">
          <td style="text-align: right; padding: 10px;">
            <strong>Total:</strong>
          </td>
          <td
            style="text-align: right; padding: 10px;"
            class="total-amount">
            {{ quote.total_amount }}
          </td>
        </tr>
      </table>
    </div>

    {% if quote.terms_conditions %}
    <div class="terms">
      <h3>Terms & Conditions</h3>
      <p>{{ quote.terms_conditions|linebreaks }}</p>
    </div>
    {% endif %}
  </body>
</html>
```

#### 2. PDF Generation Service

```python
# documents/services.py
from django.template.loader import render_to_string
from weasyprint import HTML, CSS
from django.conf import settings
import os

class PDFGenerator:
    def __init__(self, business):
        self.business = business

    def generate_quote_pdf(self, quote):
        # Try business-specific template first
        template_name = f'pdf/business_{self.business.id}/quote_template.html'
        if not self._template_exists(template_name):
            template_name = 'pdf/quote_template.html'

        context = {
            'quote': quote,
            'business': self.business,
            'client': quote.client,
        }

        html_string = render_to_string(template_name, context)

        # Custom CSS for this business
        css_path = os.path.join(
            settings.STATIC_ROOT,
            f'css/pdf/business_{self.business.id}.css'
        )

        css = None
        if os.path.exists(css_path):
            css = CSS(filename=css_path)

        html = HTML(string=html_string)
        pdf = html.write_pdf(stylesheets=[css] if css else None)

        return pdf

    def _template_exists(self, template_name):
        from django.template.loader import get_template
        try:
            get_template(template_name)
            return True
        except:
            return False
```

### Template Management Interface

#### Admin Interface for Templates

```python
# documents/admin.py
from django.contrib import admin
from .models import PDFTemplate

@admin.register(PDFTemplate)
class PDFTemplateAdmin(admin.ModelAdmin):
    list_display = ['name', 'business', 'template_type', 'is_active']
    list_filter = ['template_type', 'is_active', 'business']
    search_fields = ['name', 'business__name']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'business', 'template_type', 'is_active')
        }),
        ('Template Content', {
            'fields': ('html_content', 'css_content'),
            'classes': ('wide',)
        }),
        ('Preview', {
            'fields': ('preview_image',),
            'classes': ('collapse',)
        }),
    )

    def save_model(self, request, obj, form, change):
        super().save_model(request, obj, form, change)
        # Save template files to filesystem
        obj.save_template_files()
```

#### Template Model

```python
# documents/models.py
class PDFTemplate(models.Model):
    TEMPLATE_TYPES = [
        ('quote', 'Quote'),
        ('invoice', 'Invoice'),
        ('booking_confirmation', 'Booking Confirmation'),
        ('receipt', 'Receipt'),
    ]

    business = models.ForeignKey('businesses.Business', on_delete=models.CASCADE)
    name = models.CharField(max_length=100)
    template_type = models.CharField(max_length=50, choices=TEMPLATE_TYPES)
    html_content = models.TextField()
    css_content = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)
    preview_image = models.ImageField(upload_to='template_previews/', blank=True)

    def save_template_files(self):
        # Save HTML template
        template_dir = os.path.join(
            settings.BASE_DIR,
            'templates',
            'pdf',
            f'business_{self.business.id}'
        )
        os.makedirs(template_dir, exist_ok=True)

        template_path = os.path.join(template_dir, f'{self.template_type}_template.html')
        with open(template_path, 'w') as f:
            f.write(self.html_content)

        # Save CSS file
        if self.css_content:
            css_dir = os.path.join(settings.STATIC_ROOT, 'css', 'pdf')
            os.makedirs(css_dir, exist_ok=True)

            css_path = os.path.join(css_dir, f'business_{self.business.id}.css')
            with open(css_path, 'w') as f:
                f.write(self.css_content)
```

## Email Template Customization

### Custom Email Templates

#### Business-Specific Email Templates

```html
<!-- templates/emails/business_123/quote_notification.html -->
{% extends "emails/base_email.html" %} {% block header %}
<div
  style="background: linear-gradient(135deg, {{ business.primary_color }}, {{ business.secondary_color }});">
  <img
    src="{{ business.get_logo_url }}"
    alt="{{ business.name }}"
    style="max-height: 60px;" />
  <h2 style="color: white;">{{ business.name }}</h2>
</div>
{% endblock %} {% block content %}
<!-- Custom content for this business -->
<div
  style="border-left: 4px solid {{ business.primary_color }}; padding-left: 20px;">
  <p>Dear {{ client.display_name }},</p>
  <p>Thank you for choosing {{ business.name }} for your adventure needs!</p>
  <!-- Rest of template -->
</div>
{% endblock %}
```

### Email Template Management

```python
# core/models.py
class EmailTemplate(models.Model):
    business = models.ForeignKey('businesses.Business', on_delete=models.CASCADE)
    template_type = models.CharField(max_length=50)
    subject_template = models.CharField(max_length=200)
    html_content = models.TextField()
    text_content = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)

    def render_subject(self, context):
        from django.template import Template, Context
        template = Template(self.subject_template)
        return template.render(Context(context))

    def render_html(self, context):
        from django.template import Template, Context
        template = Template(self.html_content)
        return template.render(Context(context))
```

## Component Customization with Django Cotton

### Creating Reusable Components

#### 1. Install and Configure Django Cotton

```python
# settings.py
INSTALLED_APPS = [
    # ...
    'django_cotton',
]

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'templates'],
        'OPTIONS': {
            'context_processors': [
                # ...
            ],
            'builtins': [
                'django_cotton.templatetags.cotton',
            ],
        },
    },
]
```

#### 2. Create Cotton Components

```html
<!-- templates/cotton/quote_card.html -->
<c-vars
  quote="None"
  show_actions="True"
  business_theme="True" />

<div
  class="quote-card"
  {%
  if
  business_theme
  %}
  style="border-color: {{ quote.business.primary_color }};"
  {%
  endif
  %}>
  <div class="quote-header">
    <h3>{{ quote.title }}</h3>
    <span class="quote-number">{{ quote.quote_number }}</span>
  </div>

  <div class="quote-details">
    <p><strong>Client:</strong> {{ quote.client.display_name }}</p>
    <p><strong>Amount:</strong> {{ quote.total_amount }}</p>
    <p>
      <strong>Status:</strong>
      <span class="status-badge status-{{ quote.status }}">
        {{ quote.get_status_display }}
      </span>
    </p>
  </div>

  {% if show_actions %}
  <div class="quote-actions">
    <a
      href="{% url 'quotes:quote-detail' quote.id %}"
      class="btn btn-primary">
      View Details
    </a>
    {% if quote.status == 'draft' %}
    <button
      onclick="sendQuote({{ quote.id }})"
      class="btn btn-secondary">
      Send Quote
    </button>
    {% endif %}
  </div>
  {% endif %}
</div>

<style>
  .quote-card {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin: 10px 0;
    background: white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .status-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
  }

  .status-draft {
    background: #ffc107;
    color: #000;
  }
  .status-sent {
    background: #007bff;
    color: #fff;
  }
  .status-approved {
    background: #28a745;
    color: #fff;
  }
  .status-expired {
    background: #dc3545;
    color: #fff;
  }
</style>
```

#### 3. Use Components in Templates

```html
<!-- templates/quotes/quote_list.html -->
{% extends "base.html" %} {% load cotton %} {% block content %}
<div class="quotes-grid">
  {% for quote in quotes %}
  <c-quote-card
    quote="{{ quote }}"
    show_actions="True" />
  {% endfor %}
</div>
{% endblock %}
```

### Advanced Component Features

#### Dynamic Component Loading

```html
<!-- templates/cotton/business_card.html -->
<c-vars business="None" />

<div
  class="business-card"
  style="--primary: {{ business.primary_color }};">
  <div class="business-header">
    {% if business.logo %}
    <img
      src="{{ business.get_logo_url }}"
      alt="{{ business.name }}"
      class="business-logo" />
    {% endif %}
    <h2>{{ business.name }}</h2>
  </div>

  <div class="business-info">
    <p>{{ business.description|truncatewords:20 }}</p>
    <div class="contact-info">
      <span>{{ business.email }}</span>
      <span>{{ business.phone }}</span>
    </div>
  </div>

  <!-- Load business-specific component if exists -->
  {% include "cotton/business_extras.html" with business=business %}
</div>
```

## Custom Fields and Widgets

### Business-Specific Form Fields

```python
# core/forms.py
class BusinessAwareForm(forms.Form):
    def __init__(self, *args, business=None, **kwargs):
        super().__init__(*args, **kwargs)
        self.business = business

        if business:
            # Customize form based on business settings
            self.apply_business_theme()

    def apply_business_theme(self):
        for field_name, field in self.fields.items():
            if hasattr(field.widget, 'attrs'):
                field.widget.attrs.update({
                    'style': f'border-color: {self.business.primary_color};'
                })
```

### Custom Widgets

```python
# core/widgets.py
class BusinessColorWidget(forms.TextInput):
    def __init__(self, business=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.business = business

    def render(self, name, value, attrs=None, renderer=None):
        if self.business:
            attrs = attrs or {}
            attrs['style'] = f'border-color: {self.business.primary_color};'

        return super().render(name, value, attrs, renderer)
```

## Next Steps

After customizing your system:

1. Test all customizations across different browsers
2. Ensure PDF templates render correctly
3. Validate email templates in different email clients
4. Set up version control for custom templates
5. Document your customizations for team members
6. Consider creating a style guide for consistency
