{% load i18n %}

<c-layouts.base>
  <c-slot name="title">
    {% trans "Set Up Your Business" %} - Tour Business Management
  </c-slot>

  <c-slot name="content">
    <div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div class="sm:mx-auto sm:w-full sm:max-w-md">
        <div class="text-center">
          <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
            {% trans "Set Up Your Business" %}
          </h2>
          <p class="mt-2 text-center text-sm text-gray-600">
            {% trans "Step 2 of 2: Business Information" %}
          </p>
        </div>
      </div>

      <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-4xl">
        <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <!-- Progress Bar -->
          <div class="mb-8">
            <div class="flex items-center">
              <div class="flex items-center text-sm font-medium text-green-600">
                <span class="flex w-8 h-8 bg-green-600 rounded-full items-center justify-center text-white text-xs">
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                </span>
                <span class="ml-2">{% trans "Profile" %}</span>
              </div>
              <div class="flex-1 mx-4 h-1 bg-blue-600 rounded"></div>
              <div class="flex items-center text-sm font-medium text-blue-600">
                <span
                  class="flex w-8 h-8 bg-blue-600 rounded-full items-center justify-center text-white text-xs">2</span>
                <span class="ml-2">{% trans "Business" %}</span>
              </div>
            </div>
          </div>

          <div class="space-y-8">
            <!-- Success Message -->
            <div class="bg-green-50 border border-green-200 rounded-md p-4">
              <div class="flex">
                <div class="flex-shrink-0">
                  <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                      clip-rule="evenodd" />
                  </svg>
                </div>
                <div class="ml-3">
                  <h3 class="text-sm font-medium text-green-800">
                    {% trans "Great! Your profile is complete." %}
                  </h3>
                  <div class="mt-2 text-sm text-green-700">
                    <p>
                      {% trans "Now let's set up your business information and get you ready to start managing tours." %}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Business Information Display or Creation Form -->
            {% if business %}
            <div class="bg-white border border-gray-200 rounded-lg p-6">
              <h3 class="text-lg font-medium text-gray-900 mb-4">
                {% trans "Your Business Profile" %}
              </h3>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label class="block text-sm font-medium text-gray-500">
                    {% trans "Business Name" %}
                  </label>
                  <p class="mt-1 text-sm text-gray-900">{{ business.name }}</p>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-500">{% trans "Email" %}</label>
                  <p class="mt-1 text-sm text-gray-900">{{ business.email }}</p>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-500">{% trans "Phone" %}</label>
                  <p class="mt-1 text-sm text-gray-900">{{ business.phone|default:"—" }}</p>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-500">{% trans "Created" %}</label>
                  <p class="mt-1 text-sm text-gray-900">{{ business.created_at|date:"M d, Y" }}</p>
                </div>
              </div>

              <div class="mt-4 pt-4 border-t border-gray-200">
                <p class="text-sm text-gray-600">
                  {% trans "You can update your business information later from the business settings page." %}
                </p>
              </div>
            </div>
            {% else %}
            <!-- Business Creation Form -->
            <div class="bg-white border border-gray-200 rounded-lg p-6">
              <h3 class="text-lg font-medium text-gray-900 mb-4">{% trans "Create Your Business" %}</h3>
              <p class="text-sm text-gray-600 mb-6">
                {% trans "Let's set up your tour business profile. You can always update this information later." %}
              </p>

              <form method="post" class="space-y-6">
                {% csrf_token %}

                <!-- Display form errors -->
                {% if business_form.non_field_errors %}
                <div class="bg-red-50 border border-red-200 rounded-md p-4">
                  <div class="text-sm text-red-600">
                    {{ business_form.non_field_errors }}
                  </div>
                </div>
                {% endif %}

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <!-- Business Name -->
                  <div class="md:col-span-2">
                    <label for="{{ business_form.name.id_for_label }}"
                      class="block text-sm font-medium text-gray-700 mb-2">
                      {{ business_form.name.label }} <span class="text-red-500">*</span>
                    </label>
                    {{ business_form.name }}
                    {% if business_form.name.errors %}
                    <p class="mt-1 text-sm text-red-600">{{ business_form.name.errors.0 }}</p>
                    {% endif %}
                  </div>

                  <!-- Email -->
                  <div>
                    <label for="{{ business_form.email.id_for_label }}"
                      class="block text-sm font-medium text-gray-700 mb-2">
                      {{ business_form.email.label }} <span class="text-red-500">*</span>
                    </label>
                    {{ business_form.email }}
                    {% if business_form.email.errors %}
                    <p class="mt-1 text-sm text-red-600">{{ business_form.email.errors.0 }}</p>
                    {% endif %}
                  </div>

                  <!-- Phone -->
                  <div>
                    <label for="{{ business_form.phone.id_for_label }}"
                      class="block text-sm font-medium text-gray-700 mb-2">
                      {{ business_form.phone.label }}
                    </label>
                    {{ business_form.phone }}
                    {% if business_form.phone.errors %}
                    <p class="mt-1 text-sm text-red-600">{{ business_form.phone.errors.0 }}</p>
                    {% endif %}
                  </div>

                  <!-- Address -->
                  <div>
                    <label for="{{ business_form.address_line1.id_for_label }}"
                      class="block text-sm font-medium text-gray-700 mb-2">
                      {{ business_form.address_line1.label }}
                    </label>
                    {{ business_form.address_line1 }}
                    {% if business_form.address_line1.errors %}
                    <p class="mt-1 text-sm text-red-600">{{ business_form.address_line1.errors.0 }}</p>
                    {% endif %}
                  </div>

                  <!-- City -->
                  <div>
                    <label for="{{ business_form.city.id_for_label }}"
                      class="block text-sm font-medium text-gray-700 mb-2">
                      {{ business_form.city.label }}
                    </label>
                    {{ business_form.city }}
                    {% if business_form.city.errors %}
                    <p class="mt-1 text-sm text-red-600">{{ business_form.city.errors.0 }}</p>
                    {% endif %}
                  </div>

                  <!-- Country -->
                  <div>
                    <label for="{{ business_form.country.id_for_label }}"
                      class="block text-sm font-medium text-gray-700 mb-2">
                      {{ business_form.country.label }}
                    </label>
                    {{ business_form.country }}
                    {% if business_form.country.errors %}
                    <p class="mt-1 text-sm text-red-600">{{ business_form.country.errors.0 }}</p>
                    {% endif %}
                  </div>

                  <!-- Description -->
                  <div class="md:col-span-2">
                    <label for="{{ business_form.description.id_for_label }}"
                      class="block text-sm font-medium text-gray-700 mb-2">
                      {{ business_form.description.label }}
                    </label>
                    {{ business_form.description }}
                    {% if business_form.description.errors %}
                    <p class="mt-1 text-sm text-red-600">{{ business_form.description.errors.0 }}</p>
                    {% endif %}
                  </div>
                </div>

                <div class="flex justify-between pt-6">
                  <a href="{% url 'accounts:onboarding_profile' %}"
                    class="bg-gray-300 hover:bg-gray-400 text-gray-800 px-6 py-2 rounded-md text-sm font-medium">
                    {% trans "Back" %}
                  </a>
                  <button type="submit"
                    class="group relative flex justify-center py-2 px-6 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <span class="flex items-center pr-3">
                      <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd"
                          d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                          clip-rule="evenodd" />
                      </svg>
                    </span>
                    {% trans "Create Business & Complete Setup" %}
                  </button>
                </div>
              </form>
            </div>
            {% endif %}

            <!-- Next Steps -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
              <h3 class="text-lg font-medium text-blue-900 mb-4">{% trans "What's Next?" %}</h3>

              <div class="space-y-4">
                <div class="flex items-start">
                  <div class="flex-shrink-0">
                    <div class="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-full">
                      <span class="text-sm font-medium text-blue-600">1</span>
                    </div>
                  </div>
                  <div class="ml-3">
                    <h4 class="text-sm font-medium text-blue-900">{% trans "Explore the Dashboard" %}</h4>
                    <p class="text-sm text-blue-700">
                      {% trans "Get familiar with your business dashboard and available features." %}</p>
                  </div>
                </div>

                <div class="flex items-start">
                  <div class="flex-shrink-0">
                    <div class="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-full">
                      <span class="text-sm font-medium text-blue-600">2</span>
                    </div>
                  </div>
                  <div class="ml-3">
                    <h4 class="text-sm font-medium text-blue-900">{% trans "Add Team Members" %}</h4>
                    <p class="text-sm text-blue-700">
                      {% trans "Invite agents and staff to join your business and help manage tours." %}</p>
                  </div>
                </div>

                <div class="flex items-start">
                  <div class="flex-shrink-0">
                    <div class="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-full">
                      <span class="text-sm font-medium text-blue-600">3</span>
                    </div>
                  </div>
                  <div class="ml-3">
                    <h4 class="text-sm font-medium text-blue-900">{% trans "Create Your First Quote" %}</h4>
                    <p class="text-sm text-blue-700">
                      {% trans "Start creating professional quotes for your tour services." %}</p>
                  </div>
                </div>

                <div class="flex items-start">
                  <div class="flex-shrink-0">
                    <div class="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-full">
                      <span class="text-sm font-medium text-blue-600">4</span>
                    </div>
                  </div>
                  <div class="ml-3">
                    <h4 class="text-sm font-medium text-blue-900">{% trans "Customize Your Business" %}</h4>
                    <p class="text-sm text-blue-700">
                      {% trans "Update business details, branding, and preferences to match your company." %}</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Features Overview -->
            <div class="bg-gray-50 border border-gray-200 rounded-lg p-6">
              <h3 class="text-lg font-medium text-gray-900 mb-4">{% trans "Platform Features" %}</h3>

              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div class="flex items-center space-x-3">
                  <div class="flex-shrink-0">
                    <svg class="w-6 h-6 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                  </div>
                  <span class="text-sm text-gray-700">{% trans "Quote Management" %}</span>
                </div>

                <div class="flex items-center space-x-3">
                  <div class="flex-shrink-0">
                    <svg class="w-6 h-6 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                  </div>
                  <span class="text-sm text-gray-700">{% trans "Booking System" %}</span>
                </div>

                <div class="flex items-center space-x-3">
                  <div class="flex-shrink-0">
                    <svg class="w-6 h-6 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                  </div>
                  <span class="text-sm text-gray-700">{% trans "Invoice Generation" %}</span>
                </div>

                <div class="flex items-center space-x-3">
                  <div class="flex-shrink-0">
                    <svg class="w-6 h-6 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                  </div>
                  <span class="text-sm text-gray-700">{% trans "Client Management" %}</span>
                </div>

                <div class="flex items-center space-x-3">
                  <div class="flex-shrink-0">
                    <svg class="w-6 h-6 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                  </div>
                  <span class="text-sm text-gray-700">{% trans "Team Management" %}</span>
                </div>

                <div class="flex items-center space-x-3">
                  <div class="flex-shrink-0">
                    <svg class="w-6 h-6 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                  </div>
                  <span class="text-sm text-gray-700">{% trans "Reporting & Analytics" %}</span>
                </div>
              </div>
            </div>

            <!-- Complete Setup (only show if business exists) -->
            {% if business %}
            <form method="post" class="pt-6">
              {% csrf_token %}
              <div class="flex justify-between">
                <a href="{% url 'accounts:onboarding_profile' %}"
                  class="bg-gray-300 hover:bg-gray-400 text-gray-800 px-6 py-2 rounded-md text-sm font-medium">
                  {% trans "Back" %}
                </a>
                <button type="submit"
                  class="group relative flex justify-center py-2 px-6 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                  <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                    <svg class="h-5 w-5 text-green-500 group-hover:text-green-400" viewBox="0 0 20 20"
                      fill="currentColor">
                      <path fill-rule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                        clip-rule="evenodd" />
                    </svg>
                  </span>
                  {% trans "Complete Setup & Go to Dashboard" %}
                </button>
              </div>
            </form>
            {% endif %}
          </div>
        </div>
      </div>
    </div>
  </c-slot name="content">
</c-layouts.base>
