<!DOCTYPE html>
<html>
<head>
    <title>Demo Test Page</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .container { max-width: 800px; margin: 0 auto; }
        .header { background: #3b82f6; color: white; padding: 20px; border-radius: 8px; }
        .content { background: #f8fafc; padding: 20px; margin: 20px 0; border-radius: 8px; }
        .button { background: #10b981; color: white; padding: 12px 24px; border: none; border-radius: 6px; cursor: pointer; }
        .button:hover { background: #059669; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 TourFlow Demo Test Page</h1>
            <p>This is a test page to verify the demo system is working.</p>
        </div>
        
        <div class="content">
            <h2>Demo Information</h2>
            <p>Demo Duration: {{ demo_duration_hours|default:"2" }} hours</p>
            <p>Features Available: {{ features|length|default:"8" }} features</p>
            
            <h3>Available Features:</h3>
            <ul>
                {% for feature in features %}
                <li>{{ feature }}</li>
                {% empty %}
                <li>Client Management - Add and manage customer profiles</li>
                <li>Quote Creation - Generate professional quotes</li>
                <li>Booking System - Schedule tours and manage capacity</li>
                <li>Invoice Generation - Create and send invoices</li>
                <li>Payment Tracking - Monitor payment status</li>
                <li>PDF Templates - Customize documents</li>
                <li>Calendar Integration - View tour schedules</li>
                <li>Analytics Dashboard - Track performance</li>
                {% endfor %}
            </ul>
        </div>
        
        <div class="content">
            <h2>Start Demo Session</h2>
            <p>Click the button below to start your demo session with sample data.</p>
            <button class="button" onclick="startDemo()">Start 2-Hour Demo</button>
        </div>
    </div>
    
    <script>
        function startDemo() {
            fetch('/demo/start/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': '{{ csrf_token }}'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Demo session created successfully!');
                    window.location.href = data.redirect_url;
                } else {
                    alert('Error: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Failed to start demo session');
            });
        }
    </script>
</body>
</html>
