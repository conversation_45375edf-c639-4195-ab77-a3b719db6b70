"""
Management command to test the demo system.
"""

import logging
from django.core.management.base import BaseCommand
from django.test import RequestFactory
from django.contrib.sessions.middleware import SessionMiddleware
from django.contrib.auth.middleware import AuthenticationMiddleware
from django.contrib.messages.middleware import MessageMiddleware

from core.demo import DemoSession

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Test the demo system functionality'

    def handle(self, *args, **options):
        """Test demo session creation."""
        self.stdout.write('Testing demo system...')
        
        try:
            # Create a fake request
            factory = RequestFactory()
            request = factory.get('/')
            
            # Add session middleware
            session_middleware = SessionMiddleware(lambda req: None)
            session_middleware.process_request(request)
            request.session.save()
            
            # Add auth middleware
            auth_middleware = AuthenticationMiddleware(lambda req: None)
            auth_middleware.process_request(request)
            
            # Add messages middleware
            messages_middleware = MessageMiddleware(lambda req: None)
            messages_middleware.process_request(request)
            
            # Test demo session creation
            self.stdout.write('Creating demo session...')
            demo_session = DemoSession(request)
            demo_user, demo_business = demo_session.create_demo_session()
            
            self.stdout.write(
                self.style.SUCCESS(
                    f'Demo session created successfully!\n'
                    f'User: {demo_user.username} (ID: {demo_user.id})\n'
                    f'Business: {demo_business.name} (ID: {demo_business.id})'
                )
            )
            
            # Check if data was generated
            from clients.models import Client
            from quotes.models import Quote
            from bookings.models import Booking
            
            client_count = Client.objects.filter(business=demo_business).count()
            quote_count = Quote.objects.filter(business=demo_business).count()
            booking_count = Booking.objects.filter(business=demo_business).count()
            
            self.stdout.write(
                self.style.SUCCESS(
                    f'Generated data:\n'
                    f'Clients: {client_count}\n'
                    f'Quotes: {quote_count}\n'
                    f'Bookings: {booking_count}'
                )
            )
            
            # Clean up
            self.stdout.write('Cleaning up demo session...')
            demo_session.cleanup_demo_session()
            self.stdout.write(self.style.SUCCESS('Demo session cleaned up successfully!'))
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error testing demo system: {str(e)}')
            )
            logger.error(f'Demo test failed: {str(e)}')
            raise
