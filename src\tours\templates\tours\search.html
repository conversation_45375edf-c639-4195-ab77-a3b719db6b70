{% extends "base.html" %}
{% load static %}
{% block title %}
    Search Tours - Alpine Adventures
{% endblock title %}
{% block content %}
    <div class="min-h-screen bg-gray-50">
        <!-- Search Header -->
        <div class="bg-white shadow-sm">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                <div class="flex items-center justify-between mb-6">
                    <h1 class="text-3xl font-bold text-gray-900">Search Tours</h1>
                    <a href="{% url 'tours:public-catalog' %}"
                       class="text-blue-600 hover:text-blue-800">← Back to Catalog</a>
                </div>
                <!-- Search Form -->
                <form method="get" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Destination</label>
                        <input type="text"
                               name="destination"
                               value="{{ request.GET.destination }}"
                               placeholder="Where do you want to go?"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Activity</label>
                        <select name="activity"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">All Activities</option>
                            <option value="hiking"
                                    {% if request.GET.activity == 'hiking' %}selected{% endif %}>Hiking</option>
                            <option value="cycling"
                                    {% if request.GET.activity == 'cycling' %}selected{% endif %}>Cycling</option>
                            <option value="wildlife"
                                    {% if request.GET.activity == 'wildlife' %}selected{% endif %}>Wildlife</option>
                            <option value="cultural"
                                    {% if request.GET.activity == 'cultural' %}selected{% endif %}>Cultural</option>
                            <option value="adventure"
                                    {% if request.GET.activity == 'adventure' %}selected{% endif %}>Adventure</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Duration</label>
                        <select name="duration"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">Any Duration</option>
                            <option value="1-3" {% if request.GET.duration == '1-3' %}selected{% endif %}>1-3 days</option>
                            <option value="4-7" {% if request.GET.duration == '4-7' %}selected{% endif %}>4-7 days</option>
                            <option value="8+" {% if request.GET.duration == '8+' %}selected{% endif %}>8+ days</option>
                        </select>
                    </div>
                    <div class="flex items-end">
                        <button type="submit"
                                class="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-semibold transition-colors">
                            Search
                        </button>
                    </div>
                </form>
            </div>
        </div>
        <!-- Search Results -->
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            {% if tours %}
                <div class="mb-6">
                    <p class="text-gray-600">Found {{ tours|length }} tour{{ tours|length|pluralize }} matching your criteria</p>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    {% for tour in tours %}
                        <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow overflow-hidden">
                            <!-- Tour Image Placeholder -->
                            <div class="h-48 bg-gradient-to-r from-blue-400 to-green-400 flex items-center justify-center">
                                <span class="text-white text-6xl">
                                    {% if tour.activity_type == 'hiking' %}
                                        🥾
                                    {% elif tour.activity_type == 'cycling' %}
                                        🚴
                                    {% elif tour.activity_type == 'wildlife' %}
                                        🦁
                                    {% elif tour.activity_type == 'cultural' %}
                                        🏛️
                                    {% elif tour.activity_type == 'adventure' %}
                                        🧗
                                    {% else %}
                                        🌟
                                    {% endif %}
                                </span>
                            </div>
                            <div class="p-6">
                                <div class="flex justify-between items-start mb-2">
                                    <h3 class="text-xl font-bold text-gray-900">{{ tour.name }}</h3>
                                    {% if tour.has_urgent_availability %}
                                        <span class="bg-red-100 text-red-800 text-xs font-semibold px-2 py-1 rounded-full">
                                            Only {{ tour.min_available_spots }} left!
                                        </span>
                                    {% endif %}
                                </div>
                                <p class="text-gray-600 mb-2">📍 {{ tour.destination }}</p>
                                <p class="text-gray-600 mb-4">⏱️ {{ tour.duration_days }} days • {{ tour.get_difficulty_level_display }}</p>
                                <p class="text-gray-700 mb-4 line-clamp-2">{{ tour.description|truncatewords:20 }}</p>
                                <div class="flex justify-between items-center">
                                    <div>
                                        {% if tour.next_available_date %}
                                            <p class="text-sm text-gray-600">Next tour: {{ tour.next_available_date|date:"M d, Y" }}</p>
                                        {% else %}
                                            <p class="text-sm text-gray-500">No upcoming dates</p>
                                        {% endif %}
                                    </div>
                                    <a href="{% url 'tours:public-detail' tour.pk %}"
                                       class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-semibold transition-colors">
                                        View Details
                                    </a>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="text-center py-12">
                    <div class="text-6xl mb-4">🔍</div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">No tours found</h3>
                    <p class="text-gray-600 mb-6">Try adjusting your search criteria or browse all available tours.</p>
                    <a href="{% url 'tours:public-catalog' %}"
                       class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors">
                        Browse All Tours
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
{% endblock content %}
{% block extra_css %}
    <style>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
    </style>
{% endblock extra_css %}
