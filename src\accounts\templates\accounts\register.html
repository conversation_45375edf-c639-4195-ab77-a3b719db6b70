{% load i18n %}

<c-layouts.base>
  <c-slot name="title">{% trans "Create Account" %}</c-slot>

  <c-slot name="content">
    <div class="flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div class="max-w-2xl w-full space-y-8">
        <div>
          <div class="mx-auto h-12 w-auto flex justify-center">
            <svg class="h-12 w-12 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
            </svg>
          </div>
          <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
            {% trans "Create your business account" %}
          </h2>
          <p class="mt-2 text-center text-sm text-gray-600">
            {% trans "Start your tour business management journey" %}
          </p>
          <p class="mt-1 text-center text-sm text-gray-600">
            {% trans "Already have an account?" %}
            <a href="{% url 'accounts:login' %}" class="font-medium text-blue-600 hover:text-blue-500">
              {% trans "Sign in here" %}
            </a>
          </p>
        </div>

        <form class="mt-8 space-y-6" method="post">
          {% csrf_token %}

          {% if form.errors %}
          <div class="bg-red-50 border border-red-200 rounded-md p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                    clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">
                  {% trans "There were errors with your registration" %}
                </h3>
                <div class="mt-2 text-sm text-red-700">
                  <ul class="list-disc pl-5 space-y-1">
                    {% for field, errors in form.errors.items %}
                    {% for error in errors %}
                    <li>{{ field|capfirst }}: {{ error }}</li>
                    {% endfor %}
                    {% endfor %}
                  </ul>
                </div>
              </div>
            </div>
          </div>
          {% endif %}

          <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">{% trans "Account Information" %}</h3>
            <p class="text-sm text-gray-600 mb-4">
              {% trans "Create your account with a username, email, and password. You can complete your profile after email confirmation." %}
            </p>

            <div class="space-y-4 mb-3">
              <div>
                <label for="{{ form.username.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                  {% trans "Username" %} *
                </label>
                {{ form.username }}
              </div>

              <div>
                <label for="{{ form.email.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                  {% trans "Email Address" %} *
                </label>
                {{ form.email }}
                <p class="text-xs text-gray-500 mt-1">
                  {% trans "We'll send a confirmation link to this email address" %}</p>
              </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label for="{{ form.password1.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                  {% trans "Password" %} *
                </label>
                {{ form.password1 }}
              </div>

              <div>
                <label for="{{ form.password2.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                  {% trans "Confirm Password" %} *
                </label>
                {{ form.password2 }}
              </div>
            </div>

            <div class="mt-4 text-sm text-gray-600">
              <ul class="list-disc pl-5 space-y-1">
                <li>{% trans "Your password must contain at least 8 characters." %}</li>
                <li>{% trans "Your password can't be too similar to your other personal information." %}
                </li>
                <li>{% trans "Your password can't be a commonly used password." %}</li>
                <li>{% trans "Your password can't be entirely numeric." %}</li>
              </ul>
            </div>
          </div>

          <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd"
                    d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                    clip-rule="evenodd"></path>
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-blue-800">
                  {% trans "What happens next?" %}
                </h3>
                <div class="mt-2 text-sm text-blue-700">
                  <ol class="list-decimal pl-5 space-y-1">
                    <li>{% trans "We'll send a confirmation email to your email address" %}</li>
                    <li>{% trans "Click the confirmation link to activate your account" %}</li>
                    <li>{% trans "You'll be set up as a business administrator" %}</li>
                    <li>{% trans "Complete your profile and business setup" %}</li>
                  </ol>
                </div>
              </div>
            </div>
          </div>

          <div>
            <button type="submit"
              class="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
              <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                <svg class="h-5 w-5 text-blue-500 group-hover:text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                  <path
                    d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </span>
              {% trans "Create Account & Send Confirmation" %}
            </button>
          </div>
        </form>
      </div>
    </div>
  </c-slot>
</c-layouts.base>
