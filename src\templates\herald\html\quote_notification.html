{% extends "herald/html/base_email.html" %}

{% block title %}Quote {{ action|title }} - {{ quote.quote_number }}{% endblock %}

{% block header %}
<h2>Quote {{ action|title }}</h2>
{% endblock %}

{% block content %}
<p>Dear {{ client.display_name }},</p>

{% if action == 'created' %}
<p>We are pleased to provide you with a quote for your tour requirements.</p>
{% elif action == 'updated' %}
<p>Your quote has been updated with new information.</p>
{% elif action == 'sent' %}
<p>Your quote is ready for review.</p>
{% elif action == 'approved' %}
<div class="success-box">
    <p><strong>Great news!</strong> Your quote has been approved and we're excited to move forward with your tour.</p>
</div>
{% elif action == 'expired' %}
<div class="warning-box">
    <p><strong>Notice:</strong> Your quote has expired. Please contact us if you're still interested in proceeding.</p>
</div>
{% endif %}

<div class="info-box">
    <h3>Quote Details</h3>
    <table class="details-table">
        <tr>
            <th>Quote Number:</th>
            <td>{{ quote.quote_number }}</td>
        </tr>
        <tr>
            <th>Title:</th>
            <td>{{ quote.title }}</td>
        </tr>
        <tr>
            <th>Total Amount:</th>
            <td>{{ quote.total_amount }}</td>
        </tr>
        <tr>
            <th>Valid Until:</th>
            <td>{{ quote.valid_until|date:"F d, Y" }}</td>
        </tr>
        <tr>
            <th>Status:</th>
            <td>{{ quote.get_status_display }}</td>
        </tr>
    </table>
</div>

{% if quote.description %}
<h3>Description</h3>
<p>{{ quote.description|linebreaks }}</p>
{% endif %}

{% if action == 'sent' and quote.public_hash %}
<p>You can view and approve your quote online by clicking the button below:</p>
<p style="text-align: center;">
    <a href="{{ request.build_absolute_uri }}{% url 'quotes:quote-public-view' quote.public_hash %}" class="button">
        View Quote Online
    </a>
</p>
{% endif %}

{% if action == 'approved' %}
<p>We will be in touch shortly to discuss the next steps and finalize your booking details.</p>
{% endif %}

<p>If you have any questions about this quote, please don't hesitate to contact us.</p>
{% endblock %}
