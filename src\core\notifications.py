"""
Email notifications using django-herald.
"""

from constance import config
from herald import registry
from herald.base import EmailNotification


@registry.register_decorator()
class QuoteNotification(EmailNotification):
    """Notification for quote-related events."""

    template_name = 'emails/quote_notification'
    subject = 'Quote Update - {{ quote.quote_number }}'

    def __init__(self, quote, action='created'):
        self.context = {
            'quote': quote,
            'action': action,
            'business': quote.business,
            'client': quote.client,
            'site_name': config.SITE_NAME,
            'company_name': config.COMPANY_NAME,
            'email_signature': config.EMAIL_SIGNATURE,
        }
        self.to_emails = [quote.client.email] if quote.client.email else []

        # Add business owner to CC if different from client
        if quote.business.email and quote.business.email != quote.client.email:
            self.cc_emails = [quote.business.email]


@registry.register_decorator()
class InvoiceNotification(EmailNotification):
    """Notification for invoice-related events."""

    template_name = 'emails/invoice_notification'
    subject = 'Invoice {{ action|title }} - {{ invoice.invoice_number }}'

    def __init__(self, invoice, action='created'):
        self.context = {
            'invoice': invoice,
            'action': action,
            'business': invoice.business,
            'client': invoice.client,
            'site_name': config.SITE_NAME,
            'company_name': config.COMPANY_NAME,
            'email_signature': config.EMAIL_SIGNATURE,
        }
        self.to_emails = [invoice.client.email] if invoice.client.email else []

        # Add business owner to CC if different from client
        if invoice.business.email and invoice.business.email != invoice.client.email:
            self.cc_emails = [invoice.business.email]


@registry.register_decorator()
class PaymentNotification(EmailNotification):
    """Notification for payment-related events."""

    template_name = 'emails/payment_notification'
    subject = 'Payment {{ action|title }} - {{ payment.invoice.invoice_number }}'

    def __init__(self, payment, action='received'):
        self.context = {
            'payment': payment,
            'action': action,
            'invoice': payment.invoice,
            'business': payment.business,
            'client': payment.client,
            'site_name': config.SITE_NAME,
            'company_name': config.COMPANY_NAME,
            'email_signature': config.EMAIL_SIGNATURE,
        }
        self.to_emails = [payment.client.email] if payment.client.email else []

        # Add business owner to CC
        if payment.business.email and payment.business.email != payment.client.email:
            self.cc_emails = [payment.business.email]


@registry.register_decorator()
class BookingNotification(EmailNotification):
    """Notification for booking-related events."""

    template_name = 'emails/booking_notification'
    subject = 'Booking {{ action|title }} - {{ booking.event.title }}'

    def __init__(self, booking, action='confirmed'):
        self.context = {
            'booking': booking,
            'action': action,
            'event': booking.event,
            'business': booking.business,
            'client': booking.client,
            'site_name': config.SITE_NAME,
            'company_name': config.COMPANY_NAME,
            'email_signature': config.EMAIL_SIGNATURE,
        }
        self.to_emails = [booking.client.email] if booking.client.email else []

        # Add business owner to CC
        if booking.business.email and booking.business.email != booking.client.email:
            self.cc_emails = [booking.business.email]


@registry.register_decorator()
class WelcomeNotification(EmailNotification):
    """Welcome notification for new clients."""

    template_name = 'emails/welcome_notification'
    subject = 'Welcome to {{ business.name }}!'

    def __init__(self, client):
        self.context = {
            'client': client,
            'business': client.business,
            'site_name': config.SITE_NAME,
            'company_name': config.COMPANY_NAME,
            'email_signature': config.EMAIL_SIGNATURE,
        }
        self.to_emails = [client.email] if client.email else []


@registry.register_decorator()
class ReminderNotification(EmailNotification):
    """Reminder notifications for various events."""

    template_name = 'emails/reminder_notification'
    subject = 'Reminder: {{ reminder_type|title }} - {{ subject }}'

    def __init__(self, recipient_email, reminder_type, subject, context_data=None):
        self.context = {
            'reminder_type': reminder_type,
            'subject': subject,
            'site_name': config.SITE_NAME,
            'company_name': config.COMPANY_NAME,
            'email_signature': config.EMAIL_SIGNATURE,
        }
        if context_data:
            self.context.update(context_data)

        self.to_emails = [recipient_email] if recipient_email else []


@registry.register_decorator()
class EmailConfirmationNotification(EmailNotification):
    """Email confirmation notification for new user registration."""

    template_name = 'email_confirmation'
    subject = 'Confirm your email address - {{ site_name }}'

    def __init__(self, user, confirmation_url):
        self.context = {
            'user': user,
            'confirmation_url': confirmation_url,
            'site_name': config.SITE_NAME,
            'company_name': config.COMPANY_NAME,
            'email_signature': config.EMAIL_SIGNATURE,
        }
        self.to_emails = [user.email] if user.email else []


@registry.register_decorator()
class UserInvitationNotification(EmailNotification):
    """Notification for user invitations to join a business."""

    template_name = 'emails/user_invitation'
    subject = 'You have been invited to join {{ business.name }}'

    def __init__(self, user, business, invitation_url, invited_by):
        self.context = {
            'user': user,
            'business': business,
            'invitation_url': invitation_url,
            'invited_by': invited_by,
            'site_name': config.SITE_NAME,
            'company_name': config.COMPANY_NAME,
            'email_signature': config.EMAIL_SIGNATURE,
        }
        self.to_emails = [user.email] if user.email else []


@registry.register_decorator()
class WelcomeBusinessOwnerNotification(EmailNotification):
    """Welcome notification for new business owners after email confirmation."""

    template_name = 'welcome_business_owner'
    subject = "Welcome to {{ site_name }} - Let's get started!"

    def __init__(self, user, login_url):
        self.context = {
            'user': user,
            'login_url': login_url,
            'site_name': config.SITE_NAME,
            'company_name': config.COMPANY_NAME,
            'email_signature': config.EMAIL_SIGNATURE,
        }
        self.to_emails = [user.email] if user.email else []


@registry.register_decorator()
class PasswordResetNotification(EmailNotification):
    """Password reset notification with proper URL namespace handling."""

    template_name = 'password_reset'
    subject = 'Password Reset Request - {{ site_name }}'

    def __init__(self, user, reset_url):
        self.context = {
            'user': user,
            'email': user.email,
            'reset_url': reset_url,
            'site_name': config.SITE_NAME,
            'company_name': config.COMPANY_NAME,
            'email_signature': config.EMAIL_SIGNATURE,
        }
        self.to_emails = [user.email] if user.email else []


# Helper functions to send notifications
def send_quote_notification(quote, action='created'):
    """Send quote notification if notifications are enabled."""
    if config.ENABLE_NOTIFICATIONS:
        QuoteNotification(quote, action).send()


def send_invoice_notification(invoice, action='created'):
    """Send invoice notification if notifications are enabled."""
    if config.ENABLE_NOTIFICATIONS:
        InvoiceNotification(invoice, action).send()


def send_payment_notification(payment, action='received'):
    """Send payment notification if notifications are enabled."""
    if config.ENABLE_NOTIFICATIONS:
        PaymentNotification(payment, action).send()


def send_booking_notification(booking, action='confirmed'):
    """Send booking notification if notifications are enabled."""
    if config.ENABLE_NOTIFICATIONS:
        BookingNotification(booking, action).send()


def send_welcome_notification(client):
    """Send welcome notification to new client if notifications are enabled."""
    if config.ENABLE_NOTIFICATIONS:
        WelcomeNotification(client).send()


def send_reminder_notification(
    recipient_email, reminder_type, subject, context_data=None
):
    """Send reminder notification if notifications are enabled."""
    if config.ENABLE_NOTIFICATIONS:
        ReminderNotification(
            recipient_email, reminder_type, subject, context_data
        ).send()
