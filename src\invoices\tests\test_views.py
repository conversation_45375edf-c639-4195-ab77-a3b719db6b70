"""
Integration tests for invoices views.
"""

import json
from datetime import date, timedelta

from django.contrib.auth import get_user_model
from django.test import Client, TestCase
from django.urls import reverse
from djmoney.money import Money

from businesses.models import Business
from clients.models import Client as ClientModel
from invoices.models import Invoice
from quotes.models import Quote

User = get_user_model()


class InvoiceViewsTest(TestCase):
    """Test cases for invoice views."""

    def setUp(self):
        """Set up test data."""
        self.client = Client()

        # Create test user and business
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            role=Profile.Role.BUSINESS_OWNER,
        )

        self.business = Business.objects.create(
            name='Test Tours',
            email='<EMAIL>',
            phone='+1234567890',
            address_line1='123 Test St',
            city='Test City',
            state_province='Test State',
            postal_code='12345',
            country='Test Country',
            created_by=self.user,
        )

        self.user.profile.business = self.business
        self.user.save()

        # Create test client
        self.test_client = ClientModel.objects.create(
            business=self.business,
            first_name='<PERSON>',
            last_name='Doe',
            email='<EMAIL>',
            phone='+1234567890',
            created_by=self.user,
        )

        # Create test quote
        self.quote = Quote.objects.create(
            business=self.business,
            client=self.test_client,
            title='Safari Adventure',
            description='3-day safari tour',
            status=Quote.Status.APPROVED,
            valid_until=date.today() + timedelta(days=30),
            subtotal=Money(1000, 'USD'),
            tax_amount=Money(100, 'USD'),
            total_amount=Money(1100, 'USD'),
            created_by=self.user,
        )

        # Create test invoice
        self.invoice = Invoice.objects.from_quote(self.quote)

    def test_invoice_detail_authenticated(self):
        """Test invoice detail view for authenticated users."""
        self.client.login(username='testuser', password='testpass123')

        url = reverse('invoices:detail', kwargs={'invoice_id': self.invoice.id})
        response = self.client.get(url)

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.invoice.invoice_number)
        self.assertContains(response, str(self.invoice.total_amount))

    def test_invoice_detail_unauthenticated(self):
        """Test invoice detail view for unauthenticated users."""
        url = reverse('invoices:detail', kwargs={'invoice_id': self.invoice.id})
        response = self.client.get(url)

        # Should redirect to login
        self.assertEqual(response.status_code, 302)

    def test_invoice_detail_unauthorized(self):
        """Test invoice detail view without proper authorization."""
        # Create another user from different business
        other_user = User.objects.create_user(
            username='otheruser', email='<EMAIL>', password='testpass123'
        )

        self.client.login(username='otheruser', password='testpass123')

        url = reverse('invoices:detail', kwargs={'invoice_id': self.invoice.id})
        response = self.client.get(url)

        self.assertEqual(response.status_code, 403)

    def test_mark_invoice_paid_success(self):
        """Test successful invoice payment marking."""
        # Set invoice to sent status
        self.invoice.status = Invoice.Status.SENT
        self.invoice.save()

        self.client.login(username='testuser', password='testpass123')

        url = reverse('invoices:mark_paid', kwargs={'invoice_id': self.invoice.id})
        response = self.client.post(url)

        self.assertEqual(response.status_code, 200)

        data = json.loads(response.content)
        self.assertTrue(data['success'])
        self.assertEqual(data['invoice_number'], self.invoice.invoice_number)
        self.assertIn('paid_at', data)

        # Refresh invoice from database
        self.invoice.refresh_from_db()
        self.assertEqual(self.invoice.status, Invoice.Status.PAID)
        self.assertIsNotNone(self.invoice.paid_at)

    def test_mark_invoice_paid_invalid_status(self):
        """Test marking invoice as paid with invalid status."""
        # Invoice is already in draft status
        self.assertEqual(self.invoice.status, Invoice.Status.DRAFT)

        self.client.login(username='testuser', password='testpass123')

        url = reverse('invoices:mark_paid', kwargs={'invoice_id': self.invoice.id})
        response = self.client.post(url)

        self.assertEqual(response.status_code, 400)

        data = json.loads(response.content)
        self.assertFalse(data['success'])

    def test_mark_invoice_paid_unauthorized(self):
        """Test marking invoice as paid without authorization."""
        # Create another user from different business
        other_user = User.objects.create_user(
            username='otheruser', email='<EMAIL>', password='testpass123'
        )

        self.invoice.status = Invoice.Status.SENT
        self.invoice.save()

        self.client.login(username='otheruser', password='testpass123')

        url = reverse('invoices:mark_paid', kwargs={'invoice_id': self.invoice.id})
        response = self.client.post(url)

        self.assertEqual(response.status_code, 403)

        data = json.loads(response.content)
        self.assertFalse(data['success'])

    def test_send_invoice_success(self):
        """Test successful invoice sending."""
        # Invoice is in draft status by default
        self.assertEqual(self.invoice.status, Invoice.Status.DRAFT)

        self.client.login(username='testuser', password='testpass123')

        url = reverse('invoices:send', kwargs={'invoice_id': self.invoice.id})
        response = self.client.post(url)

        self.assertEqual(response.status_code, 200)

        data = json.loads(response.content)
        self.assertTrue(data['success'])
        self.assertEqual(data['invoice_number'], self.invoice.invoice_number)
        self.assertIn('sent_at', data)

        # Refresh invoice from database
        self.invoice.refresh_from_db()
        self.assertEqual(self.invoice.status, Invoice.Status.SENT)
        self.assertIsNotNone(self.invoice.sent_at)

    def test_send_invoice_invalid_status(self):
        """Test sending invoice with invalid status."""
        # Set invoice to sent status
        self.invoice.status = Invoice.Status.SENT
        self.invoice.save()

        self.client.login(username='testuser', password='testpass123')

        url = reverse('invoices:send', kwargs={'invoice_id': self.invoice.id})
        response = self.client.post(url)

        self.assertEqual(response.status_code, 400)

        data = json.loads(response.content)
        self.assertFalse(data['success'])

    def test_send_invoice_unauthorized(self):
        """Test sending invoice without authorization."""
        # Create another user from different business
        other_user = User.objects.create_user(
            username='otheruser', email='<EMAIL>', password='testpass123'
        )

        self.client.login(username='otheruser', password='testpass123')

        url = reverse('invoices:send', kwargs={'invoice_id': self.invoice.id})
        response = self.client.post(url)

        self.assertEqual(response.status_code, 403)

        data = json.loads(response.content)
        self.assertFalse(data['success'])

    def test_invoice_overdue_detection(self):
        """Test overdue invoice detection in view."""
        # Set invoice to sent with past due date
        self.invoice.status = Invoice.Status.SENT
        self.invoice.due_date = date.today() - timedelta(days=1)
        self.invoice.save()

        self.client.login(username='testuser', password='testpass123')

        url = reverse('invoices:detail', kwargs={'invoice_id': self.invoice.id})
        response = self.client.get(url)

        self.assertEqual(response.status_code, 200)
        self.assertTrue(response.context['is_overdue'])

    def test_invoice_not_overdue(self):
        """Test non-overdue invoice in view."""
        # Set invoice to sent with future due date
        self.invoice.status = Invoice.Status.SENT
        self.invoice.due_date = date.today() + timedelta(days=30)
        self.invoice.save()

        self.client.login(username='testuser', password='testpass123')

        url = reverse('invoices:detail', kwargs={'invoice_id': self.invoice.id})
        response = self.client.get(url)

        self.assertEqual(response.status_code, 200)
        self.assertFalse(response.context['is_overdue'])
