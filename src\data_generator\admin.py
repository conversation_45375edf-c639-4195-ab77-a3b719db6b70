"""
Admin interface for data generator.
"""

from django.contrib import admin
from django.shortcuts import render, redirect
from django.urls import path
from django.contrib import messages
from django.db import transaction

from .services import DataGeneratorService


class DataGeneratorAdmin:
    """Admin interface for data generation."""

    def get_urls(self):
        urls = [
            path('generate/', self.admin_site.admin_view(self.generate_data_view), name='data_generator_generate'),
        ]
        return urls

    def generate_data_view(self, request):
        """Admin view for generating data."""
        if request.method == 'POST':
            action = request.POST.get('action')
            scale = request.POST.get('scale', 'medium')

            try:
                service = DataGeneratorService()

                with transaction.atomic():
                    if action == 'clear_all':
                        service.clear_all_data()
                        messages.success(request, 'All data cleared successfully!')

                    elif action == 'generate_complete':
                        clear_first = request.POST.get('clear_first') == 'on'
                        if clear_first:
                            service.clear_all_data()

                        counts = service.generate_complete_dataset(scale)
                        messages.success(
                            request,
                            f'Successfully generated {scale} dataset! '
                            f'Created {sum(counts.values())} objects.'
                        )

                    else:
                        messages.error(request, f'Unknown action: {action}')

            except Exception as e:
                messages.error(request, f'Error: {str(e)}')

            return redirect('admin:data_generator_generate')

        # Get current data counts
        from accounts.models import User
        from businesses.models import Business
        from clients.models import Client, Traveler
        from bookings.models import TourEvent, Booking
        from quotes.models import Quote
        from invoices.models import Invoice

        context = {
            'title': 'Data Generator',
            'current_counts': {
                'users': User.objects.filter(is_superuser=False).count(),
                'businesses': Business.objects.count(),
                'clients': Client.objects.count(),
                'travelers': Traveler.objects.count(),
                'events': TourEvent.objects.count(),
                'bookings': Booking.objects.count(),
                'quotes': Quote.objects.count(),
                'invoices': Invoice.objects.count(),
            },
            'scale_options': [
                ('small', 'Small Dataset (1 business, 5 clients, 8 bookings)'),
                ('medium', 'Medium Dataset (2 businesses, 10 clients, 15 bookings)'),
                ('large', 'Large Dataset (3 businesses, 20 clients, 30 bookings)'),
            ]
        }

        return render(request, 'admin/data_generator/generate.html', context)


# Note: Admin interface is available through the web interface at /data-generator/
# No need to register with Django admin since we have our own interface
