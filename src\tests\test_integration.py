"""
Integration tests for the complete registration and user management system.
Tests the full user journey from registration to business management.
"""

import pytest
from django.contrib.auth import get_user_model
from django.test import Client
from django.urls import reverse

from accounts.models import Profile
from businesses.models import Business

User = get_user_model()


@pytest.mark.django_db
class TestCompleteUserJourney:
    """Test the complete user journey from registration to business management."""

    def setup_method(self):
        """Set up test client."""
        self.client = Client()

    def test_complete_business_owner_journey(self):
        """Test the complete journey of a business owner from registration to user management."""

        # Step 1: User registration
        registration_data = {
            'username': 'newbusiness',
            'email': '<EMAIL>',
            'password1': 'securepassword123',
            'password2': 'securepassword123',
        }

        response = self.client.post(reverse('accounts:register'), registration_data)
        assert response.status_code == 302  # Redirect after registration

        # User should be created but inactive
        user = User.objects.get(username='newbusiness')
        assert not user.is_active
        assert not user.email_confirmed
        assert user.email_confirmation_token is not None

        # Step 2: Email confirmation
        token = user.email_confirmation_token
        confirmation_url = reverse(
            'accounts:confirm_email', kwargs={'token': str(token)}
        )
        response = self.client.get(confirmation_url)

        assert response.status_code == 302  # Redirect after confirmation

        # User should now be active and business owner
        user.refresh_from_db()
        assert user.is_active
        assert user.email_confirmed
        assert user.profile.role == Profile.Role.BUSINESS_OWNER
        assert user.profile.business is not None

        # Business should be created
        business = user.profile.business
        assert business.created_by == user
        assert business.email == user.email

        # Step 3: First login (should trigger onboarding)
        login_successful = self.client.login(
            username='newbusiness', password='securepassword123'
        )
        assert login_successful

        # Access home page - should redirect to onboarding
        response = self.client.get(reverse('home'))
        assert response.status_code == 302
        assert 'onboarding' in response.url

        # Step 4: Complete profile onboarding
        profile_data = {
            'first_name': 'Business',
            'last_name': 'Owner',
            'phone': '+**********',
            'address_line1': '123 Business St',
            'city': 'Business City',
            'country': 'Business Country',
        }

        response = self.client.post(
            reverse('accounts:onboarding_profile'), profile_data
        )
        assert response.status_code == 302
        assert response.url == reverse('accounts:onboarding_business')

        # User should have profile completed
        user.refresh_from_db()
        assert user.profile_completed

        # Profile should have the data
        profile = user.profile
        assert profile.first_name == 'Business'
        assert profile.last_name == 'Owner'

        # Step 5: Complete business onboarding
        response = self.client.post(reverse('accounts:onboarding_business'))
        assert response.status_code == 302
        assert response.url == reverse('core:dashboard')

        # User should have onboarding completed
        user.refresh_from_db()
        assert user.onboarding_completed

        # Step 6: Test subsequent login redirects to dashboard (not onboarding)
        # Logout first
        self.client.logout()

        # Login again
        login_successful = self.client.login(
            username='newbusiness', password='securepassword123'
        )
        assert login_successful

        # Access home page - should redirect to dashboard (not onboarding)
        response = self.client.get(reverse('home'))
        assert response.status_code == 200  # Should show dashboard, not redirect

        # Verify user methods return correct values
        user.refresh_from_db()
        assert not user.needs_onboarding()  # Should be False
        assert not user.needs_profile_completion()  # Should be False

        # Test accessing onboarding URL directly - should redirect to dashboard
        response = self.client.get(reverse('accounts:onboarding'))
        assert response.status_code == 302
        assert response.url == reverse('core:dashboard')

        # Step 7: Access user management
        response = self.client.get(reverse('accounts:user_management'))
        assert response.status_code == 200
        assert 'Manage Users' in response.content.decode()

        # Step 7: Add a team member
        user_data = {
            'username': 'teamagent',
            'email': '<EMAIL>',
            'role': Profile.Role.AGENT,
            'password1': 'agentpassword123',
            'password2': 'agentpassword123',
        }

        response = self.client.post(reverse('accounts:add_user'), user_data)
        assert response.status_code == 302
        assert response.url == reverse('accounts:user_management')

        # Agent should be created and active
        agent = User.objects.get(username='teamagent')
        assert agent.is_active
        assert agent.email_confirmed
        assert agent.business == business
        assert agent.role == Profile.Role.AGENT

        # Step 8: Verify business has both users
        business_users = User.objects.filter(business=business)
        assert business_users.count() == 2
        assert user in business_users
        assert agent in business_users

    def test_user_invitation_journey(self):
        """Test the user invitation and acceptance journey."""

        # Setup: Create business owner
        owner = User.objects.create_user(
            username='owner',
            email='<EMAIL>',
            password='ownerpass123',
            role=Profile.Role.BUSINESS_OWNER,
            is_active=True,
            email_confirmed=True,
            profile_completed=True,
            onboarding_completed=True,
        )

        business = Business.objects.create(
            name='Test Business', email=owner.email, created_by=owner
        )
        owner.business = business
        owner.save()

        # Step 1: Owner logs in and invites user
        self.client.login(username='owner', password='ownerpass123')

        invite_data = {
            'username': 'inviteduser',
            'email': '<EMAIL>',
            'role': Profile.Role.AGENT,
            'password1': '',  # No password = invitation
            'password2': '',
        }

        response = self.client.post(reverse('accounts:add_user'), invite_data)
        assert response.status_code == 302

        # Invited user should be created but inactive
        invited_user = User.objects.get(username='inviteduser')
        assert not invited_user.is_active
        assert not invited_user.email_confirmed
        assert invited_user.business == business
        assert invited_user.email_confirmation_token is not None

        # Step 2: Invited user accepts invitation
        self.client.logout()

        token = invited_user.email_confirmation_token
        invitation_url = reverse(
            'accounts:user_invitation', kwargs={'token': str(token)}
        )

        # GET should show invitation form
        response = self.client.get(invitation_url)
        assert response.status_code == 200
        assert 'Accept Invitation' in response.content.decode()

        # POST should set password and activate
        password_data = {
            'password1': 'invitedpassword123',
            'password2': 'invitedpassword123',
        }

        response = self.client.post(invitation_url, password_data)
        assert response.status_code == 302
        assert response.url == reverse('accounts:onboarding_profile')

        # User should be activated
        invited_user.refresh_from_db()
        assert invited_user.is_active
        assert invited_user.email_confirmed
        assert invited_user.check_password('invitedpassword123')

        # Step 3: Complete invited user onboarding
        profile_data = {
            'first_name': 'Invited',
            'last_name': 'Agent',
            'phone': '+**********',
            'address_line1': '456 Agent St',
            'city': 'Agent City',
            'country': 'Agent Country',
        }

        response = self.client.post(
            reverse('accounts:onboarding_profile'), profile_data
        )
        assert response.status_code == 302

        # Skip business setup for agents
        invited_user.refresh_from_db()
        assert invited_user.profile_completed

        # Step 4: Verify agent can access appropriate features
        response = self.client.get(reverse('home'))
        assert response.status_code == 200

        # Agent should NOT be able to access user management
        response = self.client.get(reverse('accounts:user_management'))
        assert response.status_code == 403  # Permission denied

    def test_profile_management_journey(self):
        """Test profile viewing and editing functionality."""

        # Setup: Create user with profile
        user = User.objects.create_user(
            username='profileuser',
            email='<EMAIL>',
            password='profilepass123',
            is_active=True,
            profile_completed=True,
        )

        profile = Profile.objects.create(
            user=user, first_name='Original', last_name='Name', phone='+**********'
        )

        # Step 1: Login and view profile
        self.client.login(username='profileuser', password='profilepass123')

        response = self.client.get(reverse('accounts:profile'))
        assert response.status_code == 200
        assert 'Original Name' in response.content.decode()

        # Step 2: Edit profile
        updated_data = {
            'first_name': 'Updated',
            'last_name': 'Name',
            'phone': '+**********',
            'address_line1': '789 Updated St',
            'city': 'Updated City',
            'country': 'Updated Country',
            'job_title': 'Test Manager',
            'bio': 'Updated bio',
            'timezone': 'UTC',
            'language': 'en',
        }

        response = self.client.post(reverse('accounts:profile_edit'), updated_data)
        assert response.status_code == 302
        assert response.url == reverse('accounts:profile')

        # Profile should be updated
        profile.refresh_from_db()
        assert profile.first_name == 'Updated'
        assert profile.phone == '+**********'
        assert profile.job_title == 'Test Manager'

        # Step 3: Verify updated profile is displayed
        response = self.client.get(reverse('accounts:profile'))
        assert response.status_code == 200
        assert 'Updated Name' in response.content.decode()
        assert 'Test Manager' in response.content.decode()


@pytest.mark.django_db
class TestBusinessManagementIntegration:
    """Test business management functionality integration."""

    def setup_method(self):
        """Set up test data."""
        self.client = Client()
        self.owner = User.objects.create_user(
            username='businessowner',
            email='<EMAIL>',
            password='ownerpass123',
            role=Profile.Role.BUSINESS_OWNER,
            is_active=True,
            email_confirmed=True,
            profile_completed=True,
            onboarding_completed=True,
        )

        self.business = Business.objects.create(
            name='Test Business', email=self.owner.email, created_by=self.owner
        )
        self.owner.business = self.business
        self.owner.save()

    def test_business_user_management_workflow(self):
        """Test the complete business user management workflow."""

        # Login as business owner
        self.client.login(username='businessowner', password='ownerpass123')

        # Step 1: Access user management dashboard
        response = self.client.get(reverse('accounts:user_management'))
        assert response.status_code == 200
        assert self.business.name in response.content.decode()

        # Step 2: Add multiple users
        users_to_add = [
            {
                'username': 'agent1',
                'email': '<EMAIL>',
                'role': Profile.Role.AGENT,
            },
            {
                'username': 'agent2',
                'email': '<EMAIL>',
                'role': Profile.Role.AGENT,
            },
            {
                'username': 'manager',
                'email': '<EMAIL>',
                'role': Profile.Role.BUSINESS_OWNER,
            },
        ]

        for user_data in users_to_add:
            form_data = {
                'username': user_data['username'],
                'email': user_data['email'],
                'role': user_data['role'],
                'password1': 'testpass123',
                'password2': 'testpass123',
            }

            response = self.client.post(reverse('accounts:add_user'), form_data)
            assert response.status_code == 302

            # Verify user was created
            user = User.objects.get(username=user_data['username'])
            assert user.profile.business == self.business
            assert user.profile.role == user_data['role']
            assert user.is_active

        # Step 3: Verify all users are listed in management dashboard
        response = self.client.get(reverse('accounts:user_management'))
        assert response.status_code == 200

        content = response.content.decode()
        for user_data in users_to_add:
            assert user_data['username'] in content
            assert user_data['email'] in content

        # Step 4: Verify business has correct number of users
        business_users = User.objects.filter(business=self.business)
        assert business_users.count() == 4  # Owner + 3 added users
