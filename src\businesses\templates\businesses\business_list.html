{% load i18n %}

<c-layout title="{% trans 'Businesses' %}">
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900">{% trans "Businesses" %}</h1>
        <a href="{% url 'businesses:create' %}" class="btn btn-primary">
            {% trans "Add Business" %}
        </a>
    </div>

    <!-- Search and filters -->
    <div class="bg-white shadow rounded-lg mb-6">
        <div class="px-6 py-4">
            <form method="get" class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
                <div class="flex-1 max-w-lg">
                    <input type="text"
                           name="search"
                           value="{{ request.GET.search }}"
                           placeholder="{% trans 'Search businesses...' %}"
                           class="form-input w-full">
                </div>
                <div class="flex space-x-4">
                    <button type="submit" class="btn btn-secondary">
                        {% trans "Search" %}
                    </button>
                    <a href="{% url 'businesses:list' %}" class="btn btn-outline-secondary">
                        {% trans "Clear" %}
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Business cards grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {% for business in businesses %}
            <div class="bg-white shadow rounded-lg p-6 hover:shadow-lg transition-shadow">
                <div class="flex items-start justify-between">
                    <div class="flex-1">
                        {% if business.logo %}
                            <img src="{{ business.get_logo_url }}"
                                 alt="{{ business.name }}"
                                 class="h-12 w-auto mb-4">
                        {% endif %}

                        <h3 class="text-lg font-semibold text-gray-900 mb-2">
                            <a href="{% url 'businesses:detail' business.pk %}"
                               class="hover:text-blue-600">
                                {{ business.name }}
                            </a>
                        </h3>

                        {% if business.description %}
                            <p class="text-gray-600 text-sm mb-4">
                                {{ business.description|truncatewords:20 }}
                            </p>
                        {% endif %}

                        <div class="text-sm text-gray-500 space-y-1">
                            {% if business.email %}
                                <div class="flex items-center">
                                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
                                        <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
                                    </svg>
                                    {{ business.email }}
                                </div>
                            {% endif %}

                            {% if business.phone %}
                                <div class="flex items-center">
                                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"></path>
                                    </svg>
                                    {{ business.phone }}
                                </div>
                            {% endif %}

                            {% if business.city %}
                                <div class="flex items-center">
                                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                                    </svg>
                                    {{ business.city }}, {{ business.country }}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="mt-6 flex space-x-2">
                    <a href="{% url 'businesses:detail' business.pk %}"
                       class="btn btn-sm btn-primary">
                        {% trans "View" %}
                    </a>
                    <a href="{% url 'businesses:dashboard' business.pk %}"
                       class="btn btn-sm btn-secondary">
                        {% trans "Dashboard" %}
                    </a>
                    <a href="{% url 'businesses:edit' business.pk %}"
                       class="btn btn-sm btn-outline-primary">
                        {% trans "Edit" %}
                    </a>
                </div>
            </div>
        {% empty %}
            <div class="col-span-full text-center py-12">
                <div class="text-gray-500">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">{% trans "No businesses" %}</h3>
                    <p class="mt-1 text-sm text-gray-500">{% trans "Get started by adding your first business." %}</p>
                    <div class="mt-6">
                        <a href="{% url 'businesses:create' %}" class="btn btn-primary">
                            {% trans "Add Business" %}
                        </a>
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if is_paginated %}
        <div class="flex justify-center mt-8">
            <nav class="flex space-x-1">
                {% if page_obj.has_previous %}
                    <a href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}" class="pagination-btn">First</a>
                    <a href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}" class="pagination-btn">Previous</a>
                {% endif %}

                {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                        <span class="pagination-btn pagination-current">{{ num }}</span>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <a href="?page={{ num }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}" class="pagination-btn">{{ num }}</a>
                    {% endif %}
                {% endfor %}

                {% if page_obj.has_next %}
                    <a href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}" class="pagination-btn">Next</a>
                    <a href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}" class="pagination-btn">Last</a>
                {% endif %}
            </nav>
        </div>
    {% endif %}
</div>

<style>
    .pagination-btn {
        @apply px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 hover:bg-gray-50 hover:text-gray-700;
    }

    .pagination-current {
        @apply bg-blue-50 border-blue-500 text-blue-600;
    }
</style>
</c-layout>
