"""
Template management models for PDF and email templates.
"""

import os

from django.conf import settings
from django.db import models
from django.utils.translation import gettext_lazy as _

from core.mixins import UserAuditModel


class PDFTemplateManager(models.Manager):
    """Manager for PDF templates."""

    def active(self):
        """Return active templates."""
        return self.filter(is_active=True)

    def for_business(self, business):
        """Return templates for a specific business."""
        return self.filter(
            models.Q(business=business) | models.Q(business__isnull=True)
        ).active()

    def default_templates(self):
        """Return default system templates."""
        return self.filter(business__isnull=True).active()


class PDFTemplate(UserAuditModel):
    """
    PDF template model for customizable document generation.
    """

    class TemplateType(models.TextChoices):
        QUOTE = 'quote', _('Quote')
        INVOICE = 'invoice', _('Invoice')
        BOOKING_CONFIRMATION = 'booking_confirmation', _('Booking Confirmation')
        RECEIPT = 'receipt', _('Receipt')
        ITINERARY = 'itinerary', _('Itinerary')
        VOUCHER = 'voucher', _('Voucher')

    # Relationships
    business = models.ForeignKey(
        'businesses.Business',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        help_text=_('Business this template belongs to (null for system templates)'),
    )

    # Template Information
    name = models.CharField(
        max_length=100,
        help_text=_('Template name for identification'),
    )

    template_type = models.CharField(
        max_length=50,
        choices=TemplateType.choices,
        help_text=_('Type of document this template generates'),
    )

    description = models.TextField(
        blank=True,
        help_text=_('Description of this template'),
    )

    # Template Content
    html_content = models.TextField(
        help_text=_('HTML content for the template'),
    )

    css_content = models.TextField(
        blank=True,
        help_text=_('CSS styles for the template'),
    )

    # Template Settings
    is_active = models.BooleanField(
        default=True,
        help_text=_('Whether this template is active and available for use'),
    )

    is_default = models.BooleanField(
        default=False,
        help_text=_('Whether this is the default template for this type'),
    )

    # Preview and Metadata
    preview_image = models.ImageField(
        upload_to='template_previews/',
        blank=True,
        null=True,
        help_text=_('Preview image of the template'),
    )

    page_size = models.CharField(
        max_length=10,
        default='A4',
        choices=[
            ('A4', 'A4'),
            ('Letter', 'Letter'),
            ('Legal', 'Legal'),
        ],
        help_text=_('Page size for PDF generation'),
    )

    orientation = models.CharField(
        max_length=10,
        default='portrait',
        choices=[
            ('portrait', 'Portrait'),
            ('landscape', 'Landscape'),
        ],
        help_text=_('Page orientation'),
    )

    # Custom manager
    objects = PDFTemplateManager()

    class Meta:
        db_table = 'templates_pdf_template'
        verbose_name = _('PDF Template')
        verbose_name_plural = _('PDF Templates')
        indexes = [
            models.Index(fields=['template_type', 'is_active']),
            models.Index(fields=['business', 'template_type']),
            models.Index(fields=['is_default', 'template_type']),
        ]
        constraints = [
            models.UniqueConstraint(
                fields=['business', 'template_type', 'is_default'],
                condition=models.Q(is_default=True),
                name='unique_default_template_per_business_type',
            )
        ]

    def __str__(self):
        business_name = self.business.name if self.business else 'System'
        return f'{self.name} ({business_name} - {self.get_template_type_display()})'

    def save_template_files(self):
        """Save template content to filesystem for rendering."""
        if self.business:
            template_dir = os.path.join(
                settings.BASE_DIR, 'templates', 'pdf', f'business_{self.business.id}'
            )
        else:
            template_dir = os.path.join(settings.BASE_DIR, 'templates', 'pdf', 'system')

        os.makedirs(template_dir, exist_ok=True)

        # Save HTML template
        template_filename = f'{self.template_type}_template.html'
        template_path = os.path.join(template_dir, template_filename)

        with open(template_path, 'w', encoding='utf-8') as f:
            f.write(self.html_content)

        # Save CSS file if provided
        if self.css_content:
            css_dir = os.path.join(
                settings.STATIC_ROOT or settings.BASE_DIR / 'static', 'css', 'pdf'
            )
            os.makedirs(css_dir, exist_ok=True)

            if self.business:
                css_filename = f'business_{self.business.id}_{self.template_type}.css'
            else:
                css_filename = f'system_{self.template_type}.css'

            css_path = os.path.join(css_dir, css_filename)

            with open(css_path, 'w', encoding='utf-8') as f:
                f.write(self.css_content)

    def get_template_path(self):
        """Get the filesystem path to this template."""
        if self.business:
            return f'pdf/business_{self.business.id}/{self.template_type}_template.html'
        else:
            return f'pdf/system/{self.template_type}_template.html'

    def get_css_path(self):
        """Get the CSS file path for this template."""
        if not self.css_content:
            return None

        if self.business:
            return f'css/pdf/business_{self.business.id}_{self.template_type}.css'
        else:
            return f'css/pdf/system_{self.template_type}.css'
