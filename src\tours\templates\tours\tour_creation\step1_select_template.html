{% extends 'tours/tour_creation/base.html' %}

{% block content %}
<div class="space-y-6" data-signals="{_tourTemplateModal: 'closed'}">
  <div class="flex items-center justify-between">
    <div>
      <p class="text-sm text-gray-500">Step <span id="current-step">1</span> of 4:</p>
      <h2 class="text-lg font-medium text-gray-900">Select Template</h2>
    </div>
    <button data-on-click="$_tourTemplateModal = 'open'"
      class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
      Create New Template
    </button>
  </div>

  <!-- Existing Templates -->
  <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-4 space-y-2 md:space-y-0 gap-3">
    <div class="flex-1">
      <label for="templates-search" class="sr-only">Search templates</label>
      <div class="relative">
        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
          </svg>
        </div>
        <input type="text" id="templates-search"
          class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
          placeholder="Search tours...">
      </div>
    </div>

    <div class="flex items-center">
      <span class="text-sm text-gray-500 mr-2">Active only:</span>
      <input id="active-filter" type="checkbox" checked class="h-4 w-4 text-indigo-600 rounded focus:ring-indigo-500">
    </div>
  </div>

  {% include "tours/partials/tour_list.html" with tours=tours %}
</div>
</div>

<!-- Tour Template Modal -->
<div data-show="$_tourTemplateModal == 'open'"
  class="fixed inset-0 bg-gray-100/80 flex items-center justify-center p-4 z-50">
  <div class="rounded-lg shadow-2xs w-full max-w-2xl bg-white">
    {% include "tours/tour_creation/new_template_card.html" %}
  </div>
</div>

<script>
  // Update step counter
  document.getElementById('current-step').textContent = '1';
</script>
{% endblock %}
