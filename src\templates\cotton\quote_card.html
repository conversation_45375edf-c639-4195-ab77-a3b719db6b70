<c-vars quote="None" show_actions="True" business_theme="True" class="" />

<div class="border-2 border-gray-200 rounded-lg p-4 bg-white shadow-sm hover:shadow-md transition-shadow {{ class }}"
    {% if business_theme and quote.business %} style="border-color: {{ quote.business.primary_color }};" {% endif %}>

    <div class="flex justify-between items-start">
        <div class="quote-title">
            <h3 class="text-lg font-semibold text-gray-900">{{ quote.title }}</h3>
            <span class="quote-number text-sm text-gray-500">{{ quote.quote_number }}</span>
        </div>
        <div class="quote-status">
            <span class="status-badge status-{{ quote.status }}">
                {{ quote.get_status_display }}
            </span>
        </div>
    </div>

    <div class="quote-details mt-4 space-y-2">
        <div class="flex justify-between">
            <span class="text-sm text-gray-600">Client:</span>
            <span class="text-sm font-medium">{{ quote.client.display_name }}</span>
        </div>
        <div class="flex justify-between">
            <span class="text-sm text-gray-600">Amount:</span>
            <span class="text-sm font-medium">{{ quote.total_amount }}</span>
        </div>
        <div class="flex justify-between">
            <span class="text-sm text-gray-600">Valid Until:</span>
            <span class="text-sm font-medium">{{ quote.valid_until|date:"M d, Y" }}</span>
        </div>
    </div>

    {% if show_actions %}
    <div class="quote-actions mt-4 flex space-x-2">
        <a href="{{ quote.get_absolute_url }}" class="btn btn-sm btn-primary">
            View Details
        </a>
        {% if quote.status == 'draft' %}
        <button onclick="sendQuote()" class="btn btn-sm btn-secondary">
            Send Quote
        </button>
        {% endif %}
        {% if quote.status == 'sent' %}
        <a href="{% url 'quotes:quote-public-view' quote.public_hash %}" class="btn btn-sm btn-outline-primary"
            target="_blank">
            Public View
        </a>
        {% endif %}
    </div>
    {% endif %}
</div>
