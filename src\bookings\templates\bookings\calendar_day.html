<c-layouts.dashboard>
  <c-slot name="title">
    Daily Calendar - {{ target_date|date:"F d, Y" }}
  </c-slot>

  <c-slot name="main">
    <div class="container mx-auto px-4 py-8">
      <!-- Calendar Header -->
      <div class="flex justify-between items-center mb-8">
        <div>
          <h1 class="text-3xl font-bold text-gray-900">Daily Calendar</h1>
          <p class="text-gray-600 mt-2">{{ target_date|date:"l, F d, Y" }}</p>
        </div>
        <div class="flex space-x-3">
          <a href="{% url 'bookings:calendar' %}"
            class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
            Month View
          </a>
          <a href="{% url 'bookings:calendar_week' %}"
            class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
            Week View
          </a>
          <a href="{% url 'tours:event_create' %}"
            class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
            New Event
          </a>
        </div>
      </div>

      <!-- Day Navigation -->
      <div class="flex justify-between items-center mb-6">
        <a href="{% url 'bookings:calendar_day_date' prev_day.year prev_day.month prev_day.day %}"
          class="flex items-center px-4 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
          </svg>
          Previous Day
        </a>

        <div class="flex items-center space-x-4">
          <a href="{% url 'bookings:calendar_day' %}" class="text-blue-600 hover:text-blue-800 font-medium">
            Today
          </a>
          <h2 class="text-xl font-semibold text-gray-900">{{ target_date|date:"F d, Y" }}</h2>
        </div>

        <a href="{% url 'bookings:calendar_day_date' next_day.year next_day.month next_day.day %}"
          class="flex items-center px-4 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors">
          Next Day
          <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
          </svg>
        </a>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Schedule -->
        <div class="lg:col-span-2">
          <div class="bg-white rounded-lg shadow overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
              <h3 class="text-lg font-semibold text-gray-900">Schedule</h3>
            </div>

            <div class="max-h-96 overflow-y-auto">
              {% for time_slot in time_slots %}
              <div class="flex border-b border-gray-100 hover:bg-gray-50">
                <!-- Time Column -->
                <div class="w-20 p-4 text-sm text-gray-500 border-r border-gray-200 text-center">
                  {{ time_slot|time:"H:i" }}
                </div>

                <!-- Event Column -->
                <div class="flex-1 p-4 min-h-16 relative">
                  {% for event in events %}
                  {% if event.event.start_time.time <= time_slot and event.event.end_time.time > time_slot %}
                  <div
                    class="absolute inset-2 rounded-lg p-3 cursor-pointer shadow-sm {% if event.event_type == 'tour' %}bg-green-100 text-green-800 border border-green-200 {% elif event.event_type == 'meeting' %}bg-blue-100 text-blue-800 border border-blue-200 {% elif event.event_type == 'consultation' %}bg-yellow-100 text-yellow-800 border border-yellow-200 {% elif event.event_type == 'maintenance' %}bg-red-100 text-red-800 border border-red-200 {% elif event.event_type == 'training' %}bg-purple-100 text-purple-800 border border-purple-200 {% else %}bg-gray-100 text-gray-800 border border-gray-200{% endif %}"
                    data-on-click="window.location.href={% url 'tours:event_detail' event.pk %}"
                    title="{{ event.event.title }}">
                    <div class="font-medium text-sm">
                      {{ event.event.title }}
                    </div>
                    <div class="text-xs opacity-75 mt-1">
                      {{ event.event.start_time|time:"H:i" }} - {{ event.event.end_time|time:"H:i" }}
                    </div>
                    {% if event.client %}
                    <div class="text-xs opacity-75 mt-1">
                      {{ event.client.display_name }}
                    </div>
                    {% endif %}
                  </div>
                  {% endif %}
                  {% endfor %}
                </div>
              </div>
              {% endfor %}
            </div>
          </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
          <!-- Day Summary -->
          <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Day Summary</h3>
            <div class="space-y-3">
              <div class="flex justify-between">
                <span class="text-sm text-gray-600">Total Events:</span>
                <span class="text-sm font-medium">{{ events|length }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-sm text-gray-600">Tours:</span>
                <span class="text-sm font-medium">
                  {% with events|dictsort:"event_type" as sorted_events %}
                  {{ sorted_events|length }}
                  {% endwith %}
                </span>
              </div>
              <div class="flex justify-between">
                <span class="text-sm text-gray-600">Meetings:</span>
                <span class="text-sm font-medium">
                  {% with events|dictsort:"event_type" as sorted_events %}
                  {{ sorted_events|length }}
                  {% endwith %}
                </span>
              </div>
            </div>
          </div>

          <!-- Events List -->
          <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
              <h3 class="text-lg font-semibold text-gray-900">Events Today</h3>
            </div>
            <div class="p-6">
              {% if events %}
              <div class="space-y-4">
                {% for event in events %}
                <div class="border border-gray-200 rounded-lg p-4 cursor-pointer hover:bg-gray-50 transition-colors"
                  data-on-click="window.location.href='{% url 'tours:event_detail' event.pk %}">
                  <div class="flex justify-between items-start">
                    <div class="flex-1">
                      <h4 class="font-medium text-gray-900">{{ event.event.title }}</h4>
                      <p class="text-sm text-gray-600 mt-1">
                        {{ event.event.start_time|time:"H:i" }} - {{ event.event.end_time|time:"H:i" }}
                      </p>
                      {% if event.client %}
                      <p class="text-sm text-gray-600">{{ event.client.display_name }}</p>
                      {% endif %}
                      {% if event.meeting_point %}
                      <p class="text-xs text-gray-500 mt-1">📍 {{ event.meeting_point|truncatechars:30 }}
                      </p>
                      {% endif %}
                    </div>
                    <div class="ml-4">
                      <span
                        class="inline-flex px-2 py-1 text-xs font-semibold rounded-full {% if event.event_type == 'tour' %}bg-green-100 text-green-800 {% elif event.event_type == 'meeting' %}bg-blue-100 text-blue-800 {% elif event.event_type == 'consultation' %}bg-yellow-100 text-yellow-800 {% elif event.event_type == 'maintenance' %}bg-red-100 text-red-800 {% elif event.event_type == 'training' %}bg-purple-100 text-purple-800 {% else %}bg-gray-100 text-gray-800{% endif %}">
                        {{ event.get_event_type_display }}
                      </span>
                    </div>
                  </div>

                  {% if event.current_participants > 0 %}
                  <div class="mt-3 flex items-center text-sm text-gray-600">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z">
                      </path>
                    </svg>
                    {{ event.current_participants }}/{{ event.max_participants }} participants
                  </div>
                  {% endif %}
                </div>
                {% endfor %}
              </div>
              {% else %}
              <div class="text-center py-8">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z">
                  </path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">
                  No events today
                </h3>
                <p class="mt-1 text-sm text-gray-500">
                  Create a new event to get started.
                </p>
                <div class="mt-6">
                  <a href="{% url 'tours:event_create' %}"
                    class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                    New Event
                  </a>
                </div>
              </div>
              {% endif %}
            </div>
          </div>

          <!-- Quick Actions -->
          <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
            <div class="space-y-2">
              <a href="{% url 'tours:event_create' %}"
                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
                Create New Event
              </a>
              <a href="{% url 'bookings:booking_create' %}"
                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
                Create New Booking
              </a>
              <a href="{% url 'bookings:booking_list' %}"
                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
                View All Bookings
              </a>
              <a href="{% url 'clients:client-list' %}"
                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
                Manage Clients
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>

    <style>
      /* Custom styles for daily calendar */
      .time-slot {
        height: 80px;
      }

      .event-block {
        border-radius: 8px;
        transition: all 0.2s ease-in-out;
      }

      .event-block:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      }
    </style>
  </c-slot>
</c-layouts.dashboard>
