import hashlib
import uuid
from datetime import date, timedelta

from django.db import models
from django.utils.translation import gettext_lazy as _
from djmoney.models.fields import MoneyField

from core.mixins import UserAuditModel


class QuoteQuerySet(models.QuerySet):
    """Custom QuerySet for Quote model with chainable methods."""

    def for_business(self, business):
        """Filter quotes by business."""
        return self.filter(business=business)

    def active(self):
        """Return non-expired quotes."""
        from datetime import date

        return self.filter(valid_until__gte=date.today())

    def by_status(self, status):
        """Filter quotes by status."""
        return self.filter(status=status)

    def sent(self):
        """Return sent quotes."""
        return self.filter(status='sent')

    def approved(self):
        """Return approved quotes."""
        return self.filter(status='approved')


class QuoteManager(models.Manager):
    """Optimized manager for Quote model."""

    def get_queryset(self):
        """Return optimized queryset."""
        return QuoteQuerySet(self.model, using=self._db).select_related(
            'business', 'client', 'created_by'
        )

    def for_business(self, business):
        """Filter quotes by business."""
        return self.get_queryset().for_business(business)

    def active(self):
        """Return non-expired quotes."""
        return self.get_queryset().active()

    def by_status(self, status):
        """Filter quotes by status."""
        return self.get_queryset().by_status(status)

    def sent(self):
        """Return sent quotes."""
        return self.get_queryset().sent()

    def approved(self):
        """Return approved quotes."""
        return self.get_queryset().approved()

    def with_client_details(self):
        """Return quotes with client and traveler details."""
        return self.select_related('client').prefetch_related('client__travelers')


class Quote(UserAuditModel):
    """
    Quote model representing pricing proposals for tour services.
    Each quote belongs to a business and client with multiple options.
    """

    class Status(models.TextChoices):
        DRAFT = 'draft', _('Draft')
        SENT = 'sent', _('Sent')
        APPROVED = 'approved', _('Approved')
        REJECTED = 'rejected', _('Rejected')
        EXPIRED = 'expired', _('Expired')

    # Relationships
    business = models.ForeignKey(
        'businesses.Business',
        on_delete=models.CASCADE,
        related_name='quotes',
        help_text=_('Business this quote belongs to'),
    )
    client = models.ForeignKey(
        'clients.Client',
        on_delete=models.CASCADE,
        related_name='quotes',
        help_text=_('Client this quote is for'),
    )

    # Quote Information
    quote_number = models.CharField(
        max_length=50,
        help_text=_('Unique quote number'),
        unique=True,
    )
    title = models.CharField(
        max_length=200,
        help_text=_('Quote title or trip name'),
    )
    description = models.TextField(
        blank=True,
        help_text=_('Detailed description of the tour/service'),
    )

    # Status and Dates
    status = models.CharField(
        max_length=20,
        choices=Status.choices,
        default=Status.DRAFT,
        help_text=_('Current status of the quote'),
    )
    valid_until = models.DateField(
        help_text=_('Quote validity expiration date'),
    )

    # Public Access
    public_hash = models.CharField(
        max_length=64,
        unique=True,
        help_text=_('Unique hash for public quote access'),
    )

    # Financial Information
    subtotal = MoneyField(
        max_digits=14,
        decimal_places=2,
        default_currency='USD',
        help_text=_('Subtotal amount before taxes'),
    )
    tax_amount = MoneyField(
        max_digits=14,
        decimal_places=2,
        default_currency='USD',
        help_text=_('Tax amount'),
    )
    total_amount = MoneyField(
        max_digits=14,
        decimal_places=2,
        default_currency='USD',
        help_text=_('Total amount including taxes'),
    )

    # Terms and Conditions
    terms_conditions = models.TextField(
        blank=True,
        help_text=_('Terms and conditions for this quote'),
    )
    payment_terms = models.TextField(
        blank=True,
        help_text=_('Payment terms and schedule'),
    )
    cancellation_policy = models.TextField(
        blank=True,
        help_text=_('Cancellation policy'),
    )

    # Internal Notes
    internal_notes = models.TextField(
        blank=True,
        help_text=_('Internal notes (not visible to client)'),
    )
    sent_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text=_('When the quote was sent to the client'),
    )
    approved_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text=_('When the quote was approved by the client'),
    )

    class Meta:
        db_table = 'quotes_quote'
        verbose_name = _('Quote')
        verbose_name_plural = _('Quotes')
        unique_together = [['business', 'quote_number']]
        indexes = [
            models.Index(fields=['business', 'status']),
            models.Index(fields=['business', 'client']),
            models.Index(fields=['public_hash']),
            models.Index(fields=['valid_until']),
        ]

    def __str__(self):
        return f'{self.quote_number} - {self.title}'

    def save(self, *args, **kwargs):
        # Generate quote number if not set
        if not self.quote_number:
            self.quote_number = self.generate_quote_number()

        # Generate public hash if not set
        if not self.public_hash:
            self.public_hash = self.generate_public_hash()

        # Set default validity period if not set
        if not self.valid_until:
            self.valid_until = date.today() + timedelta(days=30)

        super().save(*args, **kwargs)

    def generate_quote_number(self):
        """Generate a unique quote number for this business."""
        from datetime import datetime

        year = datetime.now().year

        # Get the next sequential number for this business and year
        last_quote = (
            Quote.objects.filter(
                business=self.business, quote_number__startswith=f'Q{year}'
            )
            .order_by('-quote_number')
            .first()
        )

        if last_quote:
            try:
                last_num = int(last_quote.quote_number.split('-')[-1])
                next_num = last_num + 1
            except (ValueError, IndexError):
                next_num = 1
        else:
            next_num = 1

        return f'Q{year}-{next_num:04d}'

    def generate_public_hash(self):
        """Generate a unique hash for public quote access."""
        unique_string = f'{self.business}-{uuid.uuid4()}'
        return hashlib.sha256(unique_string.encode()).hexdigest()

    @property
    def is_expired(self):
        """Check if the quote has expired."""
        return date.today() > self.valid_until

    @property
    def is_valid(self):
        """Check if the quote is still valid."""
        return not self.is_expired and self.status in [
            self.Status.SENT,
            self.Status.DRAFT,
        ]

    def can_be_approved(self):
        """Check if the quote can be approved."""
        return self.status == self.Status.SENT and self.is_valid

    def calculate_totals(self):
        """Calculate and update quote totals from options."""
        # This will be implemented after QuoteOption model
        pass

    # Custom manager
    objects = QuoteManager()
