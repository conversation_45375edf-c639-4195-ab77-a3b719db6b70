# Generated by Django 5.2.4 on 2025-08-12 11:35

import django.core.validators
import django.db.models.deletion
import djmoney.models.fields
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('businesses', '0001_initial'),
        ('clients', '0001_initial'),
        ('quotes', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='TourCategory',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, help_text='Unique identifier for this record', primary_key=True, serialize=False, unique=True)),
                ('name', models.CharField(max_length=64, unique=True, verbose_name='name')),
                ('icon', models.CharField(blank=True, max_length=32, verbose_name='icon')),
            ],
            options={
                'verbose_name': 'Tour Category',
                'verbose_name_plural': 'Tour Categories',
            },
        ),
        migrations.CreateModel(
            name='TourEvent',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, help_text='Unique identifier for this record', primary_key=True, serialize=False, unique=True)),
                ('title', models.CharField(help_text='Tour Event title', max_length=200)),
                ('description', models.TextField(blank=True, help_text='Tour Event description')),
                ('max_participants', models.PositiveIntegerField(default=1, help_text='Maximum number of participants')),
                ('current_participants', models.PositiveIntegerField(default=0, help_text='Current number of participants')),
                ('blocked_spots', models.PositiveIntegerField(default=0)),
                ('meeting_point', models.CharField(blank=True, help_text='Meeting point for the event', max_length=255)),
                ('special_instructions', models.TextField(blank=True, help_text='Special instructions for the event')),
                ('price_per_person_currency', djmoney.models.fields.CurrencyField(choices=[('AUD', 'Australian Dollar'), ('BRL', 'Brazilian Real'), ('GBP', 'British Pound'), ('CAD', 'Canadian Dollar'), ('CNY', 'Chinese Yuan'), ('EUR', 'Euro'), ('INR', 'Indian Rupee'), ('JPY', 'Japanese Yen'), ('MXN', 'Mexican Peso'), ('CHF', 'Swiss Franc'), ('TZS', 'Tanzanian Shilling'), ('USD', 'US Dollar')], default='TZS', editable=False, max_length=3, null=True)),
                ('price_per_person', djmoney.models.fields.MoneyField(blank=True, decimal_places=2, help_text='Price per person for this event', max_digits=10, null=True)),
                ('is_confirmed', models.BooleanField(default=False, help_text='Whether the event is confirmed')),
                ('is_cancelled', models.BooleanField(default=False, help_text='Whether the event is cancelled')),
                ('business', models.ForeignKey(help_text='Business this event belongs to', on_delete=django.db.models.deletion.CASCADE, related_name='tour_events', to='businesses.business')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, related_name='tour_events', to='tours.tourcategory')),
                ('client', models.ForeignKey(blank=True, help_text='Client associated with this tour', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='tour_events', to='clients.client')),
                ('created_by', models.ForeignKey(help_text='User who created this record', on_delete=django.db.models.deletion.PROTECT, related_name='%(class)ss_created', to=settings.AUTH_USER_MODEL)),
                ('quote', models.ForeignKey(blank=True, help_text='Quote associated with this event', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='tour_events', to='quotes.quote')),
            ],
            options={
                'verbose_name': 'Tour Event',
                'verbose_name_plural': 'Tour Events',
            },
        ),
        migrations.CreateModel(
            name='Occurrence',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, help_text='Unique identifier for this record', primary_key=True, serialize=False, unique=True)),
                ('start_time', models.DateTimeField(help_text='Start time of the occurrence')),
                ('end_time', models.DateTimeField(help_text='End time of the occurrence')),
                ('created_by', models.ForeignKey(help_text='User who created this record', on_delete=django.db.models.deletion.PROTECT, related_name='%(class)ss_created', to=settings.AUTH_USER_MODEL)),
                ('tour', models.ForeignKey(help_text='Associated tour', on_delete=django.db.models.deletion.CASCADE, related_name='occurrence', to='tours.tourevent')),
            ],
            options={
                'verbose_name': 'Occurrence',
                'verbose_name_plural': 'Occurrences',
                'ordering': ['start_time'],
            },
        ),
        migrations.CreateModel(
            name='TourResource',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, help_text='Unique identifier for this record', primary_key=True, serialize=False, unique=True)),
                ('name', models.CharField(max_length=100)),
                ('resource_type', models.CharField(choices=[('GUIDE', 'Tour Guide'), ('VEHICLE', 'Vehicle'), ('EQUIPMENT', 'Specialized Equipment')], max_length=20)),
                ('max_capacity', models.PositiveSmallIntegerField(help_text='How many participants this resource can handle simultineously', validators=[django.core.validators.MinValueValidator(1)])),
            ],
            options={
                'abstract': False,
                'indexes': [models.Index(fields=['created_at'], name='tours_tourr_created_b6679f_idx')],
            },
        ),
        migrations.CreateModel(
            name='ResourceRequirement',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, help_text='Unique identifier for this record', primary_key=True, serialize=False, unique=True)),
                ('quantity', models.PositiveSmallIntegerField(default=1)),
                ('resource', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='tours.tourresource')),
            ],
        ),
        migrations.CreateModel(
            name='TourSchedule',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, help_text='Unique identifier for this record', primary_key=True, serialize=False, unique=True)),
                ('start_date', models.DateField()),
                ('end_date', models.DateField()),
                ('recurrence_rule', models.CharField()),
                ('required_resources', models.ManyToManyField(help_text='Links resources to this schedule', through='tours.ResourceRequirement', to='tours.tourresource')),
            ],
            options={
                'verbose_name': 'Tour Schedule',
                'verbose_name_plural': 'Tour Schedules',
            },
        ),
        migrations.AddField(
            model_name='resourcerequirement',
            name='schedule',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='tours.tourschedule'),
        ),
        migrations.CreateModel(
            name='TourTemplate',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, help_text='Unique identifier for this record', primary_key=True, serialize=False, unique=True)),
                ('name', models.CharField(max_length=255, unique=True, verbose_name='name')),
                ('description', models.TextField()),
                ('max_group_size', models.PositiveSmallIntegerField(help_text='Max number of participants per departure', validators=[django.core.validators.MinValueValidator(1)])),
                ('min_group_size', models.PositiveSmallIntegerField(default=1, help_text='Min number of participants required to operate', validators=[django.core.validators.MinValueValidator(1)])),
                ('duration_days', models.PositiveSmallIntegerField(default=1)),
                ('pricing_rule', models.JSONField(default=dict, help_text='e.g., {"base": 100, "child_discount": 0.8, "group_min": 4, "group_rate": 90}')),
                ('is_active', models.BooleanField(default=True)),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='tours.tourcategory')),
                ('created_by', models.ForeignKey(help_text='User who created this record', on_delete=django.db.models.deletion.PROTECT, related_name='%(class)ss_created', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.AddField(
            model_name='tourschedule',
            name='tour_template',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='schedules', to='tours.tourtemplate'),
        ),
        migrations.CreateModel(
            name='TourInstance',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, help_text='Unique identifier for this record', primary_key=True, serialize=False, unique=True)),
                ('date', models.DateField()),
                ('max_capacity', models.PositiveSmallIntegerField()),
                ('blocked_spots', models.PositiveSmallIntegerField(default=0, help_text='Sports held for internal use/VIPs', validators=[django.core.validators.MinValueValidator(0)])),
                ('is_operational', models.BooleanField(default=True, help_text='False = cancelled due to low sign-ups/weather')),
                ('schedule', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='tours.tourschedule')),
                ('tour_template', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='tour_instances', to='tours.tourtemplate')),
            ],
        ),
        migrations.AddIndex(
            model_name='tourevent',
            index=models.Index(fields=['business', 'client'], name='tours_toure_busines_2674fc_idx'),
        ),
        migrations.AddIndex(
            model_name='tourevent',
            index=models.Index(fields=['business', 'is_confirmed'], name='tours_toure_busines_272178_idx'),
        ),
        migrations.AddIndex(
            model_name='occurrence',
            index=models.Index(fields=['start_time'], name='tours_occur_start_t_4a89c3_idx'),
        ),
        migrations.AddIndex(
            model_name='occurrence',
            index=models.Index(fields=['end_time'], name='tours_occur_end_tim_04f02d_idx'),
        ),
        migrations.AddIndex(
            model_name='occurrence',
            index=models.Index(fields=['tour', 'start_time'], name='tours_occur_tour_id_0d3c69_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='resourcerequirement',
            unique_together={('schedule', 'resource')},
        ),
        migrations.AddIndex(
            model_name='tourtemplate',
            index=models.Index(fields=['created_by'], name='tours_tourt_created_288067_idx'),
        ),
        migrations.AddIndex(
            model_name='tourinstance',
            index=models.Index(fields=['date'], name='tours_touri_date_d5c725_idx'),
        ),
        migrations.AddIndex(
            model_name='tourinstance',
            index=models.Index(fields=['is_operational'], name='tours_touri_is_oper_9d1780_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='tourinstance',
            unique_together={('tour_template', 'date')},
        ),
    ]
