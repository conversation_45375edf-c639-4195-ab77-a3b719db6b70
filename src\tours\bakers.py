from model_bakery import baker

from tours.models import TourCategory


class TourCategoryGenerator:
    """Data generator for tour categories."""

    def __init__(self):
        self.created_objects = {'categories': []}

    def generate_tour_categories(self, count=15):
        """Generate tour categories."""
        categories = []

        for i in range(count):
            categories.append(
                baker.make(
                    TourCategory,
                    name=f'category {i}',
                ),
            )

        self.created_objects['categories'].extend(categories)
        return categories
