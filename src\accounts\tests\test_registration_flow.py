"""
Test cases for the email confirmation registration flow.
"""

from django.test import Client, TestCase
from django.urls import reverse

from accounts.models import User


class RegistrationFlowTestCase(TestCase):
    """Test the complete registration and email confirmation flow."""

    def setUp(self):
        """Set up test data."""
        self.client = Client()
        self.test_data = {
            'username': 'testuser',
            'email': '<EMAIL>',
            'first_name': 'Test',
            'last_name': 'User',
            'password1': 'testpassword123',
            'password2': 'testpassword123',
        }

    def test_registration_creates_inactive_user(self):
        """Test that registration creates an inactive user."""
        response = self.client.post(reverse('accounts:register'), self.test_data)

        # Should redirect after successful registration
        self.assertEqual(response.status_code, 302)

        # User should be created but inactive
        user = User.objects.get(username='testuser')
        self.assertFalse(user.is_active)
        self.assertFalse(user.email_confirmed)
        self.assertEqual(user.profile.role, Profile.Role.CUSTOMER)  # Initial role
        self.assertIsNotNone(user.email_confirmation_token)

    def test_email_confirmation_activates_user(self):
        """Test that email confirmation activates the user and sets up business."""
        # First register the user
        self.client.post(reverse('accounts:register'), self.test_data)
        user = User.objects.get(username='testuser')

        # Confirm email
        token = user.email_confirmation_token
        confirmation_url = reverse(
            'accounts:confirm_email', kwargs={'token': str(token)}
        )
        response = self.client.get(confirmation_url)

        # Should redirect after confirmation
        self.assertEqual(response.status_code, 302)

        # User should now be active and confirmed
        user.refresh_from_db()
        self.assertTrue(user.is_active)
        self.assertTrue(user.email_confirmed)
        self.assertEqual(user.profile.role, Profile.Role.BUSINESS_OWNER)

        # Profile should be created
        self.assertTrue(hasattr(user, 'profile'))
        profile = user.profile
        self.assertIsNotNone(profile)

        # Business should be created
        self.assertIsNotNone(user.profile.business)
        business = user.profile.business
        self.assertEqual(business.created_by, user)
        self.assertEqual(business.email, user.email)

    def test_invalid_confirmation_token(self):
        """Test that invalid confirmation tokens are handled properly."""
        # Try to confirm with invalid token
        invalid_token = '********-1234-1234-1234-************'
        confirmation_url = reverse(
            'accounts:confirm_email', kwargs={'token': invalid_token}
        )
        response = self.client.get(confirmation_url)

        # Should redirect to register page
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, reverse('accounts:register'))

    def test_already_confirmed_user(self):
        """Test that already confirmed users can't be confirmed again."""
        # Register and confirm user
        self.client.post(reverse('accounts:register'), self.test_data)
        user = User.objects.get(username='testuser')
        user.confirm_email()

        # Try to confirm again
        token = user.email_confirmation_token
        confirmation_url = reverse(
            'accounts:confirm_email', kwargs={'token': str(token)}
        )
        response = self.client.get(confirmation_url)

        # Should redirect to register page (token no longer valid)
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, reverse('accounts:register'))

    def test_registration_form_validation(self):
        """Test form validation for registration."""
        # Test with missing required fields
        invalid_data = {
            'username': '',
            'email': 'invalid-email',
            'password1': 'short',
            'password2': 'different',
        }

        response = self.client.post(reverse('accounts:register'), invalid_data)

        # Should not redirect (form errors)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'There were errors')

        # No user should be created
        self.assertEqual(User.objects.count(), 0)

    def test_duplicate_username_email(self):
        """Test that duplicate usernames and emails are not allowed."""
        # Create first user
        self.client.post(reverse('accounts:register'), self.test_data)

        # Try to create another user with same username/email
        response = self.client.post(reverse('accounts:register'), self.test_data)

        # Should not redirect (form errors)
        self.assertEqual(response.status_code, 200)

        # Only one user should exist
        self.assertEqual(User.objects.count(), 1)
