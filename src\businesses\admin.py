from django.contrib import admin
from simple_history.admin import SimpleHistoryAdmin

from .models import Business


@admin.register(Business)
class BusinessAdmin(SimpleHistoryAdmin):
    """Admin configuration for Business model."""

    list_display = (
        'name',
        'email',
        'phone',
        'city',
        'country',
        'plan',
        'is_active',
        'created_at',
    )
    list_filter = ('plan', 'is_active', 'country', 'created_at')
    search_fields = ('name', 'email', 'phone', 'city')
    readonly_fields = ('created_at', 'updated_at')

    fieldsets = (
        ('Basic Information', {'fields': ('name', 'description')}),
        ('Contact Information', {'fields': ('email', 'phone', 'website')}),
        (
            'Address',
            {
                'fields': (
                    'address_line1',
                    'address_line2',
                    'city',
                    'state_province',
                    'postal_code',
                    'country',
                )
            },
        ),
        ('Settings', {'fields': ('timezone', 'currency', 'logo')}),
        ('Subscription', {'fields': ('plan', 'is_active')}),
        (
            'Audit',
            {
                'fields': ('created_by', 'created_at', 'updated_at'),
                'classes': ('collapse',),
            },
        ),
    )

    def get_queryset(self, request):
        """Optimize queryset with select_related."""
        return super().get_queryset(request).select_related('created_by')
