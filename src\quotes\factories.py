"""
Factory classes for quotes app models using factory_boy and faker.
"""

from decimal import Decimal

import factory
from factory import fuzzy

from businesses.factories import BusinessFactory
from clients.factories import ClientFactory
from quotes.models import Quote


class QuoteFactory(factory.django.DjangoModelFactory):
    """Factory for creating Quote instances."""

    class Meta:
        model = Quote

    business = factory.SubFactory(BusinessFactory)
    client = factory.SubFactory(
        ClientFactory, business=factory.SelfAttribute("..business")
    )
    title = factory.Faker("catch_phrase")
    description = factory.Faker("text", max_nb_chars=500)
    subtotal = factory.Faker("pydecimal", left_digits=4, right_digits=2, positive=True)
    tax_amount = factory.LazyAttribute(lambda obj: obj.subtotal * Decimal("0.1"))
    total_amount = factory.LazyAttribute(lambda obj: obj.subtotal + obj.tax_amount)
    status = fuzzy.FuzzyChoice(
        [
            Quote.Status.DRAFT,
            Quote.Status.SENT,
            Quote.Status.APPROVED,
            Quote.Status.REJECTED,
        ]
    )
    valid_until = factory.Faker("date_between", start_date="+1d", end_date="+30d")
    created_by = factory.LazyAttribute(lambda obj: obj.business.created_by)


class SafariQuoteFactory(QuoteFactory):
    """Factory for safari-related quotes."""

    title = factory.Faker(
        "random_element",
        elements=[
            "Serengeti Safari Package",
            "Big Five Wildlife Experience",
            "Masai Mara Adventure",
            "Kenya Tanzania Safari",
            "Luxury Safari Lodge Experience",
        ],
    )
    subtotal = fuzzy.FuzzyDecimal(2000, 8000, 2)


class AdventureQuoteFactory(QuoteFactory):
    """Factory for adventure-related quotes."""

    title = factory.Faker(
        "random_element",
        elements=[
            "Kilimanjaro Climbing Expedition",
            "Everest Base Camp Trek",
            "Alpine Mountaineering Course",
            "Multi-day Hiking Adventure",
            "Rock Climbing Experience",
        ],
    )
    subtotal = fuzzy.FuzzyDecimal(1500, 5000, 2)


class CulturalQuoteFactory(QuoteFactory):
    """Factory for cultural tour quotes."""

    title = factory.Faker(
        "random_element",
        elements=[
            "Ancient Temples Tour",
            "Traditional Village Experience",
            "Historical Sites Journey",
            "Cultural Heritage Package",
            "Local Artisan Workshop Tour",
        ],
    )
    subtotal = fuzzy.FuzzyDecimal(800, 3000, 2)
