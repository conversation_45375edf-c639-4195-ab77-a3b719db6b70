<div class="fixed inset-0 bg-gray-100/80 flex items-center justify-center p-4 z-50">
  <div class="rounded-lg border text-card-foreground shadow-2xs w-full max-w-2xl bg-white">
    <div class="space-y-1.5 p-6 flex flex-end">
      <button data-on-click=" = 'close'"
        class="ring-offset-background focus-visible:outline-hidden focus-visible:ring-ring inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 hover:bg-accent h-10 px-4 py-2 cursor-pointer hover:bg-blue-400 hover:text-white">
        ✕
      </button>
    </div>
    <div class="p-6 pt-0 space-y-4">
      here are the contentx
    </div>
  </div>
</div>
