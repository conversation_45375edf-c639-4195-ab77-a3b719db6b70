"""
Pytest tests for payment services.
"""
import pytest
import json
import hmac
import hashlib
from unittest.mock import Mock, patch
from django.conf import settings

from payments.services import LemonSqueezyService
from payments.models import Payment, Subscription


@pytest.mark.django_db
class TestLemonSqueezyService:
    """Test cases for LemonSqueezyService."""
    
    @pytest.fixture
    def service(self):
        """Create a LemonSqueezyService instance."""
        return LemonSqueezyService()
    
    def test_service_initialization(self, service):
        """Test service initialization with settings."""
        assert service.api_key == settings.LEMON_SQUEEZY_API_KEY
        assert service.store_id == settings.LEMON_SQUEEZY_STORE_ID
        assert service.base_url == "https://api.lemonsqueezy.com/v1"
        assert "Authorization" in service.headers
        assert "Content-Type" in service.headers
    
    def test_validate_webhook_signature_valid(self, service):
        """Test webhook signature validation with valid signature."""
        payload = '{"test": "data"}'
        secret = 'test_secret'
        service.webhook_secret = secret
        
        # Create valid signature
        signature = hmac.new(
            secret.encode('utf-8'),
            payload.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        
        assert service.validate_webhook_signature(payload, signature) is True
    
    def test_validate_webhook_signature_invalid(self, service):
        """Test webhook signature validation with invalid signature."""
        payload = '{"test": "data"}'
        service.webhook_secret = 'test_secret'
        invalid_signature = 'invalid_signature'
        
        assert service.validate_webhook_signature(payload, invalid_signature) is False
    
    def test_validate_webhook_signature_no_secret(self, service):
        """Test webhook signature validation without secret."""
        service.webhook_secret = ''
        payload = '{"test": "data"}'
        signature = 'any_signature'
        
        assert service.validate_webhook_signature(payload, signature) is False
    
    def test_validate_webhook_signature_with_prefix(self, service):
        """Test webhook signature validation with sha256= prefix."""
        payload = '{"test": "data"}'
        secret = 'test_secret'
        service.webhook_secret = secret
        
        # Create signature with prefix
        signature_hash = hmac.new(
            secret.encode('utf-8'),
            payload.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        signature_with_prefix = f'sha256={signature_hash}'
        
        assert service.validate_webhook_signature(payload, signature_with_prefix) is True
    
    @patch('payments.services.requests.post')
    def test_create_checkout_success(self, mock_post, service, mock_lemon_squeezy_response):
        """Test successful checkout creation."""
        mock_post.return_value.status_code = 201
        mock_post.return_value.json.return_value = mock_lemon_squeezy_response
        
        checkout_data = {
            'variant_id': 1,
            'custom': {'invoice_id': '1'}
        }
        
        result = service.create_checkout(checkout_data)
        
        assert result is not None
        assert result['data']['id'] == 'test_123'
        mock_post.assert_called_once()
    
    @patch('payments.services.requests.post')
    def test_create_checkout_failure(self, mock_post, service):
        """Test checkout creation failure."""
        mock_post.return_value.status_code = 400
        mock_post.return_value.json.return_value = {'error': 'Bad request'}
        
        checkout_data = {
            'variant_id': 1,
            'custom': {'invoice_id': '1'}
        }
        
        result = service.create_checkout(checkout_data)
        
        assert result is None
    
    def test_process_webhook_event_subscription_created(self, service, business, subscription):
        """Test processing subscription_created webhook event."""
        event_data = {
            'meta': {'event_name': 'subscription_created'},
            'data': {
                'id': 'sub_123',
                'attributes': {
                    'id': 'sub_123',
                    'customer_id': 'cust_123',
                    'status': 'active',
                    'custom': {'business_id': str(business.id)}
                }
            }
        }
        
        result = service.process_webhook_event(event_data)
        assert result is True
    
    def test_process_webhook_event_order_created(self, service, invoice):
        """Test processing order_created webhook event."""
        event_data = {
            'meta': {'event_name': 'order_created'},
            'data': {
                'id': 'order_123',
                'attributes': {
                    'id': 'order_123',
                    'total': 1650,
                    'order_number': 'ORD-123',
                    'custom': {'invoice_id': str(invoice.id)}
                }
            }
        }
        
        result = service.process_webhook_event(event_data)
        assert result is True
        
        # Check that payment was created
        payment = Payment.objects.filter(
            invoice=invoice,
            lemon_squeezy_order_id='order_123'
        ).first()
        assert payment is not None
        assert payment.status == Payment.Status.COMPLETED
    
    def test_process_webhook_event_invalid_event(self, service):
        """Test processing webhook event with invalid data."""
        event_data = {
            'meta': {},  # Missing event_name
            'data': {}
        }
        
        result = service.process_webhook_event(event_data)
        assert result is False
    
    def test_process_webhook_event_unhandled_event(self, service):
        """Test processing unhandled webhook event."""
        event_data = {
            'meta': {'event_name': 'unknown_event'},
            'data': {}
        }
        
        result = service.process_webhook_event(event_data)
        assert result is True  # Should return True for unhandled but valid events


@pytest.mark.django_db
class TestLemonSqueezyWebhookHandlers:
    """Test cases for specific webhook event handlers."""
    
    @pytest.fixture
    def service(self):
        """Create a LemonSqueezyService instance."""
        return LemonSqueezyService()
    
    def test_handle_subscription_created(self, service, business):
        """Test handling subscription_created event."""
        attributes = {
            'id': 'sub_123',
            'customer_id': 'cust_123',
            'status': 'active',
            'custom': {'business_id': str(business.id)}
        }
        
        result = service._handle_subscription_created(attributes)
        assert result is True
        
        # Check that subscription was created
        subscription = Subscription.objects.filter(
            business=business,
            lemon_squeezy_subscription_id='sub_123'
        ).first()
        assert subscription is not None
        assert subscription.status == Subscription.Status.ACTIVE
    
    def test_handle_subscription_created_no_business(self, service):
        """Test handling subscription_created event without business_id."""
        attributes = {
            'id': 'sub_123',
            'customer_id': 'cust_123',
            'status': 'active',
            'custom': {}  # Missing business_id
        }
        
        result = service._handle_subscription_created(attributes)
        assert result is True  # Should not fail, just log warning
    
    def test_handle_subscription_updated(self, service, subscription):
        """Test handling subscription_updated event."""
        attributes = {
            'id': subscription.lemon_squeezy_subscription_id,
            'status': 'cancelled'
        }
        
        result = service._handle_subscription_updated(attributes)
        assert result is True
        
        # Check that subscription was updated
        subscription.refresh_from_db()
        assert subscription.status == Subscription.Status.CANCELLED
    
    def test_handle_subscription_payment_success(self, service, subscription):
        """Test handling subscription_payment_success event."""
        attributes = {
            'id': 'payment_123',
            'subscription_id': subscription.lemon_squeezy_subscription_id,
            'total': 2999  # Amount in cents
        }
        
        result = service._handle_subscription_payment_success(attributes)
        assert result is True
        
        # Check that payment was created
        payment = Payment.objects.filter(
            subscription=subscription,
            lemon_squeezy_order_id='payment_123'
        ).first()
        assert payment is not None
        assert payment.status == Payment.Status.COMPLETED
    
    def test_handle_order_created(self, service, invoice):
        """Test handling order_created event."""
        attributes = {
            'id': 'order_123',
            'total': 1650,
            'order_number': 'ORD-123',
            'custom': {'invoice_id': str(invoice.id)}
        }
        
        result = service._handle_order_created(attributes)
        assert result is True
        
        # Check that payment was created and invoice updated
        payment = Payment.objects.filter(
            invoice=invoice,
            lemon_squeezy_order_id='order_123'
        ).first()
        assert payment is not None
        assert payment.status == Payment.Status.COMPLETED
    
    def test_handle_order_refunded(self, service, payment):
        """Test handling order_refunded event."""
        attributes = {
            'id': payment.lemon_squeezy_order_id
        }
        
        result = service._handle_order_refunded(attributes)
        assert result is True
        
        # Check that payment was marked as refunded
        payment.refresh_from_db()
        assert payment.status == Payment.Status.REFUNDED


@pytest.mark.integration
class TestLemonSqueezyIntegration:
    """Integration tests for Lemon Squeezy service."""
    
    @pytest.mark.slow
    @patch('payments.services.requests.get')
    def test_get_store_info_integration(self, mock_get):
        """Test getting store information from API."""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            'data': {
                'id': '1',
                'attributes': {
                    'name': 'Test Store',
                    'slug': 'test-store'
                }
            }
        }
        mock_get.return_value = mock_response
        
        service = LemonSqueezyService()
        result = service.get_store_info()
        
        assert result is not None
        assert result['data']['attributes']['name'] == 'Test Store'
