#!/usr/bin/env python
"""
Test runner script for the tour business management system.
"""
import os
import sys
import subprocess
from pathlib import Path

# Add src directory to Python path
src_dir = Path(__file__).parent / 'src'
sys.path.insert(0, str(src_dir))

# Set Django settings module
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')

def run_tests():
    """Run pytest with appropriate settings."""
    
    # Change to src directory
    os.chdir(src_dir)
    
    # Run pytest
    cmd = [
        'pytest',
        '--verbose',
        '--tb=short',
        '--cov=.',
        '--cov-report=html',
        '--cov-report=term-missing',
        '--cov-fail-under=70',
        '--reuse-db',
        '--nomigrations'
    ]
    
    # Add any additional arguments passed to this script
    cmd.extend(sys.argv[1:])
    
    try:
        result = subprocess.run(cmd, check=False)
        return result.returncode
    except FileNotFoundError:
        print("Error: pytest not found. Please install pytest:")
        print("  uv add pytest pytest-django pytest-cov")
        return 1

if __name__ == '__main__':
    sys.exit(run_tests())
