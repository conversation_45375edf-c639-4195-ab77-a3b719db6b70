import logging

# Additional imports for new views
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.db.models import Q
from django.http import JsonResponse
from django.shortcuts import get_object_or_404, render
from django.urls import reverse_lazy
from django.utils import timezone
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.views.generic import CreateView, DetailView, ListView, UpdateView

from businesses.models import Business
from clients.models import Client
from quotes.forms import QuoteForm

from .models import Quote

logger = logging.getLogger('quotes')


class PublicQuoteView(DetailView):
    """Public view for quotes accessible via hash."""

    model = Quote
    template_name = 'quotes/public_quote.html'
    context_object_name = 'quote'

    def get_object(self):
        """Get quote by public hash."""
        hash_value = self.kwargs.get('hash')
        return get_object_or_404(Quote, public_hash=hash_value)

    def get_context_data(self, **kwargs):
        """Add additional context."""
        context = super().get_context_data(**kwargs)
        quote = self.get_object()

        context.update(
            {
                'can_approve': quote.can_be_approved(),
                'is_expired': quote.is_expired,
                'is_valid': quote.is_valid,
            }
        )

        return context


@require_http_methods(['POST'])
@csrf_exempt
def approve_quote(request, hash):
    """Approve a quote via public hash."""
    try:
        quote = get_object_or_404(Quote, public_hash=hash)

        if not quote.can_be_approved():
            return JsonResponse(
                {'success': False, 'error': 'Quote cannot be approved at this time.'},
                status=400,
            )

        # Update quote status
        quote.status = Quote.Status.APPROVED
        quote.approved_at = timezone.now()
        quote.save()

        return JsonResponse(
            {
                'success': True,
                'message': 'Quote approved successfully!',
                'quote_number': quote.quote_number,
                'status': quote.get_status_display(),
            }
        )

    except Quote.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'Quote not found.'}, status=404)
    except Exception:
        return JsonResponse(
            {'success': False, 'error': 'An error occurred while approving the quote.'},
            status=500,
        )


@login_required
@require_http_methods(['POST'])
def create_invoice_from_quote(request, quote_id):
    """Create an invoice from an approved quote."""
    try:
        quote = get_object_or_404(Quote, id=quote_id)

        # Check if user has access to this quote's business
        if not request.user.has_business_access(quote.business):
            return JsonResponse(
                {
                    'success': False,
                    'error': 'You do not have permission to access this quote.',
                },
                status=403,
            )

        # Check if quote can be invoiced
        if quote.status != Quote.Status.APPROVED:
            return JsonResponse(
                {'success': False, 'error': 'Only approved quotes can be invoiced.'},
                status=400,
            )

        # Check if invoice already exists
        if hasattr(quote, 'invoice'):
            return JsonResponse(
                {
                    'success': False,
                    'error': 'Invoice already exists for this quote.',
                    'invoice_number': quote.invoice.invoice_number,
                },
                status=400,
            )

        # Create invoice
        from invoices.models import Invoice

        invoice = Invoice.objects.from_quote(quote)

        return JsonResponse(
            {
                'success': True,
                'message': 'Invoice created successfully!',
                'invoice_number': invoice.invoice_number,
                'invoice_id': invoice.id,
            }
        )

    except Quote.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'Quote not found.'}, status=404)
    except Exception as e:
        return JsonResponse(
            {
                'success': False,
                'error': f'An error occurred while creating the invoice: {str(e)}',
            },
            status=500,
        )


@login_required
def quote_detail(request, quote_id):
    """Detail view for quotes (for logged-in users)."""
    quote = get_object_or_404(Quote, id=quote_id)

    # Check if user has access to this quote's business
    if not request.user.has_business_access(quote.business):
        return render(request, '403.html', status=403)

    context = {
        'quote': quote,
        'can_approve': quote.can_be_approved(),
        'is_expired': quote.is_expired,
        'is_valid': quote.is_valid,
        'has_invoice': hasattr(quote, 'invoice'),
    }

    if hasattr(quote, 'invoice'):
        context['invoice'] = quote.invoice

    return render(request, 'quotes/quote_detail.html', context)


class QuoteListView(LoginRequiredMixin, ListView):
    """List view for quotes."""

    model = Quote
    template_name = 'quotes/quote_list.html'
    context_object_name = 'quotes'
    paginate_by = 20

    def get_queryset(self):
        """Filter quotes based on user's business access."""
        queryset = Quote.objects.all()

        # Filter by user's accessible businesses
        # if not self.request.user.is_superuser:
        #     accessible_businesses = Business.objects.filter(
        #         Q(created_by=self.request.user) | Q(users=self.request.user)
        #     ).distinct()
        #     queryset = queryset.filter(business__in=accessible_businesses)

        # Search functionality
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(quote_number__icontains=search)
                | Q(title__icontains=search)
                | Q(client__first_name__icontains=search)
                | Q(client__last_name__icontains=search)
            )

        # Filter by status
        status = self.request.GET.get('status')
        if status:
            queryset = queryset.filter(status=status)

        # Filter by business
        business_id = self.request.GET.get('business')
        if business_id:
            queryset = queryset.filter(business_id=business_id)

        return queryset.order_by('-created_at')


class QuoteCreateView(LoginRequiredMixin, CreateView):
    """Create view for quotes."""

    form_class = QuoteForm
    template_name = 'quotes/quote_form.html'
    success_url = reverse_lazy('quotes:quote-list')

    def form_valid(self, form):
        """Handle successful form submission."""
        form.instance.created_by = self.request.user
        form.instance.business = self.request.user.profile.business

        messages.success(
            self.request, f'Quote "{form.instance.quote_number}" created successfully!'
        )
        return super().form_valid(form)


class QuoteUpdateView(LoginRequiredMixin, UpdateView):
    """Update view for quotes."""

    model = Quote
    template_name = 'quotes/quote_form.html'
    fields = [
        'business',
        'client',
        'title',
        'description',
        'subtotal',
        'tax_amount',
        'total_amount',
        'currency',
        'valid_until',
        'terms_conditions',
        'status',
    ]

    def get_object(self):
        """Get quote and check permissions."""
        quote = get_object_or_404(Quote, pk=self.kwargs['pk'])

        # Check if user has access to this quote's business
        if not self.request.user.is_superuser:
            if (
                quote.business.created_by != self.request.user
                and self.request.user not in quote.business.users.all()
            ):
                raise PermissionError("You don't have access to this quote")

        return quote

    def get_form(self, form_class=None):
        """Limit choices to user's accessible businesses and clients."""
        form = super().get_form(form_class)

        if not self.request.user.is_superuser:
            accessible_businesses = Business.objects.filter(
                Q(created_by=self.request.user) | Q(users=self.request.user)
            ).distinct()
            form.fields['business'].queryset = accessible_businesses
            form.fields['client'].queryset = Client.objects.filter(
                business__in=accessible_businesses
            )

        return form

    def form_valid(self, form):
        """Handle successful form submission."""
        messages.success(
            self.request, f'Quote "{form.instance.quote_number}" updated successfully!'
        )
        return super().form_valid(form)

    def get_success_url(self):
        """Return to quote detail page."""
        return reverse_lazy('quotes:quote-detail', kwargs={'quote_id': self.object.pk})
