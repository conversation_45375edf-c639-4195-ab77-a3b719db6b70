"""
Management command to create demo data for the tour business management system.
This command creates sample users, businesses, clients, quotes, and invoices for demonstration purposes.
"""

from datetime import date, timedelta
from decimal import Decimal

from django.contrib.auth import get_user_model
from django.core.management.base import BaseCommand
from djmoney.money import Money

from accounts.models import Profile
from businesses.models import Business
from clients.models import Client, Traveler
from invoices.models import Invoice
from quotes.models import Quote

User = get_user_model()


class Command(BaseCommand):
    help = 'Create demo data for the tour business management system'

    def add_arguments(self, parser):
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear existing demo data before creating new data',
        )
        parser.add_argument(
            '--users',
            type=int,
            default=5,
            help='Number of demo users to create (default: 5)',
        )
        parser.add_argument(
            '--businesses',
            type=int,
            default=3,
            help='Number of demo businesses to create (default: 3)',
        )
        parser.add_argument(
            '--clients',
            type=int,
            default=10,
            help='Number of demo clients to create per business (default: 10)',
        )

    def handle(self, *args, **options):
        if options['clear']:
            self.stdout.write('Clearing existing demo data...')
            self.clear_demo_data()

        self.stdout.write('Creating demo data...')

        # Create demo users and businesses
        businesses = self.create_businesses(options['businesses'])

        # Create demo clients for each business
        for business in businesses:
            self.create_clients_for_business(business, options['clients'])

        # Create demo quotes and invoices
        for business in businesses:
            self.create_quotes_for_business(business)

        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully created demo data:\n'
                f'- {options["businesses"]} businesses\n'
                f'- {options["clients"] * options["businesses"]} clients\n'
                f'- Sample quotes and invoices'
            )
        )

    def clear_demo_data(self):
        """Clear existing demo data."""
        # Delete demo users (those with demo_ prefix)
        demo_users = User.objects.filter(username__startswith='demo_')
        demo_businesses = Business.objects.filter(name__startswith='Demo ')

        self.stdout.write(f'Deleting {demo_users.count()} demo users...')
        demo_users.delete()

        self.stdout.write(f'Deleting {demo_businesses.count()} demo businesses...')
        demo_businesses.delete()

    def create_businesses(self, count):
        """Create demo businesses with owners."""
        businesses = []

        business_data = [
            {
                'name': 'Demo Safari Adventures',
                'description': 'Premium African safari tours and wildlife experiences',
                'email': '<EMAIL>',
                'phone': '******-0101',
                'city': 'Nairobi',
                'country': 'Kenya',
            },
            {
                'name': 'Demo Mountain Expeditions',
                'description': 'Guided mountain climbing and trekking adventures',
                'email': '<EMAIL>',
                'phone': '******-0102',
                'city': 'Denver',
                'country': 'USA',
            },
            {
                'name': 'Demo Cultural Tours',
                'description': 'Authentic cultural experiences and heritage tours',
                'email': '<EMAIL>',
                'phone': '******-0103',
                'city': 'Cusco',
                'country': 'Peru',
            },
        ]

        for i in range(min(count, len(business_data))):
            data = business_data[i]

            # Create business owner
            username = f'demo_owner_{i + 1}'
            email = f'owner{i + 1}@demo.com'

            user, created = User.objects.get_or_create(
                username=username,
                defaults={
                    'email': email,
                    'first_name': 'Demo',
                    'last_name': f'Owner {i + 1}',
                    'role': Profile.Role.BUSINESS_OWNER,
                    'is_active': True,
                    'email_confirmed': True,
                    'profile_completed': True,
                    'onboarding_completed': True,
                },
            )

            if created:
                user.set_password('demo123')
                user.save()

                # Create profile
                Profile.objects.get_or_create(
                    user=user,
                    defaults={
                        'first_name': user.first_name,
                        'last_name': user.last_name,
                        'phone': data['phone'],
                        'city': data['city'],
                        'country': data['country'],
                    },
                )

            # Create business
            business, created = Business.objects.get_or_create(
                name=data['name'],
                defaults={
                    'description': data['description'],
                    'email': data['email'],
                    'phone': data['phone'],
                    'city': data['city'],
                    'country': data['country'],
                    'created_by': user,
                },
            )

            # Associate user with business
            user.profile.business = business
            user.save()

            businesses.append(business)

            if created:
                self.stdout.write(f'Created business: {business.name}')

        return businesses

    def create_clients_for_business(self, business, count):
        """Create demo clients for a business."""
        client_data = [
            {
                'first_name': 'John',
                'last_name': 'Smith',
                'email': '<EMAIL>',
            },
            {
                'first_name': 'Sarah',
                'last_name': 'Johnson',
                'email': '<EMAIL>',
            },
            {
                'first_name': 'Michael',
                'last_name': 'Brown',
                'email': '<EMAIL>',
            },
            {
                'first_name': 'Emily',
                'last_name': 'Davis',
                'email': '<EMAIL>',
            },
            {
                'first_name': 'David',
                'last_name': 'Wilson',
                'email': '<EMAIL>',
            },
            {
                'first_name': 'Lisa',
                'last_name': 'Anderson',
                'email': '<EMAIL>',
            },
            {
                'first_name': 'Robert',
                'last_name': 'Taylor',
                'email': '<EMAIL>',
            },
            {
                'first_name': 'Jennifer',
                'last_name': 'Thomas',
                'email': '<EMAIL>',
            },
            {
                'first_name': 'William',
                'last_name': 'Jackson',
                'email': '<EMAIL>',
            },
            {
                'first_name': 'Amanda',
                'last_name': 'White',
                'email': '<EMAIL>',
            },
        ]

        for i in range(min(count, len(client_data))):
            data = client_data[i]

            client, created = Client.objects.get_or_create(
                business=business,
                email=data['email'],
                defaults={
                    'first_name': data['first_name'],
                    'last_name': data['last_name'],
                    'phone': f'******-{1000 + i:04d}',
                    'created_by': business.created_by,
                },
            )

            if created:
                # Create some travelers for the client
                Traveler.objects.get_or_create(
                    client=client,
                    first_name=data['first_name'],
                    last_name=data['last_name'],
                    defaults={
                        'date_of_birth': date.today() - timedelta(days=365 * (25 + i)),
                        'gender': 'male' if i % 2 == 0 else 'female',
                        'created_by': business.created_by,
                    },
                )

    def create_quotes_for_business(self, business):
        """Create demo quotes and invoices for a business."""
        clients = Client.objects.filter(business=business)[:5]

        quote_data = [
            {'title': 'Safari Adventure Package', 'amount': 2500},
            {'title': 'Mountain Trekking Expedition', 'amount': 1800},
            {'title': 'Cultural Heritage Tour', 'amount': 1200},
            {'title': 'Wildlife Photography Safari', 'amount': 3200},
            {'title': 'Family Adventure Package', 'amount': 4500},
        ]

        for i, client in enumerate(clients):
            if i < len(quote_data):
                data = quote_data[i]

                # Create quote
                quote, created = Quote.objects.get_or_create(
                    business=business,
                    client=client,
                    title=data['title'],
                    defaults={
                        'description': f'Comprehensive {data["title"].lower()} including accommodation, meals, and guided activities.',
                        'subtotal': Money(data['amount'], 'USD'),
                        'tax_amount': Money(data['amount'] * Decimal('0.1'), 'USD'),
                        'total_amount': Money(data['amount'] * Decimal('1.1'), 'USD'),
                        'status': Quote.Status.SENT
                        if i % 2 == 0
                        else Quote.Status.DRAFT,
                        'valid_until': date.today() + timedelta(days=30),
                        'created_by': business.created_by,
                    },
                )

                # Create invoice for approved quotes
                if created and i % 3 == 0:
                    quote.status = Quote.Status.APPROVED
                    quote.save()

                    Invoice.objects.get_or_create(
                        business=business,
                        client=client,
                        quote=quote,
                        defaults={
                            'subtotal': quote.subtotal,
                            'tax_amount': quote.tax_amount,
                            'total_amount': quote.total_amount,
                            'issue_date': date.today(),
                            'due_date': date.today() + timedelta(days=30),
                            'status': Invoice.Status.SENT,
                            'created_by': business.created_by,
                        },
                    )
