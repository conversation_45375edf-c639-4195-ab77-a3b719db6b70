{% load i18n %}

<c-layouts.base>
  <c-slot name="title">
    {% trans "Edit Profile" %} - Tour Business Management
  </c-slot>

  <c-slot name="content">
    <div class="container mx-auto px-4 py-8">
      <div class="max-w-4xl mx-auto">
        <!-- Header -->
        <div class="flex justify-between items-center mb-6">
          <h1 class="text-3xl font-bold text-gray-900">{% trans "Edit Profile" %}</h1>
          <a href="{% url 'accounts:profile' %}"
            class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium">
            {% trans "Cancel" %}
          </a>
        </div>

        <form method="post" enctype="multipart/form-data" class="space-y-6">
          {% csrf_token %}

          {% if form.errors %}
          <div class="bg-red-50 border border-red-200 rounded-md p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                    clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">
                  {% trans "There were errors with your submission" %}
                </h3>
                <div class="mt-2 text-sm text-red-700">
                  <ul class="list-disc pl-5 space-y-1">
                    {% for field, errors in form.errors.items %}
                    {% for error in errors %}
                    <li>{{ field|capfirst }}: {{ error }}</li>
                    {% endfor %}
                    {% endfor %}
                  </ul>
                </div>
              </div>
            </div>
          </div>
          {% endif %}

          <!-- Basic Information -->
          <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">{% trans "Basic Information" %}</h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label for="{{ form.first_name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                  {% trans "First Name" %}
                </label>
                {{ form.first_name }}
              </div>

              <div>
                <label for="{{ form.last_name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                  {% trans "Last Name" %}
                </label>
                {{ form.last_name }}
              </div>

              <div>
                <label for="{{ form.phone.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                  {% trans "Phone" %}
                </label>
                {{ form.phone }}
              </div>

              <div>
                <label for="{{ form.date_of_birth.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                  {% trans "Date of Birth" %}
                </label>
                {{ form.date_of_birth }}
              </div>

              <div class="md:col-span-2">
                <label for="{{ form.avatar.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                  {% trans "Profile Picture" %}
                </label>
                {{ form.avatar }}
              </div>

              <div class="md:col-span-2">
                <label for="{{ form.bio.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                  {% trans "Bio" %}
                </label>
                {{ form.bio }}
              </div>
            </div>
          </div>

          <!-- Address Information -->
          <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">{% trans "Address" %}</h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="md:col-span-2">
                <label for="{{ form.address_line1.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                  {% trans "Address Line 1" %}
                </label>
                {{ form.address_line1 }}
              </div>

              <div class="md:col-span-2">
                <label for="{{ form.address_line2.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                  {% trans "Address Line 2" %}
                </label>
                {{ form.address_line2 }}
              </div>

              <div>
                <label for="{{ form.city.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                  {% trans "City" %}
                </label>
                {{ form.city }}
              </div>

              <div>
                <label for="{{ form.state_province.id_for_label }}"
                  class="block text-sm font-medium text-gray-700 mb-1">
                  {% trans "State/Province" %}
                </label>
                {{ form.state_province }}
              </div>

              <div>
                <label for="{{ form.postal_code.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                  {% trans "Postal Code" %}
                </label>
                {{ form.postal_code }}
              </div>

              <div>
                <label for="{{ form.country.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                  {% trans "Country" %}
                </label>
                {{ form.country }}
              </div>
            </div>
          </div>

          <!-- Professional Information -->
          <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">{% trans "Professional Information" %}</h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label for="{{ form.job_title.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                  {% trans "Job Title" %}
                </label>
                {{ form.job_title }}
              </div>

              <div>
                <label for="{{ form.department.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                  {% trans "Department" %}
                </label>
                {{ form.department }}
              </div>
            </div>
          </div>

          <!-- Emergency Contact -->
          <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">{% trans "Emergency Contact" %}</h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label for="{{ form.emergency_contact_name.id_for_label }}"
                  class="block text-sm font-medium text-gray-700 mb-1">
                  {% trans "Name" %}
                </label>
                {{ form.emergency_contact_name }}
              </div>

              <div>
                <label for="{{ form.emergency_contact_phone.id_for_label }}"
                  class="block text-sm font-medium text-gray-700 mb-1">
                  {% trans "Phone" %}
                </label>
                {{ form.emergency_contact_phone }}
              </div>

              <div>
                <label for="{{ form.emergency_contact_relationship.id_for_label }}"
                  class="block text-sm font-medium text-gray-700 mb-1">
                  {% trans "Relationship" %}
                </label>
                {{ form.emergency_contact_relationship }}
              </div>
            </div>
          </div>

          <!-- Preferences -->
          <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">{% trans "Preferences" %}</h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label for="{{ form.timezone.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                  {% trans "Timezone" %}
                </label>
                {{ form.timezone }}
              </div>

              <div>
                <label for="{{ form.language.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                  {% trans "Language" %}
                </label>
                {{ form.language }}
              </div>
            </div>
          </div>

          <!-- Submit Button -->
          <div class="flex justify-end space-x-3">
            <a href="{% url 'accounts:profile' %}"
              class="bg-gray-300 hover:bg-gray-400 text-gray-800 px-6 py-2 rounded-md text-sm font-medium">
              {% trans "Cancel" %}
            </a>
            <button type="submit"
              class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md text-sm font-medium">
              {% trans "Save Profile" %}
            </button>
          </div>
        </form>
      </div>
    </div>
  </c-slot name="content">
</c-layouts.base>
