repos:
-   repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v2.3.0
    hooks:
    -   id: check-yaml
    -   id: end-of-file-fixer
    -   id: trailing-whitespace

-   repo: https://github.com/asottile/pyupgrade
    rev: v3.20.0
    hooks:
    -   id: pyupgrade

- repo: https://github.com/astral-sh/ruff-pre-commit
  # Ruff version.
  rev: v0.12.4
  hooks:
    # Run the linter.
    - id: ruff-check
    # Run the formatter.
    - id: ruff-format

-   repo: https://github.com/adamchainz/django-upgrade
    rev: "1.25.0"
    hooks:
    -   id: django-upgrade

- repo: https://github.com/djlint/djLint
  rev: v1.36.4
  hooks:
    - id: djlint-reformat-django
    - id: djlint-django
      args:
        - --ignore=T002,H030,H031
