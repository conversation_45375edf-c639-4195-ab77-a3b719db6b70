<c-vars title="Tour Business Management" extra_head="" extra_scripts="" show_nav="True" show_footer="True" class="" />

<!DOCTYPE html>
<html lang="en">

    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>{{ title }}</title>

        <!-- Tailwind CSS -->
        <script src="https://cdn.tailwindcss.com"></script>

        <!-- DatastarJS -->
        <script type="module" src="https://cdn.jsdelivr.net/npm/@sudodevnull/datastar@latest/dist/datastar.js"></script>

        <!-- Business Theme CSS -->
        {% if request.user.is_authenticated and request.user.profile.business %}
        <style>
            {
                    {
                    request.user.profile.business.get_theme_css
                }
            }
        </style>
        {% endif %}

        <!-- Custom CSS -->
        <style>
            .btn {
                @apply px-4 py-2 rounded-md font-medium transition-colors duration-200;
            }

            .btn-primary {
                @apply bg-blue-600 text-white hover:bg-blue-700;
            }

            .btn-secondary {
                @apply bg-gray-600 text-white hover:bg-gray-700;
            }

            .btn-success {
                @apply bg-green-600 text-white hover:bg-green-700;
            }

            .btn-danger {
                @apply bg-red-600 text-white hover:bg-red-700;
            }
        </style>

        {{ extra_head|safe }}
    </head>

    <body class="min-h-screen bg-gray-50 flex flex-col">
        {% if show_nav %}
        <c-navigation />
        {% endif %}

        <!-- Main Content -->
        <main class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8 flex-1 {{ class }}">
            <!-- Messages -->
            {% if messages %}
            <div class="mb-6">
                {% for message in messages %}
                <c-alert type="{{ message.tags }}" message="{{ message }}" />
                {% endfor %}
            </div>
            {% endif %}

            {{ slot }}
        </main>

        {% if show_footer %}
        <c-footer />
        {% endif %}

        {{ extra_scripts|safe }}
    </body>

</html>
