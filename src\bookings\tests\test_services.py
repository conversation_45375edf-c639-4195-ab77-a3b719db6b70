from concurrent.futures import ThreadPoolExecutor

from django.test import TransactionTestCase

from bookings.models import Booking
from bookings.services import BookingService


class BookingServiceTest(TransactionTestCase):
    """Tests requiring transaction rollback"""

    def test_concurrent_booking(self):
        """Two booking trying to claim last spot"""
        tour = TourInstanceFactory(max_capacity=5, blocked_spots=0)
        BookingFactory(
            tour_instance=tour, num_of_participants=4, status=Booking.Status.CONFIRMED
        )

        # simulate two concurrent requests
        with ThreadPoolExecutor(max_workers=2) as executor:
            f1 = executor.submit(
                BookingService.create_pending_booking, tour.id, 1001, 1
            )
            f2 = executor.submit(
                BookingService.create_pending_booking, tour.id, 1002, 1
            )
            # only one should succeed
            results = [f1.result(), f2.result()]

            self.assertEqual(len([r for r in results if r]), 1)


def test_concurrent_spot_reservation(self):
    tour_date = TourDateFactory(available_spots=1)
    with self.assertNumQueries(4):  # Lock + update
        self.client.post('/reserve/', {'tour_date': tour_date.id})
    with self.assertRaises(ValidationError):
        self.client.post('/reserve/', {'tour_date': tour_date.id})  # No spots left
