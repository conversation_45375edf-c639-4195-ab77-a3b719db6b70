# Demo Setup Guide

This guide explains how to set up a demo environment for the Tour Business Management System using the provided management commands.

## Quick Setup

To set up a complete demo environment with one command:

```bash
python manage.py setup_demo
```

This command will:
1. Run database migrations
2. Create a demo superuser (admin/admin123)
3. Create sample businesses with owners
4. Create sample clients, quotes, and invoices

## Individual Commands

### 1. Create Demo Superuser

Create an admin user for accessing the Django admin interface:

```bash
python manage.py create_demo_superuser
```

**Default credentials:**
- Username: `admin`
- Password: `admin123`
- Email: `<EMAIL>`

**Custom credentials:**
```bash
python manage.py create_demo_superuser --username myuser --email <EMAIL> --password mypass
```

### 2. Create Demo Data

Create sample businesses, users, clients, and transactions:

```bash
python manage.py create_demo_data
```

**Options:**
```bash
# Create specific numbers of entities
python manage.py create_demo_data --businesses 5 --clients 15 --users 10

# Clear existing demo data first
python manage.py create_demo_data --clear
```

**What gets created:**
- Demo businesses (Safari Adventures, Mountain Expeditions, Cultural Tours)
- Business owner accounts for each business
- Sample clients with contact information
- Sample travelers associated with clients
- Sample quotes in various statuses (draft, sent, approved)
- Sample invoices for approved quotes

### 3. Clear Demo Data

Remove all demo data from the system:

```bash
python manage.py clear_demo_data --confirm
```

This removes:
- All users with usernames starting with "demo_"
- All businesses with names starting with "Demo "
- All associated data (clients, quotes, invoices, etc.)

## Demo Accounts

After running the demo setup, you'll have access to these accounts:

### Admin Account
- **URL:** http://localhost:8000/admin/
- **Username:** admin
- **Password:** admin123
- **Access:** Full Django admin access

### Business Owner Accounts
- **Username:** demo_owner_1
- **Password:** demo123
- **Business:** Demo Safari Adventures

- **Username:** demo_owner_2
- **Password:** demo123
- **Business:** Demo Mountain Expeditions

- **Username:** demo_owner_3
- **Password:** demo123
- **Business:** Demo Cultural Tours

## Demo Data Overview

### Businesses Created
1. **Demo Safari Adventures**
   - Location: Nairobi, Kenya
   - Focus: African safari tours and wildlife experiences
   - Email: <EMAIL>

2. **Demo Mountain Expeditions**
   - Location: Denver, USA
   - Focus: Mountain climbing and trekking adventures
   - Email: <EMAIL>

3. **Demo Cultural Tours**
   - Location: Cusco, Peru
   - Focus: Cultural experiences and heritage tours
   - Email: <EMAIL>

### Sample Clients
Each business gets 10 sample clients with:
- Complete contact information
- Associated travelers
- Realistic names and email addresses

### Sample Quotes & Invoices
- Safari Adventure Package ($2,500)
- Mountain Trekking Expedition ($1,800)
- Cultural Heritage Tour ($1,200)
- Wildlife Photography Safari ($3,200)
- Family Adventure Package ($4,500)

## Usage Scenarios

### For Development
```bash
# Set up fresh demo environment
python manage.py setup_demo --clear-existing

# Start development server
python manage.py runserver
```

### For Testing
```bash
# Create minimal demo data
python manage.py create_demo_data --businesses 1 --clients 5

# Test features, then clean up
python manage.py clear_demo_data --confirm
```

### For Demonstrations
```bash
# Full setup with all sample data
python manage.py setup_demo

# Show the application at http://localhost:8000/
```

## Customization

### Adding More Sample Data

You can modify the demo data commands or use the organized factory classes:

**Using Management Commands:**
1. Edit `src/accounts/management/commands/create_demo_data.py` for basic demo data
2. Edit `src/accounts/management/commands/create_factory_demo_data.py` for realistic data
3. Add more entries to the data arrays
4. Customize business information, client details, or quote amounts

**Using Factory Classes:**
- `accounts.factories`: User, Profile factories
- `businesses.factories`: Business factories (Safari, Adventure, Cultural)
- `clients.factories`: Client, Traveler factories
- `quotes.factories`: Quote factories by business type
- `invoices.factories`: Invoice factories
- `factories.py`: Centralized imports for all factories

### Custom Business Types

To create different types of tour businesses:

1. Modify the `business_data` array in `create_businesses()` method
2. Add new business categories (adventure tours, luxury travel, eco-tourism, etc.)
3. Customize the sample quotes and services for each business type

## Troubleshooting

### Command Not Found
If you get "Unknown command" errors:
```bash
# Make sure you're in the project directory
cd /path/to/tour-business-management

# Ensure Django can find the management commands
python manage.py help
```

### Database Errors
If you encounter database errors:
```bash
# Run migrations first
python manage.py migrate

# Then set up demo data
python manage.py setup_demo --skip-migrations
```

### Permission Errors
If you get permission errors:
```bash
# Make sure the database file is writable
chmod 664 db.sqlite3

# Or recreate the database
rm db.sqlite3
python manage.py migrate
python manage.py setup_demo
```

## Using Factories in Tests

The organized factory classes can be used in your tests for creating test data:

```python
import pytest
from factories import BusinessFactory, ClientFactory, QuoteFactory

@pytest.mark.django_db
def test_business_functionality():
    # Create a business with owner
    business = BusinessFactory()

    # Create clients for the business
    client = ClientFactory(business=business)

    # Create quotes
    quote = QuoteFactory(business=business, client=client)

    # Test your functionality
    assert quote.business == business
    assert quote.client == client
```

**Factory Organization:**
```python
# Import specific factories
from accounts.factories import UserFactory, ProfileFactory
from businesses.factories import SafariBusinessFactory
from clients.factories import ClientFactory, TravelerFactory

# Or import all from centralized module
from factories import BusinessFactory, ClientFactory, QuoteFactory
```

## Best Practices

1. **Always use `--clear-existing`** when setting up for demos to ensure clean data
2. **Create backups** before running demo commands on production-like environments
3. **Use specific parameters** to control the amount of data created
4. **Use factories in tests** for consistent and realistic test data
5. **Organize factories by app** to maintain clean code structure
6. **Document any customizations** you make to the demo data commands

## Security Notes

- Demo accounts use simple passwords (demo123, admin123)
- Never use these commands or credentials in production
- Demo data includes fake email addresses and contact information
- Clear demo data before deploying to production environments
