STATE MACHINE:
PRE_RESERVED --> CONFIRMED --> COMPLETED
↑ ↑
CANCELLED (Payment Failed)

GLOBAL CONSTANTS:
RESERVATION_TIMEOUT = 900 // 15 minutes in seconds
DEPOSIT_PERCENTAGE = 0.03 // 3% deposit

// ==================================================================
// 1. RESERVE SPOT (User selects date)
// ==================================================================
FUNCTION reserve_spot(tour_date_id, user_id):
LOCK tour_date_id FOR INVENTORY UPDATE // Critical: Row-level lock

IF tour_date.available_spots <= 0:
RETURN error("NO_SLOTS")

// Atomic inventory decrement
tour_date.available_spots = tour_date.available_spots - 1
SAVE tour_date

booking = CREATE Booking(
user_id = user_id,
tour_date_id = tour_date_id,
state = "PRE_RESERVED",
expires_at = NOW() + RESERVATION_TIMEOUT
)

SCHEDULE TASK:
cancel_reservation(booking.id)
RUN_AT = booking.expires_at

RETURN booking

// ==================================================================
// 2. EXTEND RESERVATION (User clicks "Need more time?")
// ==================================================================
FUNCTION extend_reservation(booking_id):
booking = GET Booking(booking_id)
LOCK FOR INVENTORY UPDATE // Must re-lock tour_date

IF booking.state != "PRE_RESERVED":
RETURN error("INVALID_STATE")

// Extend timeout (max 2x per booking)
IF booking.extension_count >= 2:
RETURN error("MAX_EXTENSIONS")

booking.expires_at = NOW() + RESERVATION_TIMEOUT
booking.extension_count += 1
SAVE booking

RESET SCHEDULED CANCELLATION TASK
SCHEDULE NEW TASK:
cancel_reservation(booking.id)
RUN_AT = booking.expires_at

// ==================================================================
// 3. PROCESS PAYMENT (Idempotency key required)
// ==================================================================
FUNCTION process_payment(booking_id, idempotency_key, amount):
// --- IDEMPOTENCY CHECK (MUST DO FIRST) ---
existing_payment = GET Payment WHERE
booking_id = booking_id
AND idempotency_key = idempotency_key

IF existing_payment:
RETURN existing_payment // Safe retry!

booking = GET Booking(booking_id)
LOCK FOR PAYMENT PROCESSING

IF booking.state != "PRE_RESERVED":
RETURN error("INVALID_STATE")

// --- PAYMENT GATEWAY CALL ---
TRY:
gateway_response = STRIPE.CHARGE(
amount = amount,
source = card_token,
idempotency_key = idempotency_key // Passed to Stripe
)
CATCH PaymentError as e:
// Handle specific errors (declined, timeout etc.)
EMIT_EVENT("payment_failed", booking_id, e.code)
RETURN error(e.code)

// --- ATOMIC STATE TRANSITION ---
START TRANSACTION:
booking.state = "CONFIRMED"
booking.deposit_paid = booking.deposit_paid + amount
SAVE booking

    CREATE PaymentRecord(
      booking_id = booking_id,
      amount = amount,
      idempotency_key = idempotency_key,
      gateway_id = gateway_response.id
    )

COMMIT

CANCEL SCHEDULED CANCELLATION
EMIT_EVENT("booking_confirmed", booking_id)
RETURN success

// ==================================================================
// 4. AUTO-CANCEL EXPIRED RESERVATION (Scheduled task)
// ==================================================================
FUNCTION cancel_reservation(booking_id):
booking = GET Booking(booking_id)
LOCK FOR INVENTORY UPDATE // Critical: Must lock tour_date

IF booking.state != "PRE_RESERVED" OR NOW() < booking.expires_at:
RETURN // Already processed

booking.state = "CANCELLED"
SAVE booking

// RESTORE INVENTORY
tour_date = booking.tour_date
tour_date.available_spots = tour_date.available_spots + 1
SAVE tour_date

EMIT_EVENT("reservation_expired", booking_id)

// ==================================================================
// 5. MODIFY BOOKING (Date change example)
// ==================================================================
FUNCTION change_date(booking_id, new_date_id):
booking = GET Booking(booking_id)
LOCK FOR DATE CHANGE

IF booking.state != "CONFIRMED" OR booking.balance_paid:
RETURN error("CANNOT_MODIFY")

new_date = GET TourDate(new_date_id)
LOCK FOR INVENTORY UPDATE

IF new_date.available_spots <= 0:
RETURN error("NO_SLOTS_NEW_DATE")

// Atomic swap
START TRANSACTION:
// Release old spot
booking.tour_date.available_spots += 1
SAVE booking.tour_date

    // Claim new spot
    new_date.available_spots -= 1
    SAVE new_date

    // Update booking
    booking.tour_date = new_date
    booking.modified_at = NOW()
    SAVE booking

COMMIT

RETURN success

<!-- Critical error handling cases -->

// PAYMENT FAILURE RECOVERY
ON EVENT "payment_failed" (booking_id, error_code):
IF error_code == "CARD_DECLINED":
SEND USER:
"Payment failed. [Try again] or [Update card details]"

IF error_code == "GATEWAY_TIMEOUT":
// Idempotency key will prevent duplicate charge on retry
SEND USER:
"Payment processing. [Check status] in 5 mins"

// INVENTORY CONFLICT DURING MODIFICATION
FUNCTION change_date():
...
TRY:
LOCK new_date FOR INVENTORY UPDATE
CATCH LockTimeout:
RETURN error("DATE_BUSY_TRY_AGAIN")
