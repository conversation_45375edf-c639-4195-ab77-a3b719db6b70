"""
Integration tests for user registration and email confirmation system.
"""
import pytest
from unittest.mock import patch, MagicMock
from django.test import override_settings
from django.core import mail
from django.urls import reverse
from django.contrib.auth import get_user_model
from django.contrib.messages import get_messages

from accounts.forms import CustomUserCreationForm
from core.notifications import EmailConfirmationNotification

User = get_user_model()


@pytest.mark.django_db
class TestRegistrationIntegration:
    """Integration tests for the complete registration flow."""

    @override_settings(EMAIL_BACKEND='django.core.mail.backends.locmem.EmailBackend')
    def test_complete_registration_flow(self, api_client):
        """Test the complete registration flow from form submission to email confirmation."""
        # Clear any existing emails
        mail.outbox = []
        
        # Step 1: Submit registration form
        registration_data = {
            'username': 'integrationuser',
            'email': '<EMAIL>',
            'password1': 'complexpassword123',
            'password2': 'complexpassword123'
        }
        
        response = api_client.post(reverse('accounts:register'), registration_data)
        
        # Check redirect to login page
        assert response.status_code == 302
        assert response.url == reverse('accounts:login')
        
        # Step 2: Verify user was created but not active
        user = User.objects.get(username='integrationuser')
        assert not user.is_active
        assert user.email == '<EMAIL>'
        assert user.email_confirmation_token is not None
        assert user.email_confirmation_sent_at is not None
        
        # Step 3: Verify confirmation email was sent
        assert len(mail.outbox) == 1
        email = mail.outbox[0]
        assert '<EMAIL>' in email.to
        assert 'Confirm your email address' in email.subject
        
        # Step 4: Extract confirmation token from email
        email_body = email.body
        assert str(user.email_confirmation_token) in email_body
        
        # Step 5: Confirm email using the token
        confirmation_response = api_client.get(
            reverse('accounts:confirm_email', kwargs={'token': str(user.email_confirmation_token)})
        )
        
        # Step 6: Verify user is now active
        user.refresh_from_db()
        assert user.is_active
        assert user.email_confirmed_at is not None
        
        # Step 7: Verify redirect after confirmation
        assert confirmation_response.status_code == 302

    @override_settings(EMAIL_BACKEND='django.core.mail.backends.locmem.EmailBackend')
    def test_registration_with_existing_email(self, api_client, user):
        """Test registration with an email that already exists."""
        mail.outbox = []
        
        registration_data = {
            'username': 'newuser',
            'email': user.email,  # Use existing email
            'password1': 'complexpassword123',
            'password2': 'complexpassword123'
        }
        
        response = api_client.post(reverse('accounts:register'), registration_data)
        
        # Should still create user (Django allows duplicate emails by default)
        # But we should handle this in our business logic
        assert User.objects.filter(email=user.email).count() >= 1

    @override_settings(EMAIL_BACKEND='django.core.mail.backends.locmem.EmailBackend')
    def test_registration_success_message(self, api_client):
        """Test that registration shows success message."""
        registration_data = {
            'username': 'messageuser',
            'email': '<EMAIL>',
            'password1': 'complexpassword123',
            'password2': 'complexpassword123'
        }
        
        response = api_client.post(reverse('accounts:register'), registration_data, follow=True)
        
        # Check for success message
        messages = list(get_messages(response.wsgi_request))
        assert len(messages) > 0
        success_message = str(messages[0])
        assert 'Account created successfully' in success_message
        assert '<EMAIL>' in success_message

    def test_registration_form_validation_errors(self, api_client):
        """Test registration form validation errors."""
        # Test with invalid data
        invalid_data = {
            'username': '',  # Empty username
            'email': 'invalid-email',  # Invalid email
            'password1': '123',  # Too short password
            'password2': '456'  # Different password
        }
        
        response = api_client.post(reverse('accounts:register'), invalid_data)
        
        # Should not redirect (form has errors)
        assert response.status_code == 200
        
        # Should not create user
        assert not User.objects.filter(email='invalid-email').exists()


@pytest.mark.django_db
class TestEmailConfirmationEdgeCases:
    """Test edge cases for email confirmation."""

    def test_confirmation_with_already_active_user(self, api_client, user):
        """Test confirmation attempt with already active user."""
        # Make user active and generate token
        user.is_active = True
        token = user.generate_confirmation_token()
        user.save()
        
        response = api_client.get(reverse('accounts:confirm_email', kwargs={'token': str(token)}))
        
        # Should still work (idempotent)
        assert response.status_code == 302
        user.refresh_from_db()
        assert user.is_active

    def test_confirmation_with_nonexistent_token(self, api_client):
        """Test confirmation with non-existent token."""
        import uuid
        fake_token = uuid.uuid4()
        
        response = api_client.get(reverse('accounts:confirm_email', kwargs={'token': str(fake_token)}))
        
        # Should handle gracefully
        assert response.status_code in [302, 404]  # Redirect or not found

    @override_settings(EMAIL_BACKEND='django.core.mail.backends.locmem.EmailBackend')
    def test_multiple_confirmation_emails(self, user):
        """Test sending multiple confirmation emails."""
        mail.outbox = []
        
        confirmation_url = "http://example.com/confirm/test-token"
        
        # Send multiple emails
        for i in range(3):
            EmailConfirmationNotification(user, confirmation_url).send()
        
        # Should send all emails
        assert len(mail.outbox) == 3
        
        # All should be to the same user
        for email in mail.outbox:
            assert user.email in email.to


@pytest.mark.django_db
class TestEmailBackendFailures:
    """Test handling of email backend failures."""

    @patch('core.notifications.EmailConfirmationNotification.send')
    def test_email_send_failure_handling(self, mock_send, api_client):
        """Test handling when email sending fails."""
        # Mock email sending to raise an exception
        mock_send.side_effect = Exception("SMTP server unavailable")
        
        registration_data = {
            'username': 'failuser',
            'email': '<EMAIL>',
            'password1': 'complexpassword123',
            'password2': 'complexpassword123'
        }
        
        # Registration should still work even if email fails
        response = api_client.post(reverse('accounts:register'), registration_data)
        
        # User should still be created
        assert User.objects.filter(username='failuser').exists()
        user = User.objects.get(username='failuser')
        assert user.email_confirmation_token is not None

    @override_settings(
        EMAIL_BACKEND='django.core.mail.backends.smtp.EmailBackend',
        EMAIL_HOST='nonexistent.smtp.server',
        EMAIL_PORT=587
    )
    def test_smtp_connection_failure(self, user):
        """Test SMTP connection failure handling."""
        confirmation_url = "http://example.com/confirm/test-token"
        notification = EmailConfirmationNotification(user, confirmation_url)
        
        # Should handle connection failure gracefully
        try:
            notification.send()
        except Exception as e:
            # Expected to fail with connection error
            assert 'Name or service not known' in str(e) or 'Connection refused' in str(e)


@pytest.mark.django_db
class TestCrispyFormsIntegration:
    """Test crispy forms integration with email functionality."""

    def test_crispy_form_rendering(self, api_client):
        """Test that crispy forms render correctly on registration page."""
        response = api_client.get(reverse('accounts:register'))
        
        assert response.status_code == 200
        # Check that crispy forms template tags are loaded
        assert 'crispy_forms_tags' in response.content.decode()

    @override_settings(EMAIL_BACKEND='django.core.mail.backends.locmem.EmailBackend')
    def test_crispy_form_submission(self, api_client):
        """Test form submission with crispy forms."""
        mail.outbox = []
        
        registration_data = {
            'username': 'crispyuser',
            'email': '<EMAIL>',
            'password1': 'complexpassword123',
            'password2': 'complexpassword123'
        }
        
        response = api_client.post(reverse('accounts:register'), registration_data)
        
        # Should work the same as regular forms
        assert response.status_code == 302
        assert User.objects.filter(username='crispyuser').exists()
        assert len(mail.outbox) == 1


@pytest.mark.django_db
class TestEmailTemplateContent:
    """Test email template content and formatting."""

    @override_settings(EMAIL_BACKEND='django.core.mail.backends.locmem.EmailBackend')
    def test_email_content_includes_required_elements(self, user):
        """Test that email content includes all required elements."""
        mail.outbox = []
        
        confirmation_url = "http://example.com/confirm/test-token-123"
        notification = EmailConfirmationNotification(user, confirmation_url)
        notification.send()
        
        assert len(mail.outbox) == 1
        email = mail.outbox[0]
        
        # Check subject
        assert 'Confirm your email address' in email.subject
        
        # Check body content
        body = email.body
        assert user.username in body or user.email in body
        assert confirmation_url in body
        assert 'confirm' in body.lower()
        
        # Check that it's not just plain text
        assert len(body) > 50  # Should be a substantial email

    @override_settings(EMAIL_BACKEND='django.core.mail.backends.locmem.EmailBackend')
    def test_email_html_and_text_versions(self, user):
        """Test that email has both HTML and text versions."""
        mail.outbox = []
        
        confirmation_url = "http://example.com/confirm/test-token"
        notification = EmailConfirmationNotification(user, confirmation_url)
        notification.send()
        
        assert len(mail.outbox) == 1
        email = mail.outbox[0]
        
        # Check that we have both text and HTML content
        # Django Herald should provide both versions
        assert hasattr(email, 'body')  # Text version
        # HTML version would be in alternatives if present
