from django.conf import settings
from django.core.validators import EmailValidator, RegexValidator
from django.db import models
from django.utils.translation import gettext_lazy as _

from core.mixins import UserAuditModel


class BusinessManager(models.Manager):
    """Optimized manager for Business model."""

    def get_queryset(self):
        """Return optimized queryset."""
        return (
            super()
            .get_queryset()
            .select_related(
                'created_by',
            )
        )

    def active(self):
        """Return only active businesses."""
        return self.filter(is_active=True)

    def with_users(self):
        """Return businesses with prefetched users."""
        return self.prefetch_related('profiles__user')


class Business(UserAuditModel):
    """
    Business model for multi-tenant tour business management.
    Each business is isolated and manages its own data.
    """

    # Basic Information
    name = models.CharField(
        max_length=200,
        verbose_name=_('Business name'),
    )
    description = models.TextField(
        blank=True,
        help_text=_('Business description'),
    )

    # Contact Information
    email = models.EmailField(
        validators=[EmailValidator()],
        help_text=_('Business contact email'),
    )
    phone = models.CharField(
        max_length=20,
        validators=[
            RegexValidator(
                regex=r'^\+?1?\d{9,15}$',
                message=_(
                    "Phone number must be entered in the format: '+999999999'. Up to 15 digits allowed."
                ),
            )
        ],
        help_text=_('Business contact phone'),
    )
    website = models.URLField(
        blank=True,
        help_text=_('Business website URL'),
    )

    # Address Information
    address_line1 = models.CharField(
        max_length=255,
        help_text=_('Street address'),
    )
    address_line2 = models.CharField(
        max_length=255,
        blank=True,
        help_text=_('Apartment, suite, etc.'),
    )
    city = models.CharField(
        max_length=100,
        help_text=_('City'),
    )
    state_province = models.CharField(
        max_length=100,
        help_text=_('State or Province'),
    )
    postal_code = models.CharField(
        max_length=20,
        help_text=_('Postal or ZIP code'),
    )
    country = models.CharField(
        max_length=100,
        default='Tanzania',
        verbose_name=_('Country'),
    )

    # Business Settings
    timezone = models.CharField(
        max_length=50,
        default=settings.TIME_ZONE,
        help_text=_('Business timezone'),
    )
    currency = models.CharField(
        max_length=3,
        default='TZS',
        help_text=_('Default currency code (ISO 4217)'),
    )

    # Business Logo
    logo = models.ImageField(
        upload_to='business_logos/',
        blank=True,
        null=True,
        help_text=_('Business logo for documents and branding'),
    )

    # Subscription/Plan Information
    plan = models.CharField(
        max_length=50,
        default='basic',
        help_text=_('Subscription plan'),
    )
    is_active = models.BooleanField(
        default=True,
        help_text=_('Whether the business account is active'),
    )

    # Customization fields
    logo = models.ImageField(
        upload_to='business_logos/',
        blank=True,
        null=True,
        help_text=_('Business logo for branding'),
    )
    primary_color = models.CharField(
        max_length=7,
        default='#007bff',
        help_text=_('Primary brand color (hex format)'),
    )
    secondary_color = models.CharField(
        max_length=7,
        default='#6c757d',
        help_text=_('Secondary brand color (hex format)'),
    )
    accent_color = models.CharField(
        max_length=7,
        default='#28a745',
        help_text=_('Accent color for highlights (hex format)'),
    )
    custom_css = models.TextField(
        blank=True,
        help_text=_('Custom CSS for business-specific styling'),
    )
    email_signature = models.TextField(
        blank=True,
        help_text=_('Custom email signature for this business'),
    )

    # PDF Template preferences
    quote_template = models.CharField(
        max_length=100,
        default='default',
        help_text=_('Quote PDF template to use'),
    )
    invoice_template = models.CharField(
        max_length=100,
        default='default',
        help_text=_('Invoice PDF template to use'),
    )

    class Meta:
        db_table = 'businesses_business'
        verbose_name = _('Business')
        verbose_name_plural = _('Businesses')
        indexes = [
            models.Index(fields=['name']),
            models.Index(fields=['is_active']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return self.name

    def get_logo_url(self):
        """Get the logo URL or default logo."""
        if self.logo:
            return self.logo.url
        return '/static/images/default-business-logo.png'

    def get_theme_css(self):
        """Generate CSS variables for business theme."""
        return f"""
        :root {{
            --business-primary: {self.primary_color};
            --business-secondary: {self.secondary_color};
            --business-accent: {self.accent_color};
        }}

        .business-theme {{
            --primary-color: {self.primary_color};
            --secondary-color: {self.secondary_color};
            --accent-color: {self.accent_color};
        }}

        .btn-business-primary {{
            background-color: {self.primary_color};
            border-color: {self.primary_color};
        }}

        .btn-business-primary:hover {{
            background-color: {self._darken_color(self.primary_color)};
            border-color: {self._darken_color(self.primary_color)};
        }}

        .text-business-primary {{
            color: {self.primary_color} !important;
        }}

        .bg-business-primary {{
            background-color: {self.primary_color} !important;
        }}

        .border-business-primary {{
            border-color: {self.primary_color} !important;
        }}

        {self.custom_css}
        """

    def _darken_color(self, hex_color, amount=0.1):
        """Darken a hex color by a given amount."""
        # Remove the hash if present
        hex_color = hex_color.lstrip('#')

        # Convert to RGB
        rgb = tuple(int(hex_color[i : i + 2], 16) for i in (0, 2, 4))

        # Darken each component
        darkened = tuple(max(0, int(c * (1 - amount))) for c in rgb)

        # Convert back to hex
        return f'#{darkened[0]:02x}{darkened[1]:02x}{darkened[2]:02x}'

    def get_available_templates(self, template_type):
        """Get available templates for this business."""
        from documents.models import PDFTemplate

        # Get business-specific templates
        business_templates = PDFTemplate.objects.filter(
            business=self, template_type=template_type, is_active=True
        )

        # Get default templates
        default_templates = PDFTemplate.objects.filter(
            business__isnull=True, template_type=template_type, is_active=True
        )

        return list(business_templates) + list(default_templates)

    @property
    def full_address(self):
        """Return the complete formatted address."""
        address_parts = [self.address_line1]
        if self.address_line2:
            address_parts.append(self.address_line2)
        address_parts.extend(
            [f'{self.city}, {self.state_province} {self.postal_code}', self.country]
        )
        return '\n'.join(address_parts)

    def get_users(self):
        """Get all users associated with this business."""
        # Get users through their profiles
        from accounts.models import User

        return User.objects.filter(profile__business=self, is_active=True).distinct()

    # Custom manager
    objects = BusinessManager()
