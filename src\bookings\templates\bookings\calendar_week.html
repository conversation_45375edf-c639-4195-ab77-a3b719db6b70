{% load i18n %}

<c-layouts.dashboard>
  <c-slot name="title">
    Weekly Calendar - {{ start_of_week|date:"M d" }} to {{ end_of_week|date:"M d, Y" }}
  </c-slot>

  <c-slot name="main">
    <div class="container mx-auto px-4 py-8">
      <!-- Calendar Header -->
      <div class="flex justify-between items-center mb-8">
        <div>
          <h1 class="text-3xl font-bold text-gray-900">
            Weekly Calendar
          </h1>
          <p class="text-gray-600 mt-2">
            {{ start_of_week|date:"M d" }} - {{ end_of_week|date:"M d, Y" }}
          </p>
        </div>
        <div class="flex space-x-3">
          <a href="{% url 'bookings:calendar' %}"
            class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
            Month View
          </a>
          <a href="{% url 'bookings:calendar_day' %}"
            class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
            Day View
          </a>
          <a href="{% url 'tours:event_create' %}"
            class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
            New Event
          </a>
        </div>
      </div>

      <!-- Week Navigation -->
      <div class="flex justify-between items-center mb-6">
        <a href="{% url 'bookings:calendar_week_date' prev_week.year prev_week.month prev_week.day %}"
          class="flex items-center px-4 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7">
            </path>
          </svg>
          Previous Week
        </a>

        <div class="flex items-center space-x-4">
          <a href="{% url 'bookings:calendar_week' %}" class="text-blue-600 hover:text-blue-800 font-medium">
            This Week
          </a>
        </div>

        <a href="{% url 'bookings:calendar_week_date' next_week.year next_week.month next_week.day %}"
          class="flex items-center px-4 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors">
          Next Week
          <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
          </svg>
        </a>
      </div>

      <!-- Weekly Calendar Grid -->
      <div class="bg-white rounded-lg shadow overflow-hidden">
        <!-- Day Headers -->
        <div class="grid grid-cols-8 bg-gray-50 border-b border-gray-200">
          <div class="p-4 text-center text-sm font-medium text-gray-700 border-r border-gray-200">
            Time
          </div>
          {% for day in week_days %}
          <div
            class="p-4 text-center border-r border-gray-200 last:border-r-0 {% if day == today %}bg-blue-50{% endif %}">
            <div class="text-sm font-medium text-gray-700">
              {{ day|date:"D" }}
            </div>
            <div class="text-lg font-semibold {% if day == today %}text-blue-600{% else %}text-gray-900{% endif %}">
              {{ day|date:"d" }}
            </div>
          </div>
          {% endfor %}
        </div>

        <!-- Time Slots -->
        <div class="max-h-96 overflow-y-auto">
          {% for time_slot in time_slots %}
          <div class="grid grid-cols-8 border-b border-gray-100 hover:bg-gray-50">
            <!-- Time Column -->
            <div class="p-2 text-xs text-gray-500 border-r border-gray-200 text-center">
              {{ time_slot|time:"H:i" }}
            </div>

            <!-- Day Columns -->
            {% for day in week_days %}
            <div class="p-1 border-r border-gray-200 last:border-r-0 min-h-12 relative">
              <!-- Events for this time slot -->
              {% for event in events_by_date|default_if_none:"" %}
              {% if event.event.start_time.date == day and event.event.start_time.time <= time_slot and event.event.end_time.time > time_slot %}
              <div
                class="absolute inset-1 rounded text-xs p-1 cursor-pointer {% if event.event_type == 'tour' %}bg-green-100 text-green-800 border border-green-200 {% elif event.event_type == 'meeting' %}bg-blue-100 text-blue-800 border border-blue-200 {% elif event.event_type == 'consultation' %}bg-yellow-100 text-yellow-800 border border-yellow-200 {% elif event.event_type == 'maintenance' %}bg-red-100 text-red-800 border border-red-200 {% elif event.event_type == 'training' %}bg-purple-100 text-purple-800 border border-purple-200 {% else %}bg-gray-100 text-gray-800 border border-gray-200{% endif %}"
                data-on-click="windows.location.href={% url 'tours:event_detail' %}"
                title="{{ event.event.title }} - {{ event.event.start_time|time:'H:i' }} to {{ event.event.end_time|time:'H:i' }}">
                <div class="font-medium truncate">{{ event.event.title|truncatechars:20 }}</div>
                <div class="text-xs opacity-75">
                  {{ event.event.start_time|time:"H:i" }}-{{ event.event.end_time|time:"H:i" }}
                </div>
              </div>
              {% endif %}
              {% endfor %}
            </div>
            {% endfor %}
          </div>
          {% endfor %}
        </div>
      </div>

      <!-- Week Summary -->
      <div class="mt-6 grid grid-cols-1 md:grid-cols-7 gap-4">
        {% for day in week_days %}
        <div class="bg-white rounded-lg shadow p-4">
          <div class="text-center mb-3">
            <div class="text-sm font-medium text-gray-700">{{ day|date:"D" }}</div>
            <div class="text-lg font-semibold
                        {% if day == today %}text-blue-600{% else %}text-gray-900{% endif %}">
              {{ day|date:"d" }}
            </div>
          </div>

          <div class="space-y-2">
            {% with day|date:"Y-m-d" as day_key %}
            {% for event in events_by_date|default_if_none:"" %}
            {% if event.event.start_time.date|date:"Y-m-d" == day_key %}
            <div
              class="text-xs p-2 rounded cursor-pointer {% if event.event_type == 'tour' %}bg-green-100 text-green-800 {% elif event.event_type == 'meeting' %}bg-blue-100 text-blue-800 {% elif event.event_type == 'consultation' %}bg-yellow-100 text-yellow-800 {% elif event.event_type == 'maintenance' %}bg-red-100 text-red-800 {% elif event.event_type == 'training' %}bg-purple-100 text-purple-800 {% else %}bg-gray-100 text-gray-800{% endif %}"
              data-on-click="window.location.href='{% url 'tours:event_detail' event.pk %}"
              title="{{ event.event.title }}">
              <div class="font-medium">
                {{ event.event.start_time|time:"H:i" }}
              </div>
              <div class="truncate">
                {{ event.event.title|truncatechars:15 }}
              </div>
            </div>
            {% endif %}
            {% endfor %}

            <!-- Show message if no events -->
            {% with events_by_date|default_if_none:""|length as day_event_count %}
            {% if day_event_count == 0 %}
            <div class="text-xs text-gray-500 text-center py-2">
              No events
            </div>
            {% endif %}
            {% endwith %}
            {% endwith %}
          </div>
        </div>
        {% endfor %}
      </div>

      <!-- Legend -->
      <div class="mt-6 bg-white rounded-lg shadow p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Event Types</h3>
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
          <div class="flex items-center">
            <div class="w-4 h-4 bg-green-100 border border-green-200 rounded mr-2"></div>
            <span class="text-sm text-gray-700">Tours</span>
          </div>
          <div class="flex items-center">
            <div class="w-4 h-4 bg-blue-100 border border-blue-200 rounded mr-2"></div>
            <span class="text-sm text-gray-700">Meetings</span>
          </div>
          <div class="flex items-center">
            <div class="w-4 h-4 bg-yellow-100 border border-yellow-200 rounded mr-2"></div>
            <span class="text-sm text-gray-700">Consultations</span>
          </div>
          <div class="flex items-center">
            <div class="w-4 h-4 bg-red-100 border border-red-200 rounded mr-2"></div>
            <span class="text-sm text-gray-700">Maintenance</span>
          </div>
          <div class="flex items-center">
            <div class="w-4 h-4 bg-purple-100 border border-purple-200 rounded mr-2"></div>
            <span class="text-sm text-gray-700">Training</span>
          </div>
          <div class="flex items-center">
            <div class="w-4 h-4 bg-gray-100 border border-gray-200 rounded mr-2"></div>
            <span class="text-sm text-gray-700">Other</span>
          </div>
        </div>
      </div>
    </div>

    <style>
      /* Custom styles for weekly calendar */
      .time-slot {
        height: 60px;
      }

      .event-block {
        position: absolute;
        left: 2px;
        right: 2px;
        border-radius: 4px;
        padding: 2px 4px;
        font-size: 0.75rem;
        line-height: 1rem;
        overflow: hidden;
      }

      .event-block:hover {
        transform: scale(1.02);
        z-index: 10;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        transition: all 0.1s ease-in-out;
      }
    </style>
  </c-slot>
</c-layouts.dashboard>
