# Generated by Django 5.2.4 on 2025-08-12 11:35

import django.core.validators
import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('businesses', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Client',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, help_text='Unique identifier for this record', primary_key=True, serialize=False, unique=True)),
                ('client_type', models.CharField(choices=[('individual', 'Individual'), ('corporate', 'Corporate'), ('travel_agent', 'Travel Agent')], default='individual', help_text='Type of client', max_length=20)),
                ('first_name', models.Char<PERSON><PERSON>(help_text='First name', max_length=100)),
                ('last_name', models.CharField(help_text='Last name', max_length=100)),
                ('company_name', models.CharField(blank=True, help_text='Company name (for corporate clients)', max_length=200)),
                ('email', models.EmailField(help_text='Primary email address', max_length=254, validators=[django.core.validators.EmailValidator()])),
                ('phone', models.CharField(help_text='Primary phone number', max_length=20, validators=[django.core.validators.RegexValidator(message="Phone number must be entered in the format: '+*********'. Up to 15 digits allowed.", regex='^\\+?1?\\d{9,15}$')])),
                ('secondary_phone', models.CharField(blank=True, help_text='Secondary phone number', max_length=20, validators=[django.core.validators.RegexValidator(message="Phone number must be entered in the format: '+*********'. Up to 15 digits allowed.", regex='^\\+?1?\\d{9,15}$')])),
                ('address_line1', models.CharField(blank=True, help_text='Street address', max_length=255)),
                ('address_line2', models.CharField(blank=True, help_text='Apartment, suite, etc.', max_length=255)),
                ('city', models.CharField(blank=True, help_text='City', max_length=100)),
                ('state_province', models.CharField(blank=True, help_text='State or Province', max_length=100)),
                ('postal_code', models.CharField(blank=True, help_text='Postal or ZIP code', max_length=20)),
                ('country', models.CharField(blank=True, help_text='Country', max_length=100)),
                ('notes', models.TextField(blank=True, help_text='Internal notes about the client')),
                ('preferences', models.TextField(blank=True, help_text='Client preferences and special requirements')),
                ('is_active', models.BooleanField(default=True, help_text='Whether the client is active')),
                ('business', models.ForeignKey(help_text='Business this client belongs to', on_delete=django.db.models.deletion.CASCADE, related_name='clients', to='businesses.business')),
                ('created_by', models.ForeignKey(help_text='User who created this record', on_delete=django.db.models.deletion.PROTECT, related_name='%(class)ss_created', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Client',
                'verbose_name_plural': 'Clients',
                'db_table': 'clients_client',
            },
        ),
        migrations.CreateModel(
            name='Traveler',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, help_text='Unique identifier for this record', primary_key=True, serialize=False, unique=True)),
                ('first_name', models.CharField(max_length=100, verbose_name='First name')),
                ('last_name', models.CharField(max_length=100, verbose_name='Last name')),
                ('date_of_birth', models.DateField(blank=True, null=True, verbose_name='Date of birth')),
                ('gender', models.CharField(choices=[('M', 'Male'), ('F', 'Female')], max_length=1, verbose_name='Gender')),
                ('email', models.EmailField(blank=True, help_text='Email address', max_length=254, validators=[django.core.validators.EmailValidator()])),
                ('phone', models.CharField(blank=True, help_text='Phone number', max_length=20, validators=[django.core.validators.RegexValidator(message="Phone number must be entered in the format: '+*********'. Up to 15 digits allowed.", regex='^\\+?1?\\d{9,15}$')])),
                ('passport_number', models.CharField(blank=True, help_text='Passport number', max_length=50)),
                ('passport_expiry', models.DateField(blank=True, help_text='Passport expiry date', null=True)),
                ('nationality', models.CharField(blank=True, help_text='Nationality', max_length=100)),
                ('emergency_contact_name', models.CharField(blank=True, help_text='Emergency contact name', max_length=200)),
                ('emergency_contact_phone', models.CharField(blank=True, help_text='Emergency contact phone', max_length=20, validators=[django.core.validators.RegexValidator(message="Phone number must be entered in the format: '+*********'. Up to 15 digits allowed.", regex='^\\+?1?\\d{9,15}$')])),
                ('emergency_contact_relationship', models.CharField(blank=True, help_text='Relationship to emergency contact', max_length=100)),
                ('dietary_restrictions', models.TextField(blank=True, help_text='Dietary restrictions and preferences')),
                ('medical_conditions', models.TextField(blank=True, help_text='Medical conditions and requirements')),
                ('mobility_requirements', models.TextField(blank=True, help_text='Mobility assistance requirements')),
                ('special_requests', models.TextField(blank=True, help_text='Other special requests or notes')),
                ('is_active', models.BooleanField(default=True, help_text='Whether the traveler is active')),
                ('client', models.ForeignKey(help_text='Client this traveler belongs to', on_delete=django.db.models.deletion.CASCADE, related_name='travelers', to='clients.client')),
                ('created_by', models.ForeignKey(help_text='User who created this record', on_delete=django.db.models.deletion.PROTECT, related_name='%(class)ss_created', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Traveler',
                'verbose_name_plural': 'Travelers',
            },
        ),
        migrations.AddIndex(
            model_name='client',
            index=models.Index(fields=['business', 'email'], name='clients_cli_busines_c3e75c_idx'),
        ),
        migrations.AddIndex(
            model_name='client',
            index=models.Index(fields=['business', 'last_name', 'first_name'], name='clients_cli_busines_b07a55_idx'),
        ),
        migrations.AddIndex(
            model_name='client',
            index=models.Index(fields=['business', 'company_name'], name='clients_cli_busines_7bdd9f_idx'),
        ),
        migrations.AddIndex(
            model_name='client',
            index=models.Index(fields=['business', 'is_active'], name='clients_cli_busines_e5fb49_idx'),
        ),
        migrations.AddIndex(
            model_name='client',
            index=models.Index(fields=['created_at'], name='clients_cli_created_4ff9ec_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='client',
            unique_together={('business', 'email')},
        ),
        migrations.AddIndex(
            model_name='traveler',
            index=models.Index(fields=['client', 'last_name', 'first_name'], name='clients_tra_client__bcc695_idx'),
        ),
        migrations.AddIndex(
            model_name='traveler',
            index=models.Index(fields=['client', 'is_active'], name='clients_tra_client__189f91_idx'),
        ),
        migrations.AddIndex(
            model_name='traveler',
            index=models.Index(fields=['created_at'], name='clients_tra_created_937b98_idx'),
        ),
    ]
