# Generated by Django 5.2.4 on 2025-08-13 09:17

import django.core.validators
import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ('tours', '0002_touraddon_alter_tourtemplate_options_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='GuideAssignment',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                (
                    'id',
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        help_text='Unique identifier for this record',
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                (
                    'role',
                    models.CharField(
                        choices=[
                            ('lead_guide', 'Lead Guide'),
                            ('assistant_guide', 'Assistant Guide'),
                            ('specialist', 'Specialist'),
                            ('trainee', 'Trainee'),
                        ],
                        default='lead_guide',
                        help_text='Role of the guide for this tour',
                        max_length=20,
                    ),
                ),
                (
                    'notes',
                    models.TextField(
                        blank=True, help_text='Additional notes about this assignment'
                    ),
                ),
                (
                    'created_by',
                    models.ForeignKey(
                        help_text='User who created this record',
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name='%(class)ss_created',
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    'guide',
                    models.ForeignKey(
                        help_text='Guide assigned to this tour',
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='guide_assignments',
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    'tour_instance',
                    models.ForeignKey(
                        help_text='Tour instance for this assignment',
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='guide_assignments',
                        to='tours.tourinstance',
                    ),
                ),
            ],
            options={
                'verbose_name': 'Guide Assignment',
                'verbose_name_plural': 'Guide Assignments',
                'indexes': [
                    models.Index(
                        fields=['tour_instance', 'role'],
                        name='tours_guide_tour_in_95c115_idx',
                    ),
                    models.Index(
                        fields=['guide'], name='tours_guide_guide_i_6b4108_idx'
                    ),
                ],
                'unique_together': {('tour_instance', 'guide', 'role')},
            },
        ),
        migrations.CreateModel(
            name='TemporaryReservation',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                (
                    'id',
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        help_text='Unique identifier for this record',
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                (
                    'session_key',
                    models.CharField(
                        help_text='Session key for the user making the reservation',
                        max_length=40,
                    ),
                ),
                (
                    'spots_reserved',
                    models.PositiveSmallIntegerField(
                        default=1,
                        help_text='Number of spots temporarily reserved',
                        validators=[django.core.validators.MinValueValidator(1)],
                    ),
                ),
                (
                    'expires_at',
                    models.DateTimeField(help_text='When this reservation expires'),
                ),
                (
                    'tour_instance',
                    models.ForeignKey(
                        help_text='Tour instance being reserved',
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='temporary_reservations',
                        to='tours.tourinstance',
                    ),
                ),
            ],
            options={
                'verbose_name': 'Temporary Reservation',
                'verbose_name_plural': 'Temporary Reservations',
                'indexes': [
                    models.Index(
                        fields=['session_key'], name='tours_tempo_session_42cb6e_idx'
                    ),
                    models.Index(
                        fields=['tour_instance', 'expires_at'],
                        name='tours_tempo_tour_in_561e84_idx',
                    ),
                    models.Index(
                        fields=['expires_at'], name='tours_tempo_expires_36f2aa_idx'
                    ),
                ],
            },
        ),
    ]
