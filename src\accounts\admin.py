from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from simple_history.admin import SimpleHistoryAdmin

from .models import Profile, User


@admin.register(User)
class UserAdmin(BaseUserAdmin):
    """Admin configuration for custom User model."""

    list_display = (
        'username',
        'email',
        'first_name',
        'last_name',
        'is_active',
        'date_joined',
    )
    search_fields = (
        'username',
        'email',
        'first_name',
        'last_name',
        'phone',
    )
    readonly_fields = (
        'created_at',
        'updated_at',
    )

    def get_queryset(self, request):
        """Optimize queryset with select_related."""
        return super().get_queryset(request)


@admin.register(Profile)
class ProfileAdmin(SimpleHistoryAdmin):
    list_display = (
        'user',
        'first_name',
        'last_name',
        'phone',
        'role',
        'department',
        'business',
    )
