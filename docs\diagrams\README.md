# Tour Management SaaS - System Diagrams

This directory contains comprehensive system diagrams for the Tour Management SaaS application. These diagrams provide visual documentation of the system architecture, workflows, and data relationships.

## 📊 Available Diagrams

### 1. [Database Entity Relationship Diagram](./database-erd.md)

**Purpose:** Shows the complete database schema and entity relationships

**Key Features:**

- All database entities and their fields
- Foreign key relationships and constraints
- Multi-tenant architecture visualization
- Data integrity and indexing strategy

**Use Cases:**

- Database design and development
- Understanding data relationships
- Planning database migrations
- Onboarding new developers

### 2. [Core Business Workflow](./core-business-workflow.md)

**Purpose:** Illustrates the end-to-end business process flow

**Key Features:**

- Complete customer journey from lead to completion
- Decision points and exception handling
- Quote approval and booking workflows
- Payment and tour execution processes

**Use Cases:**

- Business process documentation
- Training new team members
- Process optimization and improvement
- Stakeholder presentations

### 3. [User Roles and Permissions](./user-roles-permissions.md)

**Purpose:** Details the user role system and permission management

**Key Features:**

- Three user roles: Business Owner, Agent, Customer
- Onboarding processes for each role
- Permission checks and security controls
- Multi-tenant data isolation

**Use Cases:**

- Security planning and implementation
- User access control design
- Role-based feature development
- Compliance and audit requirements

### 4. [Quote to Payment Sequence](./quote-to-payment-sequence.md)

**Purpose:** Shows detailed interaction flow from quote creation to payment

**Key Features:**

- Step-by-step sequence of interactions
- System component communication
- External service integrations
- Error handling and alternative flows

**Use Cases:**

- Integration planning and development
- API design and implementation
- Troubleshooting workflow issues
- External service integration

### 5. [System Architecture Overview](./system-architecture.md)

**Purpose:** Displays the complete system architecture and technology stack

**Key Features:**

- Layered architecture visualization
- Service dependencies and relationships
- External integrations and security
- Technology stack and frameworks

**Use Cases:**

- System design and planning
- Technology decisions and evaluation
- Infrastructure planning
- Developer onboarding

### 6. [Data Flow Diagram](./data-flow-diagram.md)

**Purpose:** Illustrates how data moves through the system

**Key Features:**

- External entities and data sources
- Core business processes
- Data stores and relationships
- Audit and logging flows

**Use Cases:**

- Data architecture planning
- Process optimization
- Integration design
- Compliance and audit trails

## 🎯 Diagram Usage Guidelines

### For Developers

- **Start with**: System Architecture Overview
- **Focus on**: Database ERD and Data Flow Diagram
- **Reference**: Sequence diagrams for integration work

### For Business Stakeholders

- **Start with**: Core Business Workflow
- **Focus on**: User Roles and Permissions
- **Reference**: Quote to Payment Sequence for process understanding

### For System Architects

- **Start with**: System Architecture Overview
- **Focus on**: Data Flow Diagram and Database ERD
- **Reference**: All diagrams for comprehensive understanding

### For Project Managers

- **Start with**: Core Business Workflow
- **Focus on**: User Roles and Permissions
- **Reference**: System Architecture for technical planning

## 🔧 Technical Details

### Diagram Format

- **Language**: Mermaid
- **Rendering**: GitHub native support or Mermaid Live Editor
- **Maintenance**: Update diagrams when system changes occur

### Viewing Diagrams

#### **For Best Experience (Recommended):**

1. **[Mermaid Live Editor](https://mermaid.live/)**:
   - Copy diagram code and paste into editor
   - ✅ **Pan & Zoom**: Mouse wheel zoom, click-and-drag panning
   - ✅ **Full Screen**: Maximize for large diagrams
   - ✅ **Export**: Download as PNG/SVG/PDF
   - ✅ **Themes**: High contrast options available

#### **Other Viewing Options:**

2. **GitHub**: Diagrams render automatically (use light mode for best contrast)
   - Use browser zoom (`Ctrl/Cmd + Plus`) for larger view
   - Right-click diagram → "Open image in new tab" for better viewing
3. **VS Code**: Use Mermaid Preview extension with zoom support
4. **Local CLI**: Generate high-resolution images with `@mermaid-js/mermaid-cli`

#### **For Large/Complex Diagrams:**

- **Database ERD**: Best viewed in Mermaid Live with zoom
- **System Architecture**: Use full-screen mode for detailed view
- **Sequence Diagrams**: Pan horizontally to follow interactions

### Updating Diagrams

1. Modify the Mermaid code in the respective markdown files
2. Test rendering in Mermaid Live Editor
3. Update documentation if structural changes occur
4. Commit changes with descriptive messages

## 🏗️ System Overview

### Architecture Principles

- **Multi-Tenant**: Business-level data isolation
- **Service-Oriented**: Modular service architecture
- **Security-First**: Role-based access control
- **Scalable**: Efficient data models and queries

### Technology Stack

- **Backend**: Django 5.2+ with Python 3.12+
- **Database**: SQLite for development and production
- **Frontend**: Django Templates + Tailwind CSS + DatastarJS
- **Integrations**: Lemon Squeezy (payments), Email services

### Key Features

- Multi-tenant SaaS architecture
- Role-based user management
- Quote-to-payment workflow
- Calendar and event management
- Document generation and management
- Audit logging and compliance

## 📚 Related Documentation

### Project Documentation

- [Product Requirements Document](../PRD.md)
- [Installation Guide](../installation.md)
- [API Documentation](../api.md)
- [Configuration Guide](../configuration.md)

### Development Resources

- [Demo Setup](../DEMO_SETUP.md)
- [Registration System](../REGISTRATION_SYSTEM.md)
- [Customization Guide](../customization.md)

## 🤝 Contributing

### Adding New Diagrams

1. Create new markdown file in this directory
2. Follow existing naming conventions
3. Include diagram purpose and key features
4. Add entry to this README
5. Update related documentation

### Modifying Existing Diagrams

1. Update the Mermaid code
2. Test rendering and functionality
3. Update descriptions if needed
4. Commit with clear change description

### Best Practices

- Keep diagrams focused and readable
- Use consistent styling and colors
- Include comprehensive documentation
- Test diagrams before committing
- Update related documentation

---

_Last Updated: 2025-08-02_
_For questions or suggestions, please create an issue or contact the development team._
