"""
Data generation services for payments app using model_bakery.
"""

import random
import uuid
from datetime import timed<PERSON>ta
from decimal import Decimal

from djmoney.money import Money
from faker import Faker
from model_bakery import baker

from .models import Payment

fake = Faker()


class PaymentsDataGenerator:
    """Data generator for payments app models."""

    def __init__(self):
        self.created_objects = {"payments": []}

    def generate_payments(self, count=20, invoices=None):
        """Generate payments for invoices."""
        if not invoices:
            return []

        payments = []

        # Filter invoices that should have payments
        payable_invoices = [
            inv for inv in invoices if inv.status in ["sent", "paid", "overdue"]
        ]

        if not payable_invoices:
            return []

        # Payment method distribution
        payment_methods = [
            (Payment.PaymentMethod.LEMON_SQUEEZY, 0.4),
            (Payment.PaymentMethod.CREDIT_CARD, 0.3),
            (Payment.PaymentMethod.BANK_TRANSFER, 0.2),
            (Payment.PaymentMethod.CASH, 0.05),
            (Payment.PaymentMethod.CHECK, 0.03),
            (Payment.PaymentMethod.OTHER, 0.02),
        ]

        # Status distribution based on payment method
        status_distributions = {
            Payment.PaymentMethod.LEMON_SQUEEZY: [
                (Payment.Status.COMPLETED, 0.8),
                (Payment.Status.PENDING, 0.1),
                (Payment.Status.FAILED, 0.05),
                (Payment.Status.CANCELLED, 0.05),
            ],
            Payment.PaymentMethod.CREDIT_CARD: [
                (Payment.Status.COMPLETED, 0.85),
                (Payment.Status.PENDING, 0.08),
                (Payment.Status.FAILED, 0.04),
                (Payment.Status.CANCELLED, 0.03),
            ],
            Payment.PaymentMethod.BANK_TRANSFER: [
                (Payment.Status.COMPLETED, 0.9),
                (Payment.Status.PENDING, 0.08),
                (Payment.Status.FAILED, 0.02),
            ],
            Payment.PaymentMethod.CASH: [
                (Payment.Status.COMPLETED, 0.95),
                (Payment.Status.PENDING, 0.05),
            ],
            Payment.PaymentMethod.CHECK: [
                (Payment.Status.COMPLETED, 0.85),
                (Payment.Status.PENDING, 0.1),
                (Payment.Status.FAILED, 0.05),
            ],
            Payment.PaymentMethod.OTHER: [
                (Payment.Status.COMPLETED, 0.8),
                (Payment.Status.PENDING, 0.15),
                (Payment.Status.FAILED, 0.05),
            ],
        }

        for i in range(min(count, len(payable_invoices))):
            invoice = random.choice(payable_invoices)

            # Select payment method
            payment_method = random.choices(
                [pm for pm, _ in payment_methods],
                weights=[weight for _, weight in payment_methods],
            )[0]

            # Select status based on payment method
            method_statuses = status_distributions[payment_method]
            status = random.choices(
                [s for s, _ in method_statuses],
                weights=[weight for _, weight in method_statuses],
            )[0]

            # Determine payment amount
            if invoice.status == "paid":
                # Full payment
                amount = invoice.total_amount
            elif invoice.status == "overdue" and fake.boolean(
                chance_of_getting_true=40
            ):
                # Partial payment for overdue invoices
                amount = invoice.total_amount * Decimal(str(random.uniform(0.2, 0.8)))
            else:
                # Full or partial payment
                if fake.boolean(chance_of_getting_true=80):
                    amount = invoice.total_amount  # Full payment
                else:
                    amount = invoice.total_amount * Decimal(
                        str(random.uniform(0.3, 0.9))
                    )  # Partial

            # Generate payment dates
            payment_date = self._generate_payment_date(invoice, status)

            # Ensure amount is a Money object with correct currency
            if not isinstance(amount, Money):
                amount = Money(amount, invoice.business.currency or "USD")

            payment = baker.make(
                Payment,
                invoice=invoice,
                payment_id=uuid.uuid4(),
                amount=amount,
                payment_method=payment_method,
                status=status,
                # Dates
                payment_date=payment_date,
                # Payment method specific fields
                **self._generate_method_specific_fields(payment_method, status),
                # Additional information
                notes=self._generate_payment_notes(payment_method, status),
                created_by=invoice.created_by,
            )

            payments.append(payment)

        self.created_objects["payments"].extend(payments)
        return payments

    def _generate_payment_date(self, invoice, status):
        """Generate realistic payment date based on invoice and status."""
        if status == Payment.Status.COMPLETED:
            # Payment completed between invoice date and due date (or slightly after)
            start_date = invoice.issue_date
            end_date = invoice.due_date + timedelta(days=random.randint(0, 15))
            return fake.date_between(start_date=start_date, end_date=end_date)

        elif status == Payment.Status.PENDING:
            # Recent payment attempt
            return fake.date_between(start_date="-7d", end_date="today")

        elif status in [Payment.Status.FAILED, Payment.Status.CANCELLED]:
            # Failed/cancelled payment in the past
            return fake.date_between(start_date=invoice.issue_date, end_date="today")

        else:
            return fake.date_between(start_date=invoice.issue_date, end_date="today")

    def _generate_method_specific_fields(self, payment_method, status):
        """Generate payment method specific fields."""
        fields = {}

        if payment_method == Payment.PaymentMethod.LEMON_SQUEEZY:
            if status != Payment.Status.PENDING:
                fields["lemon_squeezy_order_id"] = (
                    f"ord_{fake.random_int(min=100000, max=999999)}"
                )
                fields["lemon_squeezy_checkout_id"] = (
                    f"chk_{fake.random_int(min=100000, max=999999)}"
                )
                # Note: lemon_squeezy_customer_id and lemon_squeezy_payment_id
                # don't exist in the Payment model, so we don't set them

        elif payment_method == Payment.PaymentMethod.CREDIT_CARD:
            if status == Payment.Status.COMPLETED:
                fields["transaction_id"] = fake.bothify(text="TXN-########")
                fields["authorization_code"] = fake.bothify(text="AUTH-######")

        elif payment_method == Payment.PaymentMethod.BANK_TRANSFER:
            if status == Payment.Status.COMPLETED:
                fields["reference_number"] = fake.bothify(text="REF-##########")

        elif payment_method == Payment.PaymentMethod.CHECK:
            fields["check_number"] = str(fake.random_int(min=1000, max=9999))

        return fields

    def _generate_payment_notes(self, payment_method, status):
        """Generate payment notes based on method and status."""
        notes_templates = {
            Payment.PaymentMethod.LEMON_SQUEEZY: {
                Payment.Status.COMPLETED: [
                    "Payment processed successfully via Lemon Squeezy",
                    "Online payment completed",
                    "Automatic payment confirmation received",
                ],
                Payment.Status.PENDING: [
                    "Payment processing via Lemon Squeezy",
                    "Awaiting payment confirmation",
                    "Payment initiated, pending completion",
                ],
                Payment.Status.FAILED: [
                    "Payment failed - insufficient funds",
                    "Credit card declined",
                    "Payment processing error",
                ],
                Payment.Status.CANCELLED: [
                    "Payment cancelled by customer",
                    "Transaction cancelled during processing",
                    "Customer cancelled payment",
                ],
            },
            Payment.PaymentMethod.CREDIT_CARD: {
                Payment.Status.COMPLETED: [
                    "Credit card payment processed successfully",
                    "Card payment authorized and captured",
                    "Payment completed via credit card",
                ],
                Payment.Status.PENDING: [
                    "Credit card authorization pending",
                    "Payment being processed",
                    "Awaiting bank authorization",
                ],
                Payment.Status.FAILED: [
                    "Credit card declined",
                    "Insufficient credit limit",
                    "Card payment failed",
                ],
            },
            Payment.PaymentMethod.BANK_TRANSFER: {
                Payment.Status.COMPLETED: [
                    "Bank transfer received and confirmed",
                    "Wire transfer completed successfully",
                    "Direct bank transfer processed",
                ],
                Payment.Status.PENDING: [
                    "Bank transfer initiated, awaiting confirmation",
                    "Wire transfer in progress",
                    "Awaiting bank transfer completion",
                ],
            },
            Payment.PaymentMethod.CASH: {
                Payment.Status.COMPLETED: [
                    "Cash payment received in full",
                    "Payment received in cash",
                    "Cash transaction completed",
                ]
            },
            Payment.PaymentMethod.CHECK: {
                Payment.Status.COMPLETED: [
                    "Check cleared successfully",
                    "Check payment processed",
                    "Bank check verified and deposited",
                ],
                Payment.Status.PENDING: [
                    "Check deposited, awaiting clearance",
                    "Check processing in progress",
                ],
                Payment.Status.FAILED: [
                    "Check bounced - insufficient funds",
                    "Check payment failed",
                ],
            },
            Payment.PaymentMethod.OTHER: {
                Payment.Status.COMPLETED: [
                    "Alternative payment method completed",
                    "Payment processed via other method",
                    "Custom payment arrangement fulfilled",
                ]
            },
        }

        method_notes = notes_templates.get(payment_method, {})
        status_notes = method_notes.get(status, ["Payment processed"])

        return random.choice(status_notes) if status_notes else "Payment processed"

    def generate_refunds(self, payments, count=3):
        """Generate refund payments for some completed payments."""
        completed_payments = [
            p for p in payments if p.status == Payment.Status.COMPLETED
        ]

        if not completed_payments or count == 0:
            return []

        refunds = []

        for i in range(min(count, len(completed_payments))):
            original_payment = random.choice(completed_payments)

            # Refund amount (partial or full)
            if fake.boolean(chance_of_getting_true=70):
                refund_amount = original_payment.amount  # Full refund
            else:
                refund_percentage = random.uniform(0.2, 0.8)
                refund_amount = original_payment.amount * Decimal(
                    str(refund_percentage)
                )

            refund = baker.make(
                Payment,
                invoice=original_payment.invoice,
                payment_id=uuid.uuid4(),
                amount=Money(
                    -abs(refund_amount.amount), refund_amount.currency
                ),  # Negative amount for refund
                payment_method=original_payment.payment_method,
                status=Payment.Status.REFUNDED,
                # Dates
                payment_date=fake.date_between(
                    start_date=original_payment.payment_date, end_date="today"
                ),
                # Notes
                notes=f"Refund for payment {original_payment.payment_id}. Reason: {random.choice(['Trip cancellation', 'Service not provided', 'Customer request', 'Billing error'])}",
                created_by=original_payment.created_by,
            )

            refunds.append(refund)

        self.created_objects["payments"].extend(refunds)
        return refunds

    def get_generation_summary(self):
        """Get summary of generated objects."""
        return {key: len(objects) for key, objects in self.created_objects.items()}
