"""
Example usage of Business Access Control Mixins

This file demonstrates how to use the business access control mixins
in different types of views to ensure proper data isolation.
"""

from django.views.generic import CreateView, DetailView, ListView, UpdateView

from core.business_mixins import (
    BusinessAccessMixin,
    BusinessCreateMixin,
    BusinessObjectMixin,
    BusinessOwnerRequiredMixin,
    MultiBusinessMixin,
)


# Example 1: Simple List View with Business Filtering
class ClientListView(BusinessAccessMixin, ListView):
    """
    List view that shows only clients from businesses the user has access to.

    This replaces manual business filtering with the mixin's built-in method.
    """

    model = Client
    template_name = 'clients/list.html'
    context_object_name = 'clients'
    paginate_by = 20

    def get_queryset(self):
        """Filter clients by accessible businesses."""
        queryset = super().get_queryset()
        return self.filter_by_accessible_businesses(queryset)


# Example 2: Detail View with Automatic Access Control
class InvoiceDetailView(BusinessObjectMixin, DetailView):
    """
    Detail view that automatically checks business access.

    If the user doesn't have access to the invoice's business,
    a 404 error is raised automatically.
    """

    model = Invoice
    template_name = 'invoices/detail.html'
    context_object_name = 'invoice'
    business_field = 'business'  # Field that links to business


# Example 3: Create View with Auto-Assignment
class QuoteCreateView(BusinessCreateMixin, CreateView):
    """
    Create view that automatically assigns the business.

    The business field is automatically set to the user's primary business.
    """

    model = Quote
    form_class = QuoteForm
    template_name = 'quotes/create.html'
    business_field = 'business'

    def get_success_url(self):
        return reverse('quotes:quote-detail', kwargs={'pk': self.object.pk})


# Example 4: Update View with Business Access Control
class BookingUpdateView(BusinessObjectMixin, UpdateView):
    """
    Update view that ensures user can only edit bookings from their business.

    Combines object-level access control with update functionality.
    """

    model = Booking
    form_class = BookingForm
    template_name = 'bookings/edit.html'
    business_field = 'business'

    def get_success_url(self):
        return reverse('bookings:detail', kwargs={'pk': self.object.pk})


# Example 5: Business Owner Only View
class UserManagementView(BusinessOwnerRequiredMixin, ListView):
    """
    View that only business owners can access.

    Agents and customers will get a 403 Forbidden error.
    """

    model = User
    template_name = 'accounts/user_management.html'
    context_object_name = 'users'

    def get_queryset(self):
        """Show only users from the current business."""
        business = self.get_user_business()
        return User.objects.filter(business=business)


# Example 6: Multi-Business Dashboard
class DashboardView(MultiBusinessMixin, TemplateView):
    """
    Dashboard that can switch between multiple businesses.

    Useful for users who have access to multiple businesses.
    """

    template_name = 'dashboard/multi_business.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        current_business = self.get_current_business()

        # Add business-specific data
        context.update(
            {
                'recent_quotes': Quote.objects.filter(business=current_business)[:5],
                'pending_invoices': Invoice.objects.filter(
                    business=current_business, status='pending'
                ).count(),
                'total_clients': Client.objects.filter(
                    business=current_business
                ).count(),
            }
        )
        return context


# Example 7: Combined Mixins for Complex Views
class AdvancedReportView(BusinessOwnerRequiredMixin, MultiBusinessMixin, TemplateView):
    """
    Advanced view combining multiple mixins.

    - Only business owners can access (BusinessOwnerRequiredMixin)
    - Can switch between businesses (MultiBusinessMixin)
    - Automatic business filtering (inherited from BusinessAccessMixin)
    """

    template_name = 'reports/advanced.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        current_business = self.get_current_business()

        # Generate business-specific reports
        context.update(
            {
                'revenue_data': self.get_revenue_data(current_business),
                'client_analytics': self.get_client_analytics(current_business),
                'booking_trends': self.get_booking_trends(current_business),
            }
        )
        return context

    def get_revenue_data(self, business):
        """Get revenue data for the business."""
        return Invoice.objects.filter(business=business, status='paid').aggregate(
            total=Sum('amount')
        )

    def get_client_analytics(self, business):
        """Get client analytics for the business."""
        return {
            'total_clients': Client.objects.filter(business=business).count(),
            'active_clients': Client.objects.filter(
                business=business, is_active=True
            ).count(),
        }

    def get_booking_trends(self, business):
        """Get booking trends for the business."""
        return (
            Booking.objects.filter(business=business)
            .values('created_at__month')
            .annotate(count=Count('id'))
        )


# Example 8: API View with Business Filtering
class ClientAPIView(BusinessAccessMixin, ListView):
    """
    API view that returns JSON data filtered by business access.

    Demonstrates how mixins work with API endpoints.
    """

    model = Client

    def get_queryset(self):
        queryset = super().get_queryset()
        return self.filter_by_accessible_businesses(queryset)

    def render_to_response(self, context, **response_kwargs):
        """Return JSON response instead of HTML."""
        from django.http import JsonResponse

        clients = list(
            context['object_list'].values('id', 'first_name', 'last_name', 'email')
        )
        return JsonResponse({'clients': clients})


# Example 9: Custom Business Field Name
class TourEventListView(BusinessAccessMixin, ListView):
    """
    Example with custom business field name.

    Some models might use different field names for business relationships.
    """

    model = TourEvent
    template_name = 'events/list.html'

    def get_queryset(self):
        queryset = super().get_queryset()
        # Use custom field name for business relationship
        return self.filter_by_accessible_businesses(queryset, business_field='company')


# Example 10: Error Handling
class SafeDetailView(BusinessObjectMixin, DetailView):
    """
    Example with custom error handling.

    Shows how to customize the behavior when access is denied.
    """

    model = Quote
    template_name = 'quotes/detail.html'

    def get_object(self, queryset=None):
        """Custom error handling for access denied."""
        try:
            return super().get_object(queryset)
        except Http404:
            # Log the access attempt
            logger.warning(
                f'User {self.request.user} attempted to access quote {self.kwargs["pk"]} '
                f'without permission'
            )
            # You could redirect to a custom error page here
            raise


# Migration Example: Before and After
"""
# BEFORE: Manual business filtering
class OldClientListView(LoginRequiredMixin, ListView):
    model = Client

    def get_queryset(self):
        queryset = super().get_queryset()
        if not self.request.user.is_superuser:
            accessible_businesses = Business.objects.filter(
                Q(created_by=self.request.user) | Q(users=self.request.user)
            ).distinct()
            queryset = queryset.filter(business__in=accessible_businesses)
        return queryset

# AFTER: Using BusinessAccessMixin
class NewClientListView(BusinessAccessMixin, ListView):
    model = Client

    def get_queryset(self):
        queryset = super().get_queryset()
        return self.filter_by_accessible_businesses(queryset)
"""
