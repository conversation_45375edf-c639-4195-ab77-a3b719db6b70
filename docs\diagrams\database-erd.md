# Tour Management SaaS - Database Entity Relationship Diagram

This diagram shows the complete database schema for the Tour Management SaaS application, including all entities and their relationships.

## Diagram

```mermaid
erDiagram
    %% Core Business Entity
    Business {
        id int PK
        name varchar(200)
        description text
        email varchar(254)
        phone varchar(20)
        website varchar(200)
        address_line1 varchar(255)
        address_line2 varchar(255)
        city varchar(100)
        state_province varchar(100)
        postal_code varchar(20)
        country varchar(100)
        timezone varchar(50)
        currency varchar(3)
        logo varchar(100)
        plan varchar(50)
        is_active boolean
        primary_color varchar(7)
        accent_color varchar(7)
        custom_css text
        created_at datetime
        updated_at datetime
        created_by int FK
    }

    %% User Management
    User {
        id uuid PK
        username varchar(150)
        email varchar(254)
        first_name varchar(150)
        last_name varchar(150)
        role varchar(20)
        business_id int FK
        email_confirmed boolean
        profile_completed boolean
        onboarding_completed boolean
        is_active boolean
        created_at datetime
        updated_at datetime
    }

    %% Client Management
    Client {
        id int PK
        business_id int FK
        client_type varchar(20)
        first_name varchar(100)
        last_name varchar(100)
        company_name varchar(200)
        email varchar(254)
        phone varchar(20)
        address_line1 varchar(255)
        address_line2 varchar(255)
        city varchar(100)
        state_province varchar(100)
        postal_code varchar(20)
        country varchar(100)
        lead_source varchar(100)
        notes text
        is_active boolean
        created_at datetime
        updated_at datetime
        created_by int FK
    }

    Traveler {
        id int PK
        client_id int FK
        first_name varchar(100)
        last_name varchar(100)
        date_of_birth date
        gender varchar(10)
        nationality varchar(100)
        passport_number varchar(50)
        passport_expiry date
        passport_country varchar(100)
        visa_required boolean
        visa_status varchar(20)
        emergency_contact_name varchar(200)
        emergency_contact_phone varchar(20)
        emergency_contact_relationship varchar(50)
        dietary_restrictions text
        medical_conditions text
        special_requirements text
        is_active boolean
        created_at datetime
        updated_at datetime
        created_by int FK
    }

    %% Quote System
    Quote {
        id int PK
        business_id int FK
        client_id int FK
        quote_number varchar(50)
        title varchar(200)
        description text
        status varchar(20)
        valid_until date
        public_hash varchar(64)
        subtotal decimal
        tax_amount decimal
        total_amount decimal
        terms_conditions text
        payment_terms text
        cancellation_policy text
        internal_notes text
        created_at datetime
        updated_at datetime
        created_by int FK
    }

    %% Calendar and Events
    Event {
        id int PK
        title varchar(255)
        description text
        start_time datetime
        end_time datetime
        all_day boolean
    }

    TourEvent {
        id int PK
        business_id int FK
        event_id int FK
        event_type varchar(20)
        client_id int FK
        quote_id int FK
        max_participants int
        current_participants int
        meeting_point varchar(255)
        special_instructions text
        price_per_person decimal
        is_confirmed boolean
        is_cancelled boolean
        created_at datetime
        updated_at datetime
        created_by int FK
    }

    %% Booking System
    Booking {
        id uuid PK
        business_id int FK
        client_id int FK
        tour_event_id int FK
        quote_id int FK
        booking_reference varchar(20)
        number_of_participants int
        status varchar(20)
        special_requests text
        total_amount decimal
        booking_date date
        confirmation_date datetime
        cancellation_date datetime
        cancellation_reason text
        internal_notes text
        created_at datetime
        updated_at datetime
        created_by int FK
    }

    BookingParticipant {
        id uuid PK
        booking_id uuid FK
        traveler_id int FK
        is_primary_contact boolean
        special_requirements text
        checked_in boolean
        check_in_time datetime
        no_show boolean
        created_at datetime
        updated_at datetime
    }

    %% Invoice and Payment System
    Invoice {
        id int PK
        business_id int FK
        client_id int FK
        quote_id int FK
        invoice_number varchar(50)
        status varchar(20)
        issue_date date
        due_date date
        subtotal decimal
        tax_amount decimal
        total_amount decimal
        terms_conditions text
        payment_terms text
        internal_notes text
        created_at datetime
        updated_at datetime
        created_by int FK
    }

    Payment {
        id int PK
        invoice_id int FK
        payment_id uuid
        amount decimal
        payment_method varchar(20)
        status varchar(20)
        lemon_squeezy_order_id varchar(255)
        lemon_squeezy_checkout_id varchar(255)
        external_payment_id varchar(255)
        payment_processor varchar(50)
        payment_date datetime
        reference_number varchar(100)
        notes text
        created_at datetime
        updated_at datetime
        created_by int FK
    }

    %% Relationships
    Business ||--o{ User : "employs"
    Business ||--o{ Client : "serves"
    Business ||--o{ Quote : "creates"
    Business ||--o{ TourEvent : "schedules"
    Business ||--o{ Booking : "manages"
    Business ||--o{ Invoice : "bills"

    User ||--o{ Business : "creates"
    User ||--o{ Client : "creates"
    User ||--o{ Quote : "creates"
    User ||--o{ TourEvent : "creates"
    User ||--o{ Booking : "creates"
    User ||--o{ Invoice : "creates"
    User ||--o{ Payment : "processes"

    Client ||--o{ Traveler : "includes"
    Client ||--o{ Quote : "receives"
    Client ||--o{ TourEvent : "books"
    Client ||--o{ Booking : "makes"
    Client ||--o{ Invoice : "pays"

    Quote ||--o| Invoice : "generates"
    Quote ||--o{ TourEvent : "schedules"
    Quote ||--o{ Booking : "creates"

    TourEvent ||--|| Event : "extends"
    TourEvent ||--o{ Booking : "hosts"

    Booking ||--o{ BookingParticipant : "includes"
    Traveler ||--o{ BookingParticipant : "participates"

    Invoice ||--o{ Payment : "receives"
```

## Key Features

### Multi-Tenant Architecture

- **Business Entity**: Central entity for multi-tenancy
- **Data Isolation**: All entities are scoped to a business
- **Role-Based Access**: Users have roles within businesses

### Core Entities

- **User**: Authentication and authorization
- **Client**: Customer management
- **Traveler**: Individual participants
- **Quote**: Pricing proposals
- **Booking**: Confirmed reservations
- **Invoice**: Billing documents
- **Payment**: Payment tracking

### Relationships

- One-to-many relationships between core entities
- Proper foreign key constraints
- Audit trails with created_by fields
- UUID primary keys for security

### Financial Management

- Multi-currency support with MoneyField
- Quote to Invoice workflow
- Payment tracking and reconciliation
- Lemon Squeezy integration
