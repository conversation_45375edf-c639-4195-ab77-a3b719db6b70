from django.contrib import admin
from django.utils.html import format_html
from django.utils.translation import gettext_lazy as _
from simple_history.admin import SimpleHistoryAdmin

from .models import Invoice


@admin.register(Invoice)
class InvoiceAdmin(SimpleHistoryAdmin):
    """Admin configuration for Invoice model."""

    list_display = (
        'invoice_number',
        'client',
        'business',
        'status',
        'total_amount',
        'issue_date',
        'due_date',
        'is_overdue',
        'created_at',
    )
    list_filter = (
        'status',
        'business',
        'issue_date',
        'due_date',
        'created_at',
        'sent_at',
        'paid_at',
    )
    search_fields = (
        'invoice_number',
        'client__first_name',
        'client__last_name',
        'client__company_name',
        'quote__quote_number',
        'quote__title',
    )
    readonly_fields = (
        'invoice_number',
        'is_overdue',
        'created_at',
        'updated_at',
        'sent_at',
        'paid_at',
    )

    fieldsets = (
        (
            _('Basic Information'),
            {
                'fields': (
                    'business',
                    'client',
                    'quote',
                    'invoice_number',
                )
            },
        ),
        (
            _('Financial Information'),
            {
                'fields': (
                    'subtotal',
                    'tax_amount',
                    'total_amount',
                )
            },
        ),
        (
            _('Status & Dates'),
            {
                'fields': (
                    'status',
                    'issue_date',
                    'due_date',
                    'is_overdue',
                )
            },
        ),
        (
            _('Terms & Notes'),
            {
                'fields': (
                    'terms_conditions',
                    'payment_terms',
                    'internal_notes',
                ),
                'classes': ('collapse',),
            },
        ),
        (
            _('Audit Information'),
            {
                'fields': (
                    'created_by',
                    'created_at',
                    'updated_at',
                    'sent_at',
                    'paid_at',
                ),
                'classes': ('collapse',),
            },
        ),
    )

    def get_queryset(self, request):
        """Optimize queryset with select_related."""
        return (
            super()
            .get_queryset(request)
            .select_related('business', 'client', 'quote', 'created_by')
        )

    def is_overdue(self, obj):
        """Display if invoice is overdue."""
        if obj.is_overdue:
            return format_html(
                '<span style="color: red; font-weight: bold;">Yes</span>'
            )
        return 'No'

    is_overdue.short_description = _('Overdue')

    actions = ['mark_as_sent', 'mark_as_paid']

    def mark_as_sent(self, request, queryset):
        """Mark selected invoices as sent."""
        from django.utils import timezone

        updated = queryset.filter(status=Invoice.Status.DRAFT).update(
            status=Invoice.Status.SENT, sent_at=timezone.now()
        )
        self.message_user(request, f'{updated} invoices were marked as sent.')

    mark_as_sent.short_description = _('Mark selected invoices as sent')

    def mark_as_paid(self, request, queryset):
        """Mark selected invoices as paid."""
        updated = 0
        for invoice in queryset.filter(
            status__in=[Invoice.Status.SENT, Invoice.Status.OVERDUE]
        ):
            invoice.mark_as_paid()
            updated += 1

        self.message_user(request, f'{updated} invoices were marked as paid.')

    mark_as_paid.short_description = _('Mark selected invoices as paid')
