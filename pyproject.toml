[project]
name = "tour"
version = "0.1.0"
description = "Tour Business Management SaaS - A comprehensive web application for tour businesses to manage bookings, quotes, invoices, and payments"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "django>=5.2.4",
    "python-decouple>=3.8",
    "django-tailwind>=3.8.0",
    "django-cotton>=1.0.0",
    "django-guardian>=2.4.0",
    "django-crispy-forms>=2.1",
    "django-extensions>=3.2.3",
    "pillow>=10.0.0",
    "reportlab>=4.0.0",
    "requests>=2.31.0",
    "psycopg2-binary>=2.9.7",
    "crispy-tailwind>=0.5.0",
    "django-money>=3.4.1",
    "django-simple-history>=3.4.0",
    "django-rosetta>=0.10.0",
    "django-debug-toolbar>=4.2.0",
    "requests>=2.31.0",
    "django-herald[html2text]>=0.3.0",
    "django-constance>=3.1.0",
    "django-cotton>=1.0.0",
    "model-bakery>=1.20.5",
    "django-silk[formatting]>=5.4.0",
    "django-phonenumber-field[phonenumberslite]>=8.1.0",
    "whitenoise[brotli]>=6.9.0",
    "django-cleanup>=9.0.0",
    "django-pictures>=1.5.1",
    "django-timezone-field>=7.1",
    "django-recurrence>=1.11.1",
    "django-fernet-encrypted-fields>=0.3.0",
    "django-model-utils>=5.0.0",
    "django-weasyprint>=2.4.0",
    "datastar-py>=0.6.4",
]

[dependency-groups]
dev = [
    "django-browser-reload>=1.18.0",
    "factory-boy>=3.3.3",
    "pre-commit>=4.3.0",
    "pytest>=7.4.0",
    "pytest-django>=4.5.0",
    "pytest-cov>=4.1.0",
    "factory-boy>=3.3.3",
    "werkzeug>=3.1.3",
]

[tool.ruff.format]
quote-style = "single"
