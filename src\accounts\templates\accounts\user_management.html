{% load i18n %}

<c-layouts.base>
  <c-slot name="title">
    {% trans "Manage Users" %} - Tour Business Management
  </c-slot>

  <c-slot name="content">
    <div class="container mx-auto px-4 py-8">
      <div class="max-w-6xl mx-auto">
        <!-- Header -->
        <div class="flex justify-between items-center mb-6">
          <div>
            <h1 class="text-3xl font-bold text-gray-900">{% trans "Manage Users" %}</h1>
            <p class="text-gray-600 mt-1">{% trans "Add and manage team members for" %} {{ business.name }}</p>
          </div>
          <div class="flex space-x-3">
            <a href="{% url 'accounts:user_activity' %}"
              class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-md text-sm font-medium">
              {% trans "Activity" %}
            </a>
            <a href="{% url 'accounts:add_user' %}"
              class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">
              {% trans "Add User" %}
            </a>
            <a href="{% url 'accounts:bulk_upload' %}"
              class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium">
              {% trans "Bulk Upload" %}
            </a>
          </div>
        </div>

        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z">
                    </path>
                  </svg>
                </div>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">{% trans "Total Users" %}</p>
                <p class="text-2xl font-semibold text-gray-900">{{ users.count }}</p>
              </div>
            </div>
          </div>

          <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                  <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                </div>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">{% trans "Active Users" %}</p>
                <p class="text-2xl font-semibold text-gray-900">{{ users|length }}</p>
              </div>
            </div>
          </div>

          <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                  <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                </div>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">{% trans "Pending Invites" %}</p>
                <p class="text-2xl font-semibold text-gray-900">0</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Users Table -->
        <div class="bg-white shadow rounded-lg overflow-hidden">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">{% trans "Team Members" %}</h3>
          </div>

          {% if users %}
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {% trans "User" %}
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {% trans "Role" %}
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {% trans "Status" %}
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {% trans "Joined" %}
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {% trans "Actions" %}
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                {% for user in users %}
                <tr class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="flex-shrink-0 h-10 w-10">
                        {% if user.profile.avatar %}
                        <img class="h-10 w-10 rounded-full object-cover" src="{{ user.profile.avatar.url }}" alt="">
                        {% else %}
                        <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                          <span class="text-sm font-medium text-gray-700">
                            {{ user.profile.first_name|first|default:user.username|first|upper }}
                          </span>
                        </div>
                        {% endif %}
                      </div>
                      <div class="ml-4">
                        <div class="text-sm font-medium text-gray-900">
                          {{ user.profile.get_full_name|default:user.username }}
                        </div>
                        <div class="text-sm text-gray-500">{{ user.email }}</div>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                                   {% if user.profile.role == 'business_owner' %}bg-purple-100 text-purple-800
                                                   {% elif user.profile.role == 'agent' %}bg-blue-100 text-blue-800
                                                   {% else %}bg-gray-100 text-gray-800{% endif %}">
                      {{ user.get_role_display }}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    {% if user.is_active %}
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                      {% trans "Active" %}
                    </span>
                    {% else %}
                    <span
                      class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                      {% trans "Pending" %}
                    </span>
                    {% endif %}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {{ user.date_joined|date:"M d, Y" }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div class="flex space-x-2">
                      <a href="{% url 'accounts:edit_user' user.pk %}"
                        class="text-blue-600 hover:text-blue-900">{% trans "Edit" %}</a>
                      {% if not user.is_active %}
                      <form method="post" action="{% url 'accounts:resend_invite' user.pk %}" class="inline">
                        {% csrf_token %}
                        <button type="submit"
                          class="text-green-600 hover:text-green-900 bg-transparent border-none cursor-pointer">{% trans "Resend Invite" %}</button>
                      </form>
                      {% endif %}
                      {% if user != request.user %}
                      <button type="button" onclick="confirmRemoveUser('{{ user.pk }}', '{{ user.username }}')"
                        class="text-red-600 hover:text-red-900 bg-transparent border-none cursor-pointer">{% trans "Remove" %}</button>
                      {% endif %}
                    </div>
                  </td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
          {% else %}
          <div class="text-center py-12">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">{% trans "No team members" %}</h3>
            <p class="mt-1 text-sm text-gray-500">{% trans "Get started by adding your first team member." %}</p>
            <div class="mt-6">
              <a href="{% url 'accounts:add_user' %}"
                class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                <svg class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                {% trans "Add User" %}
              </a>
            </div>
          </div>
          {% endif %}
        </div>
      </div>
    </div>

    <!-- Bulk Upload Modal (placeholder) -->
    <div id="bulkUploadModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
          <h3 class="text-lg font-medium text-gray-900 mb-4">{% trans "Bulk Upload Users" %}</h3>
          <p class="text-sm text-gray-600 mb-4">
            {% trans "Upload an Excel file with user information to add multiple users at once." %}</p>

          <form method="post" enctype="multipart/form-data">
            {% csrf_token %}
            {{ bulk_upload_form.excel_file }}

            <div class="mt-4 flex justify-end space-x-3">
              <button type="button" onclick="hideBulkUpload()"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 px-4 py-2 rounded-md text-sm">
                {% trans "Cancel" %}
              </button>
              <button type="submit" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm">
                {% trans "Upload" %}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <script>
      function showBulkUpload() {
        document.getElementById('bulkUploadModal').classList.remove('hidden');
      }

      function hideBulkUpload() {
        document.getElementById('bulkUploadModal').classList.add('hidden');
      }

      // Remove user functionality
      let userToRemove = null;

      function confirmRemoveUser(userId, username) {
        userToRemove = userId;
        document.getElementById('removeUserName').textContent = username;
        document.getElementById('removeUserModal').classList.remove('hidden');
      }

      function closeRemoveModal() {
        document.getElementById('removeUserModal').classList.add('hidden');
        userToRemove = null;
      }

      function removeUser() {
        if (userToRemove) {
          const form = document.createElement('form');
          form.method = 'POST';
          form.action = `/accounts/users/${userToRemove}/remove/`;

          const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
          const csrfInput = document.createElement('input');
          csrfInput.type = 'hidden';
          csrfInput.name = 'csrfmiddlewaretoken';
          csrfInput.value = csrfToken;
          form.appendChild(csrfInput);

          document.body.appendChild(form);
          form.submit();
        }
      }

      // Close modal when clicking outside
      document.getElementById('removeUserModal').addEventListener('click', function (e) {
        if (e.target === this) {
          closeRemoveModal();
        }
      });
    </script>

    <!-- Remove User Confirmation Modal -->
    <div id="removeUserModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
      <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
          <div class="p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <svg class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-lg font-medium text-gray-900">{% trans "Remove User" %}</h3>
                <div class="mt-2">
                  <p class="text-sm text-gray-500">
                    {% trans "Are you sure you want to remove" %} <span id="removeUserName" class="font-medium"></span>?
                    {% trans "This action cannot be undone." %}
                  </p>
                </div>
              </div>
            </div>
          </div>
          <div class="bg-gray-50 px-6 py-3 flex justify-end space-x-3">
            <button type="button" onclick="closeRemoveModal()"
              class="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-50">
              {% trans "Cancel" %}
            </button>
            <button type="button" onclick="removeUser()"
              class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium">
              {% trans "Remove" %}
            </button>
          </div>
        </div>
      </div>
    </div>
  </c-slot>
</c-layouts.base>
