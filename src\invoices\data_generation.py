"""
Data generation services for invoices app using model_bakery.
"""

import random
from decimal import Decimal
from datetime import timed<PERSON>ta
from django.utils import timezone
from model_bakery import baker
from faker import Faker

from .models import Invoice

fake = Faker()


class InvoicesDataGenerator:
    """Data generator for invoices app models."""

    def __init__(self):
        self.created_objects = {
            'invoices': []
        }

    def generate_invoices(self, count=15, quotes=None):
        """Generate invoices from approved quotes."""
        if not quotes:
            return []

        # Only create invoices from approved quotes
        approved_quotes = [q for q in quotes if q.status == 'approved']

        if not approved_quotes:
            return []

        invoices = []

        # Status distribution: 30% draft, 40% sent, 25% paid, 5% overdue
        status_weights = [
            (Invoice.Status.DRAFT, 0.3),
            (Invoice.Status.SENT, 0.4),
            (Invoice.Status.PAID, 0.25),
            (Invoice.Status.OVERDUE, 0.05)
        ]

        # Generate invoices from approved quotes
        for i, quote in enumerate(approved_quotes[:count]):
            # Generate invoice number
            invoice_number = f'INV-{fake.random_int(min=1000, max=9999)}-{fake.random_int(min=10, max=99)}'

            # Select status
            status = random.choices(
                [s for s, _ in status_weights],
                weights=[weight for _, weight in status_weights]
            )[0]

            # Generate due date based on payment terms
            if 'due 30 days' in quote.payment_terms.lower():
                due_days = 30
            elif 'due 45 days' in quote.payment_terms.lower():
                due_days = 45
            elif 'due 21 days' in quote.payment_terms.lower():
                due_days = 21
            elif 'due 60 days' in quote.payment_terms.lower():
                due_days = 60
            else:
                due_days = 30  # Default

            # Adjust due date based on status
            if status == Invoice.Status.OVERDUE:
                # Make it overdue by 1-30 days
                due_date = fake.past_date(start_date='-30d')
            else:
                due_date = fake.future_date(end_date=f'+{due_days}d')

            # Generate payment dates for paid invoices
            paid_at = None
            if status == Invoice.Status.PAID:
                # Payment date should be before due date - make it timezone aware
                paid_date = fake.date_between(start_date='-60d', end_date=due_date)
                paid_at = timezone.make_aware(
                    timezone.datetime.combine(paid_date, timezone.datetime.min.time())
                )

            invoice = baker.make(
                Invoice,
                business=quote.business,
                client=quote.client,
                quote=quote,
                invoice_number=invoice_number,

                # Copy financial information from quote
                subtotal=quote.subtotal,
                tax_amount=quote.tax_amount,
                total_amount=quote.total_amount,

                # Dates
                issue_date=fake.past_date(start_date='-90d'),
                due_date=due_date,
                paid_at=paid_at,

                # Status
                status=status,

                # Terms and conditions from quote
                terms_conditions=quote.terms_conditions,
                payment_terms=quote.payment_terms,

                # Additional information
                internal_notes=self._generate_invoice_notes(status),

                created_by=quote.created_by,
            )

            invoices.append(invoice)

        # Note: All invoices must be associated with quotes in this system
        # If we need more invoices, we would need more approved quotes

        self.created_objects['invoices'].extend(invoices)
        return invoices



    def _generate_invoice_notes(self, status):
        """Generate status-appropriate invoice notes."""
        notes_by_status = {
            Invoice.Status.DRAFT: [
                'Draft invoice - pending review',
                'Awaiting final approval before sending',
                'Draft created - requires client confirmation'
            ],
            Invoice.Status.SENT: [
                'Invoice sent to client via email',
                'Awaiting payment from client',
                'Payment reminder sent',
                'Client confirmed receipt of invoice'
            ],
            Invoice.Status.PAID: [
                'Payment received in full - thank you!',
                'Paid via bank transfer',
                'Payment processed successfully',
                'Paid via credit card'
            ],
            Invoice.Status.OVERDUE: [
                'Payment overdue - follow up required',
                'Second payment reminder sent',
                'Client contacted regarding overdue payment',
                'Payment plan discussed with client'
            ],
            Invoice.Status.CANCELLED: [
                'Invoice cancelled at client request',
                'Cancelled due to trip cancellation',
                'Replaced by revised invoice'
            ]
        }

        status_notes = notes_by_status.get(status, [''])
        return random.choice(status_notes) if status_notes else ''

    def _generate_service_terms(self):
        """Generate terms for service invoices."""
        return """1. Payment Terms: Payment is due within 30 days of invoice date.
2. Late Fees: A 1.5% monthly service charge may be applied to overdue accounts.
3. Services: All services have been provided as agreed.
4. Disputes: Any disputes must be raised within 10 days of invoice date.
5. Currency: All amounts are in USD unless otherwise specified."""

    def generate_payment_history(self, invoices):
        """Generate realistic payment history for invoices."""
        for invoice in invoices:
            if invoice.status == Invoice.Status.PAID and not invoice.paid_at:
                # Set a realistic payment date - make it timezone aware
                paid_date = fake.date_between(
                    start_date=invoice.issue_date,
                    end_date=invoice.due_date
                )
                invoice.paid_at = timezone.make_aware(
                    timezone.datetime.combine(paid_date, timezone.datetime.min.time())
                )
                invoice.save()

    def get_generation_summary(self):
        """Get summary of generated objects."""
        return {
            key: len(objects) for key, objects in self.created_objects.items()
        }
