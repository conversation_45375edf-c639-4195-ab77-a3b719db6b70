# Generated by Django 5.2.4 on 2025-08-12 11:35

import django.core.validators
import django.db.models.deletion
import djmoney.models.fields
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('bookings', '0001_initial'),
        ('businesses', '0001_initial'),
        ('invoices', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='BookingPayment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount_currency', djmoney.models.fields.CurrencyField(choices=[('AUD', 'Australian Dollar'), ('BRL', 'Brazilian Real'), ('GBP', 'British Pound'), ('CAD', 'Canadian Dollar'), ('CNY', 'Chinese Yuan'), ('EUR', 'Euro'), ('INR', 'Indian Rupee'), ('JPY', 'Japanese Yen'), ('MXN', 'Mexican Peso'), ('CHF', 'Swiss Franc'), ('TZS', 'Tanzanian Shilling'), ('USD', 'US Dollar')], default='TZS', editable=False, max_length=3)),
                ('amount', djmoney.models.fields.MoneyField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(0.01)])),
                ('payment_date', models.DateTimeField(auto_now_add=True)),
                ('payment_method', models.CharField(choices=[('lemon_squeezy', 'Lemon Squeezy'), ('credit_card', 'Credit Card'), ('bank_transfer', 'Bank Transfer'), ('cash', 'Cash'), ('check', 'Check'), ('other', 'Other')], max_length=50)),
                ('transaction_id', models.CharField(max_length=255, unique=True)),
                ('is_deposit', models.BooleanField(default=False)),
                ('booking', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payments', to='bookings.booking')),
            ],
        ),
        migrations.CreateModel(
            name='Payment',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, help_text='Unique identifier for this record', primary_key=True, serialize=False, unique=True)),
                ('payment_id', models.UUIDField(default=uuid.uuid4, help_text='Unique payment identifier', unique=True)),
                ('amount_currency', djmoney.models.fields.CurrencyField(choices=[('AUD', 'Australian Dollar'), ('BRL', 'Brazilian Real'), ('GBP', 'British Pound'), ('CAD', 'Canadian Dollar'), ('CNY', 'Chinese Yuan'), ('EUR', 'Euro'), ('INR', 'Indian Rupee'), ('JPY', 'Japanese Yen'), ('MXN', 'Mexican Peso'), ('CHF', 'Swiss Franc'), ('TZS', 'Tanzanian Shilling'), ('USD', 'US Dollar')], default='USD', editable=False, max_length=3)),
                ('amount', djmoney.models.fields.MoneyField(decimal_places=2, default_currency='USD', help_text='Payment amount', max_digits=14)),
                ('payment_method', models.CharField(choices=[('lemon_squeezy', 'Lemon Squeezy'), ('credit_card', 'Credit Card'), ('bank_transfer', 'Bank Transfer'), ('cash', 'Cash'), ('check', 'Check'), ('other', 'Other')], default='lemon_squeezy', help_text='Payment method used', max_length=20)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('processing', 'Processing'), ('completed', 'Completed'), ('failed', 'Failed'), ('cancelled', 'Cancelled'), ('refunded', 'Refunded')], default='pending', help_text='Current payment status', max_length=20)),
                ('lemon_squeezy_order_id', models.CharField(blank=True, max_length=255, verbose_name='Lemon Squeezy order ID')),
                ('lemon_squeezy_checkout_id', models.CharField(blank=True, max_length=255, verbose_name='Lemon Squeezy checkout ID')),
                ('external_payment_id', models.CharField(blank=True, help_text='External payment processor ID', max_length=255)),
                ('payment_processor', models.CharField(default='lemon_squeezy', help_text='Payment processor used', max_length=50)),
                ('payment_date', models.DateTimeField(blank=True, help_text='When the payment was completed', null=True)),
                ('reference_number', models.CharField(blank=True, help_text='Payment reference number', max_length=100)),
                ('notes', models.TextField(blank=True, help_text='Additional payment notes')),
                ('created_by', models.ForeignKey(help_text='User who created this record', on_delete=django.db.models.deletion.PROTECT, related_name='%(class)ss_created', to=settings.AUTH_USER_MODEL)),
                ('invoice', models.ForeignKey(help_text='Invoice this payment is for', on_delete=django.db.models.deletion.CASCADE, related_name='payments', to='invoices.invoice')),
            ],
            options={
                'verbose_name': 'Payment',
                'verbose_name_plural': 'Payments',
                'db_table': 'payments_payment',
            },
        ),
        migrations.CreateModel(
            name='PaymentLink',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('link_id', models.UUIDField(default=uuid.uuid4, help_text='Unique payment link identifier', unique=True)),
                ('status', models.CharField(choices=[('active', 'Active'), ('expired', 'Expired'), ('used', 'Used'), ('cancelled', 'Cancelled')], default='active', help_text='Current link status', max_length=20)),
                ('expires_at', models.DateTimeField(help_text='When this payment link expires')),
                ('lemon_squeezy_checkout_url', models.URLField(blank=True, help_text='Lemon Squeezy checkout URL')),
                ('lemon_squeezy_checkout_id', models.CharField(blank=True, help_text='Lemon Squeezy checkout ID', max_length=255)),
                ('access_count', models.PositiveIntegerField(default=0, help_text='Number of times this link has been accessed')),
                ('last_accessed_at', models.DateTimeField(blank=True, help_text='When this link was last accessed', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(help_text='User who created this payment link', on_delete=django.db.models.deletion.PROTECT, related_name='created_payment_links', to=settings.AUTH_USER_MODEL)),
                ('invoice', models.OneToOneField(help_text='Invoice this payment link is for', on_delete=django.db.models.deletion.CASCADE, related_name='payment_link', to='invoices.invoice')),
            ],
            options={
                'verbose_name': 'Payment Link',
                'verbose_name_plural': 'Payment Links',
                'db_table': 'payments_payment_link',
            },
        ),
        migrations.CreateModel(
            name='Refund',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount_currency', djmoney.models.fields.CurrencyField(choices=[('AUD', 'Australian Dollar'), ('BRL', 'Brazilian Real'), ('GBP', 'British Pound'), ('CAD', 'Canadian Dollar'), ('CNY', 'Chinese Yuan'), ('EUR', 'Euro'), ('INR', 'Indian Rupee'), ('JPY', 'Japanese Yen'), ('MXN', 'Mexican Peso'), ('CHF', 'Swiss Franc'), ('TZS', 'Tanzanian Shilling'), ('USD', 'US Dollar')], default='TZS', editable=False, max_length=3)),
                ('amount', djmoney.models.fields.MoneyField(decimal_places=2, max_digits=10)),
                ('payment', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='payments.payment')),
            ],
        ),
        migrations.CreateModel(
            name='Subscription',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, help_text='Unique identifier for this record', primary_key=True, serialize=False, unique=True)),
                ('subscription_id', models.UUIDField(default=uuid.uuid4, help_text='Unique subscription identifier', unique=True)),
                ('plan', models.CharField(choices=[('basic', 'Basic Plan'), ('professional', 'Professional Plan'), ('enterprise', 'Enterprise Plan')], default='basic', help_text='Subscription plan', max_length=20)),
                ('status', models.CharField(choices=[('active', 'Active'), ('cancelled', 'Cancelled'), ('expired', 'Expired'), ('past_due', 'Past Due'), ('paused', 'Paused'), ('unpaid', 'Unpaid')], default='active', help_text='Current subscription status', max_length=20)),
                ('lemon_squeezy_subscription_id', models.CharField(blank=True, help_text='Lemon Squeezy subscription ID', max_length=255)),
                ('lemon_squeezy_customer_id', models.CharField(blank=True, help_text='Lemon Squeezy customer ID', max_length=255)),
                ('current_period_start', models.DateTimeField(blank=True, help_text='Start of current billing period', null=True)),
                ('current_period_end', models.DateTimeField(blank=True, help_text='End of current billing period', null=True)),
                ('trial_end', models.DateTimeField(blank=True, help_text='End of trial period', null=True)),
                ('monthly_price_currency', djmoney.models.fields.CurrencyField(choices=[('AUD', 'Australian Dollar'), ('BRL', 'Brazilian Real'), ('GBP', 'British Pound'), ('CAD', 'Canadian Dollar'), ('CNY', 'Chinese Yuan'), ('EUR', 'Euro'), ('INR', 'Indian Rupee'), ('JPY', 'Japanese Yen'), ('MXN', 'Mexican Peso'), ('CHF', 'Swiss Franc'), ('TZS', 'Tanzanian Shilling'), ('USD', 'US Dollar')], default='USD', editable=False, max_length=3, null=True)),
                ('monthly_price', djmoney.models.fields.MoneyField(blank=True, decimal_places=2, default_currency='USD', help_text='Monthly subscription price', max_digits=10, null=True)),
                ('business', models.OneToOneField(help_text='Business this subscription belongs to', on_delete=django.db.models.deletion.CASCADE, related_name='subscription', to='businesses.business')),
            ],
            options={
                'verbose_name': 'Subscription',
                'verbose_name_plural': 'Subscriptions',
                'db_table': 'payments_subscription',
            },
        ),
        migrations.AddIndex(
            model_name='payment',
            index=models.Index(fields=['invoice', 'status'], name='payments_pa_invoice_e2429a_idx'),
        ),
        migrations.AddIndex(
            model_name='payment',
            index=models.Index(fields=['payment_method'], name='payments_pa_payment_5c92d7_idx'),
        ),
        migrations.AddIndex(
            model_name='payment',
            index=models.Index(fields=['status'], name='payments_pa_status_7ad4af_idx'),
        ),
        migrations.AddIndex(
            model_name='payment',
            index=models.Index(fields=['lemon_squeezy_order_id'], name='payments_pa_lemon_s_8cd156_idx'),
        ),
        migrations.AddIndex(
            model_name='payment',
            index=models.Index(fields=['payment_date'], name='payments_pa_payment_1d6e55_idx'),
        ),
        migrations.AddIndex(
            model_name='paymentlink',
            index=models.Index(fields=['status'], name='payments_pa_status_9eaf9c_idx'),
        ),
        migrations.AddIndex(
            model_name='paymentlink',
            index=models.Index(fields=['expires_at'], name='payments_pa_expires_4445bf_idx'),
        ),
        migrations.AddIndex(
            model_name='paymentlink',
            index=models.Index(fields=['lemon_squeezy_checkout_id'], name='payments_pa_lemon_s_a18be7_idx'),
        ),
        migrations.AddIndex(
            model_name='paymentlink',
            index=models.Index(fields=['created_at'], name='payments_pa_created_b74b15_idx'),
        ),
        migrations.AddIndex(
            model_name='subscription',
            index=models.Index(fields=['status'], name='payments_su_status_9a9040_idx'),
        ),
        migrations.AddIndex(
            model_name='subscription',
            index=models.Index(fields=['plan'], name='payments_su_plan_23b2f6_idx'),
        ),
        migrations.AddIndex(
            model_name='subscription',
            index=models.Index(fields=['lemon_squeezy_subscription_id'], name='payments_su_lemon_s_1771c6_idx'),
        ),
        migrations.AddIndex(
            model_name='subscription',
            index=models.Index(fields=['current_period_end'], name='payments_su_current_d26b0c_idx'),
        ),
        migrations.AddIndex(
            model_name='subscription',
            index=models.Index(fields=['created_at'], name='payments_su_created_0c3a00_idx'),
        ),
    ]
