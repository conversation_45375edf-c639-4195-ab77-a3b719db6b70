"""
Factory classes for invoices app models using factory_boy and faker.
"""

import factory
from factory import fuzzy
from datetime import timedelta
from invoices.models import Invoice
from businesses.factories import BusinessFactory
from clients.factories import ClientFactory
from quotes.factories import QuoteFactory


class InvoiceFactory(factory.django.DjangoModelFactory):
    """Factory for creating Invoice instances."""
    
    class Meta:
        model = Invoice
    
    business = factory.SubFactory(BusinessFactory)
    client = factory.SubFactory(ClientFactory, business=factory.SelfAttribute('..business'))
    quote = factory.SubFactory(QuoteFactory, 
                              business=factory.SelfAttribute('..business'),
                              client=factory.SelfAttribute('..client'))
    subtotal = factory.LazyAttribute(lambda obj: obj.quote.subtotal if obj.quote else 1000)
    tax_amount = factory.LazyAttribute(lambda obj: obj.quote.tax_amount if obj.quote else 100)
    total_amount = factory.LazyAttribute(lambda obj: obj.quote.total_amount if obj.quote else 1100)
    issue_date = factory.Faker('date_between', start_date='-30d', end_date='today')
    due_date = factory.LazyAttribute(lambda obj: obj.issue_date + timedelta(days=30))
    status = fuzzy.FuzzyChoice([
        Invoice.Status.DRAFT,
        Invoice.Status.SENT,
        Invoice.Status.PAID,
        Invoice.Status.OVERDUE
    ])
    created_by = factory.LazyAttribute(lambda obj: obj.business.created_by)
