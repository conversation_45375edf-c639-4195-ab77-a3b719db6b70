"""
Data generation services for quotes app using model_bakery.
"""

import random
import hashlib
from decimal import Decimal
from datetime import timed<PERSON><PERSON>
from django.utils import timezone
from model_bakery import baker
from faker import Faker

from .models import Quote

fake = Faker()


class QuotesDataGenerator:
    """Data generator for quotes app models."""

    def __init__(self):
        self.created_objects = {
            'quotes': []
        }

    def generate_quotes(self, count=25, businesses=None, clients=None):
        """Generate quotes for businesses and clients."""
        if not businesses or not clients:
            return []

        quotes = []

        # Quote title templates by business theme
        quote_templates = {
            'safari': [
                'Big Five Safari Experience', 'Serengeti Migration Tour', 'Masai Mara Adventure',
                'Ngorongoro Crater Safari', 'Kilimanjaro Base Camp Trek', 'Amboseli Elephant Safari',
                'Lake Nakuru Flamingo Tour', 'Tsavo Wildlife Experience', 'Cultural Village Safari'
            ],
            'cultural': [
                'Ancient Civilizations Tour', 'Heritage Sites Experience', 'Traditional Arts Workshop',
                'Local Community Immersion', 'Historical Landmarks Tour', 'Cultural Festival Experience',
                'Artisan Village Visit', 'Sacred Sites Pilgrimage', 'Traditional Cooking Class'
            ],
            'adventure': [
                'Mountain Climbing Expedition', 'White Water Rafting Adventure', 'Rock Climbing Course',
                'Wilderness Survival Training', 'Multi-Day Trekking', 'Extreme Sports Package',
                'Mountaineering Base Camp', 'Adventure Photography Tour', 'Outdoor Skills Workshop'
            ],
            'eco': [
                'Conservation Experience', 'Sustainable Tourism Package', 'Wildlife Research Tour',
                'Eco-Lodge Experience', 'Carbon Neutral Adventure', 'Marine Conservation Tour',
                'Rainforest Canopy Walk', 'Organic Farm Visit', 'Renewable Energy Tour'
            ],
            'luxury': [
                'VIP Safari Experience', 'Private Jet Tour', 'Luxury Lodge Package',
                'Exclusive Access Tour', 'Personal Butler Service', 'Helicopter Safari',
                'Five-Star Glamping', 'Private Island Experience', 'Yacht Charter Adventure'
            ]
        }

        # Status distribution: 40% draft, 30% sent, 20% approved, 10% rejected
        status_weights = [
            (Quote.Status.DRAFT, 0.4),
            (Quote.Status.SENT, 0.3),
            (Quote.Status.APPROVED, 0.2),
            (Quote.Status.REJECTED, 0.1)
        ]

        for i in range(count):
            business = random.choice(businesses)

            # Filter clients by business
            business_clients = [c for c in clients if c.business == business]
            if not business_clients:
                continue

            client = random.choice(business_clients)

            # Determine business theme from name
            business_theme = self._get_business_theme(business.name)
            templates = quote_templates.get(business_theme, quote_templates['safari'])

            # Generate quote number
            quote_number = f'Q-{fake.random_int(min=1000, max=9999)}-{fake.random_int(min=10, max=99)}'

            # Generate pricing based on business theme
            pricing = self._generate_pricing(business_theme, client.client_type)

            # Select status
            status = random.choices(
                [s for s, _ in status_weights],
                weights=[weight for _, weight in status_weights]
            )[0]

            # Generate public hash
            public_hash = hashlib.sha256(f'{quote_number}{client.email}{fake.random_int(min=1000, max=9999)}'.encode()).hexdigest()

            quote = baker.make(
                Quote,
                business=business,
                client=client,
                quote_number=quote_number,
                title=random.choice(templates),
                description=self._generate_quote_description(business_theme),
                status=status,
                valid_until=fake.future_date(end_date='+90d'),
                public_hash=public_hash,

                # Pricing
                subtotal=pricing['subtotal'],
                tax_amount=pricing['tax_amount'],
                total_amount=pricing['total_amount'],

                # Terms and conditions
                terms_conditions=self._generate_terms_conditions(),
                payment_terms=self._generate_payment_terms(),

                # Additional information
                internal_notes=fake.text(max_nb_chars=200) if fake.boolean(chance_of_getting_true=40) else '',

                created_by=business.created_by,
            )

            quotes.append(quote)

        self.created_objects['quotes'].extend(quotes)
        return quotes

    def _get_business_theme(self, business_name):
        """Determine business theme from name."""
        name_lower = business_name.lower()

        if any(word in name_lower for word in ['safari', 'wildlife', 'serengeti', 'masai', 'big five']):
            return 'safari'
        elif any(word in name_lower for word in ['cultural', 'heritage', 'traditional', 'ancient']):
            return 'cultural'
        elif any(word in name_lower for word in ['adventure', 'mountain', 'extreme', 'peak']):
            return 'adventure'
        elif any(word in name_lower for word in ['eco', 'green', 'sustainable', 'conservation']):
            return 'eco'
        elif any(word in name_lower for word in ['luxury', 'platinum', 'elite', 'premium']):
            return 'luxury'
        else:
            return 'safari'  # Default

    def _generate_pricing(self, theme, client_type):
        """Generate realistic pricing based on theme and client type."""
        # Base pricing by theme (per person per day)
        base_prices = {
            'safari': (200, 800),
            'cultural': (100, 400),
            'adventure': (150, 600),
            'eco': (120, 500),
            'luxury': (500, 2000)
        }

        min_price, max_price = base_prices.get(theme, (200, 800))

        # Adjust for client type
        if client_type == 'corporate':
            min_price = int(min_price * 1.2)  # 20% markup for corporate
            max_price = int(max_price * 1.2)
        elif client_type == 'travel_agent':
            min_price = int(min_price * 0.9)  # 10% discount for agents
            max_price = int(max_price * 0.9)

        # Generate random duration (1-14 days)
        duration = fake.random_int(min=1, max=14)

        # Generate random group size (1-12 people)
        group_size = fake.random_int(min=1, max=12)

        # Calculate base amount
        per_person_per_day = fake.random_int(min=min_price, max=max_price)
        subtotal = Decimal(str(per_person_per_day * duration * group_size))

        # Tax rate (varies by region)
        tax_rate = Decimal(random.choice(['0.00', '0.05', '0.08', '0.10', '0.15', '0.18']))
        tax_amount = subtotal * tax_rate
        total_amount = subtotal + tax_amount

        return {
            'subtotal': subtotal,
            'tax_amount': tax_amount,
            'total_amount': total_amount
        }

    def _generate_quote_description(self, theme):
        """Generate theme-appropriate quote description."""
        descriptions = {
            'safari': [
                'Experience the ultimate African safari adventure with game drives, luxury accommodations, and expert guides. Witness the Big Five in their natural habitat and create memories that will last a lifetime.',
                'Join us for an unforgettable wildlife experience in East Africa. Our carefully crafted itinerary includes the best national parks, comfortable lodges, and professional guides.',
                'Discover the wonders of African wildlife on this comprehensive safari package. Includes all meals, accommodations, park fees, and transportation.'
            ],
            'cultural': [
                'Immerse yourself in local traditions and customs with our authentic cultural experience. Meet local communities, participate in traditional activities, and learn about ancient customs.',
                'Explore historical sites and cultural landmarks with expert local guides. This package includes visits to museums, archaeological sites, and traditional villages.',
                'Experience the rich cultural heritage through hands-on workshops, traditional performances, and interactions with local artisans and community leaders.'
            ],
            'adventure': [
                'Challenge yourself with this thrilling adventure package designed for outdoor enthusiasts. Includes professional instruction, safety equipment, and experienced guides.',
                'Push your limits with our multi-day adventure experience. Perfect for those seeking adrenaline-pumping activities in stunning natural settings.',
                'Embark on an epic outdoor adventure with activities ranging from hiking and climbing to water sports and wilderness survival training.'
            ],
            'eco': [
                'Join our sustainable tourism initiative that supports local conservation efforts while providing an educational and inspiring travel experience.',
                'Experience nature responsibly with our eco-friendly tour package. Learn about conservation efforts and contribute to environmental protection.',
                'Discover pristine natural environments while supporting local communities and conservation projects. Carbon-neutral travel options available.'
            ],
            'luxury': [
                'Indulge in the ultimate luxury travel experience with personalized service, exclusive access, and premium accommodations throughout your journey.',
                'Experience unparalleled luxury with private guides, five-star accommodations, and exclusive access to premium destinations and experiences.',
                'Enjoy the finest in luxury travel with bespoke itineraries, personal concierge service, and access to exclusive venues and experiences.'
            ]
        }

        return random.choice(descriptions.get(theme, descriptions['safari']))

    def _generate_terms_conditions(self):
        """Generate standard terms and conditions."""
        return """1. Booking Confirmation: A 30% deposit is required to confirm your booking.
2. Payment Terms: Full payment is due 30 days before departure.
3. Cancellation Policy: Cancellations made 60+ days before departure: 10% penalty. 30-59 days: 25% penalty. Less than 30 days: 50% penalty.
4. Travel Insurance: Comprehensive travel insurance is strongly recommended.
5. Health Requirements: Guests are responsible for meeting all health and vaccination requirements.
6. Weather Conditions: Itineraries may be modified due to weather or other circumstances beyond our control.
7. Liability: The company's liability is limited as per our standard terms of service."""

    def _generate_payment_terms(self):
        """Generate payment terms."""
        terms = [
            "30% deposit required to confirm booking, balance due 30 days before departure",
            "50% deposit required, balance due 45 days before departure",
            "Full payment required 60 days before departure",
            "25% deposit required, balance due 21 days before departure"
        ]
        return random.choice(terms)

    def get_generation_summary(self):
        """Get summary of generated objects."""
        return {
            key: len(objects) for key, objects in self.created_objects.items()
        }
