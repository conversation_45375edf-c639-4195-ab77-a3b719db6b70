"""
Unit tests for accounts models.
"""

from django.contrib.auth import get_user_model
from django.test import TestCase

from accounts.models import Profile
from businesses.models import Business

User = get_user_model()


class UserModelTest(TestCase):
    """Test cases for User model."""

    def setUp(self):
        """Set up test data."""
        self.business_owner = User.objects.create_user(
            username='owner',
            email='<EMAIL>',
            password='testpass123',
            role=Profile.Role.BUSINESS_OWNER,
        )

        self.business = Business.objects.create(
            name='Test Tours',
            email='<EMAIL>',
            phone='+**********',
            address_line1='123 Test St',
            city='Test City',
            state_province='Test State',
            postal_code='12345',
            country='Test Country',
            created_by=self.business_owner,
        )

        self.business_owner.business = self.business
        self.business_owner.save()

    def test_user_creation(self):
        """Test user creation with different roles."""
        agent = User.objects.create_user(
            username='agent',
            email='<EMAIL>',
            password='testpass123',
            role=Profile.Role.AGENT,
            business=self.business,
        )

        self.assertEqual(agent.role, Profile.Role.AGENT)
        self.assertEqual(agent.business, self.business)
        self.assertTrue(agent.is_active)

    def test_user_str_representation(self):
        """Test user string representation."""
        user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            first_name='Test',
            last_name='User',
            role=Profile.Role.CUSTOMER,
        )

        expected = 'Test User (Customer)'
        self.assertEqual(str(user), expected)

    def test_full_name_property(self):
        """Test full_name property."""
        user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            first_name='Test',
            last_name='User',
        )

        self.assertEqual(user.full_name, 'Test User')

        # Test with no name
        user_no_name = User.objects.create_user(
            username='noname', email='<EMAIL>'
        )
        self.assertEqual(user_no_name.full_name, 'noname')

    def test_has_business_access(self):
        """Test business access checking."""
        # Business owner should have access to their business
        self.assertTrue(self.business_owner.has_business_access(self.business))

        # Agent should have access to their business
        agent = User.objects.create_user(
            username='agent',
            email='<EMAIL>',
            password='testpass123',
            role=Profile.Role.AGENT,
            business=self.business,
        )
        self.assertTrue(agent.has_business_access(self.business))

        # Customer should not have business access
        customer = User.objects.create_user(
            username='customer',
            email='<EMAIL>',
            password='testpass123',
            role=Profile.Role.CUSTOMER,
        )
        self.assertFalse(customer.has_business_access(self.business))

        # User from different business should not have access
        other_business = Business.objects.create(
            name='Other Tours',
            email='<EMAIL>',
            phone='+1234567891',
            address_line1='456 Other St',
            city='Other City',
            state_province='Other State',
            postal_code='54321',
            country='Other Country',
            created_by=self.business_owner,
        )
        self.assertFalse(self.business_owner.has_business_access(other_business))

    def test_user_role_choices(self):
        """Test user role choices."""
        self.assertEqual(Profile.Role.BUSINESS_OWNER, 'business_owner')
        self.assertEqual(Profile.Role.AGENT, 'agent')
        self.assertEqual(Profile.Role.CUSTOMER, 'customer')

    def test_user_audit_fields(self):
        """Test audit fields are set correctly."""
        user = User.objects.create_user(
            username='testuser', email='<EMAIL>', password='testpass123'
        )

        self.assertIsNotNone(user.created_at)
        self.assertIsNotNone(user.updated_at)
        self.assertTrue(user.is_active)
