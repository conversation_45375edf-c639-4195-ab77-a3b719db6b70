"""
URL configuration for accounts app.
"""

from django.contrib.auth import views as auth_views
from django.urls import path

from . import views
from .forms import CustomPasswordResetForm

app_name = 'accounts'

urlpatterns = [
    # Authentication URLs
    path('login/', views.CustomLoginView.as_view(), name='login'),
    path(
        'logout/',
        views.CustomLogoutView.as_view(),
        name='logout',
    ),
    path('register/', views.RegisterView.as_view(), name='register'),
    # Email confirmation
    path(
        'confirm-email/<uuid:token>/',
        views.email_confirmation_view,
        name='confirm_email',
    ),
    # Profile management
    path('profile/', views.ProfileDetailView.as_view(), name='profile'),
    path('profile/edit/', views.ProfileUpdateView.as_view(), name='profile_edit'),
    # Onboarding
    path('onboarding/', views.check_onboarding_status, name='onboarding'),
    path(
        'onboarding/welcome/',
        views.OnboardingWelcomeView.as_view(),
        name='onboarding_welcome',
    ),
    path(
        'onboarding/profile/',
        views.OnboardingProfileView.as_view(),
        name='onboarding_profile',
    ),
    path(
        'onboarding/business/',
        views.OnboardingBusinessView.as_view(),
        name='onboarding_business',
    ),
    # User management
    path('users/', views.UserManagementView.as_view(), name='user_management'),
    path('users/activity/', views.UserActivityView.as_view(), name='user_activity'),
    path('users/add/', views.AddUserView.as_view(), name='add_user'),
    path('users/<uuid:pk>/edit/', views.EditUserView.as_view(), name='edit_user'),
    path('users/<uuid:pk>/remove/', views.RemoveUserView.as_view(), name='remove_user'),
    path(
        'users/<uuid:pk>/resend-invite/',
        views.ResendInviteView.as_view(),
        name='resend_invite',
    ),
    path('users/bulk-upload/', views.BulkUserUploadView.as_view(), name='bulk_upload'),
    path('users/template/', views.download_user_template, name='download_template'),
    # User invitation
    path(
        'invitation/<uuid:token>/',
        views.UserInvitationView.as_view(),
        name='user_invitation',
    ),
    # Password reset URLs
    path(
        'password-reset/',
        auth_views.PasswordResetView.as_view(
            form_class=CustomPasswordResetForm,
            template_name='accounts/password_reset.html',
            success_url='/accounts/password-reset/done/',
        ),
        name='password_reset',
    ),
    path(
        'password-reset/done/',
        auth_views.PasswordResetDoneView.as_view(
            template_name='accounts/password_reset_done.html'
        ),
        name='password_reset_done',
    ),
    path(
        'reset/<uidb64>/<token>/',
        auth_views.PasswordResetConfirmView.as_view(
            template_name='accounts/password_reset_confirm.html',
            success_url='/accounts/password-reset-complete/',
        ),
        name='password_reset_confirm',
    ),
    path(
        'password-reset-complete/',
        auth_views.PasswordResetCompleteView.as_view(
            template_name='accounts/password_reset_complete.html'
        ),
        name='password_reset_complete',
    ),
    # Password change URLs (for logged-in users)
    path(
        'password-change/',
        auth_views.PasswordChangeView.as_view(
            template_name='accounts/password_change.html',
            success_url='/accounts/password-change/done/',
        ),
        name='password_change',
    ),
    path(
        'password-change/done/',
        auth_views.PasswordChangeDoneView.as_view(
            template_name='accounts/password_change_done.html'
        ),
        name='password_change_done',
    ),
]
