<c-vars type="info" message="" dismissible="True" class="" />

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
  {% with alert_classes="alert alert-"|add:type %}
  <div class="alert {{ alert_classes }} {{ class }}
           {% if type == 'success' %}bg-green-50 border-green-200 text-green-800
           {% elif type == 'error' or type == 'danger' %}bg-red-50 border-red-200 text-red-800
           {% elif type == 'warning' %}bg-yellow-50 border-yellow-200 text-yellow-800
           {% elif type == 'info' %}bg-blue-50 border-blue-200 text-blue-800
           {% else %}bg-gray-50 border-gray-200 text-gray-800{% endif %}
           border rounded-md p-4 mb-4" {% if dismissible %}data-dismissible="true" {% endif %}>

    <div class="flex items-center">
      <!-- Icon -->
      <div class="flex-shrink-0">
        {% if type == 'success' %}
        <c-icons.alert.success class="h-5 w-5 text-green-400" />
        {% elif type == 'error' or type == 'danger' %}
        <c-icons.alert.error class="h-5 w-5 text-red-400" />
        {% elif type == 'warning' %}
        <c-icons.alert.warning class="h-5 w-5 text-yellow-400" />
        {% else %}
        <c-icons.alert.info class="h-5 w-5 text-blue-400" />
        {% endif %}
      </div>

      <!-- Message -->
      <div class="ml-3 flex-1">
        <p class="text-sm font-medium">
          {{ message }}
        </p>
      </div>

      <!-- Dismiss button -->
      {% if dismissible %}
      <div class="ml-auto pl-3">
        <button type="button" class="inline-flex rounded-md p-1.5 focus:outline-none focus:ring-2 focus:ring-offset-2
        {% if type == 'success' %}text-green-500 hover:bg-green-100 focus:ring-green-600
        {% elif type == 'error' or type == 'danger' %}text-red-500 hover:bg-red-100 focus:ring-red-600
        {% elif type == 'warning' %}text-yellow-500 hover:bg-yellow-100 focus:ring-yellow-600
        {% else %}text-blue-500 hover:bg-blue-100 focus:ring-blue-600{% endif %}"
          onclick="this.parentElement.parentElement.parentElement.remove()">
          <span class="sr-only">Dismiss</span>
          <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd"
              d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
              clip-rule="evenodd" />
          </svg>
        </button>
      </div>
      {% endif %}
    </div>
  </div>
  {% endwith %}
</div>
