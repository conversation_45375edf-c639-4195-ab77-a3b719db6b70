# Generated by Django 5.2.4 on 2025-08-12 11:35

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('accounts', '0001_initial'),
        ('businesses', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='profile',
            name='business',
            field=models.ForeignKey(blank=True, help_text='Business this profile belongs to', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='profiles', to='businesses.business'),
        ),
        migrations.AddField(
            model_name='profile',
            name='user',
            field=models.OneToOneField(help_text='User this profile belongs to', on_delete=django.db.models.deletion.CASCADE, related_name='profile', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddIndex(
            model_name='user',
            index=models.Index(fields=['email'], name='accounts_us_email_74c8d6_idx'),
        ),
        migrations.AddIndex(
            model_name='user',
            index=models.Index(fields=['is_active'], name='accounts_us_is_acti_a5841d_idx'),
        ),
        migrations.AddIndex(
            model_name='profile',
            index=models.Index(fields=['created_at'], name='accounts_pr_created_4522d8_idx'),
        ),
    ]
