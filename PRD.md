# Tour Business Management SaaS - Product Requirements Document

## Project Overview

**Project Name:** Tour Business Management SaaS
**Version:** 1.0
**Date:** 2025-07-30
**Status:** In Development

### Vision

A comprehensive SaaS web application designed to organize and simplify daily tour management activities for tour businesses, enabling efficient booking management, quotation generation, invoicing, and payment processing.

### Tech Stack

- **Backend:** Django 5.2+
- **Frontend:** Django Templates + Tailwind CSS + DatastarJS
- **Database:** SQLite for dev and prod
- **Authentication:** Django Auth + django-guardian (RBAC)
- **Environment Management:** python-decouple

### Key Dependencies

- django-tailwind-cli
- django-cotton
- django-guardian
- django-tailwind
- django-crispy-forms
- django-extensions

## Core Features

### 1. Multi-Tenant Business Management

- Business account creation and management
- Role-based access control (RBAC)
- Data isolation between businesses

### 2. Client & Traveler Management

- Client profile creation and management
- Traveler information collection
- Lead generation and tracking

### 3. Quote & Booking System

- Trip request management
- Quote generation with multiple options
- Public quote viewing via secure hash
- Quote approval workflows
- Booking creation from approved quotes

### 4. Invoice & Payment Processing

- Automated invoice generation
- Payment link creation
- Webhook-based payment capture
- Receipt document generation

### 5. Document Generation

- PDF generation for quotes, invoices, receipts
- Itinerary formatting
- Cost breakdown presentation
- Company branding integration

## User Roles & Permissions

### Business Owner

- Full access to business data
- User management
- System configuration

### Agent

- Client and booking management
- Quote creation and management
- Limited to assigned business data

### Customer

- View own bookings and quotes
- Access public quote pages
- Make payments

## Acceptance Criteria

### Core Workflow Test

1. ✅ Create client → traveler → lead → trip request → quote with 2 options & items → send
2. ✅ Public client view accessible via hash; approval blocked after expiry; works before expiry
3. ✅ Approval creates booking with items & itinerary; invoice generated; payment link created
4. ✅ Webhook marks payment captured; receipt document created

### Security & Access Control

1. ✅ RBAC: Agent cannot access other account data
2. ✅ Customer can only view own public/private resources

### Document Requirements

1. ✅ PDF contains: itinerary, cost breakdown, validity, company info, payment terms, cancellation policy

## Development Progress

### Current Status: Project Setup Complete ✅

- ✅ Basic Django project structure created
- ✅ All dependencies installed and configured
- ✅ Task planning completed
- ✅ Environment configuration with python-decouple
- ✅ Django apps created (accounts, businesses, clients, bookings, quotes, invoices, payments, documents)
- ✅ Settings configured for dev/test/prod environments
- ✅ Static files and media configuration
- ✅ Logging configuration
- ✅ Security settings for production
- ✅ Django system check passes

### Technical Implementation Details

**Dependencies Installed:**

- Django 5.2.4 with all required packages
- django-guardian for RBAC
- django-crispy-forms with tailwind support
- django-extensions for development tools
- reportlab for PDF generation
- psycopg2-binary for PostgreSQL support
- python-decouple for environment management

**Project Structure:**

- Multi-app Django architecture with clear separation of concerns
- Environment-based configuration system
- Proper static files and media handling
- Comprehensive logging setup
- Security-first configuration

### Database Models Complete ✅

**Core Models Implemented:**

- **User Model**: Custom user extending AbstractUser with roles (Business Owner, Agent, Customer)
- **Business Model**: Multi-tenant business accounts with complete profile information
- **Client Model**: Customer management with individual/corporate support
- **Traveler Model**: Individual traveler profiles with travel documents and special requirements
- **Quote Model**: Quote generation with public hash access and status workflow

**Key Features:**

- Multi-tenant data isolation with business-scoped managers
- Role-based user system with proper relationships
- Comprehensive audit trails (created_at, updated_at, created_by)
- Proper indexing for performance
- Validation and constraints for data integrity
- Django admin integration for management

**Database Architecture:**

- Business-level data isolation ensures security
- Proper foreign key relationships with cascade protection
- Unique constraints where needed (email per business, quote numbers)
- Optimized queries with select_related in admin

### Authentication & Authorization System Complete ✅

**Enhanced Features Implemented:**

- **Multi-Language Support**: Django internationalization with 6 supported languages (Swahili, English, Spanish, French, German, Italian, Portuguese)
- **Multi-Currency Support**: Django Money integration with 11 supported currencies (TZS, USD, EUR, GBP, CAD, AUD, JPY, CHF, CNY, INR, BRL, MXN)
- **Calendar Events**: Custom event models for tour scheduling and event management
- **Model History Tracking**: Django Simple History for complete audit trails
- **Translation Management**: Django Rosetta for easy translation management

**Updated Models:**

- **Quote Model**: Now uses MoneyField for multi-currency support (subtotal, tax_amount, total_amount)
- **TourEvent Model**: New calendar-based event model with pricing, participant tracking, and business logic
- **History Tracking**: Prepared for all models (commented out until needed)

**Technical Implementation:**

- Proper middleware configuration for localization and history tracking
- Swingtime calendar configuration with business-specific settings
- MoneyField integration with proper currency handling
- Multi-language template support with locale paths

### Next Steps

1. 🔄 Core business logic implementation
2. Quote and booking system workflows
3. Frontend development with Tailwind CSS and DatastarJS

## Technical Architecture

### Database Design

- Multi-tenant architecture with business-level data isolation
- Core entities: Business, User, Client, Traveler, Lead, TripRequest, Quote, Booking, Invoice, Payment
- Proper foreign key relationships and constraints

### Security Considerations

- Environment-based configuration
- Secure hash generation for public links
- Role-based permissions with django-guardian
- CSRF protection and secure headers

### Testing Strategy

- Unit tests for all models and business logic
- Integration tests for complete workflows
- Acceptance tests matching defined criteria

---

_Last Updated: 2025-07-30_
_Next Review: After Project Setup Completion_
