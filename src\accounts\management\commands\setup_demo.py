"""
Management command to set up a complete demo environment for the tour business management system.
This command runs migrations, creates a superuser, and populates sample data.
"""

from django.contrib.auth import get_user_model
from django.core.management import call_command
from django.core.management.base import BaseCommand

User = get_user_model()


class Command(BaseCommand):
    help = 'Set up a complete demo environment with sample data'

    def add_arguments(self, parser):
        parser.add_argument(
            '--skip-migrations',
            action='store_true',
            help='Skip running migrations',
        )
        parser.add_argument(
            '--skip-superuser',
            action='store_true',
            help='Skip creating superuser',
        )
        parser.add_argument(
            '--skip-demo-data',
            action='store_true',
            help='Skip creating demo data',
        )
        parser.add_argument(
            '--clear-existing',
            action='store_true',
            help='Clear existing demo data before setup',
        )
        parser.add_argument(
            '--use-factories',
            action='store_true',
            help='Use factory_boy and faker for more realistic demo data',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Setting up demo environment...'))

        # Run migrations
        if not options['skip_migrations']:
            self.stdout.write('Running migrations...')
            call_command('migrate', verbosity=0)
            self.stdout.write(self.style.SUCCESS('✓ Migrations completed'))

        # Clear existing demo data if requested
        if options['clear_existing']:
            self.stdout.write('Clearing existing demo data...')
            call_command('clear_demo_data', confirm=True, verbosity=0)
            self.stdout.write(self.style.SUCCESS('✓ Demo data cleared'))

        # Create superuser
        if not options['skip_superuser']:
            if not User.objects.filter(username='admin').exists():
                self.stdout.write('Creating demo superuser...')
                call_command('create_demo_superuser', verbosity=0)
                self.stdout.write(self.style.SUCCESS('✓ Demo superuser created'))
            else:
                self.stdout.write(self.style.WARNING('✓ Demo superuser already exists'))

        # Create demo data
        if not options['skip_demo_data']:
            if options['use_factories']:
                self.stdout.write('Creating realistic demo data with factories...')
                call_command('create_factory_demo_data', verbosity=0)
                self.stdout.write(self.style.SUCCESS('✓ Realistic demo data created'))
            else:
                self.stdout.write('Creating demo data...')
                call_command('create_demo_data', verbosity=0)
                self.stdout.write(self.style.SUCCESS('✓ Demo data created'))

        # Display summary
        self.stdout.write('\n' + '=' * 50)
        self.stdout.write(self.style.SUCCESS('Demo environment setup complete!'))
        self.stdout.write('=' * 50)

        if not options['skip_superuser']:
            self.stdout.write('\nAdmin Access:')
            self.stdout.write('URL: http://localhost:8000/admin/')
            self.stdout.write('Username: admin')
            self.stdout.write('Password: admin123')

        if not options['skip_demo_data']:
            self.stdout.write('\nDemo Business Owners:')
            demo_users = User.objects.filter(username__startswith='demo_owner_')
            for user in demo_users:
                self.stdout.write(f'Username: {user.username}')
                self.stdout.write('Password: demo123')
                if user.profile.business:
                    self.stdout.write(f'Business: {user.profile.business.name}')
                self.stdout.write('')

        self.stdout.write('Application URL: http://localhost:8000/')
        self.stdout.write('\nTo start the development server, run:')
        self.stdout.write('python manage.py runserver')
        self.stdout.write('\n' + '=' * 50)
