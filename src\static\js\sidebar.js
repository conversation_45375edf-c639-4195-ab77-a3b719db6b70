let sidebarCollapsed = false
const sidebar = document.getElementById('sidebar')
const sidebarTitle = document.getElementById('sidebar-title')
const sidebarLogo = document.getElementById('sidebar-logo')
const sidebarTexts = document.querySelectorAll('.sidebar-text')
const sidebarItems = document.querySelectorAll('.sidebar-item')
const tooltip = document.getElementById('sidebar-tooltip')

// Initialize section states
const sectionStates = {
  'business-ops': true,
  management: true,
  tools: true,
}

// Initialize menu item states
const menuItemStates = {
  'tour-templates-submenu': false,
  'tours-submenu': false,
  'clients-submenu': false,
  'quotes-submenu': false,
  'invoices-submenu': false,
  'bookings-submenu': false,
  'payments-submenu': false,
}

function toggleSidebar() {
  sidebarCollapsed = !sidebarCollapsed

  if (sidebarCollapsed) {
    // Collapse sidebar
    sidebar.style.width = '64px'
    sidebarTexts.forEach((text) => (text.style.display = 'none'))

    // Hide section headers when collapsed
    document.querySelectorAll('.sidebar-section-header').forEach((header) => {
      header.style.display = 'none'
    })

    // Hide subsections when collapsed
    document.querySelectorAll('.sidebar-subsection').forEach((subsection) => {
      subsection.style.display = 'none'
    })

    // Hide sub-menus when collapsed
    document.querySelectorAll('.sidebar-submenu').forEach((submenu) => {
      submenu.style.display = 'none'
    })

    // Enable tooltips
    enableTooltips()
  } else {
    // Expand sidebar
    sidebar.style.width = '256px'
    setTimeout(() => {
      sidebarTexts.forEach((text) => (text.style.display = 'inline'))

      // Show section headers when expanded
      document.querySelectorAll('.sidebar-section-header').forEach((header) => {
        header.style.display = 'flex'
      })

      // Restore section states
      Object.keys(sectionStates).forEach((sectionId) => {
        const subsection = document.getElementById(sectionId)
        const icon = document.getElementById(sectionId + '-icon')
        if (subsection && icon) {
          if (sectionStates[sectionId]) {
            subsection.style.display = 'block'
            icon.style.transform = 'rotate(0deg)'
          } else {
            subsection.style.display = 'none'
            icon.style.transform = 'rotate(-90deg)'
          }
        }
      })

      // Restore menu item states
      Object.keys(menuItemStates).forEach((menuId) => {
        const submenu = document.getElementById(menuId)
        const icon = document.getElementById(menuId + '-icon')
        if (submenu && icon) {
          if (menuItemStates[menuId]) {
            submenu.style.display = 'block'
            icon.style.transform = 'rotate(0deg)'
          } else {
            submenu.style.display = 'none'
            icon.style.transform = 'rotate(-90deg)'
          }
        }
      })
    }, 150)

    // Disable tooltips
    disableTooltips()
  }
}

function toggleSection(sectionId) {
  if (sidebarCollapsed) return

  const subsection = document.getElementById(sectionId)
  const icon = document.getElementById(sectionId + '-icon')

  if (subsection && icon) {
    const isVisible = subsection.style.display !== 'none'

    if (isVisible) {
      subsection.style.display = 'none'
      icon.style.transform = 'rotate(-90deg)'
      sectionStates[sectionId] = false
    } else {
      subsection.style.display = 'block'
      icon.style.transform = 'rotate(0deg)'
      sectionStates[sectionId] = true
    }
  }
}

function toggleMenuItem(menuId) {
  if (sidebarCollapsed) return

  const submenu = document.getElementById(menuId)
  const icon = document.getElementById(menuId + '-icon')

  if (submenu && icon) {
    const isVisible = submenu.style.display !== 'none'

    if (isVisible) {
      submenu.style.display = 'none'
      icon.style.transform = 'rotate(-90deg)'
      menuItemStates[menuId] = false
    } else {
      submenu.style.display = 'block'
      icon.style.transform = 'rotate(0deg)'
      menuItemStates[menuId] = true
    }
  }
}

function enableTooltips() {
  sidebarItems.forEach((item) => {
    item.addEventListener('mouseenter', showTooltip)
    item.addEventListener('mouseleave', hideTooltip)
  })

  // Also enable tooltips for sub-items
  document.querySelectorAll('.sidebar-subitem').forEach((item) => {
    item.addEventListener('mouseenter', showTooltip)
    item.addEventListener('mouseleave', hideTooltip)
  })

  // Enable tooltips for menu items with sub-menus
  document.querySelectorAll('.sidebar-menu-item > div').forEach((item) => {
    item.addEventListener('mouseenter', showTooltip)
    item.addEventListener('mouseleave', hideTooltip)
  })
}

function disableTooltips() {
  sidebarItems.forEach((item) => {
    item.removeEventListener('mouseenter', showTooltip)
    item.removeEventListener('mouseleave', hideTooltip)
  })

  document.querySelectorAll('.sidebar-subitem').forEach((item) => {
    item.removeEventListener('mouseenter', showTooltip)
    item.removeEventListener('mouseleave', hideTooltip)
  })

  document.querySelectorAll('.sidebar-menu-item > div').forEach((item) => {
    item.removeEventListener('mouseenter', showTooltip)
    item.removeEventListener('mouseleave', hideTooltip)
  })

  hideTooltip()
}

function showTooltip(event) {
  if (!sidebarCollapsed) return

  const tooltipText = event.currentTarget.getAttribute('data-tooltip')
  if (tooltipText) {
    tooltip.textContent = tooltipText
    tooltip.style.opacity = '1'

    const rect = event.currentTarget.getBoundingClientRect()
    tooltip.style.left = rect.right + 8 + 'px'
    tooltip.style.top =
      rect.top + rect.height / 2 - tooltip.offsetHeight / 2 + 'px'
  }
}

function hideTooltip() {
  tooltip.style.opacity = '0'
}

// Initialize sidebar state
document.addEventListener('DOMContentLoaded', function () {
  // Set initial section states
  Object.keys(sectionStates).forEach((sectionId) => {
    const subsection = document.getElementById(sectionId)
    const icon = document.getElementById(sectionId + '-icon')
    if (subsection && icon) {
      if (sectionStates[sectionId]) {
        subsection.style.display = 'block'
        icon.style.transform = 'rotate(0deg)'
      } else {
        subsection.style.display = 'none'
        icon.style.transform = 'rotate(-90deg)'
      }
    }
  })

  // Set initial menu item states
  Object.keys(menuItemStates).forEach((menuId) => {
    const submenu = document.getElementById(menuId)
    const icon = document.getElementById(menuId + '-icon')
    if (submenu && icon) {
      if (menuItemStates[menuId]) {
        submenu.style.display = 'block'
        icon.style.transform = 'rotate(0deg)'
      } else {
        submenu.style.display = 'none'
        icon.style.transform = 'rotate(-90deg)'
      }
    }
  })
})
