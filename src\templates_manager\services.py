"""
PDF generation service using customizable templates.
"""
import os
import logging
from django.template.loader import render_to_string, get_template
from django.template import Template, Context
from django.conf import settings
from django.core.files.base import ContentFile
from django.utils import timezone
from .models import PDFTemplate

logger = logging.getLogger(__name__)

try:
    from weasyprint import HTML, CSS
    WEASYPRINT_AVAILABLE = True
except ImportError:
    WEASYPRINT_AVAILABLE = False
    logger.warning("WeasyPrint not available. PDF generation will be disabled.")


class PDFGenerationService:
    """Service for generating PDFs using customizable templates."""
    
    def __init__(self, business=None):
        self.business = business
    
    def generate_pdf(self, template_type, context_data, filename=None):
        """
        Generate a PDF using the specified template type and context data.
        
        Args:
            template_type: Type of template (quote, invoice, etc.)
            context_data: Dictionary of context variables for template
            filename: Optional filename for the generated PDF
            
        Returns:
            ContentFile containing the PDF data
        """
        if not WEASYPRINT_AVAILABLE:
            raise RuntimeError("WeasyPrint is not available. Please install it to generate PDFs.")
        
        # Get the template
        template = self._get_template(template_type)
        if not template:
            raise ValueError(f"No template found for type: {template_type}")
        
        # Prepare context
        context = self._prepare_context(context_data)
        
        # Render HTML
        html_content = self._render_template(template, context)
        
        # Generate PDF
        pdf_data = self._generate_pdf_from_html(html_content, template)
        
        # Create filename if not provided
        if not filename:
            timestamp = timezone.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{template_type}_{timestamp}.pdf"
        
        return ContentFile(pdf_data, name=filename)
    
    def _get_template(self, template_type):
        """Get the appropriate template for the given type."""
        # Try to get business-specific template first
        if self.business:
            template = PDFTemplate.objects.filter(
                business=self.business,
                template_type=template_type,
                is_active=True
            ).first()
            
            if template:
                return template
        
        # Fall back to default template
        template = PDFTemplate.objects.filter(
            business__isnull=True,
            template_type=template_type,
            is_active=True,
            is_default=True
        ).first()
        
        if not template:
            # Try any system template of this type
            template = PDFTemplate.objects.filter(
                business__isnull=True,
                template_type=template_type,
                is_active=True
            ).first()
        
        return template
    
    def _prepare_context(self, context_data):
        """Prepare context with additional variables."""
        context = context_data.copy()
        
        # Add business information if available
        if self.business:
            context['business'] = self.business
        
        # Add common template variables
        context.update({
            'current_date': timezone.now().date(),
            'current_datetime': timezone.now(),
            'site_name': getattr(settings, 'SITE_NAME', 'Tour Business Management'),
        })
        
        return context
    
    def _render_template(self, template, context):
        """Render the template with the given context."""
        try:
            # If template has custom HTML content, use it directly
            if template.html_content:
                django_template = Template(template.html_content)
                return django_template.render(Context(context))
            else:
                # Use filesystem template
                template_path = template.get_template_path()
                return render_to_string(template_path, context)
        except Exception as e:
            logger.error(f"Error rendering template {template.name}: {e}")
            raise
    
    def _generate_pdf_from_html(self, html_content, template):
        """Generate PDF from HTML content."""
        try:
            # Create HTML object
            html_doc = HTML(string=html_content)
            
            # Add CSS if available
            stylesheets = []
            if template.css_content:
                css = CSS(string=template.css_content)
                stylesheets.append(css)
            
            # Check for external CSS file
            css_path = template.get_css_path()
            if css_path:
                full_css_path = os.path.join(settings.STATIC_ROOT or settings.BASE_DIR / 'static', css_path)
                if os.path.exists(full_css_path):
                    css = CSS(filename=full_css_path)
                    stylesheets.append(css)
            
            # Generate PDF
            pdf_data = html_doc.write_pdf(stylesheets=stylesheets)
            return pdf_data
            
        except Exception as e:
            logger.error(f"Error generating PDF: {e}")
            raise
    
    def preview_template(self, template_type, sample_context=None):
        """
        Generate a preview of the template with sample data.
        
        Args:
            template_type: Type of template to preview
            sample_context: Optional sample context data
            
        Returns:
            HTML content for preview
        """
        template = self._get_template(template_type)
        if not template:
            return f"<p>No template found for type: {template_type}</p>"
        
        # Use sample context or create default
        if not sample_context:
            sample_context = self._get_sample_context(template_type)
        
        context = self._prepare_context(sample_context)
        
        try:
            return self._render_template(template, context)
        except Exception as e:
            return f"<p>Error rendering template: {e}</p>"
    
    def _get_sample_context(self, template_type):
        """Get sample context data for template preview."""
        from datetime import date, timedelta
        from djmoney.money import Money
        
        # Create sample business if none provided
        sample_business = self.business or type('SampleBusiness', (), {
            'name': 'Sample Tour Company',
            'email': '<EMAIL>',
            'phone': '******-123-4567',
            'address': '123 Adventure Street',
            'city': 'Adventure City',
            'state_province': 'AC',
            'country': 'US',
            'postal_code': '12345',
            'get_logo_url': lambda: '/static/images/sample-logo.png',
            'primary_color': '#007bff',
            'secondary_color': '#6c757d',
        })()
        
        # Create sample client
        sample_client = type('SampleClient', (), {
            'first_name': 'John',
            'last_name': 'Doe',
            'email': '<EMAIL>',
            'phone': '******-987-6543',
            'display_name': 'John Doe',
        })()
        
        base_context = {
            'business': sample_business,
            'client': sample_client,
        }
        
        if template_type == 'quote':
            base_context.update({
                'quote': type('SampleQuote', (), {
                    'quote_number': 'QUO-2024-001',
                    'title': 'Adventure Tour Package',
                    'description': 'A comprehensive 7-day adventure tour including hiking, rafting, and camping.',
                    'subtotal': Money(1500, 'USD'),
                    'tax_amount': Money(150, 'USD'),
                    'total_amount': Money(1650, 'USD'),
                    'valid_until': date.today() + timedelta(days=30),
                    'created_at': timezone.now(),
                    'terms_conditions': 'Payment due within 30 days of approval.',
                })()
            })
        
        elif template_type == 'invoice':
            base_context.update({
                'invoice': type('SampleInvoice', (), {
                    'invoice_number': 'INV-2024-001',
                    'subtotal': Money(1500, 'USD'),
                    'tax_amount': Money(150, 'USD'),
                    'total_amount': Money(1650, 'USD'),
                    'issue_date': date.today(),
                    'due_date': date.today() + timedelta(days=30),
                    'payment_terms': 'Net 30',
                    'terms_conditions': 'Payment due within 30 days.',
                })()
            })
        
        elif template_type == 'booking_confirmation':
            base_context.update({
                'booking': type('SampleBooking', (), {
                    'confirmation_number': 'BOOK-2024-001',
                    'tour_name': 'Mountain Adventure Tour',
                    'tour_date': date.today() + timedelta(days=14),
                    'participants': 2,
                    'meeting_point': 'Adventure Center Parking Lot',
                    'meeting_time': '08:00 AM',
                })()
            })
        
        return base_context


# Convenience functions
def generate_quote_pdf(quote, business=None):
    """Generate PDF for a quote."""
    service = PDFGenerationService(business or quote.business)
    context = {
        'quote': quote,
        'client': quote.client,
    }
    return service.generate_pdf('quote', context, f'quote_{quote.quote_number}.pdf')


def generate_invoice_pdf(invoice, business=None):
    """Generate PDF for an invoice."""
    service = PDFGenerationService(business or invoice.business)
    context = {
        'invoice': invoice,
        'client': invoice.client,
    }
    return service.generate_pdf('invoice', context, f'invoice_{invoice.invoice_number}.pdf')


def generate_booking_confirmation_pdf(booking, business=None):
    """Generate PDF for a booking confirmation."""
    service = PDFGenerationService(business or booking.business)
    context = {
        'booking': booking,
        'client': booking.client,
        'event': booking.event,
    }
    return service.generate_pdf('booking_confirmation', context, f'booking_{booking.confirmation_number}.pdf')
