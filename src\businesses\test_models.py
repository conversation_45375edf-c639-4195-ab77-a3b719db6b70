"""
Pytest tests for business models.
"""
import pytest
from django.core.exceptions import ValidationError
from django.db import IntegrityError

from businesses.models import Business


@pytest.mark.django_db
class TestBusinessModel:
    """Test cases for the Business model."""
    
    def test_create_business(self, business_owner):
        """Test creating a business with valid data."""
        business = Business.objects.create(
            name='Test Business',
            email='<EMAIL>',
            phone='******-123-4567',
            address='123 Test St',
            city='Test City',
            state_province='TS',
            country='US',
            postal_code='12345',
            created_by=business_owner
        )
        
        assert business.name == 'Test Business'
        assert business.email == '<EMAIL>'
        assert business.created_by == business_owner
        assert business.is_active is True
    
    def test_business_str_representation(self, business):
        """Test the string representation of a business."""
        assert str(business) == business.name
    
    def test_business_full_address(self, business):
        """Test the full_address property."""
        expected_address = f"{business.address}, {business.city}, {business.state_province} {business.postal_code}, {business.country}"
        assert business.full_address == expected_address
    
    def test_get_logo_url_with_logo(self, business):
        """Test get_logo_url when business has a logo."""
        # Mock a logo file
        business.logo = 'logos/test_logo.png'
        business.save()
        
        assert business.get_logo_url() == '/media/logos/test_logo.png'
    
    def test_get_logo_url_without_logo(self, business):
        """Test get_logo_url when business has no logo."""
        assert business.get_logo_url() == '/static/images/default-business-logo.png'
    
    def test_get_theme_css(self, business):
        """Test theme CSS generation."""
        css = business.get_theme_css()
        
        assert business.primary_color in css
        assert business.secondary_color in css
        assert business.accent_color in css
        assert '--business-primary' in css
        assert '.btn-business-primary' in css
    
    def test_darken_color(self, business):
        """Test color darkening utility method."""
        darkened = business._darken_color('#007bff', 0.1)
        
        # Should return a valid hex color
        assert darkened.startswith('#')
        assert len(darkened) == 7
        # Should be darker than original
        assert darkened != '#007bff'
    
    def test_business_unique_email(self, business_owner):
        """Test that business email must be unique."""
        Business.objects.create(
            name='First Business',
            email='<EMAIL>',
            created_by=business_owner
        )
        
        with pytest.raises(IntegrityError):
            Business.objects.create(
                name='Second Business',
                email='<EMAIL>',  # Duplicate email
                created_by=business_owner
            )
    
    def test_business_required_fields(self, business_owner):
        """Test that required fields are enforced."""
        with pytest.raises(IntegrityError):
            Business.objects.create(
                # Missing required name field
                email='<EMAIL>',
                created_by=business_owner
            )
    
    def test_business_default_colors(self, business_owner):
        """Test that default colors are set correctly."""
        business = Business.objects.create(
            name='Test Business',
            email='<EMAIL>',
            created_by=business_owner
        )
        
        assert business.primary_color == '#007bff'
        assert business.secondary_color == '#6c757d'
        assert business.accent_color == '#28a745'
    
    def test_business_custom_css(self, business):
        """Test custom CSS functionality."""
        custom_css = '.custom-class { color: red; }'
        business.custom_css = custom_css
        business.save()
        
        theme_css = business.get_theme_css()
        assert custom_css in theme_css
    
    def test_business_email_signature(self, business):
        """Test email signature functionality."""
        signature = 'Best regards,\nTest Team'
        business.email_signature = signature
        business.save()
        
        assert business.email_signature == signature
    
    def test_business_template_preferences(self, business):
        """Test PDF template preferences."""
        business.quote_template = 'custom_quote'
        business.invoice_template = 'custom_invoice'
        business.save()
        
        assert business.quote_template == 'custom_quote'
        assert business.invoice_template == 'custom_invoice'


@pytest.mark.django_db
class TestBusinessQuerySet:
    """Test cases for Business model querysets and managers."""
    
    def test_active_businesses(self, business_owner):
        """Test filtering active businesses."""
        # Create active business
        active_business = Business.objects.create(
            name='Active Business',
            email='<EMAIL>',
            is_active=True,
            created_by=business_owner
        )
        
        # Create inactive business
        Business.objects.create(
            name='Inactive Business',
            email='<EMAIL>',
            is_active=False,
            created_by=business_owner
        )
        
        active_businesses = Business.objects.filter(is_active=True)
        assert active_businesses.count() == 1
        assert active_businesses.first() == active_business
    
    def test_business_ordering(self, business_owner):
        """Test default ordering of businesses."""
        business1 = Business.objects.create(
            name='B Business',
            email='<EMAIL>',
            created_by=business_owner
        )
        business2 = Business.objects.create(
            name='A Business',
            email='<EMAIL>',
            created_by=business_owner
        )
        
        businesses = list(Business.objects.all())
        # Should be ordered by name
        assert businesses[0] == business2  # A Business
        assert businesses[1] == business1  # B Business
