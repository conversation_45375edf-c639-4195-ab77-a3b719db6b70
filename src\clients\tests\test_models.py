"""
Unit tests for clients models using pytest.
"""

from datetime import date, timedelta

import pytest
from django.contrib.auth import get_user_model

from businesses.models import Business
from clients.models import Client, Traveler

User = get_user_model()


@pytest.mark.django_db
class TestClientModel:
    """Test cases for Client model."""

    def setup_method(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            username="testuser", email="<EMAIL>", password="testpass123"
        )

        self.business = Business.objects.create(
            name="Test Tours",
            email="<EMAIL>",
            phone="+1234567890",
            address_line1="123 Test St",
            city="Test City",
            state_province="Test State",
            postal_code="12345",
            country="Test Country",
            created_by=self.user,
        )

    def test_individual_client_creation(self):
        """Test individual client creation."""
        client = Client.objects.create(
            business=self.business,
            client_type=Client.ClientType.INDIVIDUAL,
            first_name="<PERSON>",
            last_name="Doe",
            email="<EMAIL>",
            phone="+1234567890",
            created_by=self.user,
        )

        assert client.client_type == Client.ClientType.INDIVIDUAL
        assert client.first_name == "John"
        assert client.last_name == "Doe"
        assert client.business == self.business
        assert client.is_active

    def test_corporate_client_creation(self):
        """Test corporate client creation."""
        client = Client.objects.create(
            business=self.business,
            client_type=Client.ClientType.CORPORATE,
            first_name="Jane",
            last_name="Smith",
            company_name="ABC Corporation",
            email="<EMAIL>",
            phone="+1234567891",
            created_by=self.user,
        )

        assert client.client_type == Client.ClientType.CORPORATE
        assert client.company_name == "ABC Corporation"

    def test_client_str_representation(self):
        """Test client string representation."""
        # Individual client
        individual = Client.objects.create(
            business=self.business,
            client_type=Client.ClientType.INDIVIDUAL,
            first_name="John",
            last_name="Doe",
            email="<EMAIL>",
            phone="+1234567890",
            created_by=self.user,
        )
        self.assertEqual(str(individual), "John Doe")

        # Corporate client
        corporate = Client.objects.create(
            business=self.business,
            client_type=Client.ClientType.CORPORATE,
            first_name="Jane",
            last_name="Smith",
            company_name="ABC Corporation",
            email="<EMAIL>",
            phone="+1234567891",
            created_by=self.user,
        )
        self.assertEqual(str(corporate), "ABC Corporation (Jane Smith)")

    def test_full_name_property(self):
        """Test full_name property."""
        client = Client.objects.create(
            business=self.business,
            first_name="John",
            last_name="Doe",
            email="<EMAIL>",
            phone="+1234567890",
            created_by=self.user,
        )

        self.assertEqual(client.full_name, "John Doe")

    def test_display_name_property(self):
        """Test display_name property."""
        # Individual client
        individual = Client.objects.create(
            business=self.business,
            client_type=Client.ClientType.INDIVIDUAL,
            first_name="John",
            last_name="Doe",
            email="<EMAIL>",
            phone="+1234567890",
            created_by=self.user,
        )
        self.assertEqual(individual.display_name, "John Doe")

        # Corporate client
        corporate = Client.objects.create(
            business=self.business,
            client_type=Client.ClientType.CORPORATE,
            first_name="Jane",
            last_name="Smith",
            company_name="ABC Corporation",
            email="<EMAIL>",
            phone="+1234567891",
            created_by=self.user,
        )
        self.assertEqual(corporate.display_name, "ABC Corporation")

    def test_full_address_property(self):
        """Test full_address property."""
        client = Client.objects.create(
            business=self.business,
            first_name="John",
            last_name="Doe",
            email="<EMAIL>",
            phone="+1234567890",
            address_line1="123 Main St",
            address_line2="Apt 4B",
            city="New York",
            state_province="NY",
            postal_code="10001",
            country="USA",
            created_by=self.user,
        )

        expected_address = "123 Main St\nApt 4B\nNew York, NY, 10001\nUSA"
        self.assertEqual(client.full_address, expected_address)

    def test_client_manager_methods(self):
        """Test ClientManager methods."""
        # Create different types of clients
        individual = Client.objects.create(
            business=self.business,
            client_type=Client.ClientType.INDIVIDUAL,
            first_name="John",
            last_name="Doe",
            email="<EMAIL>",
            phone="+1234567890",
            created_by=self.user,
        )

        corporate = Client.objects.create(
            business=self.business,
            client_type=Client.ClientType.CORPORATE,
            first_name="Jane",
            last_name="Smith",
            company_name="ABC Corporation",
            email="<EMAIL>",
            phone="+1234567891",
            created_by=self.user,
        )

        inactive_client = Client.objects.create(
            business=self.business,
            first_name="Inactive",
            last_name="Client",
            email="<EMAIL>",
            phone="+1234567892",
            is_active=False,
            created_by=self.user,
        )

        # Test manager methods
        self.assertIn(individual, Client.objects.individual())
        self.assertNotIn(corporate, Client.objects.individual())

        self.assertIn(corporate, Client.objects.corporate())
        self.assertNotIn(individual, Client.objects.corporate())

        active_clients = Client.objects.active()
        self.assertIn(individual, active_clients)
        self.assertIn(corporate, active_clients)
        self.assertNotIn(inactive_client, active_clients)

        business_clients = Client.objects.for_business(self.business)
        self.assertIn(individual, business_clients)
        self.assertIn(corporate, business_clients)


@pytest.mark.django_db
class TestTravelerModel:
    """Test cases for Traveler model."""

    def setup_method(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            username="testuser", email="<EMAIL>", password="testpass123"
        )

        self.business = Business.objects.create(
            name="Test Tours",
            email="<EMAIL>",
            phone="+1234567890",
            address_line1="123 Test St",
            city="Test City",
            state_province="Test State",
            postal_code="12345",
            country="Test Country",
            created_by=self.user,
        )

        self.client = Client.objects.create(
            business=self.business,
            first_name="John",
            last_name="Doe",
            email="<EMAIL>",
            phone="+1234567890",
            created_by=self.user,
        )

    def test_traveler_creation(self):
        """Test traveler creation."""
        birth_date = date.today() - timedelta(days=365 * 30)  # 30 years old

        traveler = Traveler.objects.create(
            client=self.client,
            first_name="Alice",
            last_name="Johnson",
            date_of_birth=birth_date,
            gender="female",
            email="<EMAIL>",
            phone="+1234567893",
            passport_number="A12345678",
            passport_expiry=date.today() + timedelta(days=365 * 2),  # 2 years from now
            nationality="American",
            created_by=self.user,
        )

        self.assertEqual(traveler.client, self.client)
        self.assertEqual(traveler.first_name, "Alice")
        self.assertEqual(traveler.last_name, "Johnson")
        self.assertEqual(traveler.gender, "female")
        self.assertTrue(traveler.is_active)

    def test_traveler_str_representation(self):
        """Test traveler string representation."""
        traveler = Traveler.objects.create(
            client=self.client,
            first_name="Alice",
            last_name="Johnson",
            created_by=self.user,
        )

        self.assertEqual(str(traveler), "Alice Johnson")

    def test_full_name_property(self):
        """Test full_name property."""
        traveler = Traveler.objects.create(
            client=self.client,
            first_name="Alice",
            last_name="Johnson",
            created_by=self.user,
        )

        self.assertEqual(traveler.full_name, "Alice Johnson")

    def test_age_property(self):
        """Test age property calculation."""
        # Create a birth date that is exactly 25 years ago
        today = date.today()
        birth_date = date(today.year - 25, today.month, today.day)

        traveler = Traveler.objects.create(
            client=self.client,
            first_name="Alice",
            last_name="Johnson",
            date_of_birth=birth_date,
            created_by=self.user,
        )

        self.assertEqual(traveler.age, 25)

        # Test with no birth date
        traveler_no_birth = Traveler.objects.create(
            client=self.client,
            first_name="Bob",
            last_name="Smith",
            created_by=self.user,
        )

        self.assertIsNone(traveler_no_birth.age)

    def test_business_property(self):
        """Test business property."""
        traveler = Traveler.objects.create(
            client=self.client,
            first_name="Alice",
            last_name="Johnson",
            created_by=self.user,
        )

        self.assertEqual(traveler.business, self.business)

    def test_traveler_manager_methods(self):
        """Test TravelerManager methods."""
        # Create travelers with different passport statuses
        valid_passport_traveler = Traveler.objects.create(
            client=self.client,
            first_name="Valid",
            last_name="Passport",
            passport_expiry=date.today() + timedelta(days=365),
            created_by=self.user,
        )

        expired_passport_traveler = Traveler.objects.create(
            client=self.client,
            first_name="Expired",
            last_name="Passport",
            passport_expiry=date.today() - timedelta(days=1),
            created_by=self.user,
        )

        no_passport_traveler = Traveler.objects.create(
            client=self.client,
            first_name="No",
            last_name="Passport",
            created_by=self.user,
        )

        # Test with_valid_passport method
        valid_passport_travelers = Traveler.objects.with_valid_passport()
        self.assertIn(valid_passport_traveler, valid_passport_travelers)
        self.assertNotIn(expired_passport_traveler, valid_passport_travelers)
        self.assertNotIn(no_passport_traveler, valid_passport_travelers)

        # Test for_business method
        business_travelers = Traveler.objects.for_business(self.business)
        self.assertIn(valid_passport_traveler, business_travelers)
        self.assertIn(expired_passport_traveler, business_travelers)
        self.assertIn(no_passport_traveler, business_travelers)
