<c-vars stats="" />

<!-- Dashboard Statistics Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    {% for stat in stats %}
    <div class="bg-white rounded-lg shadow p-6 hover:shadow-md transition-shadow">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="w-10 h-10 {{ stat.bg_color|default:'bg-blue-500' }} rounded-lg flex items-center justify-center">
                    <i class="{{ stat.icon|default:'fas fa-chart-line' }} text-white"></i>
                </div>
            </div>
            <div class="ml-4 flex-1">
                <p class="text-sm font-medium text-gray-500">{{ stat.label }}</p>
                <div class="flex items-baseline">
                    <p class="text-2xl font-semibold text-gray-900">{{ stat.value }}</p>
                    {% if stat.change %}
                    <p class="ml-2 text-sm {% if stat.change_positive %}text-green-600{% else %}text-red-600{% endif %}">
                        <i class="fas fa-arrow-{% if stat.change_positive %}up{% else %}down{% endif %} mr-1"></i>
                        {{ stat.change }}
                    </p>
                    {% endif %}
                </div>
                {% if stat.subtitle %}
                <p class="text-xs text-gray-500 mt-1">{{ stat.subtitle }}</p>
                {% endif %}
            </div>
        </div>
        {% if stat.link %}
        <div class="mt-4">
            <a href="{{ stat.link }}" class="text-{{ stat.link_color|default:'blue' }}-600 hover:text-{{ stat.link_color|default:'blue' }}-700 text-sm font-medium">
                {{ stat.link_text|default:'View details' }} →
            </a>
        </div>
        {% endif %}
    </div>
    {% endfor %}
</div>
