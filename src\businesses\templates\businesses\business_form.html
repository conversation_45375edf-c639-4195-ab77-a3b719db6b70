{% extends "base.html" %}
{% load i18n %}

{% block title %}
    {% if object %}
        {% trans "Edit Business" %} - {{ object.name }}
    {% else %}
        {% trans "Create Business" %}
    {% endif %}
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="max-w-4xl mx-auto">
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h1 class="text-2xl font-bold text-gray-900">
                    {% if object %}
                        {% trans "Edit Business" %}
                    {% else %}
                        {% trans "Create Business" %}
                    {% endif %}
                </h1>
            </div>
            
            <form method="post" enctype="multipart/form-data" class="px-6 py-6">
                {% csrf_token %}
                
                {% if form.errors %}
                    <div class="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-red-800">
                                    {% trans "There were errors with your submission" %}
                                </h3>
                                <div class="mt-2 text-sm text-red-700">
                                    <ul class="list-disc pl-5 space-y-1">
                                        {% for field, errors in form.errors.items %}
                                            {% for error in errors %}
                                                <li>{{ field|capfirst }}: {{ error }}</li>
                                            {% endfor %}
                                        {% endfor %}
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                {% endif %}
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Basic Information -->
                    <div class="md:col-span-2">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">{% trans "Basic Information" %}</h3>
                    </div>
                    
                    <div>
                        <label for="{{ form.name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            {{ form.name.label }}
                        </label>
                        {{ form.name }}
                    </div>
                    
                    <div>
                        <label for="{{ form.email.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            {{ form.email.label }}
                        </label>
                        {{ form.email }}
                    </div>
                    
                    <div>
                        <label for="{{ form.phone.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            {{ form.phone.label }}
                        </label>
                        {{ form.phone }}
                    </div>
                    
                    <div>
                        <label for="{{ form.website.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            {{ form.website.label }}
                        </label>
                        {{ form.website }}
                    </div>
                    
                    <div class="md:col-span-2">
                        <label for="{{ form.description.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            {{ form.description.label }}
                        </label>
                        {{ form.description }}
                    </div>
                    
                    <!-- Address Information -->
                    <div class="md:col-span-2 mt-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">{% trans "Address Information" %}</h3>
                    </div>
                    
                    <div class="md:col-span-2">
                        <label for="{{ form.address.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            {{ form.address.label }}
                        </label>
                        {{ form.address }}
                    </div>
                    
                    <div>
                        <label for="{{ form.city.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            {{ form.city.label }}
                        </label>
                        {{ form.city }}
                    </div>
                    
                    <div>
                        <label for="{{ form.state_province.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            {{ form.state_province.label }}
                        </label>
                        {{ form.state_province }}
                    </div>
                    
                    <div>
                        <label for="{{ form.country.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            {{ form.country.label }}
                        </label>
                        {{ form.country }}
                    </div>
                    
                    <div>
                        <label for="{{ form.postal_code.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            {{ form.postal_code.label }}
                        </label>
                        {{ form.postal_code }}
                    </div>
                    
                    <!-- Branding -->
                    <div class="md:col-span-2 mt-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">{% trans "Branding" %}</h3>
                    </div>
                    
                    <div class="md:col-span-2">
                        <label for="{{ form.logo.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            {{ form.logo.label }}
                        </label>
                        {{ form.logo }}
                        {% if object.logo %}
                            <div class="mt-2">
                                <img src="{{ object.get_logo_url }}" alt="Current logo" class="h-16 w-auto">
                                <p class="text-sm text-gray-500 mt-1">{% trans "Current logo" %}</p>
                            </div>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label for="{{ form.primary_color.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            {{ form.primary_color.label }}
                        </label>
                        {{ form.primary_color }}
                    </div>
                    
                    <div>
                        <label for="{{ form.secondary_color.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            {{ form.secondary_color.label }}
                        </label>
                        {{ form.secondary_color }}
                    </div>
                    
                    <div>
                        <label for="{{ form.accent_color.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            {{ form.accent_color.label }}
                        </label>
                        {{ form.accent_color }}
                    </div>
                    
                    <!-- Advanced Settings (only for edit) -->
                    {% if object %}
                        <div class="md:col-span-2 mt-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">{% trans "Advanced Settings" %}</h3>
                        </div>
                        
                        <div class="md:col-span-2">
                            <label for="{{ form.custom_css.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                {{ form.custom_css.label }}
                            </label>
                            {{ form.custom_css }}
                            <p class="text-sm text-gray-500 mt-1">{% trans "Custom CSS for additional styling" %}</p>
                        </div>
                        
                        <div class="md:col-span-2">
                            <label for="{{ form.email_signature.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                {{ form.email_signature.label }}
                            </label>
                            {{ form.email_signature }}
                        </div>
                        
                        <div>
                            <label for="{{ form.quote_template.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                {{ form.quote_template.label }}
                            </label>
                            {{ form.quote_template }}
                        </div>
                        
                        <div>
                            <label for="{{ form.invoice_template.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                                {{ form.invoice_template.label }}
                            </label>
                            {{ form.invoice_template }}
                        </div>
                    {% endif %}
                </div>
                
                <div class="mt-8 flex justify-end space-x-3">
                    <a href="{% if object %}{% url 'businesses:detail' object.pk %}{% else %}{% url 'businesses:list' %}{% endif %}" 
                       class="btn btn-secondary">
                        {% trans "Cancel" %}
                    </a>
                    <button type="submit" class="btn btn-primary">
                        {% if object %}
                            {% trans "Update Business" %}
                        {% else %}
                            {% trans "Create Business" %}
                        {% endif %}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
    /* Style form inputs */
    input[type="text"], input[type="email"], input[type="url"], input[type="tel"], 
    input[type="color"], textarea, select {
        @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500;
    }
    
    textarea {
        @apply h-24 resize-vertical;
    }
    
    input[type="color"] {
        @apply h-10 w-20;
    }
    
    input[type="file"] {
        @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500;
    }
</style>
{% endblock %}
