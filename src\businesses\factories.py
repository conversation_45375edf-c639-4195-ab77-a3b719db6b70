"""
Factory classes for businesses app models using factory_boy and faker.
"""

import factory
from businesses.models import Business
from accounts.factories import BusinessOwnerFactory


class BusinessFactory(factory.django.DjangoModelFactory):
    """Factory for creating Business instances."""
    
    class Meta:
        model = Business
    
    name = factory.Faker('company')
    description = factory.Faker('catch_phrase')
    email = factory.Faker('company_email')
    phone = factory.Faker('phone_number')
    website = factory.Faker('url')
    address_line1 = factory.Faker('street_address')
    address_line2 = factory.Faker('secondary_address')
    city = factory.Faker('city')
    state_province = factory.Faker('state')
    postal_code = factory.Faker('postcode')
    country = factory.Faker('country')
    created_by = factory.SubFactory(BusinessOwnerFactory)
    
    @factory.post_generation
    def setup_owner(obj, create, extracted, **kwargs):
        """Associate the business with its owner."""
        if create and obj.created_by:
            obj.created_by.business = obj
            obj.created_by.save()


class SafariBusinessFactory(BusinessFactory):
    """Factory for safari tour businesses."""
    
    name = factory.Faker('random_element', elements=[
        'African Safari Adventures',
        'Serengeti Expeditions',
        'Masai Mara Tours',
        'Big Five Safaris',
        'Wilderness Safaris'
    ])
    description = factory.Faker('random_element', elements=[
        'Premium African safari experiences',
        'Authentic wildlife adventures',
        'Luxury safari tours and camps',
        'Conservation-focused safari experiences',
        'Family-friendly safari adventures'
    ])


class AdventureBusinessFactory(BusinessFactory):
    """Factory for adventure tour businesses."""
    
    name = factory.Faker('random_element', elements=[
        'Mountain Peak Adventures',
        'Extreme Expeditions',
        'Alpine Adventures',
        'Summit Seekers',
        'Wilderness Trekking'
    ])
    description = factory.Faker('random_element', elements=[
        'Challenging mountain expeditions',
        'Extreme adventure experiences',
        'Professional guided climbing',
        'Multi-day trekking adventures',
        'Wilderness survival experiences'
    ])


class CulturalBusinessFactory(BusinessFactory):
    """Factory for cultural tour businesses."""
    
    name = factory.Faker('random_element', elements=[
        'Heritage Cultural Tours',
        'Ancient Civilizations',
        'Local Traditions Experience',
        'Cultural Immersion Tours',
        'Historical Journeys'
    ])
    description = factory.Faker('random_element', elements=[
        'Authentic cultural experiences',
        'Historical site explorations',
        'Local community interactions',
        'Traditional craft workshops',
        'Heritage preservation tours'
    ])
