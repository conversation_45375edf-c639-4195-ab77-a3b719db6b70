"""
Unit tests for businesses models.
"""
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from businesses.models import Business

User = get_user_model()


class BusinessModelTest(TestCase):
    """Test cases for Business model."""
    
    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
    
    def test_business_creation(self):
        """Test business creation with required fields."""
        business = Business.objects.create(
            name='Test Tours',
            email='<EMAIL>',
            phone='+1234567890',
            address_line1='123 Test St',
            city='Test City',
            state_province='Test State',
            postal_code='12345',
            country='Test Country',
            created_by=self.user
        )
        
        self.assertEqual(business.name, 'Test Tours')
        self.assertEqual(business.email, '<EMAIL>')
        self.assertEqual(business.created_by, self.user)
        self.assertTrue(business.is_active)
        self.assertEqual(business.plan, 'basic')
        self.assertEqual(business.currency, 'USD')
        self.assertEqual(business.timezone, 'UTC')
    
    def test_business_str_representation(self):
        """Test business string representation."""
        business = Business.objects.create(
            name='Amazing Tours',
            email='<EMAIL>',
            phone='+1234567890',
            address_line1='123 Amazing St',
            city='Amazing City',
            state_province='Amazing State',
            postal_code='12345',
            country='Amazing Country',
            created_by=self.user
        )
        
        self.assertEqual(str(business), 'Amazing Tours')
    
    def test_full_address_property(self):
        """Test full_address property."""
        business = Business.objects.create(
            name='Test Tours',
            email='<EMAIL>',
            phone='+1234567890',
            address_line1='123 Test St',
            address_line2='Suite 100',
            city='Test City',
            state_province='Test State',
            postal_code='12345',
            country='Test Country',
            created_by=self.user
        )
        
        expected_address = (
            "123 Test St\n"
            "Suite 100\n"
            "Test City, Test State 12345\n"
            "Test Country"
        )
        self.assertEqual(business.full_address, expected_address)
    
    def test_full_address_without_line2(self):
        """Test full_address property without address line 2."""
        business = Business.objects.create(
            name='Test Tours',
            email='<EMAIL>',
            phone='+1234567890',
            address_line1='123 Test St',
            city='Test City',
            state_province='Test State',
            postal_code='12345',
            country='Test Country',
            created_by=self.user
        )
        
        expected_address = (
            "123 Test St\n"
            "Test City, Test State 12345\n"
            "Test Country"
        )
        self.assertEqual(business.full_address, expected_address)
    
    def test_get_users_method(self):
        """Test get_users method."""
        business = Business.objects.create(
            name='Test Tours',
            email='<EMAIL>',
            phone='+1234567890',
            address_line1='123 Test St',
            city='Test City',
            state_province='Test State',
            postal_code='12345',
            country='Test Country',
            created_by=self.user
        )
        
        # Create users for this business
        user1 = User.objects.create_user(
            username='user1',
            email='<EMAIL>',
            business=business
        )
        user2 = User.objects.create_user(
            username='user2',
            email='<EMAIL>',
            business=business,
            is_active=False  # Inactive user
        )
        
        active_users = business.get_users()
        self.assertIn(user1, active_users)
        self.assertNotIn(user2, active_users)  # Inactive user should not be included
    
    def test_business_manager_active(self):
        """Test BusinessManager active method."""
        active_business = Business.objects.create(
            name='Active Tours',
            email='<EMAIL>',
            phone='+1234567890',
            address_line1='123 Active St',
            city='Active City',
            state_province='Active State',
            postal_code='12345',
            country='Active Country',
            created_by=self.user,
            is_active=True
        )
        
        inactive_business = Business.objects.create(
            name='Inactive Tours',
            email='<EMAIL>',
            phone='+1234567891',
            address_line1='123 Inactive St',
            city='Inactive City',
            state_province='Inactive State',
            postal_code='54321',
            country='Inactive Country',
            created_by=self.user,
            is_active=False
        )
        
        active_businesses = Business.objects.active()
        self.assertIn(active_business, active_businesses)
        self.assertNotIn(inactive_business, active_businesses)
    
    def test_business_audit_fields(self):
        """Test audit fields are set correctly."""
        business = Business.objects.create(
            name='Test Tours',
            email='<EMAIL>',
            phone='+1234567890',
            address_line1='123 Test St',
            city='Test City',
            state_province='Test State',
            postal_code='12345',
            country='Test Country',
            created_by=self.user
        )
        
        self.assertIsNotNone(business.created_at)
        self.assertIsNotNone(business.updated_at)
        self.assertEqual(business.created_by, self.user)
