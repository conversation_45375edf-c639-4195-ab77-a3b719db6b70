{% load i18n cotton %}

<c-layouts.dashboard>
  <c-slot name="title">
    {% trans "Quotes" %}
  </c-slot>

  <c-slot name="main">
    <div class="container mx-auto px-4 py-8">

      <!-- Grid view for quote cards -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        {% for quote in quotes %}
        <c-quote-card :quote="quote" show_actions="True" business_theme="True" />
        {% empty %}
        <div class="col-span-full text-center py-12">
          <div class="text-gray-500">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">{% trans "No quotes" %}</h3>
            <p class="mt-1 text-sm text-gray-500">{% trans "Get started by creating a new quote." %}</p>
            <div class="mt-6">
              <a href="{% url 'quotes:quote-create' %}" class="btn btn-primary">
                {% trans "Create Quote" %}
              </a>
            </div>
          </div>
        </div>
        {% endfor %}
      </div>

      <!-- Pagination -->
      {% if is_paginated %}
      <div class="flex justify-center mt-8">
        <nav class="flex space-x-1">
          {% if page_obj.has_previous %}
          <a href="?page=1" class="pagination-btn">First</a>
          <a href="?page={{ page_obj.previous_page_number }}" class="pagination-btn">Previous</a>
          {% endif %}

          {% for num in page_obj.paginator.page_range %}
          {% if page_obj.number == num %}
          <span class="pagination-btn pagination-current">{{ num }}</span>
          {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
          <a href="?page={{ num }}" class="pagination-btn">{{ num }}</a>
          {% endif %}
          {% endfor %}

          {% if page_obj.has_next %}
          <a href="?page={{ page_obj.next_page_number }}" class="pagination-btn">Next</a>
          <a href="?page={{ page_obj.paginator.num_pages }}" class="pagination-btn">Last</a>
          {% endif %}
        </nav>
      </div>
      {% endif %}
    </div>

    <style>
      .pagination-btn {
        @apply px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 hover:bg-gray-50 hover:text-gray-700;
      }

      .pagination-current {
        @apply bg-blue-50 border-blue-500 text-blue-600;
      }
    </style>
  </c-slot>
</c-layouts.dashboard>
