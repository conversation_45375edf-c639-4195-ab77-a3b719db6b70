# Generated by Django 5.2.4 on 2025-08-12 11:35

import datetime
import django.db.models.deletion
import djmoney.models.fields
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('businesses', '0001_initial'),
        ('clients', '0001_initial'),
        ('quotes', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Invoice',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, help_text='Unique identifier for this record', primary_key=True, serialize=False, unique=True)),
                ('invoice_number', models.CharField(help_text='Unique invoice number', max_length=50, unique=True)),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('sent', 'Sent'), ('paid', 'Paid'), ('overdue', 'Overdue'), ('cancelled', 'Cancelled')], default='draft', help_text='Current status of the invoice', max_length=20)),
                ('issue_date', models.DateField(default=datetime.date.today, verbose_name='Invoice issue date')),
                ('due_date', models.DateField(verbose_name='Invoice due date')),
                ('subtotal_currency', djmoney.models.fields.CurrencyField(choices=[('AUD', 'Australian Dollar'), ('BRL', 'Brazilian Real'), ('GBP', 'British Pound'), ('CAD', 'Canadian Dollar'), ('CNY', 'Chinese Yuan'), ('EUR', 'Euro'), ('INR', 'Indian Rupee'), ('JPY', 'Japanese Yen'), ('MXN', 'Mexican Peso'), ('CHF', 'Swiss Franc'), ('TZS', 'Tanzanian Shilling'), ('USD', 'US Dollar')], default='USD', editable=False, max_length=3)),
                ('subtotal', djmoney.models.fields.MoneyField(decimal_places=2, default_currency='USD', help_text='Subtotal amount before taxes', max_digits=14)),
                ('tax_amount_currency', djmoney.models.fields.CurrencyField(choices=[('AUD', 'Australian Dollar'), ('BRL', 'Brazilian Real'), ('GBP', 'British Pound'), ('CAD', 'Canadian Dollar'), ('CNY', 'Chinese Yuan'), ('EUR', 'Euro'), ('INR', 'Indian Rupee'), ('JPY', 'Japanese Yen'), ('MXN', 'Mexican Peso'), ('CHF', 'Swiss Franc'), ('TZS', 'Tanzanian Shilling'), ('USD', 'US Dollar')], default='USD', editable=False, max_length=3)),
                ('tax_amount', djmoney.models.fields.MoneyField(decimal_places=2, default_currency='USD', help_text='Tax amount', max_digits=14)),
                ('total_amount_currency', djmoney.models.fields.CurrencyField(choices=[('AUD', 'Australian Dollar'), ('BRL', 'Brazilian Real'), ('GBP', 'British Pound'), ('CAD', 'Canadian Dollar'), ('CNY', 'Chinese Yuan'), ('EUR', 'Euro'), ('INR', 'Indian Rupee'), ('JPY', 'Japanese Yen'), ('MXN', 'Mexican Peso'), ('CHF', 'Swiss Franc'), ('TZS', 'Tanzanian Shilling'), ('USD', 'US Dollar')], default='USD', editable=False, max_length=3)),
                ('total_amount', djmoney.models.fields.MoneyField(decimal_places=2, default_currency='USD', help_text='Total amount including taxes', max_digits=14)),
                ('terms_conditions', models.TextField(blank=True, help_text='Terms and conditions for this invoice')),
                ('payment_terms', models.TextField(blank=True, help_text='Payment terms and schedule')),
                ('internal_notes', models.TextField(blank=True, help_text='Internal notes (not visible to client)')),
                ('sent_at', models.DateTimeField(blank=True, help_text='When the invoice was sent to the client', null=True)),
                ('paid_at', models.DateTimeField(blank=True, help_text='When the invoice was paid', null=True)),
                ('business', models.ForeignKey(help_text='Business this invoice belongs to', on_delete=django.db.models.deletion.CASCADE, related_name='invoices', to='businesses.business')),
                ('client', models.ForeignKey(help_text='Client this invoice is for', on_delete=django.db.models.deletion.CASCADE, related_name='invoices', to='clients.client')),
                ('created_by', models.ForeignKey(help_text='User who created this record', on_delete=django.db.models.deletion.PROTECT, related_name='%(class)ss_created', to=settings.AUTH_USER_MODEL)),
                ('quote', models.OneToOneField(help_text='Quote this invoice was created from', on_delete=django.db.models.deletion.PROTECT, related_name='invoice', to='quotes.quote')),
            ],
            options={
                'verbose_name': 'Invoice',
                'verbose_name_plural': 'Invoices',
                'db_table': 'invoices_invoice',
                'indexes': [models.Index(fields=['business', 'status'], name='invoices_in_busines_94d607_idx'), models.Index(fields=['business', 'client'], name='invoices_in_busines_866384_idx'), models.Index(fields=['due_date'], name='invoices_in_due_dat_6cc0ed_idx')],
                'unique_together': {('business', 'invoice_number')},
            },
        ),
    ]
