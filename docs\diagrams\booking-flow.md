```mermaid
flowchart TD
A[User Starts Booking] --> B[Search Tours]
B --> C{Select Criteria}
C -->|Date, Location, Group Size| D[View Available Tours]
D --> E{Select Tour}
E -->|Choose Specific Tour| F[View Tour Details]
F --> G{Continue Booking?}
G -->|Yes| H[Enter Passenger Information]
G -->|No| D
H --> I[Select Add-ons/Extras]
I --> J[Review Booking Summary]
J --> K{Confirm Details?}
K -->|Yes| L[Proceed to Payment]
K -->|No| H
L --> M[Process Payment]
M --> N{Payment Successful?}
N -->|Yes| O[Generate Booking Confirmation]
N -->|No| P[Display Payment Error]
P --> L
O --> Q[Send Email Confirmation]
O --> R[Update Booking Calendar]
O --> S[Save to Database]
Q --> T[User Receives Confirmation]
R --> U[Admin Dashboard Updated]
S --> U
T --> V[Pre-Tour Reminders]
V --> W[Tour Departure]
W --> X[Post-Tour Feedback Request]

    classDef process fill:#e6f7ff,stroke:#1890ff;
    classDef decision fill:#fff7e6,stroke:#fa8c16;
    classDef success fill:#f6ffed,stroke:#52c41a;

    class B,D,F,H,I,J,L,M,V,X process;
    class C,E,G,K,N decision;
    class O,Q,R,S,T,U,W success;
```
