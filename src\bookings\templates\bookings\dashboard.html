{% load static %}

<c-layouts.dashboard>
  <c-slot name="title">
    Bookings Dashboard
  </c-slot>

  <c-slot name="main">
    <div class="container mx-auto px-4 py-8">
      <div class="flex justify-between items-center mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Bookings Dashboard</h1>
        <div class="flex space-x-4">
          <a href="{% url 'bookings:calendar' %}"
            class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors">
            Calendar
          </a>
          <a href="{% url 'bookings:booking_create' %}"
            class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
            New Booking
          </a>
          <a href="{% url 'tours:event_create' %}"
            class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
            New Event
          </a>
          {% if user.is_superuser or user.profile.business or user.created_businesses.exists %}
          <a href="{% url 'data_generator:dashboard' %}"
            class="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors">
            🎲 Generate Data
          </a>
          {% endif %}
        </div>
      </div>

      <!-- Statistics Cards -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center">
            <div class="p-2 bg-blue-100 rounded-lg">
              <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2">
                </path>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Total Bookings</p>
              <p class="text-2xl font-semibold text-gray-900">{{ total_bookings }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center">
            <div class="p-2 bg-yellow-100 rounded-lg">
              <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Pending</p>
              <p class="text-2xl font-semibold text-gray-900">{{ pending_bookings }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center">
            <div class="p-2 bg-green-100 rounded-lg">
              <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Confirmed</p>
              <p class="text-2xl font-semibold text-gray-900">{{ confirmed_bookings }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center">
            <div class="p-2 bg-purple-100 rounded-lg">
              <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1">
                </path>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Total Revenue</p>
              <p class="text-2xl font-semibold text-gray-900">${{ total_revenue|floatformat:2 }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
          <div class="space-y-3">
            <a href="{% url 'bookings:calendar' %}"
              class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
              📅 Calendar View
            </a>
            <a href="{% url 'bookings:booking_list' %}"
              class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
              View All Bookings
            </a>
            <a href="{% url 'bookings:booking_list' %}?status=pending"
              class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
              Pending Bookings
            </a>
            <a href="{% url 'tours:event_list' %}"
              class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
              Manage Events
            </a>
            <a href="{% url 'clients:client-list' %}"
              class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
              Manage Clients
            </a>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Revenue Summary</h3>
          <div class="space-y-3">
            <div class="flex justify-between">
              <span class="text-sm text-gray-600">Total Revenue:</span>
              <span class="text-sm font-medium">${{ total_revenue|floatformat:2 }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-sm text-gray-600">Deposits Collected:</span>
              <span class="text-sm font-medium">${{ deposits_collected|floatformat:2 }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-sm text-gray-600">Completed Bookings:</span>
              <span class="text-sm font-medium">{{ completed_bookings }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-sm text-gray-600">Cancelled Bookings:</span>
              <span class="text-sm font-medium">{{ cancelled_bookings }}</span>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Status Overview</h3>
          <div class="space-y-2">
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <div class="w-3 h-3 bg-yellow-400 rounded-full mr-2"></div>
                <span class="text-sm text-gray-600">Pending</span>
              </div>
              <span class="text-sm font-medium">{{ pending_bookings }}</span>
            </div>
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <div class="w-3 h-3 bg-green-400 rounded-full mr-2"></div>
                <span class="text-sm text-gray-600">Confirmed</span>
              </div>
              <span class="text-sm font-medium">{{ confirmed_bookings }}</span>
            </div>
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <div class="w-3 h-3 bg-blue-400 rounded-full mr-2"></div>
                <span class="text-sm text-gray-600">Completed</span>
              </div>
              <span class="text-sm font-medium">{{ completed_bookings }}</span>
            </div>
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <div class="w-3 h-3 bg-red-400 rounded-full mr-2"></div>
                <span class="text-sm text-gray-600">Cancelled</span>
              </div>
              <span class="text-sm font-medium">{{ cancelled_bookings }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Recent Activity -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div class="bg-white rounded-lg shadow">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Recent Bookings</h3>
          </div>
          <div class="p-6">
            {% if recent_bookings %}
            <div class="space-y-4">
              {% for booking in recent_bookings %}
              <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                  <p class="font-medium text-gray-900">{{ booking.booking_reference }}</p>
                  <p class="text-sm text-gray-600">{{ booking.client.display_name }}</p>
                  <p class="text-xs text-gray-500">{{ booking.booking_date|date:"M d, Y" }}</p>
                </div>
                <div class="text-right">
                  <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                        {% if booking.status == 'pending' %}bg-yellow-100 text-yellow-800
                                        {% elif booking.status == 'confirmed' %}bg-green-100 text-green-800
                                        {% elif booking.status == 'completed' %}bg-blue-100 text-blue-800
                                        {% elif booking.status == 'cancelled' %}bg-red-100 text-red-800
                                        {% endif %}">
                    {{ booking.get_status_display }}
                  </span>
                  <p class="text-sm font-medium text-gray-900 mt-1">${{ booking.total_amount }}</p>
                </div>
              </div>
              {% endfor %}
            </div>
            <div class="mt-4">
              <a href="{% url 'bookings:booking_list' %}" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                View all bookings →
              </a>
            </div>
            {% else %}
            <p class="text-gray-500 text-center py-8">No recent bookings</p>
            {% endif %}
          </div>
        </div>

        <div class="bg-white rounded-lg shadow">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Upcoming Events</h3>
          </div>
          <div class="p-6">
            {% if upcoming_events %}
            <div class="space-y-4">
              {% for event in upcoming_events %}
              <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                  <p class="font-medium text-gray-900">{{ event.event.title }}</p>
                  <p class="text-sm text-gray-600">{{ event.get_event_type_display }}</p>
                  <p class="text-xs text-gray-500">{{ event.event.occurrence.start_time|date:"M d, Y H:i" }}</p>
                </div>
                <div class="text-right">
                  <p class="text-sm text-gray-600">
                    {{ event.current_participants }}/{{ event.max_participants }}</p>
                  {% if event.is_confirmed %}
                  <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                    Confirmed
                  </span>
                  {% else %}
                  <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                    Pending
                  </span>
                  {% endif %}
                </div>
              </div>
              {% endfor %}
            </div>
            <div class="mt-4">
              <a href="{% url 'tours:event_list' %}" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                View all events →
              </a>
            </div>
            {% else %}
            <p class="text-gray-500 text-center py-8">No upcoming events</p>
            {% endif %}
          </div>
        </div>
      </div>
    </div>
  </c-slot>
</c-layouts.dashboard>
