# Generated by Django 5.2.4 on 2025-08-12 11:35

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('businesses', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='PDFTemplate',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, help_text='Unique identifier for this record', primary_key=True, serialize=False, unique=True)),
                ('name', models.CharField(help_text='Template name for identification', max_length=100)),
                ('template_type', models.CharField(choices=[('quote', 'Quote'), ('invoice', 'Invoice'), ('booking_confirmation', 'Booking Confirmation'), ('receipt', 'Receipt'), ('itinerary', 'Itinerary'), ('voucher', 'Voucher')], help_text='Type of document this template generates', max_length=50)),
                ('description', models.TextField(blank=True, help_text='Description of this template')),
                ('html_content', models.TextField(help_text='HTML content for the template')),
                ('css_content', models.TextField(blank=True, help_text='CSS styles for the template')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this template is active and available for use')),
                ('is_default', models.BooleanField(default=False, help_text='Whether this is the default template for this type')),
                ('preview_image', models.ImageField(blank=True, help_text='Preview image of the template', null=True, upload_to='template_previews/')),
                ('page_size', models.CharField(choices=[('A4', 'A4'), ('Letter', 'Letter'), ('Legal', 'Legal')], default='A4', help_text='Page size for PDF generation', max_length=10)),
                ('orientation', models.CharField(choices=[('portrait', 'Portrait'), ('landscape', 'Landscape')], default='portrait', help_text='Page orientation', max_length=10)),
                ('business', models.ForeignKey(blank=True, help_text='Business this template belongs to (null for system templates)', null=True, on_delete=django.db.models.deletion.CASCADE, to='businesses.business')),
                ('created_by', models.ForeignKey(help_text='User who created this record', on_delete=django.db.models.deletion.PROTECT, related_name='%(class)ss_created', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'PDF Template',
                'verbose_name_plural': 'PDF Templates',
                'db_table': 'templates_pdf_template',
                'indexes': [models.Index(fields=['template_type', 'is_active'], name='templates_p_templat_0968b6_idx'), models.Index(fields=['business', 'template_type'], name='templates_p_busines_949373_idx'), models.Index(fields=['is_default', 'template_type'], name='templates_p_is_defa_afb547_idx')],
                'constraints': [models.UniqueConstraint(condition=models.Q(('is_default', True)), fields=('business', 'template_type', 'is_default'), name='unique_default_template_per_business_type')],
            },
        ),
    ]
