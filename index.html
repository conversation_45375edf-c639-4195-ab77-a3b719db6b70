<!DOCTYPE html>
<html lang="en">

  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TourFlow - Complete Tour Business Management SaaS</title>
    <meta name="description"
      content="Streamline your tour business with our comprehensive SaaS platform. Manage clients, create quotes, handle bookings, process payments, and grow your business.">
    <meta name="keywords" content="tour management, booking system, travel business, SaaS, quotes, payments, calendar">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Custom Tailwind Config -->
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: '#1565c0',
              secondary: '#e65100',
              accent: '#2e7d32',
              danger: '#d32f2f'
            }
          }
        }
      }
    </script>

    <!-- Font Awesome for Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom Styles -->
    <style>
      .gradient-bg {
        background: linear-gradient(135deg, #1565c0 0%, #1976d2 100%);
      }

      .feature-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
      }

      .feature-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
      }

      .hero-animation {
        animation: float 6s ease-in-out infinite;
      }

      @keyframes float {

        0%,
        100% {
          transform: translateY(0px);
        }

        50% {
          transform: translateY(-20px);
        }
      }
    </style>
  </head>

  <body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg fixed w-full z-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <div class="flex-shrink-0 flex items-center">
              <i class="fas fa-route text-primary text-2xl mr-2"></i>
              <span class="text-2xl font-bold text-gray-900">TourFlow</span>
            </div>
          </div>
          <div class="hidden md:flex items-center space-x-8">
            <a href="#features" class="text-gray-700 hover:text-primary transition-colors">Features</a>
            <a href="#pricing" class="text-gray-700 hover:text-primary transition-colors">Pricing</a>
            <a href="#demo" class="text-gray-700 hover:text-primary transition-colors">Demo</a>
            <a href="#contact" class="text-gray-700 hover:text-primary transition-colors">Contact</a>
            <button class="bg-primary text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
              Get Started
            </button>
          </div>
          <div class="md:hidden flex items-center">
            <button id="mobile-menu-button" class="text-gray-700 hover:text-primary transition-colors">
              <i class="fas fa-bars text-xl"></i>
            </button>
          </div>
        </div>

        <!-- Mobile Menu -->
        <div id="mobile-menu" class="md:hidden hidden">
          <div class="px-2 pt-2 pb-3 space-y-1 bg-white border-t border-gray-200">
            <a href="#features"
              class="block px-3 py-2 text-gray-700 hover:text-primary hover:bg-gray-50 rounded-md transition-colors">Features</a>
            <a href="#pricing"
              class="block px-3 py-2 text-gray-700 hover:text-primary hover:bg-gray-50 rounded-md transition-colors">Pricing</a>
            <a href="#demo"
              class="block px-3 py-2 text-gray-700 hover:text-primary hover:bg-gray-50 rounded-md transition-colors">Demo</a>
            <a href="#contact"
              class="block px-3 py-2 text-gray-700 hover:text-primary hover:bg-gray-50 rounded-md transition-colors">Contact</a>
            <div class="px-3 py-2">
              <button class="w-full bg-primary text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                Get Started
              </button>
            </div>
          </div>
        </div>
      </div>
    </nav>

    <!-- Hero Section -->
    <section class="gradient-bg pt-20 pb-16 text-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            <h1 class="text-4xl md:text-6xl font-bold mb-6 leading-tight">
              Streamline Your <span class="text-yellow-300">Tour Business</span> Operations
            </h1>
            <p class="text-xl mb-8 text-blue-100">
              Complete SaaS platform for tour operators. Manage clients, create quotes, handle bookings,
              process payments, and grow your business with our all-in-one solution.
            </p>
            <div class="flex flex-col sm:flex-row gap-4">
              <button
                class="bg-yellow-400 text-gray-900 px-8 py-4 rounded-lg font-semibold hover:bg-yellow-300 transition-colors">
                <i class="fas fa-rocket mr-2"></i>
                Start Free Trial
              </button>
              <button
                class="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold hover:bg-white hover:text-primary transition-colors">
                <i class="fas fa-play mr-2"></i>
                Watch Demo
              </button>
            </div>
          </div>
          <div class="hero-animation">
            <div class="bg-white rounded-2xl shadow-2xl p-8 text-gray-900">
              <div class="flex items-center mb-4">
                <div class="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                <div class="w-3 h-3 bg-yellow-500 rounded-full mr-2"></div>
                <div class="w-3 h-3 bg-green-500 rounded-full"></div>
              </div>
              <div class="space-y-4">
                <div class="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                  <span class="text-sm font-medium">Safari Adventure Quote</span>
                  <span class="text-green-600 font-bold">$2,450</span>
                </div>
                <div class="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                  <span class="text-sm font-medium">City Tour Booking</span>
                  <span class="text-blue-600 font-bold">Confirmed</span>
                </div>
                <div class="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                  <span class="text-sm font-medium">Payment Received</span>
                  <span class="text-yellow-600 font-bold">$1,200</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="py-20 bg-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2 class="text-4xl font-bold text-gray-900 mb-4">Everything You Need to Run Your Tour Business</h2>
          <p class="text-xl text-gray-600 max-w-3xl mx-auto">
            From lead generation to payment processing, our platform handles every aspect of your tour business
            operations.
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <!-- Client Management -->
          <div class="feature-card bg-white p-8 rounded-xl shadow-lg border border-gray-100">
            <div class="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center mb-6">
              <i class="fas fa-users text-primary text-2xl"></i>
            </div>
            <h3 class="text-xl font-bold text-gray-900 mb-4">Client Management</h3>
            <p class="text-gray-600 mb-4">
              Organize customer profiles, traveler information, and lead tracking in one centralized system.
            </p>
            <ul class="text-sm text-gray-500 space-y-2">
              <li><i class="fas fa-check text-green-500 mr-2"></i>Customer profiles & history</li>
              <li><i class="fas fa-check text-green-500 mr-2"></i>Traveler documentation</li>
              <li><i class="fas fa-check text-green-500 mr-2"></i>Lead source tracking</li>
            </ul>
          </div>

          <!-- Quote Management -->
          <div class="feature-card bg-white p-8 rounded-xl shadow-lg border border-gray-100">
            <div class="w-16 h-16 bg-green-100 rounded-lg flex items-center justify-center mb-6">
              <i class="fas fa-file-invoice-dollar text-accent text-2xl"></i>
            </div>
            <h3 class="text-xl font-bold text-gray-900 mb-4">Smart Quotes</h3>
            <p class="text-gray-600 mb-4">
              Create professional quotes with public links, approval workflows, and automated follow-ups.
            </p>
            <ul class="text-sm text-gray-500 space-y-2">
              <li><i class="fas fa-check text-green-500 mr-2"></i>Public quote access</li>
              <li><i class="fas fa-check text-green-500 mr-2"></i>Multi-currency support</li>
              <li><i class="fas fa-check text-green-500 mr-2"></i>Approval tracking</li>
            </ul>
          </div>

          <!-- Booking System -->
          <div class="feature-card bg-white p-8 rounded-xl shadow-lg border border-gray-100">
            <div class="w-16 h-16 bg-yellow-100 rounded-lg flex items-center justify-center mb-6">
              <i class="fas fa-calendar-check text-secondary text-2xl"></i>
            </div>
            <h3 class="text-xl font-bold text-gray-900 mb-4">Booking Management</h3>
            <p class="text-gray-600 mb-4">
              Handle reservations, scheduling, and participant management with integrated calendar system.
            </p>
            <ul class="text-sm text-gray-500 space-y-2">
              <li><i class="fas fa-check text-green-500 mr-2"></i>Calendar integration</li>
              <li><i class="fas fa-check text-green-500 mr-2"></i>Participant tracking</li>
              <li><i class="fas fa-check text-green-500 mr-2"></i>Check-in system</li>
            </ul>
          </div>

          <!-- Payment Processing -->
          <div class="feature-card bg-white p-8 rounded-xl shadow-lg border border-gray-100">
            <div class="w-16 h-16 bg-purple-100 rounded-lg flex items-center justify-center mb-6">
              <i class="fas fa-credit-card text-purple-600 text-2xl"></i>
            </div>
            <h3 class="text-xl font-bold text-gray-900 mb-4">Payment Processing</h3>
            <p class="text-gray-600 mb-4">
              Secure payment links, automated invoicing, and real-time payment tracking with webhooks.
            </p>
            <ul class="text-sm text-gray-500 space-y-2">
              <li><i class="fas fa-check text-green-500 mr-2"></i>Secure payment links</li>
              <li><i class="fas fa-check text-green-500 mr-2"></i>Automated invoicing</li>
              <li><i class="fas fa-check text-green-500 mr-2"></i>Real-time tracking</li>
            </ul>
          </div>

          <!-- Document Generation -->
          <div class="feature-card bg-white p-8 rounded-xl shadow-lg border border-gray-100">
            <div class="w-16 h-16 bg-red-100 rounded-lg flex items-center justify-center mb-6">
              <i class="fas fa-file-pdf text-danger text-2xl"></i>
            </div>
            <h3 class="text-xl font-bold text-gray-900 mb-4">Document Generation</h3>
            <p class="text-gray-600 mb-4">
              Professional PDF documents with your branding for quotes, invoices, and receipts.
            </p>
            <ul class="text-sm text-gray-500 space-y-2">
              <li><i class="fas fa-check text-green-500 mr-2"></i>Branded templates</li>
              <li><i class="fas fa-check text-green-500 mr-2"></i>PDF generation</li>
              <li><i class="fas fa-check text-green-500 mr-2"></i>Email delivery</li>
            </ul>
          </div>

          <!-- Multi-Tenant Security -->
          <div class="feature-card bg-white p-8 rounded-xl shadow-lg border border-gray-100">
            <div class="w-16 h-16 bg-indigo-100 rounded-lg flex items-center justify-center mb-6">
              <i class="fas fa-shield-alt text-indigo-600 text-2xl"></i>
            </div>
            <h3 class="text-xl font-bold text-gray-900 mb-4">Enterprise Security</h3>
            <p class="text-gray-600 mb-4">
              Role-based access control, data isolation, and comprehensive audit trails for compliance.
            </p>
            <ul class="text-sm text-gray-500 space-y-2">
              <li><i class="fas fa-check text-green-500 mr-2"></i>Role-based access</li>
              <li><i class="fas fa-check text-green-500 mr-2"></i>Data isolation</li>
              <li><i class="fas fa-check text-green-500 mr-2"></i>Audit logging</li>
            </ul>
          </div>
        </div>
      </div>
    </section>

    <!-- Workflow Section -->
    <section class="py-20 bg-gray-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2 class="text-4xl font-bold text-gray-900 mb-4">Simple, Streamlined Workflow</h2>
          <p class="text-xl text-gray-600">From inquiry to completion in just a few steps</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div class="text-center">
            <div class="w-20 h-20 bg-primary rounded-full flex items-center justify-center mx-auto mb-4">
              <span class="text-white text-2xl font-bold">1</span>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Create Quote</h3>
            <p class="text-gray-600">Build professional quotes with pricing and terms</p>
          </div>

          <div class="text-center">
            <div class="w-20 h-20 bg-accent rounded-full flex items-center justify-center mx-auto mb-4">
              <span class="text-white text-2xl font-bold">2</span>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Client Approval</h3>
            <p class="text-gray-600">Secure public links for client review and approval</p>
          </div>

          <div class="text-center">
            <div class="w-20 h-20 bg-secondary rounded-full flex items-center justify-center mx-auto mb-4">
              <span class="text-white text-2xl font-bold">3</span>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Process Payment</h3>
            <p class="text-gray-600">Automated invoicing and secure payment processing</p>
          </div>

          <div class="text-center">
            <div class="w-20 h-20 bg-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <span class="text-white text-2xl font-bold">4</span>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Deliver Tour</h3>
            <p class="text-gray-600">Manage bookings and deliver exceptional experiences</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Stats Section -->
    <section class="py-16 bg-primary text-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
          <div>
            <div class="text-4xl font-bold mb-2">500+</div>
            <div class="text-blue-200">Tour Operators</div>
          </div>
          <div>
            <div class="text-4xl font-bold mb-2">50K+</div>
            <div class="text-blue-200">Bookings Processed</div>
          </div>
          <div>
            <div class="text-4xl font-bold mb-2">$2M+</div>
            <div class="text-blue-200">Revenue Managed</div>
          </div>
          <div>
            <div class="text-4xl font-bold mb-2">99.9%</div>
            <div class="text-blue-200">Uptime</div>
          </div>
        </div>
      </div>
    </section>

    <!-- Pricing Section -->
    <section id="pricing" class="py-20 bg-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2 class="text-4xl font-bold text-gray-900 mb-4">Simple, Transparent Pricing</h2>
          <p class="text-xl text-gray-600">Choose the plan that fits your business size</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
          <!-- Starter Plan -->
          <div class="bg-white rounded-xl shadow-lg border border-gray-200 p-8">
            <div class="text-center">
              <h3 class="text-2xl font-bold text-gray-900 mb-4">Starter</h3>
              <div class="text-4xl font-bold text-primary mb-2">$29</div>
              <div class="text-gray-600 mb-8">per month</div>
            </div>
            <ul class="space-y-4 mb-8">
              <li class="flex items-center">
                <i class="fas fa-check text-green-500 mr-3"></i>
                <span>Up to 50 bookings/month</span>
              </li>
              <li class="flex items-center">
                <i class="fas fa-check text-green-500 mr-3"></i>
                <span>Basic client management</span>
              </li>
              <li class="flex items-center">
                <i class="fas fa-check text-green-500 mr-3"></i>
                <span>Quote generation</span>
              </li>
              <li class="flex items-center">
                <i class="fas fa-check text-green-500 mr-3"></i>
                <span>Payment processing</span>
              </li>
              <li class="flex items-center">
                <i class="fas fa-check text-green-500 mr-3"></i>
                <span>Email support</span>
              </li>
            </ul>
            <button
              class="w-full bg-gray-100 text-gray-900 py-3 rounded-lg font-semibold hover:bg-gray-200 transition-colors">
              Start Free Trial
            </button>
          </div>

          <!-- Professional Plan -->
          <div class="bg-primary text-white rounded-xl shadow-lg border-2 border-primary p-8 relative">
            <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
              <span class="bg-yellow-400 text-gray-900 px-4 py-1 rounded-full text-sm font-semibold">Most Popular</span>
            </div>
            <div class="text-center">
              <h3 class="text-2xl font-bold mb-4">Professional</h3>
              <div class="text-4xl font-bold mb-2">$79</div>
              <div class="text-blue-200 mb-8">per month</div>
            </div>
            <ul class="space-y-4 mb-8">
              <li class="flex items-center">
                <i class="fas fa-check text-yellow-400 mr-3"></i>
                <span>Up to 200 bookings/month</span>
              </li>
              <li class="flex items-center">
                <i class="fas fa-check text-yellow-400 mr-3"></i>
                <span>Advanced client management</span>
              </li>
              <li class="flex items-center">
                <i class="fas fa-check text-yellow-400 mr-3"></i>
                <span>Multi-currency support</span>
              </li>
              <li class="flex items-center">
                <i class="fas fa-check text-yellow-400 mr-3"></i>
                <span>Calendar integration</span>
              </li>
              <li class="flex items-center">
                <i class="fas fa-check text-yellow-400 mr-3"></i>
                <span>Priority support</span>
              </li>
              <li class="flex items-center">
                <i class="fas fa-check text-yellow-400 mr-3"></i>
                <span>Custom branding</span>
              </li>
            </ul>
            <button
              class="w-full bg-yellow-400 text-gray-900 py-3 rounded-lg font-semibold hover:bg-yellow-300 transition-colors">
              Start Free Trial
            </button>
          </div>

          <!-- Enterprise Plan -->
          <div class="bg-white rounded-xl shadow-lg border border-gray-200 p-8">
            <div class="text-center">
              <h3 class="text-2xl font-bold text-gray-900 mb-4">Enterprise</h3>
              <div class="text-4xl font-bold text-primary mb-2">$199</div>
              <div class="text-gray-600 mb-8">per month</div>
            </div>
            <ul class="space-y-4 mb-8">
              <li class="flex items-center">
                <i class="fas fa-check text-green-500 mr-3"></i>
                <span>Unlimited bookings</span>
              </li>
              <li class="flex items-center">
                <i class="fas fa-check text-green-500 mr-3"></i>
                <span>Multi-user accounts</span>
              </li>
              <li class="flex items-center">
                <i class="fas fa-check text-green-500 mr-3"></i>
                <span>Advanced analytics</span>
              </li>
              <li class="flex items-center">
                <i class="fas fa-check text-green-500 mr-3"></i>
                <span>API access</span>
              </li>
              <li class="flex items-center">
                <i class="fas fa-check text-green-500 mr-3"></i>
                <span>24/7 phone support</span>
              </li>
              <li class="flex items-center">
                <i class="fas fa-check text-green-500 mr-3"></i>
                <span>Custom integrations</span>
              </li>
            </ul>
            <button
              class="w-full bg-gray-100 text-gray-900 py-3 rounded-lg font-semibold hover:bg-gray-200 transition-colors">
              Contact Sales
            </button>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="py-20 gradient-bg text-white">
      <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
        <h2 class="text-4xl font-bold mb-6">Ready to Transform Your Tour Business?</h2>
        <p class="text-xl text-blue-100 mb-8">
          Join hundreds of tour operators who have streamlined their operations and increased revenue with TourFlow.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <button
            class="bg-yellow-400 text-gray-900 px-8 py-4 rounded-lg font-semibold hover:bg-yellow-300 transition-colors">
            <i class="fas fa-rocket mr-2"></i>
            Start Your Free Trial
          </button>
          <button
            class="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold hover:bg-white hover:text-primary transition-colors">
            <i class="fas fa-calendar mr-2"></i>
            Schedule Demo
          </button>
        </div>
        <p class="text-sm text-blue-200 mt-4">No credit card required • 14-day free trial • Cancel anytime</p>
      </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div>
            <div class="flex items-center mb-4">
              <i class="fas fa-route text-primary text-2xl mr-2"></i>
              <span class="text-2xl font-bold">TourFlow</span>
            </div>
            <p class="text-gray-400 mb-4">
              Complete tour business management platform designed for modern tour operators.
            </p>
            <div class="flex space-x-4">
              <a href="#" class="text-gray-400 hover:text-white transition-colors">
                <i class="fab fa-twitter text-xl"></i>
              </a>
              <a href="#" class="text-gray-400 hover:text-white transition-colors">
                <i class="fab fa-linkedin text-xl"></i>
              </a>
              <a href="#" class="text-gray-400 hover:text-white transition-colors">
                <i class="fab fa-facebook text-xl"></i>
              </a>
            </div>
          </div>

          <div>
            <h3 class="text-lg font-semibold mb-4">Product</h3>
            <ul class="space-y-2 text-gray-400">
              <li><a href="#" class="hover:text-white transition-colors">Features</a></li>
              <li><a href="#" class="hover:text-white transition-colors">Pricing</a></li>
              <li><a href="#" class="hover:text-white transition-colors">API</a></li>
              <li><a href="#" class="hover:text-white transition-colors">Integrations</a></li>
            </ul>
          </div>

          <div>
            <h3 class="text-lg font-semibold mb-4">Support</h3>
            <ul class="space-y-2 text-gray-400">
              <li><a href="docs/" class="hover:text-white transition-colors">Documentation</a></li>
              <li><a href="#" class="hover:text-white transition-colors">Help Center</a></li>
              <li><a href="#contact" class="hover:text-white transition-colors">Contact Us</a></li>
              <li><a href="#" class="hover:text-white transition-colors">Status</a></li>
            </ul>
          </div>

          <div>
            <h3 class="text-lg font-semibold mb-4">Company</h3>
            <ul class="space-y-2 text-gray-400">
              <li><a href="#" class="hover:text-white transition-colors">About</a></li>
              <li><a href="#" class="hover:text-white transition-colors">Blog</a></li>
              <li><a href="#" class="hover:text-white transition-colors">Careers</a></li>
              <li><a href="#" class="hover:text-white transition-colors">Privacy</a></li>
            </ul>
          </div>
        </div>

        <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
          <p>&copy; 2025 TourFlow. All rights reserved. Built with Django and modern web technologies.</p>
        </div>
      </div>
    </footer>

    <!-- JavaScript for Mobile Menu and Smooth Scrolling -->
    <script>
      // Mobile menu functionality
      document.addEventListener('DOMContentLoaded', function () {
        const mobileMenuButton = document.getElementById('mobile-menu-button');
        const mobileMenu = document.getElementById('mobile-menu');
        const menuIcon = mobileMenuButton.querySelector('i');

        mobileMenuButton.addEventListener('click', function () {
          // Toggle menu visibility
          mobileMenu.classList.toggle('hidden');

          // Toggle icon between hamburger and X
          if (mobileMenu.classList.contains('hidden')) {
            menuIcon.classList.remove('fa-times');
            menuIcon.classList.add('fa-bars');
          } else {
            menuIcon.classList.remove('fa-bars');
            menuIcon.classList.add('fa-times');
          }
        });

        // Close mobile menu when clicking on a link
        const mobileMenuLinks = mobileMenu.querySelectorAll('a');
        mobileMenuLinks.forEach(link => {
          link.addEventListener('click', function () {
            mobileMenu.classList.add('hidden');
            menuIcon.classList.remove('fa-times');
            menuIcon.classList.add('fa-bars');
          });
        });

        // Close mobile menu when clicking outside
        document.addEventListener('click', function (event) {
          const isClickInsideMenu = mobileMenu.contains(event.target);
          const isClickOnButton = mobileMenuButton.contains(event.target);

          if (!isClickInsideMenu && !isClickOnButton && !mobileMenu.classList.contains('hidden')) {
            mobileMenu.classList.add('hidden');
            menuIcon.classList.remove('fa-times');
            menuIcon.classList.add('fa-bars');
          }
        });
      });

      // Smooth scrolling for navigation links
      document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
          e.preventDefault();
          const target = document.querySelector(this.getAttribute('href'));
          if (target) {
            target.scrollIntoView({
              behavior: 'smooth',
              block: 'start'
            });
          }
        });
      });

      // Add scroll effect to navigation
      window.addEventListener('scroll', function () {
        const nav = document.querySelector('nav');
        if (window.scrollY > 100) {
          nav.classList.add('bg-white/95', 'backdrop-blur-sm');
        } else {
          nav.classList.remove('bg-white/95', 'backdrop-blur-sm');
        }
      });
    </script>
  </body>

</html>
