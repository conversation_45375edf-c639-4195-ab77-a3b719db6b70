"""
Tests for payments app using pytest.
"""

from datetime import timedelta

import pytest
from django.contrib.auth import get_user_model
from django.utils import timezone
from djmoney.money import Money

from businesses.models import Business
from clients.models import Client
from invoices.models import Invoice

from .models import Payment, PaymentLink, Subscription

User = get_user_model()


@pytest.mark.django_db
class TestPaymentModel:
    """Test Payment model functionality."""

    def setup_method(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            username="testuser", email="<EMAIL>", password="testpass123"
        )

        self.business = Business.objects.create(
            name="Test Business",
            email="<EMAIL>",
            phone="+1234567890",
            created_by=self.user,
        )

        self.test_client = Client.objects.create(
            business=self.business,
            first_name="John",
            last_name="Doe",
            email="<EMAIL>",
            created_by=self.user,
        )

        self.invoice = Invoice.objects.create(
            business=self.business,
            client=self.test_client,
            invoice_number="INV-001",
            subtotal=Money(100, "USD"),
            tax_amount=Money(10, "USD"),
            total_amount=Money(110, "USD"),
            issue_date=timezone.now().date(),
            due_date=timezone.now().date() + timedelta(days=30),
            created_by=self.user,
        )

    def test_payment_creation(self):
        """Test payment creation."""
        payment = Payment.objects.create(
            invoice=self.invoice,
            amount=Money(110, "USD"),
            payment_method=Payment.PaymentMethod.LEMON_SQUEEZY,
            status=Payment.Status.COMPLETED,
            created_by=self.user,
        )

        self.assertEqual(payment.invoice, self.invoice)
        self.assertEqual(payment.amount, Money(110, "USD"))
        self.assertEqual(payment.status, Payment.Status.COMPLETED)
        self.assertEqual(payment.business, self.business)
        self.assertEqual(payment.client, self.test_client)

    def test_payment_mark_as_completed(self):
        """Test marking payment as completed."""
        payment = Payment.objects.create(
            invoice=self.invoice,
            amount=Money(110, "USD"),
            payment_method=Payment.PaymentMethod.LEMON_SQUEEZY,
            status=Payment.Status.PENDING,
            created_by=self.user,
        )

        payment.mark_as_completed()

        self.assertEqual(payment.status, Payment.Status.COMPLETED)
        self.assertIsNotNone(payment.payment_date)

        # Refresh invoice from database
        self.invoice.refresh_from_db()
        self.assertEqual(self.invoice.status, "paid")


class PaymentLinkModelTest(TestCase):
    """Test PaymentLink model functionality."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            username="testuser", email="<EMAIL>", password="testpass123"
        )

        self.business = Business.objects.create(
            name="Test Business",
            email="<EMAIL>",
            phone="+1234567890",
            created_by=self.user,
        )

        self.test_client = Client.objects.create(
            business=self.business,
            first_name="John",
            last_name="Doe",
            email="<EMAIL>",
            created_by=self.user,
        )

        self.invoice = Invoice.objects.create(
            business=self.business,
            client=self.test_client,
            invoice_number="INV-001",
            subtotal=Money(100, "USD"),
            tax_amount=Money(10, "USD"),
            total_amount=Money(110, "USD"),
            issue_date=timezone.now().date(),
            due_date=timezone.now().date() + timedelta(days=30),
            status="sent",
            created_by=self.user,
        )

    def test_payment_link_creation(self):
        """Test payment link creation."""
        payment_link = PaymentLink.objects.create(
            invoice=self.invoice,
            expires_at=timezone.now() + timedelta(days=30),
            created_by=self.user,
        )

        self.assertEqual(payment_link.invoice, self.invoice)
        self.assertEqual(payment_link.status, PaymentLink.Status.ACTIVE)
        self.assertIsNotNone(payment_link.link_id)

    def test_payment_link_is_active(self):
        """Test payment link active status."""
        payment_link = PaymentLink.objects.create(
            invoice=self.invoice,
            expires_at=timezone.now() + timedelta(days=30),
            created_by=self.user,
        )

        self.assertTrue(payment_link.is_active)

        # Test expired link
        payment_link.expires_at = timezone.now() - timedelta(days=1)
        payment_link.save()

        self.assertFalse(payment_link.is_active)


class SubscriptionModelTest(TestCase):
    """Test Subscription model functionality."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            username="testuser", email="<EMAIL>", password="testpass123"
        )

        self.business = Business.objects.create(
            name="Test Business",
            email="<EMAIL>",
            phone="+1234567890",
            created_by=self.user,
        )

    def test_subscription_creation(self):
        """Test subscription creation."""
        subscription = Subscription.objects.create(
            business=self.business,
            plan=Subscription.Plan.PROFESSIONAL,
            status=Subscription.Status.ACTIVE,
            monthly_price=Money(79, "USD"),
            current_period_start=timezone.now(),
            current_period_end=timezone.now() + timedelta(days=30),
        )

        self.assertEqual(subscription.business, self.business)
        self.assertEqual(subscription.plan, Subscription.Plan.PROFESSIONAL)
        self.assertEqual(subscription.status, Subscription.Status.ACTIVE)
        self.assertTrue(subscription.is_active)

    def test_subscription_trial(self):
        """Test subscription trial functionality."""
        subscription = Subscription.objects.create(
            business=self.business,
            plan=Subscription.Plan.BASIC,
            status=Subscription.Status.ACTIVE,
            trial_end=timezone.now() + timedelta(days=14),
            current_period_start=timezone.now(),
            current_period_end=timezone.now() + timedelta(days=30),
        )

        self.assertTrue(subscription.is_trial)

        # Test expired trial
        subscription.trial_end = timezone.now() - timedelta(days=1)
        subscription.save()

        self.assertFalse(subscription.is_trial)
