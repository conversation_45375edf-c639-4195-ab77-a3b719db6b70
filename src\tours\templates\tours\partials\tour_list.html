  <div class="grid grid-cols-1 md:grid-cols-2 gap-4" id="tour-list">
    {% for tour in tours %}
    <div class="bg-white border rounded-lg overflow-hidden hover:shadow-md transition-shadow">
      <div class="p-4">
        <div class="flex justify-between">
          <h3 class="text-lg font-medium text-gray-900">{{ tour.name }}</h3>
          {% if tour.low_demand_alert %}
          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
            Low demand
          </span>
          {% endif %}
        </div>

        <div class="mt-2 flex items-center">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
              <path
                d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z">
              </path>
            </svg>
          </div>
          <p class="ml-1 text-sm text-gray-600">{{ tour.avg_rating }} ({{ tour.review_count }})</p>
        </div>

        <div class="mt-3 flex justify-between items-center">
          <div>
            <p class="text-sm text-gray-500">From <span class="font-medium text-gray-900">${{ tour.base_price }}</span>
            </p>
            <p class="text-sm text-gray-500">Group Size: {{ tour.max_group_size }} (Max)</p>
            <p class="text-xs text-gray-400 mt-1">🕘 {{ tour.timezone }}</p>
          </div>
          <button hx-post="" hx-vals='{"tour_id": "{{ tour.id }}"}'
            class="px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
            Select
          </button>
        </div>
      </div>
    </div>
    {% endfor %}
  </div>
