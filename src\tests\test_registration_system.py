"""
Comprehensive tests for the registration and user management system using pytest.
"""

import pytest
from django.contrib.auth import get_user_model
from django.test import Client
from django.urls import reverse

from accounts.models import Profile
from businesses.models import Business

User = get_user_model()


@pytest.mark.django_db
class TestRegistrationFlow:
    """Test the complete registration flow."""

    def setup_method(self):
        """Set up test data."""
        self.client = Client()
        self.registration_data = {
            'username': 'testuser',
            'email': '<EMAIL>',
            'password1': 'testpassword123',
            'password2': 'testpassword123',
        }

    def test_registration_creates_inactive_user(self):
        """Test that registration creates an inactive user."""
        response = self.client.post(
            reverse('accounts:register'), self.registration_data
        )

        # Should redirect after successful registration
        assert response.status_code == 302

        # User should be created but inactive
        user = User.objects.get(username='testuser')
        assert not user.is_active
        assert not user.email_confirmed
        assert user.profile.role == Profile.Role.CUSTOMER  # Initial role
        assert user.email_confirmation_token is not None

    def test_email_confirmation_activates_user(self):
        """Test that email confirmation activates the user and sets up business."""
        # First register the user
        self.client.post(reverse('accounts:register'), self.registration_data)
        user = User.objects.get(username='testuser')

        # Confirm email
        token = user.email_confirmation_token
        confirmation_url = reverse(
            'accounts:confirm_email', kwargs={'token': str(token)}
        )
        response = self.client.get(confirmation_url)

        # Should redirect after confirmation
        assert response.status_code == 302

        # User should now be active and confirmed
        user.refresh_from_db()
        assert user.is_active
        assert user.email_confirmed
        assert user.profile.role == Profile.Role.BUSINESS_OWNER

        # Profile should be created
        assert hasattr(user, 'profile')
        profile = user.profile
        assert profile is not None

        # Business should be created
        assert user.profile.business is not None
        business = user.profile.business
        assert business.created_by == user
        assert business.email == user.email

    def test_invalid_confirmation_token(self):
        """Test that invalid confirmation tokens are handled properly."""
        # Try to confirm with invalid token
        invalid_token = '********-1234-1234-1234-**********12'
        confirmation_url = reverse(
            'accounts:confirm_email', kwargs={'token': invalid_token}
        )
        response = self.client.get(confirmation_url)

        # Should redirect to register page
        assert response.status_code == 302
        assert response.url == reverse('accounts:register')


@pytest.mark.django_db
class TestUserManagement:
    """Test user management functionality."""

    def setup_method(self):
        """Set up test data."""
        self.client = Client()
        self.business_owner = User.objects.create_user(
            username='owner',
            email='<EMAIL>',
            password='testpass123',
            role=Profile.Role.BUSINESS_OWNER,
            is_active=True,
            email_confirmed=True,
        )
        self.business = Business.objects.create(
            name='Test Business',
            email=self.business_owner.email,
            created_by=self.business_owner,
        )
        self.business_owner.business = self.business
        self.business_owner.save()

    def test_add_user_with_password(self):
        """Test adding a user with a password set."""
        self.client.login(username='owner', password='testpass123')

        user_data = {
            'username': 'newuser',
            'email': '<EMAIL>',
            'role': Profile.Role.AGENT,
            'password1': 'newpassword123',
            'password2': 'newpassword123',
        }

        response = self.client.post(reverse('accounts:add_user'), user_data)

        # Should redirect to user management
        assert response.status_code == 302
        assert response.url == reverse('accounts:user_management')

        # User should be created and active
        new_user = User.objects.get(username='newuser')
        assert new_user.is_active
        assert new_user.email_confirmed
        assert new_user.business == self.business
        assert new_user.role == Profile.Role.AGENT


@pytest.mark.django_db
class TestOnboardingFlow:
    """Test the onboarding process."""

    def setup_method(self):
        """Set up test data."""
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            is_active=True,
            email_confirmed=True,
            role=Profile.Role.BUSINESS_OWNER,
        )
        self.business = Business.objects.create(
            name='Test Business', email=self.user.email, created_by=self.user
        )
        self.user.profile.business = self.business
        self.user.save()

    def test_onboarding_profile_completion(self):
        """Test profile completion during onboarding."""
        self.client.login(username='testuser', password='testpass123')

        profile_data = {
            'first_name': 'Test',
            'last_name': 'User',
            'phone': '+**********',
            'address_line1': '123 Test St',
            'city': 'Test City',
            'country': 'Test Country',
        }

        response = self.client.post(
            reverse('accounts:onboarding_profile'), profile_data
        )

        # Should redirect to business setup
        assert response.status_code == 302
        assert response.url == reverse('accounts:onboarding_business')

        # User should have profile completed
        self.user.refresh_from_db()
        assert self.user.profile_completed


@pytest.mark.django_db
class TestProfileManagement:
    """Test profile management functionality."""

    def setup_method(self):
        """Set up test data."""
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            is_active=True,
        )
        self.profile = Profile.objects.create(user=self.user)

    def test_profile_completion_check(self):
        """Test profile completion validation."""
        # Incomplete profile
        assert not self.profile.is_complete()

        # Complete profile
        self.profile.first_name = 'Test'
        self.profile.last_name = 'User'
        self.profile.phone = '+**********'
        self.profile.address_line1 = '123 Test St'
        self.profile.city = 'Test City'
        self.profile.country = 'Test Country'
        self.profile.save()

        assert self.profile.is_complete()


@pytest.mark.django_db
class TestUserInvitation:
    """Test user invitation system."""

    def setup_method(self):
        """Set up test data."""
        self.client = Client()
        self.business = Business.objects.create(
            name='Test Business', email='<EMAIL>'
        )
        self.invited_user = User.objects.create(
            username='invited',
            email='<EMAIL>',
            role=Profile.Role.AGENT,
            is_active=False,
            email_confirmed=False,
            business=self.business,
        )
        self.token = self.invited_user.generate_confirmation_token()

    def test_invitation_acceptance(self):
        """Test accepting an invitation and setting password."""
        invitation_url = reverse(
            'accounts:user_invitation', kwargs={'token': str(self.token)}
        )

        # GET should show the invitation form
        response = self.client.get(invitation_url)
        assert response.status_code == 200

        # POST should set password and activate user
        password_data = {
            'password1': 'newpassword123',
            'password2': 'newpassword123',
        }

        response = self.client.post(invitation_url, password_data)

        # Should redirect to onboarding
        assert response.status_code == 302
        assert response.url == reverse('accounts:onboarding_profile')

        # User should be activated
        self.invited_user.refresh_from_db()
        assert self.invited_user.is_active
        assert self.invited_user.email_confirmed
        assert self.invited_user.check_password('newpassword123')
