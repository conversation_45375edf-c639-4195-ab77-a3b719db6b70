// Datastar v1.0.0-RC.4
var tt=/🖕JS_DS🚀/.source,Te=tt.slice(0,5),Ie=tt.slice(4),P="datastar",nt="Datastar-Request",it=1e3;var rt=!1,Re="outer",ot="inner",st="remove",Ne="replace",at="prepend",ct="append",lt="before",ut="after",ft=Re,ce="datastar-patch-elements",le="datastar-patch-signals";function Se(e){return e instanceof HTMLElement||e instanceof SVGElement}var Z=e=>e!==null&&typeof e=="object"&&(Object.getPrototypeOf(e)===Object.prototype||Object.getPrototypeOf(e)===null);function Ae(e){for(let t in e)if(Object.hasOwn(e,t))return!1;return!0}function ue(e,t){for(let n in e){let i=e[n];Z(i)||Array.isArray(i)?ue(i,t):e[n]=t(i)}}var E=(e,t)=>{for(let n in t){let i=n.split("."),r=i.pop(),s=i.reduce((o,a)=>o[a]??={},e);s[r]=t[n]}return e};var dt=e=>e.trim()==="true",R=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").replace(/([a-z])([0-9]+)/gi,"$1-$2").replace(/([0-9]+)([a-z])/gi,"$1-$2").toLowerCase(),Y=e=>R(e).replace(/-./g,t=>t[1].toUpperCase()),fe=e=>R(e).replace(/-/g,"_"),Mn=e=>Y(e).replace(/(^.|(?<=\.).)/g,t=>t[0].toUpperCase()),X=e=>{try{return JSON.parse(e)}catch{return Function(`return (${e})`)()}},wn={kebab:R,snake:fe,pascal:Mn};function x(e,t){for(let n of t.get("case")||[]){let i=wn[n];i&&(e=i(e))}return e}var Cn="https://data-star.dev/errors";function pt(e,t,n={}){let i=new Error;i.name=`${P} ${e} error`;let r=fe(t),s=new URLSearchParams({metadata:JSON.stringify(n)}).toString(),o=JSON.stringify(n,null,2);return i.message=`${t}
More info: ${Cn}/${e}/${r}?${s}
Context: ${o}`,i}function Ve(e,t,n={}){let i={plugin:{name:e.plugin.name,type:e.plugin.type}};return pt("init",t,Object.assign(i,n))}function mt(e,t,n={}){let i={plugin:{name:e.plugin.name,type:e.plugin.type},element:{id:e.el.id,tag:e.el.tagName},expression:{rawKey:e.rawKey,key:e.key,value:e.value,fnContent:e.fnContent}};return pt("runtime",t,Object.assign(i,n))}var W=`${P}-signal-patch`;var xe={},qe=[],Fe=0,Me=0,je=0,N,me=()=>{Fe++},ge=()=>{--Fe||(Rt(),$())},ee=e=>On.bind(0,{previousValue:e,t:e,e:1}),Ge=Symbol("computed"),bt=e=>{let t=Ln.bind(0,{e:17,getter:e});return t[Ge]=1,t},Et=e=>{let t={d:e,e:2};N&&Ke(t,N);let n=V(t);me();try{t.d()}finally{ge(),V(n)}return Mt.bind(0,t)},Tt=e=>{let t=V(void 0);try{return e()}finally{V(t)}},Rt=()=>{for(;Me<je;){let e=qe[Me];qe[Me++]=void 0,xt(e,e.e&=-65)}Me=0,je=0},gt=e=>"getter"in e?St(e):At(e,e.t),V=e=>{let t=N;return N=e,t},St=e=>{let t=V(e);wt(e);try{let n=e.t;return n!==(e.t=e.getter(n))}finally{V(t),Ct(e)}},At=(e,t)=>(e.e=1,e.previousValue!==(e.previousValue=t)),We=e=>{let t=e.e;if(!(t&64)){e.e=t|64;let n=e.o;n?We(n.s):qe[je++]=e}},xt=(e,t)=>{if(t&16||t&32&&Lt(e.r,e)){let i=V(e);wt(e),me();try{e.d()}finally{ge(),V(i),Ct(e)}return}t&32&&(e.e=t&-33);let n=e.r;for(;n;){let i=n.c,r=i.e;r&64&&xt(i,i.e=r&-65),n=n.n}},Ln=e=>{let t=e.e;if(t&16||t&32&&Lt(e.r,e)){if(St(e)){let n=e.o;n&&Oe(n)}}else t&32&&(e.e=t&-33);return N&&Ke(e,N),e.t},On=(e,...t)=>{if(t.length){let i=t[0];if(e.t!==(e.t=i)){e.e=17;let r=e.o;return r&&(Pn(r),Fe||Rt()),!0}return!1}let n=e.t;if(e.e&16&&At(e,n)){let i=e.o;i&&Oe(i)}return N&&Ke(e,N),n},Mt=e=>{let t=e.r;for(;t;)t=Le(t,e);let n=e.o;n&&Le(n),e.e=0},Ke=(e,t)=>{let n=t.a;if(n&&n.c===e)return;let i,r=t.e&4;if(r&&(i=n?n.n:t.r,i&&i.c===e)){t.a=i;return}let s=e.p;if(s&&s.s===t&&(!r||Ot(s,t)))return;let o=t.a=e.p={c:e,s:t,l:n,n:i,u:s};i&&(i.l=o),n?n.n=o:t.r=o,s?s.i=o:e.o=o},Le=(e,t=e.s)=>{let n=e.c,i=e.l,r=e.n,s=e.i,o=e.u;if(r?r.l=i:t.a=i,i?i.n=r:t.r=r,s?s.u=o:n.p=o,o)o.i=s;else if(!(n.o=s))if("getter"in n){let a=n.r;if(a){n.e=17;do a=Le(a,n);while(a)}}else"previousValue"in n||Mt(n);return r},Pn=e=>{let t=e.i,n;e:for(;;){let i=e.s,r=i.e;if(r&3&&(r&60?r&12?r&4?!(r&48)&&Ot(e,i)?(i.e=r|40,r&=1):r=0:i.e=r&-9|32:r=0:i.e=r|32,r&2&&We(i),r&1)){let s=i.o;if(s){e=s,s.i&&(n={t,f:n},t=e.i);continue}}if(e=t){t=e.i;continue}for(;n;)if(e=n.t,n=n.f,e){t=e.i;continue e}break}},wt=e=>{e.a=void 0,e.e=e.e&-57|4},Ct=e=>{let t=e.a,n=t?t.n:e.r;for(;n;)n=Le(n,e);e.e&=-5},Lt=(e,t)=>{let n,i=0;e:for(;;){let r=e.c,s=r.e,o=!1;if(t.e&16)o=!0;else if((s&17)===17){if(gt(r)){let a=r.o;a.i&&Oe(a),o=!0}}else if((s&33)===33){(e.i||e.u)&&(n={t:e,f:n}),e=r.r,t=r,++i;continue}if(!o&&e.n){e=e.n;continue}for(;i;){--i;let a=t.o,c=a.i;if(c?(e=n.t,n=n.f):e=a,o){if(gt(t)){c&&Oe(a),t=e.s;continue}}else t.e&=-33;if(t=e.s,e.n){e=e.n;continue e}o=!1}return o}},Oe=e=>{do{let t=e.s,n=e.i,i=t.e;(i&48)===32&&(t.e=i|16,i&2&&We(t)),e=n}while(e)},Ot=(e,t)=>{let n=t.a;if(n){let i=t.r;do{if(i===e)return!0;if(i===n)break;i=i.n}while(i)}return!1},Ue=e=>{let t=K,n=e.split(".");for(let i of n){if(t==null||!Object.hasOwn(t,i))return;t=t[i]}return t},yt=Symbol("delete"),we=(e,t="")=>{let n=Array.isArray(e);if(n||Z(e)){let i=n?[]:{};for(let s in e)i[s]=ee(we(e[s],`${t+s}.`));let r=ee(0);return new Proxy(i,{get:(s,o)=>{if(!(o==="toJSON"&&!Object.hasOwn(i,o)))return n&&o in Array.prototype?(r(),i[o]):typeof o=="symbol"?i[o]:((!Object.hasOwn(i,o)||i[o]()==null)&&(i[o]=ee(""),$({[t+o]:""}),r(r()+1)),i[o]())},set:(s,o,a)=>(a===yt?Object.hasOwn(i,o)&&(delete i[o],$({[t+o]:yt}),r(r()+1)):n&&o==="length"?(i[o]=a,$({[t.slice(0,-1)]:i}),r(r()+1)):Object.hasOwn(i,o)?a==null?i[o](null)&&$({[t+o]:null}):Object.hasOwn(a,Ge)?(i[o]=a,$({[t+o]:""})):i[o](we(a,`${t+o}.`))&&$({[t+o]:a}):a!=null&&(Object.hasOwn(a,Ge)?(i[o]=a,$({[t+o]:""})):(i[o]=ee(we(a,`${t+o}.`)),$({[t+o]:a})),r(r()+1)),!0),deleteProperty:(s,o)=>(Object.hasOwn(i,o)&&i[o](null)&&$({[t+o]:null}),!0),ownKeys:()=>(r(),Reflect.ownKeys(i)),has(s,o){return r(),o in i}})}return e},$=e=>{if(e&&E(xe,e),!Fe&&!Ae(xe)){let t=xe;xe={},document.dispatchEvent(new CustomEvent(W,{detail:t}))}},Pt=(e,{ifMissing:t}={})=>{me();for(let n in e)e[n]==null?t||delete K[n]:Ft(e[n],n,K,"",t);ge()},Ft=(e,t,n,i,r)=>{if(Z(e)){Object.hasOwn(n,t)&&(Z(n[t])||Array.isArray(n[t]))||(n[t]={});for(let s in e)e[s]==null?r||delete n[t][s]:Ft(e[s],s,n[t],`${i+t}.`,r)}else r&&Object.hasOwn(n,t)||(n[t]=e)};function Dt({include:e=/.*/,exclude:t=/(?!)/}={},n=K){let i={},r=[[n,""]];for(;r.length;){let[s,o]=r.pop();for(let a in s)Z(s[a])?r.push([s[a],`${o+a}.`]):vt(e).test(o+a)&&!vt(t).test(o+a)&&(i[o+a]=Ue(o+a))}return E({},i)}function vt(e){return typeof e=="string"?RegExp(e.replace(/^\/|\/$/g,"")):e}var K=we({}),de={},Ce=[],kt=[],pe=new Map,_e=null,te="";function $t(e){te=e}function I(e){return te?`data-${te}-${e}`:`data-${e}`}function Be(...e){for(let t of e){let n={plugin:t,actions:de,root:K,filtered:Dt,signal:ee,computed:bt,effect:Et,mergePatch:Pt,peek:Tt,getPath:Ue,startBatch:me,endBatch:ge,initErr:0};if(n.initErr=Ve.bind(0,n),t.type==="action")de[t.name]=t;else if(t.type==="attribute")Ce.push(t),t.onGlobalInit?.(n);else if(t.type==="watcher")t.onGlobalInit?.(n);else throw n.initErr("InvalidPluginType")}Ce.sort((t,n)=>{let i=n.name.length-t.name.length;return i!==0?i:t.name.localeCompare(n.name)}),kt=Ce.map(t=>RegExp(`^${t.name}([A-Z]|_|$)`))}function Pe(e){let t=`[${I("ignore")}]`;for(let n of e)if(!n.closest(t))for(let i in n.dataset)Ht(n,i,n.dataset[i])}function ht(e){for(let t of e){let n=pe.get(t);if(pe.delete(t)){for(let i of n.values())i();n.clear()}}}function Je(e=document.body){queueMicrotask(()=>{Pe([e]),Pe(e.querySelectorAll("*")),_e||(_e=new MutationObserver(Fn),_e.observe(e,{subtree:!0,childList:!0,attributes:!0}))})}function Ht(e,t,n){if(t.startsWith(te)){let i=Y(te?t.slice(te.length):t),r=Ce.find((s,o)=>kt[o].test(i));if(r){let[s,...o]=i.slice(r.name.length).split(/__+/),a=!!s;a&&(s=Y(s));let c=!!n,l={plugin:r,actions:de,root:K,filtered:Dt,signal:ee,computed:bt,effect:Et,mergePatch:Pt,peek:Tt,getPath:Ue,startBatch:me,endBatch:ge,initErr:0,el:e,rawKey:i,key:s,value:n,mods:new Map,runtimeErr:0,rx:0};l.initErr=Ve.bind(0,l),l.runtimeErr=mt.bind(0,l),(r.shouldEvaluate===void 0||r.shouldEvaluate===!0)&&(l.rx=Dn(l));let u=r.keyReq||"allowed";if(a){if(u==="denied")throw l.runtimeErr(`${r.name}KeyNotAllowed`)}else if(u==="must")throw l.runtimeErr(`${r.name}KeyRequired`);let f=r.valReq||"allowed";if(c){if(f==="denied")throw l.runtimeErr(`${r.name}ValueNotAllowed`)}else if(f==="must")throw l.runtimeErr(`${r.name}ValueRequired`);if(u==="exclusive"||f==="exclusive"){if(a&&c)throw l.runtimeErr(`${r.name}KeyAndValueProvided`);if(!a&&!c)throw l.runtimeErr(`${r.name}KeyOrValueRequired`)}for(let m of o){let[v,...S]=m.split(".");l.mods.set(Y(v),new Set(S.map(d=>d.toLowerCase())))}let p=r.onLoad(l);if(p){let m=pe.get(e);m?m.get(i)?.():(m=new Map,pe.set(e,m)),m.set(i,p)}}}}function Fn(e){let t=`[${I("ignore")}]`;for(let{target:n,type:i,attributeName:r,addedNodes:s,removedNodes:o}of e)if(i==="childList"){for(let a of o)Se(a)&&(ht([a]),ht(a.querySelectorAll("*")));for(let a of s)Se(a)&&(Pe([a]),Pe(a.querySelectorAll("*")))}else if(i==="attributes"&&Se(n)&&!n.closest(t)){let a=Y(r.slice(5)),c=n.getAttribute(r);if(c===null){let l=pe.get(n);l&&(l.get(a)?.(),l.delete(a))}else Ht(n,a,c)}}function Dn(e){let t="",n=e.plugin||void 0;if(n?.returnsValue){let p=/(\/(\\\/|[^/])*\/|"(\\"|[^"])*"|'(\\'|[^'])*'|`(\\`|[^`])*`|\(\s*((function)\s*\(\s*\)|(\(\s*\))\s*=>)\s*(?:\{[\s\S]*?\}|[^;){]*)\s*\)\s*\(\s*\)|[^;])+/gm,m=e.value.trim().match(p);if(m){let v=m.length-1,S=m[v].trim();S.startsWith("return")||(m[v]=`return (${S});`),t=m.join(`;
`)}}else t=e.value.trim();t=t.replace(/\$\['([a-zA-Z_$\d][\w$]*)'\]/g,"$$$1").replace(/\$([a-zA-Z_\d]\w*(?:[.-]\w+)*)/g,(p,m)=>m.split(".").reduce((S,d)=>`${S}['${d}']`,"$")).replace(/\[(\$[a-zA-Z_\d]\w*)\]/g,(p,m)=>`[$['${m.slice(1)}']]`);let i=new Map,r=RegExp(`(?:${Te})(.*?)(?:${Ie})`,"gm"),s=0;for(let p of t.matchAll(r)){let m=p[1],v=`dsEscaped${s++}`;i.set(v,m),t=t.replace(Te+m+Ie,v)}let o=(p,m)=>`${p}${fe(m).replaceAll(/\./g,"_")}`,a=new Set,c=RegExp(`@(${Object.keys(de).join("|")})\\(`,"gm"),l=[...t.matchAll(c)],u=new Set,f=new Set;if(l.length){let p=`${P}Act_`;for(let m of l){let v=m[1],S=de[v];if(!S)continue;a.add(v);let d=o(p,v);t=t.replace(`@${v}(`,`${d}(`),u.add(d),f.add((...y)=>S.fn(e,...y))}}for(let[p,m]of i)t=t.replace(p,m);e.fnContent=t;try{let p=Function("el","$",...n?.argNames||[],...u,t);return(...m)=>{try{return p(e.el,K,...m,...f)}catch(v){throw e.runtimeErr("ExecuteExpression",{error:v.message})}}}catch(p){throw e.runtimeErr("GenerateExpression",{error:p.message})}}var It={type:"action",name:"peek",fn:({peek:e},t)=>e(t)};var Nt={type:"action",name:"setAll",fn:({filtered:e,mergePatch:t,peek:n},i,r)=>{n(()=>{let s=e(r);ue(s,()=>i),t(s)})}};var Vt={type:"action",name:"toggleAll",fn:({filtered:e,mergePatch:t,peek:n},i)=>{n(()=>{let r=e(i);ue(r,s=>!s),t(r)})}};var _t={type:"attribute",name:"attr",valReq:"must",returnsValue:!0,onLoad:({el:e,effect:t,key:n,rx:i})=>{let r=(c,l)=>{l===""||l===!0?e.setAttribute(c,""):l===!1||l==null?e.removeAttribute(c):typeof l=="string"?e.setAttribute(c,l):e.setAttribute(c,JSON.stringify(l))};n=R(n);let s=n?()=>{o.disconnect();let c=i();r(n,c),o.observe(e,{attributeFilter:[n]})}:()=>{o.disconnect();let c=i(),l=Object.keys(c);for(let u of l)r(u,c[u]);o.observe(e,{attributeFilter:l})},o=new MutationObserver(s),a=t(s);return()=>{o.disconnect(),a()}}};var kn=/^data:(?<mime>[^;]+);base64,(?<contents>.*)$/,qt=Symbol("empty"),jt={type:"attribute",name:"bind",keyReq:"exclusive",valReq:"exclusive",shouldEvaluate:!1,onLoad:({el:e,key:t,mods:n,value:i,effect:r,mergePatch:s,runtimeErr:o,getPath:a})=>{let c=t?x(t,n):i,l=(d,y)=>y==="number"?+d.value:d.value,u=d=>{e.value=`${d}`};if(e instanceof HTMLInputElement)switch(e.type){case"range":case"number":l=(d,y)=>y==="string"?d.value:+d.value;break;case"checkbox":l=(d,y)=>d.value!=="on"?y==="boolean"?d.checked:d.checked?d.value:"":y==="string"?d.checked?d.value:"":d.checked,u=d=>{e.checked=typeof d=="string"?d===e.value:d};break;case"radio":e.getAttribute("name")?.length||e.setAttribute("name",c),l=(d,y)=>d.checked?y==="number"?+d.value:d.value:qt,u=d=>{e.checked=d===(typeof d=="number"?+e.value:e.value)};break;case"file":{let d=()=>{let y=[...e.files||[]],h=[],T=[],F=[];Promise.all(y.map(D=>new Promise(M=>{let g=new FileReader;g.onload=()=>{if(typeof g.result!="string")throw o("InvalidFileResultType",{resultType:typeof g.result});let L=g.result.match(kn);if(!L?.groups)throw o("InvalidDataUri",{result:g.result});h.push(L.groups.contents),T.push(L.groups.mime),F.push(D.name)},g.onloadend=()=>M(),g.readAsDataURL(D)}))).then(()=>{s(E({},{[c]:h,[`${c}Mimes`]:T,[`${c}Names`]:F}))})};return e.addEventListener("change",d),e.addEventListener("input",d),()=>{e.removeEventListener("change",d),e.removeEventListener("input",d)}}}else if(e instanceof HTMLSelectElement){if(e.multiple){let d=new Map;l=y=>[...y.selectedOptions].map(h=>{let T=d.get(h.value);return T==="string"||T==null?h.value:+h.value}),u=y=>{for(let h of e.options)y.includes(h.value)?(d.set(h.value,"string"),h.selected=!0):y.includes(+h.value)?(d.set(h.value,"number"),h.selected=!0):h.selected=!1}}}else e instanceof HTMLTextAreaElement||(l=d=>"value"in d?d.value:d.getAttribute("value"),u=d=>{"value"in e?e.value=d:e.setAttribute("value",d)});let f=a(c),p=typeof f,m=c;if(Array.isArray(f)&&!(e instanceof HTMLSelectElement&&e.multiple)){let d=document.querySelectorAll(`[${I("bind")}-${t}],[${I("bind")}="${i}"]`),y={},h=0;for(let T of d){if(y[`${m}.${h}`]=l(T,"none"),e===T)break;h++}s(E({},y),{ifMissing:!0}),m=`${m}.${h}`}else s(E({},{[m]:l(e,p)}),{ifMissing:!0});let v=()=>{let d=a(m);if(d!=null){let y=l(e,typeof d);y!==qt&&s(E({},{[m]:y}))}};e.addEventListener("input",v),e.addEventListener("change",v);let S=r(()=>{u(a(m))});return()=>{S(),e.removeEventListener("input",v),e.removeEventListener("change",v)}}};var Gt={type:"attribute",name:"class",valReq:"must",returnsValue:!0,onLoad:({key:e,el:t,effect:n,mods:i,rx:r})=>{e&&(e=x(R(e),i));let s=()=>{o.disconnect();let c=e?{[e]:r()}:r();for(let l in c){let u=l.split(/\s+/).filter(f=>f.length>0);if(c[l])for(let f of u)t.classList.contains(f)||t.classList.add(f);else for(let f of u)t.classList.contains(f)&&t.classList.remove(f)}o.observe(t,{attributeFilter:["class"]})},o=new MutationObserver(s),a=n(s);return()=>{o.disconnect(),a();let c=e?{[e]:r()}:r();for(let l in c){let u=l.split(/\s+/).filter(f=>f.length>0);for(let f of u)t.classList.remove(f)}}}};var Wt={type:"attribute",name:"computed",keyReq:"must",valReq:"must",returnsValue:!0,onLoad:({key:e,mods:t,rx:n,computed:i,mergePatch:r})=>{r(E({},{[x(e,t)]:i(n)}))}};var Kt={type:"attribute",name:"effect",keyReq:"denied",valReq:"must",onLoad:({effect:e,rx:t})=>e(t)};var _=`${P}-fetch`,De="started",ke="finished",Ut="error",Bt="retrying",Jt="retries-failed";function $e(e,t){document.addEventListener(_,n=>{if(n.detail.type===e){let{argsRaw:i}=n.detail;t(i)}})}var zt={type:"attribute",name:"indicator",keyReq:"exclusive",valReq:"exclusive",shouldEvaluate:!1,onLoad:({el:e,key:t,mods:n,mergePatch:i,value:r})=>{let s=t?x(t,n):r;i(E({},{[s]:!1}),{ifMissing:!0});let o=a=>{let{type:c,el:l}=a.detail;if(l===e)switch(c){case De:i(E({},{[s]:!0}));break;case ke:i(E({},{[s]:!1}));break}};return document.addEventListener(_,o),()=>{i(E({},{[s]:!1})),document.removeEventListener(_,o)}}};var Qt={type:"attribute",name:"jsonSignals",keyReq:"denied",onLoad:({el:e,effect:t,value:n,filtered:i,mods:r})=>{let s=r.has("terse")?0:2,o={};n&&(o=X(n));let a=()=>{c.disconnect(),e.textContent=JSON.stringify(i(o),null,s),c.observe(e,{childList:!0,characterData:!0,subtree:!0})},c=new MutationObserver(a),l=t(a);return()=>{c.disconnect(),l()}}};function q(e){if(!e||e.size<=0)return 0;for(let t of e){if(t.endsWith("ms"))return+t.replace("ms","");if(t.endsWith("s"))return+t.replace("s","")*1e3;try{return Number.parseFloat(t)}catch{}}return 0}function U(e,t,n=!1){return e?e.has(t.toLowerCase()):n}function ze(e,t){return(...n)=>{setTimeout(()=>{e(...n)},t)}}function $n(e,t,n=!1,i=!0){let r=0;return(...s)=>{r&&clearTimeout(r),n&&!r&&e(...s),r=setTimeout(()=>{i&&e(...s),r&&clearTimeout(r)},t)}}function Hn(e,t,n=!0,i=!1){let r=!1;return(...s)=>{r||(n&&e(...s),r=!0,setTimeout(()=>{r=!1,i&&e(...s)},t))}}function ne(e,t){let n=t.get("delay");if(n){let s=q(n);e=ze(e,s)}let i=t.get("debounce");if(i){let s=q(i),o=U(i,"leading",!1),a=!U(i,"notrail",!1);e=$n(e,s,o,a)}let r=t.get("throttle");if(r){let s=q(r),o=!U(r,"noleading",!1),a=U(r,"trail",!1);e=Hn(e,s,o,a)}return e}var Qe=!!document.startViewTransition;function j(e,t){if(t.has("viewtransition")&&Qe){let n=e;e=(...i)=>document.startViewTransition(()=>n(...i))}return e}var Zt={type:"attribute",name:"on",keyReq:"must",valReq:"must",argNames:["evt"],onLoad:e=>{let{el:t,key:n,mods:i,rx:r,startBatch:s,endBatch:o}=e,a=t;i.has("window")&&(a=window);let c=f=>{if(f){if(i.has("prevent")&&f.preventDefault(),i.has("stop")&&f.stopPropagation(),!(f.isTrusted||f instanceof CustomEvent||i.has("trusted")))return;e.evt=f}s(),r(f),o()};c=ne(c,i),c=j(c,i);let l={capture:i.has("capture"),passive:i.has("passive"),once:i.has("once")};if(i.has("outside")){a=document;let f=c;c=p=>{t.contains(p?.target)||f(p)}}let u=R(n);if(u=x(u,i),(u===_||u===W)&&(a=document),t instanceof HTMLFormElement&&u==="submit"){let f=c;c=p=>{p?.preventDefault(),f(p)}}return a.addEventListener(u,c,l),()=>{a.removeEventListener(u,c)}}};var Ze=new WeakSet,Yt={type:"attribute",name:"onIntersect",keyReq:"denied",onLoad:({el:e,mods:t,rx:n,startBatch:i,endBatch:r})=>{let s=()=>{i(),n(),r()};s=ne(s,t),s=j(s,t);let o={threshold:0};t.has("full")?o.threshold=1:t.has("half")&&(o.threshold=.5);let a=new IntersectionObserver(c=>{for(let l of c)l.isIntersecting&&(s(),a&&Ze.has(e)&&a.disconnect())},o);return a.observe(e),t.has("once")&&Ze.add(e),()=>{t.has("once")||Ze.delete(e),a&&(a.disconnect(),a=null)}}};var Xt={type:"attribute",name:"onInterval",keyReq:"denied",valReq:"must",onLoad:({mods:e,rx:t,startBatch:n,endBatch:i})=>{let r=()=>{n(),t(),i()};r=j(r,e);let s=1e3,o=e.get("duration");o&&(s=q(o),U(o,"leading",!1)&&r());let a=setInterval(r,s);return()=>{clearInterval(a)}}};var en={type:"attribute",name:"onLoad",keyReq:"denied",valReq:"must",onLoad:({rx:e,mods:t,startBatch:n,endBatch:i})=>{let r=()=>{n(),e(),i()};r=j(r,t);let s=0,o=t.get("delay");o&&(s=q(o)),r=ze(r,s),r()}};var tn={type:"attribute",name:"onSignalPatch",valReq:"must",argNames:["patch"],returnsValue:!0,onLoad:({el:e,key:t,mods:n,plugin:i,rx:r,filtered:s,runtimeErr:o,startBatch:a,endBatch:c})=>{if(t&&t!=="filter")throw o(`${i.name}KeyNotAllowed`);let l=e.getAttribute("data-on-signal-patch-filter"),u={};l&&(u=X(l));let f=ne(p=>{let m=s(u,p.detail);Ae(m)||(a(),r(m),c())},n);return document.addEventListener(W,f),()=>{document.removeEventListener(W,f)}}};var nn={type:"attribute",name:"ref",keyReq:"exclusive",valReq:"exclusive",shouldEvaluate:!1,onLoad:({el:e,key:t,mods:n,value:i,mergePatch:r})=>{let s=t?x(t,n):i;r(E({},{[s]:e}))}};var rn="none",on="display",sn={type:"attribute",name:"show",keyReq:"denied",valReq:"must",returnsValue:!0,onLoad:({el:e,effect:t,rx:n})=>{let i=()=>{r.disconnect(),n()?e.style.display===rn&&e.style.removeProperty(on):e.style.setProperty(on,rn),r.observe(e,{attributeFilter:["style"]})},r=new MutationObserver(i),s=t(i);return()=>{r.disconnect(),s()}}};var an={type:"attribute",name:"signals",returnsValue:!0,onLoad:({key:e,mods:t,rx:n,mergePatch:i})=>{let r=t.has("ifmissing");if(e)e=x(e,t),i(E({},{[e]:n()}),{ifMissing:r});else{let s=n(),o={};for(let a in s)o[a]=s[a];i(E({},o),{ifMissing:r})}}};var cn={type:"attribute",name:"style",valReq:"must",returnsValue:!0,onLoad:({key:e,el:t,effect:n,rx:i})=>{let{style:r}=t,s=new Map;e&&=R(e);let o=(u,f)=>{let p=s.get(u);!f&&f!==0?p!==void 0&&(p?r.setProperty(u,p):r.removeProperty(u)):(p===void 0&&s.set(u,r.getPropertyValue(u)),r.setProperty(u,String(f)))},a=()=>{if(c.disconnect(),e)o(e,i());else{let u=i();for(let[f,p]of s)f in u||(p?r.setProperty(f,p):r.removeProperty(f));for(let f in u)o(R(f),u[f])}c.observe(t,{attributeFilter:["style"]})},c=new MutationObserver(a),l=n(a);return()=>{c.disconnect(),l();for(let[u,f]of s)f?r.setProperty(u,f):r.removeProperty(u)}}};var ln={type:"attribute",name:"text",keyReq:"denied",valReq:"must",returnsValue:!0,onLoad:({el:e,effect:t,rx:n})=>{let i=()=>{r.disconnect(),e.textContent=`${n()}`,r.observe(e,{childList:!0,characterData:!0,subtree:!0})},r=new MutationObserver(i),s=t(i);return()=>{r.disconnect(),s()}}};var He=new WeakMap,H=(e,t)=>({type:"action",name:e,fn:async(n,i,r)=>{let{el:s}=n,o=r?.requestCancellation??"auto",a=o instanceof AbortController?o:new AbortController,c=o==="disabled";c||He.get(s)?.abort(),!c&&!(o instanceof AbortController)&&He.set(s,a);try{await In(n,t,i,r,a.signal)}finally{He.get(s)===a&&He.delete(s)}}}),B=(e,t,n)=>document.dispatchEvent(new CustomEvent(_,{detail:{type:e,el:t,argsRaw:n}})),un=e=>`${e}`.includes("text/event-stream"),In=async({el:e,evt:t,filtered:n,runtimeErr:i},r,s,{selector:o,headers:a,contentType:c="json",filterSignals:l={include:/.*/,exclude:/(^|\.)_/},openWhenHidden:u=!1,retryInterval:f=it,retryScaler:p=2,retryMaxWaitMs:m=3e4,retryMaxCount:v=10}={},S)=>{let d=r.toLowerCase(),y=()=>{};try{if(!s?.length)throw i("FetchNoUrlProvided",{action:d});let h={Accept:"text/event-stream, text/html, application/json",[nt]:!0};c==="json"&&(h["Content-Type"]="application/json");let T=Object.assign({},h,a),F={method:r,headers:T,openWhenHidden:u,retryInterval:f,retryScaler:p,retryMaxWaitMs:m,retryMaxCount:v,signal:S,onopen:async g=>{g.status>=400&&B(Ut,e,{status:g.status.toString()})},onmessage:g=>{if(!g.event.startsWith(P))return;let L=g.event,k={};for(let w of g.data.split(`
`)){let b=w.indexOf(" "),A=w.slice(0,b),J=w.slice(b+1);(k[A]||=[]).push(J)}let G=Object.fromEntries(Object.entries(k).map(([w,b])=>[w,b.join(`
`)]));B(L,e,G)},onerror:g=>{if(un(g))throw i("InvalidContentType",{url:s});g&&(console.error(g.message),B(Bt,e,{message:g.message}))}},D=new URL(s,window.location.href),M=new URLSearchParams(D.search);if(c==="json"){let g=JSON.stringify(n(l));r==="GET"?M.set(P,g):F.body=g}else if(c==="form"){let g=o?document.querySelector(o):e.closest("form");if(!g)throw i(o?"FetchFormNotFound":"FetchClosestFormNotFound",{action:d,selector:o});if(!g.checkValidity()){g.reportValidity(),y();return}let L=new FormData(g),k=e;if(e===g&&t instanceof SubmitEvent)k=t.submitter;else{let b=A=>A.preventDefault();g.addEventListener("submit",b),y=()=>g.removeEventListener("submit",b)}if(k instanceof HTMLButtonElement){let b=k.getAttribute("name");b&&L.append(b,k.value)}let G=g.getAttribute("enctype")==="multipart/form-data";G||(T["Content-Type"]="application/x-www-form-urlencoded");let w=new URLSearchParams(L);if(r==="GET")for(let[b,A]of w)M.append(b,A);else G?F.body=L:F.body=w}else throw i("FetchInvalidContentType",{action:d,contentType:c});B(De,e,{}),D.search=M.toString();try{await jn(D.toString(),e,F)}catch(g){if(!un(g))throw i("FetchFailed",{method:r,url:s,error:g})}}finally{B(ke,e,{}),y()}};async function Nn(e,t){let n=e.getReader(),i=await n.read();for(;!i.done;)t(i.value),i=await n.read()}function Vn(e){let t,n,i,r=!1;return function(o){t?t=qn(t,o):(t=o,n=0,i=-1);let a=t.length,c=0;for(;n<a;){r&&(t[n]===10&&(c=++n),r=!1);let l=-1;for(;n<a&&l===-1;++n)switch(t[n]){case 58:i===-1&&(i=n-c);break;case 13:r=!0;case 10:l=n;break}if(l===-1)break;e(t.subarray(c,l),i),c=n,i=-1}c===a?t=void 0:c&&(t=t.subarray(c),n-=c)}}function _n(e,t,n){let i=fn(),r=new TextDecoder;return function(o,a){if(!o.length)n?.(i),i=fn();else if(a>0){let c=r.decode(o.subarray(0,a)),l=a+(o[a+1]===32?2:1),u=r.decode(o.subarray(l));switch(c){case"data":i.data=i.data?`${i.data}
${u}`:u;break;case"event":i.event=u;break;case"id":e(i.id=u);break;case"retry":{let f=+u;Number.isNaN(f)||t(i.retry=f);break}}}}}var qn=(e,t)=>{let n=new Uint8Array(e.length+t.length);return n.set(e),n.set(t,e.length),n},fn=()=>({data:"",event:"",id:"",retry:void 0});function jn(e,t,{signal:n,headers:i,onopen:r,onmessage:s,onclose:o,onerror:a,openWhenHidden:c,fetch:l,retryInterval:u=1e3,retryScaler:f=2,retryMaxWaitMs:p=3e4,retryMaxCount:m=10,overrides:v,...S}){return new Promise((d,y)=>{let h={...i},T;function F(){T.abort(),document.hidden||w()}c||document.addEventListener("visibilitychange",F);let D=0;function M(){document.removeEventListener("visibilitychange",F),window.clearTimeout(D),T.abort()}n?.addEventListener("abort",()=>{M(),d()});let g=l||window.fetch,L=r||(()=>{}),k=0,G=u;async function w(){T=new AbortController;try{let b=await g(e,{...S,headers:h,signal:T.signal});k=0,u=G,await L(b);let A=async(O,z,se,Q,...ve)=>{let he={[se]:await z.text()};for(let be of ve){let Ee=z.headers.get(`datastar-${R(be)}`);if(Q){let ae=Q[be];ae&&(Ee=typeof ae=="string"?ae:JSON.stringify(ae))}Ee&&(he[be]=Ee)}B(O,t,he),M(),d()},J=b.headers.get("Content-Type");if(J?.includes("text/html"))return await A(ce,b,"elements",v,"selector","mode","useViewTransition");if(J?.includes("application/json"))return await A(le,b,"signals",v,"onlyIfMissing");if(J?.includes("text/javascript")){let O=document.createElement("script"),z=b.headers.get("datastar-script-attributes");if(z)for(let[se,Q]of Object.entries(JSON.parse(z)))O.setAttribute(se,Q);O.textContent=await b.text(),document.head.appendChild(O),M();return}await Nn(b.body,Vn(_n(O=>{O?h["last-event-id"]=O:delete h["last-event-id"]},O=>{G=u=O},s))),o?.(),M(),d()}catch(b){if(!T.signal.aborted)try{let A=a?.(b)||u;window.clearTimeout(D),D=window.setTimeout(w,A),u=Math.min(u*f,p),++k>=m?(B(Jt,t,{}),M(),y("Max retries reached.")):console.error(`Datastar failed to reach ${e.toString()} retrying in ${A}ms.`)}catch(A){M(),y(A)}}}w()})}var dn=H("delete","DELETE");var pn=H("get","GET");var mn=H("patch","PATCH");var gn=H("post","POST");var yn=H("put","PUT");var Rn={type:"watcher",name:ce,async onGlobalInit(e){$e(ce,t=>{Qe&&t.useViewTransition?.trim()==="true"?document.startViewTransition(()=>vn(e,t)):vn(e,t)})}};function vn(e,{elements:t="",selector:n,mode:i=ft}){let{initErr:r}=e,s=t.replace(/<svg(\s[^>]*>|>)([\s\S]*?)<\/svg>/gim,""),o=/<\/html>/.test(s),a=/<\/head>/.test(s),c=/<\/body>/.test(s),l=new DOMParser().parseFromString(o||a||c?t:`<body><template>${t}</template></body>`,"text/html"),u=document.createDocumentFragment();if(o?u.appendChild(l.documentElement):a&&c?(u.appendChild(l.head),u.appendChild(l.body)):a?u.appendChild(l.head):c?u.appendChild(l.body):u=l.querySelector("template").content,!n&&(i===Re||i===Ne))for(let f of u.children){let p;if(f instanceof HTMLHtmlElement)p=document.documentElement;else if(f instanceof HTMLBodyElement)p=document.body;else if(f instanceof HTMLHeadElement)p=document.head;else if(p=document.getElementById(f.id),!p){console.error(r("NoTargetsFound",{id:f.id}));continue}bn(e,i,f,[p])}else{let f=document.querySelectorAll(n);if(!f.length){console.error(r("NoTargetsFound",{selector:n}));return}bn(e,i,u,f)}}var Xe=new WeakSet;for(let e of document.querySelectorAll("script"))Xe.add(e);function hn(e){let t=e instanceof HTMLScriptElement?[e]:e.querySelectorAll("script");for(let n of t)if(!Xe.has(n)){let i=document.createElement("script");for(let{name:r,value:s}of n.attributes)i.setAttribute(r,s);i.text=n.text,n.replaceWith(i),Xe.add(i)}}function bn({initErr:e},t,n,i){for(let r of i){let s=n.cloneNode(!0);if(t===st)r.remove();else if(t===Re||t===ot)Gn(r,s,t),hn(r);else if(hn(s),t===Ne)r.replaceWith(s);else if(t===at)r.prepend(s);else if(t===ct)r.append(s);else if(t===lt)r.before(s);else if(t===ut)r.after(s);else throw e("InvalidPatchMode",{mode:t})}}var ie=new Map,C=new Map,re=new Set,ye=new Set,oe=document.createElement("div");oe.hidden=!0;function Gn(e,t,n){let i=I("ignore-morph");if(e.hasAttribute(i)&&t instanceof HTMLElement&&t.hasAttribute(i)||e.parentElement?.closest(`[${i}]`))return;let r=document.createElement("div");r.append(t),document.body.insertAdjacentElement("afterend",oe);let s=e.querySelectorAll("[id]");for(let{id:a,tagName:c}of s)ie.has(a)?ye.add(a):ie.set(a,c);e.id&&(ie.has(e.id)?ye.add(e.id):ie.set(e.id,e.tagName)),re.clear();let o=r.querySelectorAll("[id]");for(let{id:a,tagName:c}of o)re.has(a)?ye.add(a):ie.get(a)===c&&re.add(a);ie.clear();for(let a of ye)re.delete(a);ye.clear(),C.clear(),Tn(n==="outer"?e.parentElement:e,s),Tn(r,o),Sn(n==="outer"?e.parentElement:e,r,n==="outer"?e:null,e.nextSibling),oe.remove()}function Sn(e,t,n=null,i=null){e instanceof HTMLTemplateElement&&t instanceof HTMLTemplateElement&&(e=e.content,t=t.content),n??=e.firstChild;for(let r of t.childNodes){if(n&&n!==i){let o=Wn(r,n,i);if(o){if(o!==n){let a=n;for(;a&&a!==o;){let c=a;a=a.nextSibling,et(c)}}Ye(o,r),n=o.nextSibling;continue}}let s=r.id;if(r instanceof Element&&re.has(s)){let o=window[s],a=o;for(;a=a.parentNode;){let c=C.get(a);c&&(c.delete(s),c.size||C.delete(a))}An(e,o,n),Ye(o,r),n=o.nextSibling;continue}if(C.has(r)){let o=document.createElement(r.tagName);e.insertBefore(o,n),Ye(o,r),n=o.nextSibling}else{let o=document.importNode(r,!0);e.insertBefore(o,n),n=o.nextSibling}}for(;n&&n!==i;){let r=n;n=n.nextSibling,et(r)}}function Wn(e,t,n){let i=null,r=e.nextSibling,s=0,o=0,a=C.get(e)?.size||0,c=t;for(;c&&c!==n;){if(En(c,e)){let l=!1,u=C.get(c),f=C.get(e);if(f&&u){for(let p of u)if(f.has(p)){l=!0;break}}if(l)return c;if(!i&&!C.has(c)){if(!a)return c;i=c}}if(o+=C.get(c)?.size||0,o>a||(i===null&&r&&En(c,r)&&(s++,r=r.nextSibling,s>=2&&(i=void 0)),c.contains(document.activeElement)))break;c=c.nextSibling}return i||null}function En(e,t){let n=e.id;return e.nodeType===t.nodeType&&e.tagName===t.tagName&&(!n||n===t.id)}function et(e){C.has(e)?An(oe,e,null):e.parentNode?.removeChild(e)}var An=et.call.bind(oe.moveBefore??oe.insertBefore);function Ye(e,t){let n=t.nodeType;if(n===1){let i=I("ignore-morph");if(e.hasAttribute(i)&&t.hasAttribute(i))return e;if(e instanceof HTMLInputElement&&t instanceof HTMLInputElement&&t.type!=="file")t.getAttribute("value")!==e.getAttribute("value")&&(e.value=t.getAttribute("value")??"");else if(e instanceof HTMLTextAreaElement&&t instanceof HTMLTextAreaElement){let o=t.value;o!==e.value&&(e.value=o),e.firstChild&&e.firstChild.nodeValue!==o&&(e.firstChild.nodeValue=o)}let r=(t.getAttribute(I("preserve-attr"))??"").split(" ");for(let{name:o,value:a}of t.attributes)e.getAttribute(o)!==a&&!r.includes(R(o))&&e.setAttribute(o,a);let s=e.attributes;for(let o=s.length-1;o>=0;o--){let{name:a}=s[o];!t.hasAttribute(a)&&!r.includes(R(a))&&e.removeAttribute(a)}}return(n===8||n===3)&&e.nodeValue!==t.nodeValue&&(e.nodeValue=t.nodeValue),e.isEqualNode(t)||Sn(e,t),e}function Tn(e,t){for(let n of t)if(re.has(n.id)){let i=n;for(;i&&i!==e;){let r=C.get(i);r||(r=new Set,C.set(i,r)),r.add(n.id),i=i.parentElement}}}var xn={type:"watcher",name:le,onGlobalInit:e=>$e(le,({signals:t="{}",onlyIfMissing:n=`${rt}`})=>e.mergePatch(X(t),{ifMissing:dt(n)}))};Be(pn,gn,yn,mn,dn,Rn,xn,_t,jt,Gt,Wt,Kt,zt,Qt,Zt,Yt,Xt,en,tn,nn,sn,an,cn,ln,It,Nt,Vt);Je();export{Je as apply,Be as load,$t as setAlias};
//# sourceMappingURL=datastar.js.map
