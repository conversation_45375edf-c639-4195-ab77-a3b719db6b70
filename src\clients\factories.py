"""
Factory classes for clients app models using factory_boy and faker.
"""

import factory
from factory import fuzzy

from businesses.factories import BusinessFactory
from clients.models import Client, Traveler


class ClientFactory(factory.django.DjangoModelFactory):
    """Factory for creating Client instances."""

    class Meta:
        model = Client

    business = factory.SubFactory(BusinessFactory)
    client_type = Client.ClientType.INDIVIDUAL
    first_name = factory.Faker("first_name")
    last_name = factory.Faker("last_name")
    email = factory.Faker("email")
    phone = factory.Faker("phone_number")
    address_line1 = factory.Faker("street_address")
    city = factory.Faker("city")
    state_province = factory.Faker("state")
    postal_code = factory.Faker("postcode")
    country = factory.Faker("country")
    notes = factory.Faker("text", max_nb_chars=100)
    created_by = factory.LazyAttribute(lambda obj: obj.business.created_by)


class CorporateClientFactory(ClientFactory):
    """Factory for creating corporate clients."""

    client_type = Client.ClientType.CORPORATE
    company_name = factory.Faker("company")
    first_name = factory.Faker("first_name")  # Contact person's first name
    last_name = factory.Faker("last_name")  # Contact person's last name


class TravelerFactory(factory.django.DjangoModelFactory):
    """Factory for creating Traveler instances."""

    class Meta:
        model = Traveler

    client = factory.SubFactory(ClientFactory)
    first_name = factory.Faker("first_name")
    last_name = factory.Faker("last_name")
    date_of_birth = factory.Faker("date_of_birth", minimum_age=18, maximum_age=80)
    gender = fuzzy.FuzzyChoice(["male", "female", "other"])
    nationality = factory.Faker("country")
    passport_number = factory.Faker("bothify", text="??#######")
    passport_expiry = factory.Faker("date_between", start_date="+1y", end_date="+10y")
    dietary_restrictions = factory.Faker(
        "random_element",
        elements=["", "Vegetarian", "Vegan", "Gluten-free", "Halal", "Kosher"],
    )
    medical_conditions = factory.Faker(
        "random_element",
        elements=["", "None", "Diabetes", "Heart condition", "Allergies"],
    )
    emergency_contact_relationship = factory.Faker(
        "random_element", elements=["Spouse", "Parent", "Sibling", "Friend", "Partner"]
    )
    emergency_contact_name = factory.Faker("name")
    emergency_contact_phone = factory.Faker("phone_number")
    created_by = factory.LazyAttribute(lambda obj: obj.client.created_by)
