<form id="payment-form" data-booking-id="{{ booking.id }}">
  <div class="form-group">
    <label>Card Number</label>
    <div id="card-element"><!-- Stripe Elements mounts here --></div>
  </div>

  <button type="submit"
          id="pay-button"
          data-idempotency-key="{{ request.session.idempotency_key|default:generate_uuid }}">
    Pay Deposit (${{ booking.deposit_amount }})
  </button>

  <!-- Error display -->
  <div id="payment-errors" class="alert hidden"></div>
</form>

<script>
  // 1. Generate idempotency key ONCE per booking
if (!sessionStorage.getItem('idempotency_key')) {
  sessionStorage.setItem('idempotency_key', generateUUID());
}

// 2. Disable button during processing
document.getElementById('pay-button').addEventListener('click', (e) => {
  e.target.disabled = true;
  e.target.textContent = 'Processing...';

  // 3. Send key in header (not in body!)
  fetch('/api/process-payment/', {
    headers: {
      'Idempotency-Key': sessionStorage.getItem('idempotency_key'),
      'X-CSRFToken': getCookie('csrftoken')
    }
  });
});

</script>
