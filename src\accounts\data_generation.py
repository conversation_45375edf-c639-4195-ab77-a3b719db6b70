"""
Data generation services for accounts app using model_bakery.
"""

import random

from django.contrib.auth import get_user_model
from faker import Faker
from model_bakery import baker

from .models import Profile

fake = Faker()
User = get_user_model()


class AccountsDataGenerator:
    """Data generator for accounts app models."""

    def __init__(self):
        self.created_objects = {
            'users': [],
        }

    def generate_users(self, count=10, businesses=None):
        """Generate users with different roles."""
        users = []

        for i in range(count):
            user = baker.make(
                User,
                username=f'user_{i}_{fake.random_int(min=1000, max=9999)}',
                email=fake.email(),
                first_name=fake.first_name(),
                last_name=fake.last_name(),
                email_confirmed=fake.boolean(chance_of_getting_true=80),
                profile_completed=fake.boolean(chance_of_getting_true=70),
                onboarding_completed=fake.boolean(chance_of_getting_true=60),
                is_active=True,
                is_superuser=False,
            )

            # Set password
            user.set_password('password123')
            user.save()

            users.append(user)

        self.created_objects['users'].extend(users)
        return users

    def update_profiles(self, users=None, businesses=None):
        """Generate profiles for users."""
        if not users:
            users = self.created_objects['users']

        profiles = list(Profile.objects.all())

        # Role distribution: 20% business owners, 30% agents, 50% customers
        role_weights = [
            (Profile.Role.BUSINESS_OWNER, 0.2),
            (Profile.Role.AGENT, 0.3),
            (Profile.Role.CUSTOMER, 0.5),
        ]

        for profile in profiles:
            # Select role based on weights
            role = random.choices(
                [role for role, _ in role_weights],
                weights=[weight for _, weight in role_weights],
            )[0]

            # Assign business for business owners and agents
            business = None
            if businesses and role in [Profile.Role.BUSINESS_OWNER, Profile.Role.AGENT]:
                business = random.choice(businesses)

            profile = baker.make(
                Profile,
                phone=fake.phone_number(),
                role=role,
                business=business,
                date_of_birth=fake.date_of_birth(minimum_age=18, maximum_age=80),
                # Address information
                address_line1=fake.street_address(),
                address_line2=fake.secondary_address()
                if fake.boolean(chance_of_getting_true=30)
                else '',
                city=fake.city(),
                state_province=fake.state(),
                postal_code=fake.postcode(),
                country=fake.country(),
                department=fake.bs() if fake.boolean(chance_of_getting_true=50) else '',
                # Preferences
                timezone=random.choice(
                    [
                        'UTC',
                        'America/New_York',
                        'America/Los_Angeles',
                        'Europe/London',
                        'Europe/Paris',
                        'Asia/Tokyo',
                    ]
                ),
                language=random.choice(
                    ['en', 'es', 'fr', 'de', 'it'],
                ),
            )

            profiles.append(profile)

        self.created_objects['profiles'].extend(profiles)
        return profiles

    def generate_business_team(self, business, owner_count=1, agent_count=3):
        """Generate a complete business team."""
        team_members = []

        # Generate business owners
        for i in range(owner_count):
            owner = baker.make(
                User,
                username=f'owner_{business.name.lower().replace(" ", "_")}_{i}',
                email=f'owner{i}@{business.name.lower().replace(" ", "")}.com',
                first_name=fake.first_name(),
                last_name=fake.last_name(),
                role=Profile.Role.BUSINESS_OWNER,
                business=business,
                email_confirmed=True,
                profile_completed=True,
                onboarding_completed=True,
                is_active=True,
                is_staff=False,  # Only for Django admin access
                is_superuser=False,
            )
            owner.set_password('password123')
            owner.save()
            team_members.append(owner)

        # Generate agents
        for i in range(agent_count):
            agent = baker.make(
                User,
                username=f'agent_{business.name.lower().replace(" ", "_")}_{i}',
                email=f'agent{i}@{business.name.lower().replace(" ", "")}.com',
                first_name=fake.first_name(),
                last_name=fake.last_name(),
                role=Profile.Role.AGENT,
                business=business,
                email_confirmed=True,
                profile_completed=fake.boolean(chance_of_getting_true=80),
                onboarding_completed=fake.boolean(chance_of_getting_true=70),
                is_active=True,
                is_staff=False,  # Only for Django admin access
                is_superuser=False,
            )
            agent.set_password('password123')
            agent.save()
            team_members.append(agent)

        # Generate profiles for all team members
        self.update_profiles(team_members)

        self.created_objects['users'].extend(team_members)
        return team_members

    def generate_customer_users(self, count=20):
        """Generate customer users (potential clients)."""
        customers = []

        for i in range(count):
            customer = baker.make(
                User,
                username=f'customer_{i}_{fake.random_int(min=1000, max=9999)}',
                email=fake.email(),
                first_name=fake.first_name(),
                last_name=fake.last_name(),
                role=Profile.Role.CUSTOMER,
                business=None,  # Customers don't belong to businesses
                email_confirmed=fake.boolean(chance_of_getting_true=90),
                profile_completed=fake.boolean(chance_of_getting_true=50),
                onboarding_completed=fake.boolean(chance_of_getting_true=40),
                is_active=True,
                is_staff=False,
                is_superuser=False,
            )
            customer.set_password('password123')
            customer.save()
            customers.append(customer)

        # Generate profiles for customers
        self.update_profiles(customers)

        self.created_objects['users'].extend(customers)
        return customers

    def get_generation_summary(self):
        """Get summary of generated objects."""
        return {key: len(objects) for key, objects in self.created_objects.items()}
