"""
Centralized imports for all factory classes across the application.
This module provides easy access to all factories for testing and demo data generation.
"""

# User and Profile factories
from accounts.factories import (
    AgentFactory,
    BusinessOwnerFactory,
    ProfileFactory,
    SuperuserFactory,
    UserFactory,
)

# Business factories
from businesses.factories import (
    AdventureBusinessFactory,
    BusinessFactory,
    CulturalBusinessFactory,
    SafariBusinessFactory,
)

# Client and Traveler factories
from clients.factories import (
    ClientFactory,
    CorporateClientFactory,
    TravelerFactory,
)

# Invoice factories
from invoices.factories import (
    InvoiceFactory,
)

# Quote factories
from quotes.factories import (
    AdventureQuoteFactory,
    CulturalQuoteFactory,
    QuoteFactory,
    SafariQuoteFactory,
)

__all__ = [
    # User factories
    "UserFactory",
    "BusinessOwnerFactory",
    "AgentFactory",
    "SuperuserFactory",
    "ProfileFactory",
    # Business factories
    "BusinessFactory",
    "SafariBusinessFactory",
    "AdventureBusinessFactory",
    "CulturalBusinessFactory",
    # Client factories
    "ClientFactory",
    "CorporateClientFactory",
    "TravelerFactory",
    # Quote factories
    "QuoteFactory",
    "SafariQuoteFactory",
    "AdventureQuoteFactory",
    "CulturalQuoteFactory",
    # Invoice factories
    "InvoiceFactory",
]
