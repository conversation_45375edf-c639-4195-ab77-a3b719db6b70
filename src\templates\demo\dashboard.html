<c-layouts.dashboard title="Demo Dashboard - {{ business.name|default:'Demo Business' }}"
	subtitle="Explore TourFlow with real sample data">
	<div class="space-y-8">
		<!-- Demo Mode Banner -->
		<div class="bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg p-6 shadow-lg"
			data-intro="Welcome to your TourFlow demo! This banner shows your demo session status and remaining time. You can take a guided tour or end the demo anytime."
			data-step="1">
			<div class="flex items-center justify-between">
				<div>
					<h2 class="text-2xl font-bold mb-2">🚀 Demo Mode Active</h2>
					<p class="text-blue-100">
						You're exploring <strong>{{ business.name }}</strong> with real sample data.
						All features are fully functional in this demo environment.
					</p>
				</div>
				<div class="text-right">
					<div class="bg-white/20 rounded-lg p-4">
						<div class="text-sm text-blue-100">Time Remaining</div>
						<div id="demo-timer" class="text-2xl font-bold" data-expires="{{ expires_at }}">
							{{ time_remaining }}
						</div>
					</div>
				</div>
			</div>
			<div class="mt-4 flex gap-3">
				<button onclick="startFeatureTour()"
					class="bg-white/20 hover:bg-white/30 px-4 py-2 rounded-lg transition-colors">
					<i class="fas fa-route mr-2"></i>Take Feature Tour
				</button>
				<button onclick="endDemo()" class="bg-red-500/80 hover:bg-red-500 px-4 py-2 rounded-lg transition-colors">
					<i class="fas fa-stop mr-2"></i>End Demo
				</button>
			</div>
		</div>

		<!-- Demo Statistics -->
		<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
			data-intro="These statistics show your business performance at a glance. Each card displays key metrics and links to detailed views."
			data-step="2">
			<!-- Total Clients -->
			<div class="bg-white rounded-lg shadow p-6 hover:shadow-md transition-shadow"
				data-intro="Client management is the heart of your tour business. Track all your customers, their contact information, and booking history."
				data-step="3">
				<div class="flex items-center">
					<div class="flex-shrink-0">
						<div class="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
							<i class="fas fa-users text-white"></i>
						</div>
					</div>
					<div class="ml-4 flex-1">
						<p class="text-sm font-medium text-gray-500">Total Clients</p>
						<p class="text-2xl font-semibold text-gray-900">{{ stats.total_clients }}</p>
						<div class="mt-2">
							<a href="{% url 'clients:client-list' %}" class="text-blue-600 hover:text-blue-700 text-sm font-medium">
								View all clients →
							</a>
						</div>
					</div>
				</div>
			</div>

			<!-- Active Quotes -->
			<div class="bg-white rounded-lg shadow p-6 hover:shadow-md transition-shadow"
				data-intro="Create professional quotes with multiple tour options, pricing tiers, and terms. Track quote status from draft to approved."
				data-step="4">
				<div class="flex items-center">
					<div class="flex-shrink-0">
						<div class="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center">
							<i class="fas fa-file-invoice-dollar text-white"></i>
						</div>
					</div>
					<div class="ml-4 flex-1">
						<p class="text-sm font-medium text-gray-500">Active Quotes</p>
						<p class="text-2xl font-semibold text-gray-900">{{ stats.active_quotes }}</p>
						<div class="mt-2">
							<a href="{% url 'quotes:quote-list' %}" class="text-green-600 hover:text-green-700 text-sm font-medium">
								View all quotes →
							</a>
						</div>
					</div>
				</div>
			</div>

			<!-- Total Bookings -->
			<div class="bg-white rounded-lg shadow p-6 hover:shadow-md transition-shadow"
				data-intro="Manage tour bookings and schedules. Track confirmed bookings, manage capacity, and coordinate with your team."
				data-step="5">
				<div class="flex items-center">
					<div class="flex-shrink-0">
						<div class="w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center">
							<i class="fas fa-calendar-check text-white"></i>
						</div>
					</div>
					<div class="ml-4 flex-1">
						<p class="text-sm font-medium text-gray-500">Total Bookings</p>
						<p class="text-2xl font-semibold text-gray-900">{{ stats.total_bookings }}</p>
						<div class="mt-2">
							<a href="{% url 'bookings:booking_list' %}"
								class="text-purple-600 hover:text-purple-700 text-sm font-medium">
								View all bookings →
							</a>
						</div>
					</div>
				</div>
			</div>

			<!-- Pending Invoices -->
			<div class="bg-white rounded-lg shadow p-6 hover:shadow-md transition-shadow"
				data-intro="Generate and send professional invoices. Track payment status and manage your business finances efficiently."
				data-step="6">
				<div class="flex items-center">
					<div class="flex-shrink-0">
						<div class="w-10 h-10 bg-yellow-500 rounded-lg flex items-center justify-center">
							<i class="fas fa-receipt text-white"></i>
						</div>
					</div>
					<div class="ml-4 flex-1">
						<p class="text-sm font-medium text-gray-500">Pending Invoices</p>
						<p class="text-2xl font-semibold text-gray-900">{{ stats.pending_invoices }}</p>
						<div class="mt-2">
							<a href="{% url 'invoices:list' %}" class="text-yellow-600 hover:text-yellow-700 text-sm font-medium">
								View all invoices →
							</a>
						</div>
					</div>
				</div>
			</div>
		</div>

		<!-- Feature Showcase -->
		<div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
			<!-- Recent Clients -->
			<div class="bg-white rounded-lg shadow"
				data-intro="View your most recent clients and their information. Click 'View all clients' to access the full client management system."
				data-step="7">
				<div class="px-6 py-4 border-b border-gray-200">
					<h3 class="text-lg font-medium text-gray-900">Recent Clients</h3>
					<p class="text-sm text-gray-500">Sample customer data for demonstration</p>
				</div>
				<div class="p-6">
					{% if recent_clients %}
					<div class="space-y-4">
						{% for client in recent_clients %}
						<div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
							<div>
								<p class="font-medium text-gray-900">{{ client.display_name }}</p>
								<p class="text-sm text-gray-500">{{ client.email }}</p>
							</div>
							<span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">Active</span>
						</div>
						{% endfor %}
					</div>
					<div class="mt-4">
						<a href="{% url 'clients:client-list' %}" class="text-blue-600 hover:text-blue-700 text-sm font-medium">
							View all clients →
						</a>
					</div>
					{% else %}
					<p class="text-gray-500">No clients yet. <a href="{% url 'clients:client-create' %}"
							class="text-blue-600 hover:text-blue-700">Add your first client</a></p>
					{% endif %}
				</div>
			</div>

			<!-- Recent Quotes -->
			<div class="bg-white rounded-lg shadow"
				data-intro="See your latest quotes with pricing and status. Create professional quotes with multiple options and track their approval status."
				data-step="8">
				<div class="px-6 py-4 border-b border-gray-200">
					<h3 class="text-lg font-medium text-gray-900">Recent Quotes</h3>
					<p class="text-sm text-gray-500">Sample quotes with realistic pricing</p>
				</div>
				<div class="p-6">
					{% if recent_quotes %}
					<div class="space-y-4">
						{% for quote in recent_quotes %}
						<div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
							<div>
								<p class="font-medium text-gray-900">{{ quote.title }}</p>
								<p class="text-sm text-gray-500">${{ quote.total_amount }}</p>
							</div>
							<span
								class="text-xs {% if quote.status == 'approved' %}bg-green-100 text-green-800{% elif quote.status == 'sent' %}bg-blue-100 text-blue-800{% else %}bg-gray-100 text-gray-800{% endif %} px-2 py-1 rounded-full">
								{{ quote.get_status_display }}
							</span>
						</div>
						{% endfor %}
					</div>
					<div class="mt-4">
						<a href="{% url 'quotes:quote-list' %}" class="text-blue-600 hover:text-blue-700 text-sm font-medium">
							View all quotes →
						</a>
					</div>
					{% else %}
					<p class="text-gray-500">No quotes yet. <a href="{% url 'quotes:quote-create' %}"
							class="text-blue-600 hover:text-blue-700">Create your first quote</a></p>
					{% endif %}
				</div>
			</div>
		</div>

		<!-- Demo Features Grid -->
		<div class="bg-white rounded-lg shadow p-6"
			data-intro="This is your feature navigation center. Click on any feature to explore the full functionality with your demo data. You have {{ time_remaining }} remaining to explore everything!"
			data-step="9">
			<h3 class="text-lg font-medium text-gray-900 mb-4">Explore All Features</h3>
			<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
				<a href="{% url 'clients:client-list' %}"
					class="p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:shadow-md transition-all">
					<div class="text-center">
						<i class="fas fa-users text-2xl text-blue-500 mb-2"></i>
						<h4 class="font-medium text-gray-900">Client Management</h4>
						<p class="text-sm text-gray-500 mt-1">Manage customer profiles and contact information</p>
					</div>
				</a>

				<a href="{% url 'quotes:quote-list' %}"
					class="p-4 border border-gray-200 rounded-lg hover:border-green-300 hover:shadow-md transition-all">
					<div class="text-center">
						<i class="fas fa-file-invoice-dollar text-2xl text-green-500 mb-2"></i>
						<h4 class="font-medium text-gray-900">Quote System</h4>
						<p class="text-sm text-gray-500 mt-1">Create professional quotes with multiple options</p>
					</div>
				</a>

				<a href="{% url 'bookings:booking_list' %}"
					class="p-4 border border-gray-200 rounded-lg hover:border-purple-300 hover:shadow-md transition-all">
					<div class="text-center">
						<i class="fas fa-calendar-check text-2xl text-purple-500 mb-2"></i>
						<h4 class="font-medium text-gray-900">Booking System</h4>
						<p class="text-sm text-gray-500 mt-1">Schedule tours and manage capacity</p>
					</div>
				</a>

				<a href="{% url 'invoices:list' %}"
					class="p-4 border border-gray-200 rounded-lg hover:border-yellow-300 hover:shadow-md transition-all">
					<div class="text-center">
						<i class="fas fa-receipt text-2xl text-yellow-500 mb-2"></i>
						<h4 class="font-medium text-gray-900">Invoice Management</h4>
						<p class="text-sm text-gray-500 mt-1">Generate and track invoices automatically</p>
					</div>
				</a>
			</div>
		</div>
	</div>

	<!-- Custom Intro.js Styling -->
	<style>
		/* Custom Intro.js styling to match TourFlow design */
		.introjs-custom-tooltip {
			background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
			color: white;
			border-radius: 12px;
			box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
			border: none;
			font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
		}

		.introjs-custom-tooltip .introjs-tooltiptext {
			color: white;
			font-size: 16px;
			line-height: 1.6;
		}

		.introjs-custom-tooltip .introjs-tooltipbuttons {
			border-top: 1px solid rgba(255, 255, 255, 0.2);
			padding-top: 15px;
			margin-top: 15px;
		}

		.introjs-custom-tooltip .introjs-button {
			background: rgba(255, 255, 255, 0.2);
			color: white;
			border: 1px solid rgba(255, 255, 255, 0.3);
			border-radius: 8px;
			padding: 8px 16px;
			font-weight: 500;
			transition: all 0.2s ease;
		}

		.introjs-custom-tooltip .introjs-button:hover {
			background: rgba(255, 255, 255, 0.3);
			transform: translateY(-1px);
		}

		.introjs-custom-tooltip .introjs-nextbutton {
			background: white;
			color: #3b82f6;
		}

		.introjs-custom-tooltip .introjs-nextbutton:hover {
			background: #f8fafc;
			color: #2563eb;
		}

		.introjs-custom-highlight {
			border-radius: 8px;
			box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.3);
		}

		.introjs-overlay {
			background: rgba(0, 0, 0, 0.8);
		}

		.introjs-progressbar {
			background: linear-gradient(90deg, #3b82f6 0%, #8b5cf6 100%);
		}

		.introjs-progress {
			background: rgba(255, 255, 255, 0.2);
		}
	</style>

	<script>
		// Demo timer countdown
		function updateDemoTimer() {
			const timerElement = document.getElementById('demo-timer');
			if (!timerElement) return;

			const expiresAt = timerElement.dataset.expires;
			if (!expiresAt) return;

			const now = new Date();
			const expires = new Date(expiresAt);
			const remaining = expires - now;

			if (remaining <= 0) {
				timerElement.textContent = 'Expired';
				timerElement.classList.add('text-red-400');
				return;
			}

			const hours = Math.floor(remaining / (1000 * 60 * 60));
			const minutes = Math.floor((remaining % (1000 * 60 * 60)) / (1000 * 60));
			const seconds = Math.floor((remaining % (1000 * 60)) / 1000);

			timerElement.textContent = `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
		}

		// Update timer every second
		setInterval(updateDemoTimer, 1000);
		updateDemoTimer();

		// End demo function
		function endDemo() {
			if (confirm('Are you sure you want to end the demo session? All demo data will be deleted.')) {
				fetch('{% url "end_demo" %}', {
					method: 'POST',
					headers: {
						'X-CSRFToken': '{{ csrf_token }}',
						'Content-Type': 'application/json'
					}
				})
					.then(response => response.json())
					.then(data => {
						if (data.success) {
							window.location.href = data.redirect_url;
						} else {
							alert('Error ending demo: ' + data.error);
						}
					})
					.catch(error => {
						console.error('Error:', error);
						alert('Failed to end demo session');
					});
			}
		}

		// Feature tour function
		function startFeatureTour() {
			// Start Intro.js tour
			introJs().setOptions({
				nextLabel: 'Next →',
				prevLabel: '← Back',
				skipLabel: 'Skip Tour',
				doneLabel: 'Finish Tour',
				showProgress: true,
				showBullets: false,
				exitOnOverlayClick: false,
				exitOnEsc: true,
				scrollToElement: true,
				overlayOpacity: 0.8,
				tooltipClass: 'introjs-custom-tooltip',
				highlightClass: 'introjs-custom-highlight',
				helperElementPadding: 10,
				showStepNumbers: true,
				keyboardNavigation: true,
				disableInteraction: false
			}).oncomplete(function () {
				// Tour completed
				console.log('Feature tour completed');
				// Optional: Track tour completion
				fetch('{% url "demo_feature_tour" %}', {
					method: 'POST',
					headers: {
						'X-CSRFToken': '{{ csrf_token }}',
						'Content-Type': 'application/json'
					},
					body: JSON.stringify({ action: 'tour_completed' })
				}).catch(error => console.log('Tour tracking error:', error));
			}).onexit(function () {
				// Tour exited
				console.log('Feature tour exited');
			}).onbeforechange(function (targetElement) {
				// Before each step
				console.log('Moving to step:', targetElement);
			}).start();
		}
	</script>
	</c-layout>
