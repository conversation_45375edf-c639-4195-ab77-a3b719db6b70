"""
URL configuration for Tour Business Management SaaS.
"""

from django.conf import settings
from django.conf.urls.static import static
from django.contrib import admin
from django.urls import include, path
from pictures.conf import get_settings

from core.demo_views import (
    DemoDashboardView,
    DemoInfoView,
    demo_feature_tour,
    demo_status,
    end_demo,
    start_demo,
)

urlpatterns = [
    path(
        '',
        include('core.urls'),
    ),
    # Demo URLs
    path(
        'demo/',
        DemoInfoView.as_view(),
        name='demo_info',
    ),
    path(
        'demo/test/',
        DemoInfoView.as_view(template_name='demo/test.html'),
        name='demo_test',
    ),
    path('demo/start/', start_demo, name='start_demo'),
    path('demo/end/', end_demo, name='end_demo'),
    path('demo/status/', demo_status, name='demo_status'),
    path('demo/dashboard/', DemoDashboardView.as_view(), name='demo_dashboard'),
    path('demo/tour/', demo_feature_tour, name='demo_feature_tour'),
    path('admin/', admin.site.urls),
    path('rosetta/', include('rosetta.urls')),
    path('__reload__/', include('django_browser_reload.urls')),
    # Authentication URLs
    path('accounts/', include('accounts.urls')),
    # App URLs
    path('businesses/', include('businesses.urls')),
    path('clients/', include('clients.urls')),
    path('quotes/', include('quotes.urls')),
    path('invoices/', include('invoices.urls')),
    path('payments/', include('payments.urls')),
    path('bookings/', include('bookings.urls')),
    path('tours/', include('tours.urls')),
    path('data_generator/', include('data_generator.urls')),
    path('silk/', include('silk.urls', namespace='silk')),
]

if settings.DEBUG:
    # Serve media files in development
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)

    # Django Debug Toolbar and Django Herald
    import debug_toolbar

    urlpatterns = [
        path('__debug__/', include(debug_toolbar.urls)),
        path('herald/', include('herald.urls')),
    ] + urlpatterns

if get_settings().USE_PLACEHOLDERS:
    urlpatterns += [
        path('_pictures/', include('pictures.urls')),
    ]
