"""
Logging utilities for the Tour Business Management system.
Provides consistent logging patterns and audit trail functionality.
"""

import logging
from functools import wraps
from typing import Any, Dict, Optional
from django.contrib.auth import get_user_model
from django.http import HttpRequest
from django.utils import timezone

User = get_user_model()

# Get loggers
logger = logging.getLogger('tour_business')
audit_logger = logging.getLogger('tour_business.audit')
security_logger = logging.getLogger('tour_business.security')


class AuditLogger:
    """Centralized audit logging for business operations."""
    
    @staticmethod
    def log_user_action(user: User, action: str, resource: str, resource_id: Optional[str] = None, 
                       details: Optional[Dict[str, Any]] = None, request: Optional[HttpRequest] = None):
        """Log user actions for audit trail."""
        log_data = {
            'user_id': str(user.id) if user else 'anonymous',
            'username': user.username if user else 'anonymous',
            'action': action,
            'resource': resource,
            'resource_id': resource_id,
            'timestamp': timezone.now().isoformat(),
        }
        
        if request:
            log_data.update({
                'ip_address': get_client_ip(request),
                'user_agent': request.META.get('HTTP_USER_AGENT', ''),
                'method': request.method,
                'path': request.path,
            })
        
        if details:
            log_data['details'] = details
        
        # Format log message
        message = f"User {log_data['username']} performed {action} on {resource}"
        if resource_id:
            message += f" (ID: {resource_id})"
        
        audit_logger.info(message, extra=log_data)
    
    @staticmethod
    def log_business_operation(business_id: str, operation: str, user: User, 
                             details: Optional[Dict[str, Any]] = None):
        """Log business-specific operations."""
        log_data = {
            'business_id': business_id,
            'user_id': str(user.id),
            'username': user.username,
            'operation': operation,
            'timestamp': timezone.now().isoformat(),
        }
        
        if details:
            log_data['details'] = details
        
        message = f"Business operation: {operation} by {user.username} for business {business_id}"
        audit_logger.info(message, extra=log_data)
    
    @staticmethod
    def log_data_access(user: User, resource: str, action: str = 'view', 
                       count: Optional[int] = None, filters: Optional[Dict[str, Any]] = None):
        """Log data access for compliance."""
        log_data = {
            'user_id': str(user.id),
            'username': user.username,
            'resource': resource,
            'action': action,
            'timestamp': timezone.now().isoformat(),
        }
        
        if count is not None:
            log_data['record_count'] = count
        
        if filters:
            log_data['filters'] = filters
        
        message = f"Data access: {user.username} {action} {resource}"
        if count is not None:
            message += f" ({count} records)"
        
        audit_logger.info(message, extra=log_data)


class SecurityLogger:
    """Security-focused logging for authentication and authorization events."""
    
    @staticmethod
    def log_login_attempt(username: str, success: bool, request: HttpRequest, 
                         failure_reason: Optional[str] = None):
        """Log login attempts."""
        log_data = {
            'username': username,
            'success': success,
            'ip_address': get_client_ip(request),
            'user_agent': request.META.get('HTTP_USER_AGENT', ''),
            'timestamp': timezone.now().isoformat(),
        }
        
        if not success and failure_reason:
            log_data['failure_reason'] = failure_reason
        
        level = logging.INFO if success else logging.WARNING
        message = f"Login {'successful' if success else 'failed'} for user {username}"
        
        security_logger.log(level, message, extra=log_data)
    
    @staticmethod
    def log_permission_denied(user: User, resource: str, action: str, request: HttpRequest):
        """Log permission denied events."""
        log_data = {
            'user_id': str(user.id) if user.is_authenticated else 'anonymous',
            'username': user.username if user.is_authenticated else 'anonymous',
            'resource': resource,
            'action': action,
            'ip_address': get_client_ip(request),
            'path': request.path,
            'method': request.method,
            'timestamp': timezone.now().isoformat(),
        }
        
        message = f"Permission denied: {log_data['username']} attempted {action} on {resource}"
        security_logger.warning(message, extra=log_data)
    
    @staticmethod
    def log_suspicious_activity(user: User, activity: str, details: Dict[str, Any], 
                              request: Optional[HttpRequest] = None):
        """Log suspicious activities."""
        log_data = {
            'user_id': str(user.id) if user.is_authenticated else 'anonymous',
            'username': user.username if user.is_authenticated else 'anonymous',
            'activity': activity,
            'details': details,
            'timestamp': timezone.now().isoformat(),
        }
        
        if request:
            log_data.update({
                'ip_address': get_client_ip(request),
                'user_agent': request.META.get('HTTP_USER_AGENT', ''),
                'path': request.path,
            })
        
        message = f"Suspicious activity: {activity} by {log_data['username']}"
        security_logger.warning(message, extra=log_data)


def get_client_ip(request: HttpRequest) -> str:
    """Extract client IP address from request."""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip


def log_view_access(resource_name: str):
    """Decorator to log view access."""
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            if request.user.is_authenticated:
                AuditLogger.log_user_action(
                    user=request.user,
                    action='view',
                    resource=resource_name,
                    request=request
                )
            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator


def log_model_changes(action: str):
    """Decorator to log model changes."""
    def decorator(func):
        @wraps(func)
        def wrapper(self, *args, **kwargs):
            # Get user from request if available
            user = None
            if hasattr(self, 'request') and hasattr(self.request, 'user'):
                user = self.request.user
            
            # Execute the function
            result = func(self, *args, **kwargs)
            
            # Log the action
            if user and user.is_authenticated:
                model_name = self.__class__.__name__.replace('View', '').lower()
                resource_id = getattr(self, 'object', None)
                if resource_id and hasattr(resource_id, 'pk'):
                    resource_id = str(resource_id.pk)
                
                AuditLogger.log_user_action(
                    user=user,
                    action=action,
                    resource=model_name,
                    resource_id=resource_id,
                    request=getattr(self, 'request', None)
                )
            
            return result
        return wrapper
    return decorator


class BusinessLogger:
    """Business-specific logging utilities."""
    
    @staticmethod
    def log_quote_action(user: User, quote, action: str, details: Optional[Dict[str, Any]] = None):
        """Log quote-related actions."""
        AuditLogger.log_user_action(
            user=user,
            action=action,
            resource='quote',
            resource_id=str(quote.id),
            details={
                'quote_number': quote.quote_number,
                'client': quote.client.full_name,
                'total_amount': str(quote.total_amount),
                **(details or {})
            }
        )
    
    @staticmethod
    def log_booking_action(user: User, booking, action: str, details: Optional[Dict[str, Any]] = None):
        """Log booking-related actions."""
        AuditLogger.log_user_action(
            user=user,
            action=action,
            resource='booking',
            resource_id=str(booking.id),
            details={
                'booking_reference': booking.booking_reference,
                'client': booking.client.display_name,
                'total_amount': str(booking.total_amount),
                'status': booking.status,
                **(details or {})
            }
        )
    
    @staticmethod
    def log_invoice_action(user: User, invoice, action: str, details: Optional[Dict[str, Any]] = None):
        """Log invoice-related actions."""
        AuditLogger.log_user_action(
            user=user,
            action=action,
            resource='invoice',
            resource_id=str(invoice.id),
            details={
                'invoice_number': invoice.invoice_number,
                'client': invoice.client.full_name,
                'total_amount': str(invoice.total_amount),
                'status': invoice.status,
                **(details or {})
            }
        )
    
    @staticmethod
    def log_payment_action(user: User, payment, action: str, details: Optional[Dict[str, Any]] = None):
        """Log payment-related actions."""
        AuditLogger.log_user_action(
            user=user,
            action=action,
            resource='payment',
            resource_id=str(payment.id),
            details={
                'amount': str(payment.amount),
                'method': payment.payment_method,
                'status': payment.status,
                **(details or {})
            }
        )


# Convenience functions for common logging patterns
def log_info(message: str, extra: Optional[Dict[str, Any]] = None):
    """Log info message."""
    logger.info(message, extra=extra)


def log_warning(message: str, extra: Optional[Dict[str, Any]] = None):
    """Log warning message."""
    logger.warning(message, extra=extra)


def log_error(message: str, extra: Optional[Dict[str, Any]] = None, exc_info: bool = False):
    """Log error message."""
    logger.error(message, extra=extra, exc_info=exc_info)


def log_debug(message: str, extra: Optional[Dict[str, Any]] = None):
    """Log debug message."""
    logger.debug(message, extra=extra)
