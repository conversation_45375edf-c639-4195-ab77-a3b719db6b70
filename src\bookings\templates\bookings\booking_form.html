{% load static %}
{% load crispy_forms_tags %}

<c-layouts.dashboard>
  <c-slot name="title">
    {% if object %}Edit Booking{% else %}New Booking{% endif %}
  </c-slot>

  <c-slot name="main">
    <div class="container mx-auto px-4 py-8">
      <div class="max-w-2xl mx-auto">
        <div class="flex justify-between items-center mb-8">
          <h1 class="text-3xl font-bold text-gray-900">
            {% if object %}Edit Booking {{ object.booking_reference }}{% else %}New Booking{% endif %}
          </h1>
          <a href="{% if object %}{% url 'bookings:booking_detail' object.pk %}{% else %}{% url 'bookings:booking_list' %}{% endif %}"
            class="text-gray-600 hover:text-gray-800">
            ← Back
          </a>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
          <form method="post" class="space-y-6">
            {% csrf_token %}
            {% crispy form %}

            <!-- Form Actions -->
            <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
              <a href="{% if object %}{% url 'bookings:booking_detail' object.pk %}{% else %}{% url 'bookings:booking_list' %}{% endif %}"
                class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300 transition-colors">
                Cancel
              </a>
              <button type="submit"
                class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 transition-colors">
                {% if object %}Update Booking{% else %}Create Booking{% endif %}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <style>
      /* Form field styling */
      form select,
      form input[type="text"],
      form input[type="number"],
      form input[type="email"],
      form textarea {
        @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500;
      }

      form textarea {
        @apply resize-vertical;
        min-height: 80px;
      }

      form input[type="checkbox"] {
        @apply h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded;
      }
    </style>
  </c-slot>
</c-layouts.dashboard>
