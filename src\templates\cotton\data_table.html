<c-vars 
    headers="[]"
    rows="[]"
    actions="[]"
    searchable="True"
    sortable="True"
    paginated="True"
    class=""
    table_id=""
/>

<div class="data-table-container {{ class }}" 
     {% if table_id %}id="{{ table_id }}"{% endif %}>
    
    {% if searchable %}
        <div class="table-controls mb-4">
            <div class="flex justify-between items-center">
                <div class="search-box">
                    <input type="text" 
                           placeholder="Search..." 
                           class="form-input w-64"
                           data-search-target="{{ table_id }}-table">
                </div>
                
                {% if actions %}
                    <div class="table-actions space-x-2">
                        {% for action in actions %}
                            <button class="btn btn-sm {{ action.class|default:'btn-primary' }}"
                                    onclick="{{ action.onclick }}">
                                {{ action.label }}
                            </button>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>
        </div>
    {% endif %}
    
    <div class="table-wrapper overflow-x-auto">
        <table class="data-table w-full" 
               {% if table_id %}id="{{ table_id }}-table"{% endif %}>
            <thead class="bg-gray-50">
                <tr>
                    {% for header in headers %}
                        <th class="table-header {% if sortable %}sortable{% endif %}"
                            {% if header.sortable %}data-sort="{{ header.key }}"{% endif %}>
                            <div class="flex items-center justify-between">
                                <span>{{ header.label }}</span>
                                {% if sortable and header.sortable %}
                                    <svg class="w-4 h-4 text-gray-400 sort-icon" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M5 12l5-5 5 5H5z"></path>
                                    </svg>
                                {% endif %}
                            </div>
                        </th>
                    {% endfor %}
                    {% if actions %}
                        <th class="table-header">Actions</th>
                    {% endif %}
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for row in rows %}
                    <tr class="table-row hover:bg-gray-50">
                        {% for header in headers %}
                            <td class="table-cell">
                                {% if header.template %}
                                    {% include header.template with item=row %}
                                {% else %}
                                    {{ row|lookup:header.key }}
                                {% endif %}
                            </td>
                        {% endfor %}
                        {% if actions %}
                            <td class="table-cell">
                                <div class="flex space-x-2">
                                    {% for action in actions %}
                                        {% if action.condition %}
                                            {% if row|lookup:action.condition %}
                                                <a href="{{ action.url|replace:'__ID__':row.id }}" 
                                                   class="btn btn-xs {{ action.class|default:'btn-primary' }}">
                                                    {{ action.label }}
                                                </a>
                                            {% endif %}
                                        {% else %}
                                            <a href="{{ action.url|replace:'__ID__':row.id }}" 
                                               class="btn btn-xs {{ action.class|default:'btn-primary' }}">
                                                {{ action.label }}
                                            </a>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                            </td>
                        {% endif %}
                    </tr>
                {% empty %}
                    <tr>
                        <td colspan="{{ headers|length|add:1 }}" class="table-cell text-center text-gray-500 py-8">
                            No data available
                        </td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    
    {% if paginated and page_obj %}
        <div class="table-pagination mt-4">
            <div class="flex justify-between items-center">
                <div class="pagination-info text-sm text-gray-600">
                    Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} 
                    of {{ page_obj.paginator.count }} results
                </div>
                
                <div class="pagination-controls">
                    <nav class="flex space-x-1">
                        {% if page_obj.has_previous %}
                            <a href="?page=1" class="pagination-btn">First</a>
                            <a href="?page={{ page_obj.previous_page_number }}" class="pagination-btn">Previous</a>
                        {% endif %}
                        
                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                                <span class="pagination-btn pagination-current">{{ num }}</span>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <a href="?page={{ num }}" class="pagination-btn">{{ num }}</a>
                            {% endif %}
                        {% endfor %}
                        
                        {% if page_obj.has_next %}
                            <a href="?page={{ page_obj.next_page_number }}" class="pagination-btn">Next</a>
                            <a href="?page={{ page_obj.paginator.num_pages }}" class="pagination-btn">Last</a>
                        {% endif %}
                    </nav>
                </div>
            </div>
        </div>
    {% endif %}
</div>

<style>
    .data-table {
        @apply min-w-full divide-y divide-gray-200;
    }
    
    .table-header {
        @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
    }
    
    .table-header.sortable {
        @apply cursor-pointer hover:bg-gray-100;
    }
    
    .table-cell {
        @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
    }
    
    .table-row {
        @apply transition-colors duration-150;
    }
    
    .pagination-btn {
        @apply px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 hover:bg-gray-50 hover:text-gray-700;
    }
    
    .pagination-current {
        @apply bg-blue-50 border-blue-500 text-blue-600;
    }
    
    .sort-icon {
        @apply transition-transform duration-200;
    }
    
    .sort-asc .sort-icon {
        @apply transform rotate-180;
    }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Search functionality
    const searchInputs = document.querySelectorAll('[data-search-target]');
    searchInputs.forEach(input => {
        const tableId = input.getAttribute('data-search-target');
        const table = document.getElementById(tableId);
        
        input.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const rows = table.querySelectorAll('tbody tr');
            
            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                row.style.display = text.includes(searchTerm) ? '' : 'none';
            });
        });
    });
    
    // Sort functionality
    const sortableHeaders = document.querySelectorAll('.sortable[data-sort]');
    sortableHeaders.forEach(header => {
        header.addEventListener('click', function() {
            const sortKey = this.getAttribute('data-sort');
            const table = this.closest('table');
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));
            
            // Toggle sort direction
            const isAsc = this.classList.contains('sort-asc');
            
            // Remove sort classes from all headers
            sortableHeaders.forEach(h => h.classList.remove('sort-asc', 'sort-desc'));
            
            // Add sort class to current header
            this.classList.add(isAsc ? 'sort-desc' : 'sort-asc');
            
            // Sort rows (simplified - would need more complex logic for different data types)
            rows.sort((a, b) => {
                const aText = a.textContent.trim();
                const bText = b.textContent.trim();
                return isAsc ? bText.localeCompare(aText) : aText.localeCompare(bText);
            });
            
            // Reorder rows in DOM
            rows.forEach(row => tbody.appendChild(row));
        });
    });
});
</script>
